@tailwind base;
@tailwind components;
@tailwind utilities;
@import './_font-roboto';

@font-face {
  font-family: 'vie';
  src: url('/fonts/vie-font/vie.ttf?viefv617') format('truetype'),
    url('/fonts/vie-font/vie.woff?viefv617') format('woff'),
    url('/fonts/vie-font/vie.svg?viefv617#vie') format('svg');
  font-weight: normal;
  font-style: normal;
}

@layer utilities {
  .custom-gradient {
    @apply bg-custom-radial;
  }
  .custom-gradient-1 {
    @apply bg-custom-radial-1;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

body {
  @apply bg-[#111] font-Roboto;
}

textarea {
  @apply mb-4 p-2;
}

/* Update CSS React-DatePicker */
.react-datepicker__triangle {
  left: 21% !important;
  fill: #595959 !important;
  color: #595959 !important;
  stroke: unset !important;
}

.react-datepicker__input-container {
  border-bottom: 1px solid #646464;
}

.react-datepicker__input-container {
  border-bottom: 1px solid #646464;
}

.react-datepicker__month-container {
  position: relative;
  z-index: 10;
}

.react-datepicker__month-container .react-datepicker__header {
  background-color: #595959;
  border: none;
  position: unset;
  padding-left: 8px;
  padding-right: 8px;
}

.react-datepicker__month-container .react-datepicker__header .react-datepicker__current-month {
  padding-bottom: 4px;
  font-weight: 500;
}

.react-datepicker__header .react-datepicker__day-name,
.react-datepicker__week .react-datepicker__day {
  width: 36px;
  height: 36px;
  font-size: 14px;
  line-height: 34px;
}

.react-datepicker__month-container .react-datepicker__month {
  background-color: #222;
  margin: 0;
}

.react-datepicker__week .react-datepicker__day:hover {
  background-color: #383838;
  transition: background-color 0.2s ease-in-out;
}

.react-datepicker-popper .react-datepicker {
  border: none;
  font-family: Roboto;
  border-radius: 8px;
  display: block;
}

.react-datepicker .react-datepicker__navigation-icon::before {
  width: 10px;
  height: 10px;
  border-color: #ffffff;
}

.react-datepicker__week .react-datepicker__day.react-datepicker__day--selected {
  background-color: transparent;
  border: 1px solid #3ac882;
  border-radius: 4px;
  color: #3ac882;
}

.react-datepicker__week .react-datepicker__day.react-datepicker__day--keyboard-selected,
.react-datepicker__week .react-datepicker__day.react-datepicker__day--today {
  background-color: #3ac882;
  border: 1px solid #3ac882;
  border-radius: 4px;
  color: #ffffff !important;
}

.react-datepicker__month-container .react-datepicker__header div,
.react-datepicker__month-container .react-datepicker__month div {
  color: #ffffff;

  [aria-disabled='true'] {
    color: #404040;
  }
}

@media screen and (max-width: 1440px) {
  .react-datepicker__week .react-datepicker__day,
  .react-datepicker__header .react-datepicker__day-name {
    width: 32px;
    height: 32px;
    line-height: 26px;
  }
  .react-datepicker__month-container .react-datepicker__header {
    padding-bottom: 0;
    padding-top: 2px;
  }
  .react-datepicker__month-container .react-datepicker__day-names {
    height: 28px;
    display: flex;
    align-items: center;
    margin-bottom: 0;
    position: relative;
    z-index: 0;
  }
}

@media screen and (max-width: 1280px) {
  .react-datepicker__week .react-datepicker__day,
  .react-datepicker__header .react-datepicker__day-name {
    width: 28px;
    height: 28px;
    line-height: 28px;
  }
}

/* End Update CSS React-DatePicker */

@layer base {
  img {
    display: inline-block;
  }
}

#pause-player-ads {
  display: flex;
  justify-content: center;
  align-items: center;
}
/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: #888 #f1f1f1; /* For Firefox */
  padding-right: 5px;
  box-sizing: content-box;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 2px; /* Width of the scrollbar */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* Background of the scrollbar track */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #888; /* Color of the scrollbar thumb */
  border-radius: 10px; /* Roundness of the scrollbar thumb */
  border: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555; /* Color of the scrollbar thumb on hover */
}

.modal-limit-region {
  @apply space-y-[20px];
}

.grecaptcha-badge {
  @apply hidden;
}
