import React, { useEffect, useMemo, useRef, useState } from 'react';
import DatePicker, { registerLocale } from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import classNames from 'classnames';
import { getMonth, getYear } from 'date-fns';
import { vi, el } from 'date-fns/locale';
import IconArrowLeft from '../components/Icons/IconArrowLeft';
import { useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';
import styles from './Styles.module.scss';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { BREAKPOINT, DATE_FORMAT, LANG } from '@vieon/core/constants/constants';
import Image from '../basic/Image/Image';
import FramePicker from './FramePicker';
import Button from '../basic/Buttons/Button';

const cx = classNames.bind(styles);
const STEPS = {
  MONTH: 'month',
  YEAR: 'year'
};

const DatePickerCustom = ({
  value,
  onChange,
  label,
  message,
  id,
  labelClass,
  icon,
  isDisabledKeyboardNavigation,
  onFocus,
  onBlur
}: any) => {
  const inputRef = useRef<any>(null);
  const [startDate, setStartDate] = useState(value);
  const [step, setStep] = useState<any>(null);
  const [isFocused, setIsFocused] = useState(false);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  const isActive = useMemo(() => isFocused || !!startDate, [isFocused, startDate]);
  const isExtraExtraLargeScreen = useMediaQuery({ minWidth: BREAKPOINT.XXL });

  useEffect(() => {
    if (isGlobal) {
      registerLocale('el', el as any);
    } else {
      registerLocale('vi', vi as any);
    }
  }, [isGlobal]);

  useEffect(() => {
    if (inputRef.current !== null) {
      inputRef.current.input.inputmode = 'none';
    }
  }, [inputRef]);

  const handleChange = (date: any) => {
    setStartDate(date);
    if (onChange) {
      onChange(date);
    }
  };

  const handleKeyDown = (event: any) => {
    event.preventDefault();
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (onFocus) {
      onFocus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (onBlur) {
      onBlur();
    }
  };

  const months = useMemo(
    () =>
      isGlobal
        ? [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
          ]
        : [
            'Tháng 1',
            'Tháng 2',
            'Tháng 3',
            'Tháng 4',
            'Tháng 5',
            'Tháng 6',
            'Tháng 7',
            'Tháng 8',
            'Tháng 9',
            'Tháng 10',
            'Tháng 11',
            'Tháng 12'
          ],
    [isGlobal]
  );

  return (
    <div className={styles.floatingDatePicker}>
      <DatePicker
        ref={inputRef}
        popperPlacement="bottom-start"
        popperClassName={styles.customPopper}
        locale={isGlobal ? LANG.EN : LANG.VI}
        selected={startDate}
        onChange={handleChange}
        maxDate={new Date()}
        onFocus={handleFocus}
        dropdownMode="select"
        onBlur={handleBlur}
        icon={icon || <Image src={ConfigImage.calendar} alt="calendar" />}
        className={cx(
          styles.datePicker,
          isActive ? styles.datePickerActive : styles.datePickerDisabled
        )}
        id={id}
        placeholderText={isActive ? DATE_FORMAT.SLASH.toLowerCase() : ''}
        dateFormat={DATE_FORMAT.SLASH}
        showIcon={!!icon}
        isClearable={!!startDate}
        onKeyDown={isDisabledKeyboardNavigation ? handleKeyDown : undefined}
        renderCustomHeader={({
          date,
          changeYear,
          changeMonth,
          decreaseMonth,
          increaseMonth,
          prevMonthButtonDisabled,
          nextMonthButtonDisabled
        }: any) => (
          <div className={styles.customHeader}>
            <button
              className={styles.navigatePrev}
              onClick={(e) => {
                e.preventDefault();
                decreaseMonth();
              }}
              disabled={prevMonthButtonDisabled}
              aria-label="Previous Month"
            >
              <IconArrowLeft size={isExtraExtraLargeScreen ? 24 : 18} />
            </button>
            <Button
              className={styles.buttonCurrent}
              onClick={(e: any) => {
                e.preventDefault();
                setStep(STEPS.MONTH);
              }}
              title={`${months[getMonth(date)]} ${getYear(date)}`}
              subTitle={`${months[getMonth(date)]} ${getYear(date)}`}
              disabled={step === STEPS.MONTH}
              textClass="!text-base"
            />
            {!!step && (
              <FramePicker
                isExtraExtraLargeScreen={isExtraExtraLargeScreen}
                isGlobal={isGlobal}
                step={step}
                STEPS={STEPS}
                year={getYear(date)}
                setStep={setStep}
                months={months}
                date={date}
                changeYear={changeYear}
                changeMonth={changeMonth}
              />
            )}
            <button
              className={styles.navigateNext}
              onClick={(e) => {
                e.preventDefault();
                increaseMonth();
              }}
              disabled={nextMonthButtonDisabled}
              aria-label="Next Month"
            >
              <IconArrowLeft size={isExtraExtraLargeScreen ? 24 : 18} className="rotate-180" />
            </button>
          </div>
        )}
      />
      <label
        htmlFor={id}
        className={cx(
          styles.label,
          labelClass,
          { 'left-8': icon },
          isActive ? styles.labelActive : styles.labelDisabled
        )}
      >
        {label}
      </label>
      {!!message && <p className={cx(styles.message, 'text-right')}>{message}</p>}
    </div>
  );
};

export default DatePickerCustom;
