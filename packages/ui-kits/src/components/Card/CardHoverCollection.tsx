import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import classNames from 'classnames';
import { openCardHover } from '@vieon/core/store/actions/popup';
import { getDataRibbonsId } from '@vieon/core/store/actions/page';
import Billboard from '../components/Billboard/Billboard';
import { useOutsideEvent, useMoveOutSideElement } from '@customHook';
import { AnimatePresence, motion } from 'framer-motion';
import { initAnimateScale, animateScale } from '@vieon/core/utils/common';
import styles from './Styles.module.scss';

const CardHoverCollection = () => {
  const dispatch = useDispatch();
  const timerRef = useRef<any>(null);
  const wrapperRef = useRef<any>(null);
  const ref = useRef<any>(null);
  const { cardHover } = useSelector((state: any) => state?.Popup || {});
  const { id, name, isOriginal, seo, displayPosition } = cardHover || {};
  const ribbonData = useSelector((state: any) => state?.Page?.ribbonData);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const [itemIndex, setItemIndex] = useState(0);
  const [isOpen, setOpen] = useState(false);
  const ribbonDataItem = ribbonData?.[seo?.url];
  const cardClassName = classNames(
    'intro intro--preview intro--preview-vod intro--preview-vod--mini',
    isOriginal && 'intro--original',
    styles[`${displayPosition}`]
  );
  const collectionContentItem = useMemo(
    () => ribbonDataItem?.items?.[itemIndex],
    [ribbonDataItem, itemIndex]
  );

  useEffect(
    () => () => {
      onClose();
    },
    []
  );

  useEffect(() => {
    if (id) {
      setOpen(true);
      dispatch(getDataRibbonsId({ id, ribbonName: name, isGlobal }));
    }
  }, [id]);

  useEffect(() => {
    if (ribbonDataItem?.items?.length) {
      timerRef.current = setInterval(() => {
        setItemIndex((prevIndex) => {
          const newIndex = prevIndex + 1 > ribbonDataItem?.items?.length - 1 ? 0 : prevIndex + 1;
          return newIndex;
        });
      }, 3000);
    }
    return () => {
      clearInterval(timerRef.current);
    };
  }, [ribbonDataItem?.items]);

  const onClose = () => {
    setOpen(false);
    dispatch(openCardHover({}));
  };

  useOutsideEvent(wrapperRef, onClose);

  useMoveOutSideElement(ref, onClose);

  const variants = {
    open: animateScale(displayPosition),
    closed: { scale: 0, opacity: 0 }
  };

  const bubbleStyles = {
    ...(cardHover?.bubbleStyles || {})
  };

  const bubbleContainerStyles = {
    ...(cardHover?.bubbleContainerStyles || {}),
    height: 'auto',
    display: 'flex'
  };

  return (
    <div
      className={cardClassName}
      onMouseLeave={onClose}
      id="card-hover-collection"
      ref={ref}
      style={bubbleStyles}
    >
      <div className={classNames('!bg-transparent', styles.cardHoverContainer)}>
        <AnimatePresence>
          <motion.div
            className={classNames(styles.cardHoverWrap)}
            initial={initAnimateScale(displayPosition)}
            animate={isOpen ? 'open' : 'closed'}
            exit={!isOpen ? 'closed' : ''}
            transition={{ duration: 0.45, delay: 0.45 }}
            variants={variants}
            style={bubbleContainerStyles}
            id="CARD_HOVER_COLLECTION"
            ref={wrapperRef}
          >
            <Billboard
              billboardData={{ ...collectionContentItem, isViewCollection: true }}
              cardData={cardHover}
              noPlayer
              fadeIn
              className="billboard--intro"
            />
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default CardHoverCollection;
