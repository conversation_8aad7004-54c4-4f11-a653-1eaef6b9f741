import React, { useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import classNames from 'classnames';
import Billboard from '../components/Billboard/Billboard';
import CardHoverInfo from '../components/Card/CardHoverInfo';
import { getTipData } from '@vieon/core/store/actions/page';
import Transition from '../components/basic/Transition';
import { CONTENT_TYPE, PAGE, RIBBON_TYPE } from '@vieon/core/constants/constants';
import { PLAYER_TYPE } from '@vieon/core/constants/player';
import { watchNowBehavior } from '../components/trigger/triggerFunction';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import LocalStorage from '@vieon/core/config/LocalStorage';
import { NAME, PROPERTY, VALUE } from '@vieon/core/config/ConfigSegment';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { useMoveOutSideElement, useOutsideEvent, useVieRouter } from '@customHook';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import { openCardRelatedHover } from '@vieon/core/store/actions/popup';

const CardRelatedHoverItem = () => {
  const wrapperRef = useRef<any>(null);
  const ref = useRef<any>(null);
  const dispatch = useDispatch();
  const router = useVieRouter();
  const { pathname, query } = router || {};
  const { webConfig, isMobile } = useSelector((state: any) => state?.App || {});
  const searchContents = useSelector((state: any) => state?.Search?.SEARCH_CONTENT);
  const expiredString = get(webConfig, 'tVod.text.expiredString', '');
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
  const { tipData } = useSelector((state: any) => state?.Page || {});
  const { cardRelatedHover } = useSelector((state: any) => state?.Popup || {});
  const {
    id,
    groupId,
    title,
    type,
    index,
    contentTypeTvod,
    isLiveTv,
    isOriginal,
    isMain,
    categoryTracking,
    ribbonType,
    enableBanner,
    onBackToPlayer,
    isEndScreenVod
  } = cardRelatedHover || {};
  const masterBannerData = useSelector((state: any) => {
    const dataMenu = state?.Menu?.activeSubMenu || state?.Menu?.activeMenu || {};
    return state?.Page?.pageBanner?.[dataMenu?.seo?.url];
  });
  const tipDataItem = useMemo(() => {
    if (ribbonType === RIBBON_TYPE.WATCH_MORE && type === CONTENT_TYPE.EPISODE) {
      return tipData?.[groupId];
    }
    return tipData?.[id];
  }, [tipData, cardRelatedHover]);
  const cardClassName = classNames(
    'intro intro--preview intro--preview-vod intro--preview-vod--mini',
    isOriginal && 'intro--original',
    isMain && 'intro--variant',
    enableBanner && '!z-[10001]'
  );

  const wrapStyle = useMemo(
    () => ({
      ...cardRelatedHover?.offset,
      height: 'fit-content',
      top: 'unset',
      bottom: `-${cardRelatedHover.offset.bottom}px`
    }),
    [cardRelatedHover, wrapperRef?.current]
  );

  useEffect(
    () => () => {
      onClose();
    },
    []
  );

  useEffect(() => {
    if (
      id &&
      (isEmpty(tipDataItem) ||
        (ribbonType === RIBBON_TYPE.WATCH_MORE && type === CONTENT_TYPE?.EPISODE)) &&
      (type === CONTENT_TYPE?.SEASON ||
        type === CONTENT_TYPE?.EPISODE ||
        type === CONTENT_TYPE?.MOVIE)
    ) {
      if (ribbonType === RIBBON_TYPE.WATCH_MORE && type === CONTENT_TYPE?.EPISODE) {
        dispatch(getTipData({ id: groupId }));
      } else {
        dispatch(getTipData({ id }));
      }
    }
  }, [id]);

  const handleSaveProgress = () => {
    const player: any = document.getElementById(PLAYER_TYPE.CARD_HOVER);
    if (player) {
      const currentTime = player?.ended ? 0 : player?.currentTime || 0;
      ConfigLocalStorage.set(
        LocalStorage.MINI_PLAYER_PROGRESS,
        JSON.stringify({ [id]: currentTime })
      );
    }
  };

  const onClose = () => {
    handleSaveProgress();
    dispatch(openCardRelatedHover({}));
  };

  useOutsideEvent(wrapperRef, onClose);

  useMoveOutSideElement(ref, onClose);

  const onClickBanner = async () => {
    onClose();
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: cardRelatedHover,
      router,
      dispatch,
      isMobile,
      isEndScreenVod
    });

    if (typeof cardRelatedHover?.onContentSelected === 'function') {
      cardRelatedHover.onContentSelected({ cardData: cardRelatedHover, contentTypeTvod });
    } else {
      TrackingApp.contentSelected({
        data: {
          ...cardRelatedHover,
          seasonThumb: cardRelatedHover?.images?.thumbnail,
          seasonGenre: cardRelatedHover?.genreText
        },
        masterBannerData,
        clickType: VALUE.HOVER_CLICK,
        isLiveTv,
        category: isLiveTv ? categoryTracking : undefined
      });
    }

    if (pathname === PAGE.SEARCH) {
      const searchItems = searchContents?.[query?.q]?.[-1]?.[0]?.items || [];
      const itemPosition = (searchItems || []).findIndex((it: any) => it?.id === id);
      segmentEvent(NAME.SELECT_SEARCH_RESULT, {
        [PROPERTY.KEYWORD]: query?.q,
        [PROPERTY.CONTENT_TITLE]: title,
        [PROPERTY.CONTENT_TYPE]: type,
        [PROPERTY.CONTENT_POSITION]: itemPosition || index || 0,
        [PROPERTY.CURRENT_PAGE]: window?.location?.href
      });
    }
  };
  if (isEmpty(cardRelatedHover)) return null;

  return (
    <div className={cardClassName} id="card-hover" onMouseLeave={onClose} ref={ref}>
      <Transition animateClass="ohYeah-enter" childrenId="CARD_HOVER_ITEM">
        <div
          className="intro__wrap ohYeah-enter layer-1"
          style={wrapStyle}
          id="CARD_HOVER_ITEM"
          ref={wrapperRef}
        >
          <Billboard
            playerId={PLAYER_TYPE.CARD_HOVER}
            billboardData={cardRelatedHover}
            tipDataItem={tipDataItem}
            className="billboard--intro"
            onClickBanner={onClickBanner}
            isAnimateText
            canClickBillboard
            isCardDetail={false}
            isCardHover={false}
            isEndScreenVod={isEndScreenVod}
            onBackToPlayer={onBackToPlayer}
          />
          {!isOriginal && (
            <CardHoverInfo
              data={cardRelatedHover}
              tipData={tipDataItem}
              profile={profile}
              expiredString={expiredString}
            />
          )}
        </div>
      </Transition>
    </div>
  );
};

export default CardRelatedHoverItem;
