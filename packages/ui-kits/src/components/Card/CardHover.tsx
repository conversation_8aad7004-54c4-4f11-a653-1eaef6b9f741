import React from 'react';
import dynamic from 'next/dynamic';
import isEmpty from 'lodash/isEmpty';
import { CONTENT_TYPE } from '@vieon/core/constants/constants';

const CardHoverCollection = dynamic(() => import('@components/Card/CardHoverCollection'), {
  ssr: false
});
const CardHoverItem = dynamic(() => import('@components/Card/CardHoverItem'), {
  ssr: false
});

const CardHover = ({ data }: any) => {
  if (isEmpty(data)) return null;
  if (data?.type === CONTENT_TYPE.RIBBON) return <CardHoverCollection />;

  return <CardHoverItem />;
};

export default CardHover;
