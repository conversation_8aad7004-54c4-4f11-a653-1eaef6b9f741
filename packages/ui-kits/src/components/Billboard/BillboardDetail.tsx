import React, { useEffect, useState } from 'react';
import { BILLBOARD } from '@vieon/core/constants/constants';
import { PLAYER_STATUS } from '@vieon/core/constants/player';
import { useSelector } from 'react-redux';
import BillboardTitleCard from './BillboardTitleCard';
import BillboardInfoOriginal from './BillboardInfoOriginal';

let timeInterval: any = 0;
let unmounted = false;

const BillboardDetail = React.memo((props: any) => {
  const { billboardData, className, cardData, tipDataItem, isVodDetail, isCollectionBanner } =
    props || {};

  const [playerStatus, setPlayerStatus] = useState(PLAYER_STATUS.ERROR);

  const [timer, setTimer] = useState(1);
  const previewCard = useSelector((state: any) => state?.Popup?.previewCard);

  const { expand } = previewCard || {};

  const { images, isMasterBanner, isOriginal } = billboardData || cardData || {};

  useEffect(() => {
    unmounted = false;
    return () => {
      unmounted = true;
      clearInterval(timeInterval);
    };
  }, []);

  useEffect(() => {
    clearInterval(timeInterval);
    setPlayerStatus(0);
    setTimer(0);
  }, [billboardData?.id]);

  useEffect(() => {
    if (playerStatus === PLAYER_STATUS.PLAYING) {
      handleBillboardTimer();
    }
  }, [playerStatus]);

  const handleBillboardTimer = () => {
    if (unmounted) return;

    if (timeInterval) clearInterval(timeInterval);
    timeInterval = setInterval(() => {
      if (unmounted) {
        clearInterval(timeInterval);
        return;
      }
      setTimer((a) => a + 1);
      if (timer >= BILLBOARD.TITLE_CARD_TIMER) {
        clearInterval(timeInterval);
      }
    }, 1000);
  };

  useEffect(() => {
    if (timer >= BILLBOARD.TITLE_CARD_TIMER) {
      clearInterval(timeInterval);
    }
  }, [timer]);

  let ratioClass = ' ratio-16-9';
  if (isOriginal) ratioClass = ' ratio-1-2';

  const volumeButtonStyle: any = {};
  if (!images?.titleCardLight) {
    volumeButtonStyle.top = '-72px';
  }

  if (!images?.titleCardLight && !(billboardData?.isLive && isMasterBanner)) return null;

  return (
    <>
      <div className={`billboard ${className}`}>
        <div className="billboard-container">
          <div
            className={`billboard__pane relative${
              isVodDetail
                ? ' ratio-16-9'
                : isMasterBanner
                ? ' ratio-variant'
                : isCollectionBanner
                ? ''
                : ratioClass
            }`}
          >
            <div className="billboard__content absolute" id="billboard_Banner">
              <div className="billboard__content__wrap">
                <div className="billboard__info">
                  {!billboardData?.isViewCollection && (
                    <BillboardTitleCard
                      billboardData={billboardData}
                      scaleAnimation={playerStatus === PLAYER_STATUS.PLAYING}
                      expand={expand}
                      timer={timer}
                      isMasterBanner={isMasterBanner}
                      tipDataItem={tipDataItem}
                    />
                  )}
                  <BillboardInfoOriginal billboardData={billboardData} tipDataItem={tipDataItem} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
});

export default BillboardDetail;
