import React from 'react';
import { TAG_KEY } from '@vieon/core/constants/constants';
import { parseTagsData } from '@vieon/core/utils/common';
import Tags from '../basic/Tags/Tags';
import TagsOutline from '../basic/Tags/TagsOutline';
import CardProgress from '../basic/Card/CardProgress';
const BillboardInfoOriginal = React.memo(({ billboardData, tipDataItem }: any) => {
  if (!billboardData?.isOriginal) return null;
  const { isPremium, isLiveTv, labelSubtitleAudio, subtitle, seo, isPremiumDisplay } =
    billboardData || {};

  const { progress, remainText, progressPercent } = tipDataItem || {};
  const newTagData = parseTagsData({ ...billboardData, ...tipDataItem });

  return (
    <>
      <div className="flex flex-wrap space-x-[10px] lg:space-x-3">
        {isPremium && !isLiveTv && <Tags isPremiumDisplay={isPremiumDisplay} />}
        {isLiveTv && <Tags tagKey={TAG_KEY.LIVE} />}
        <TagsOutline tagArray={newTagData} />

        {labelSubtitleAudio && <Tags title={labelSubtitleAudio} />}
        {isLiveTv && subtitle && <Tags title={subtitle} />}
      </div>
      <h3 className="intro__info__title hide">{tipDataItem?.text || `${seo?.title}`}</h3>
      {progress > 0 && (
        <CardProgress
          progressPercent={progressPercent}
          remainText={remainText}
          progress={progress}
          className="m-b"
        />
      )}
    </>
  );
});

export default BillboardInfoOriginal;
