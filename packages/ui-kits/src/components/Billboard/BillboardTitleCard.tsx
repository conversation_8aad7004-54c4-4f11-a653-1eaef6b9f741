import React from 'react';
import { BILLBOARD, TAG_KEY } from '@vieon/core/constants/constants';
import TrackingLog from '@vieon/core/services/trackingLog';
import Image from '../basic/Image/Image';
import CardProgress from '../basic/Card/CardProgress';
import Tags from '../basic/Tags/Tags';

let billboardId: any = null;

const BillboardTitleCard = React.memo(
  ({
    billboardData,
    scaleAnimation,
    expand,
    timer,
    isMasterBanner,
    tipDataItem,
    forwardRef,
    onClickBanner,
    linkPlay
  }: any) => {
    const { altSEOImg, images, isLive, isPremiere } = billboardData || {};
    const { progress } = tipDataItem || {};
    const isHoverItems = !expand && !isMasterBanner;
    const emptyDescription = !billboardData?.shortDescription;
    const { totalCCU } = new TrackingLog();
    let style: any = isMasterBanner
      ? timer === BILLBOARD.TITLE_CARD_TIMER && scaleAnimation
        ? emptyDescription
          ? styles.scaledNoDes
          : styles.scaled
        : styles.default
      : (timer === BILLBOARD.TITLE_CARD_TIMER && scaleAnimation && !expand) ||
        (isHoverItems && !scaleAnimation)
      ? styles.opacityHide
      : styles.opacityNormal;

    if (billboardId !== billboardData?.id) {
      style = null;
      billboardId = billboardData?.id;
    }

    let wrapperTagLiveStyle: any = {
      position: 'absolute',
      top: '-30px'
    };
    if (!images?.titleCardLight) wrapperTagLiveStyle = null;

    if (!linkPlay && !isMasterBanner && !expand) return null;

    return (
      <>
        {(images?.titleCardLight || ((isLive || isPremiere) && isMasterBanner)) && (
          <div
            role="presentation"
            className="billboard__title billboard__title--meta on-click"
            ref={forwardRef}
            onClick={onClickBanner || (() => {})}
            id="billboardTitleMeta"
            style={{ ...style }}
          >
            {(isLive || isPremiere) && isMasterBanner && (
              <div className="tags-group horizontal shrink" style={wrapperTagLiveStyle}>
                <Tags title={isPremiere ? TAG_KEY.PREMIERE : ''} tagKey={TAG_KEY.LIVE} />
                {totalCCU > 0 ? (
                  <Tags
                    tagKey={TAG_KEY.TOTAL_CCU}
                    subClass="tags--box tags--live-count tags--dark-glass"
                    iClass="vie-eye-on-o-rc-medium"
                    content={totalCCU}
                  />
                ) : (
                  ''
                )}
              </div>
            )}
            {images?.titleCardLight && (
              <Image src={images?.titleCardLight} defaultNone alt={altSEOImg} title={altSEOImg} />
            )}
          </div>
        )}
        {expand && progress > 0 && (
          <CardProgress
            progressPercent={tipDataItem?.progressPercent}
            remainText={tipDataItem?.remainText}
            progress={progress}
          />
        )}
      </>
    );
  }
);
const styles = {
  scaledNoDes: {
    transform: 'scale3d(0.6,0.6,0.6) translate3d(0, 30%, 0)',
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  scaled: {
    transform: 'scale3d(0.6,0.6,0.6) translate3d(0, 30%, 0)', // `scale(1, 1.5) translate3d(0, 100%, 0)`,
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  default: {
    transform: ' scale(1) translate3d(0, 0, 0)',
    transitionDuration: '1.3s',
    transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  opacityHide: {
    opacity: 0,
    transition: 'opacity 1.3s ease-out'
  },
  opacityNormal: {
    opacity: 1,
    transition: 'opacity 1.3s ease-out'
  },
  liveStatusHide: {
    opacity: 0,
    transform: 'translateY(200%)'
  },
  liveStatusShow: {
    opacity: 1,
    transform: 'translateY(0)'
  }
};

export default BillboardTitleCard;
