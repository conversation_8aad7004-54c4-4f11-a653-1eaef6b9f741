import React from 'react';
import { BILLBOARD } from '@vieon/core/constants/constants';
import Icon from '../basic/Icon/Icon';
import TagsOutline from '../basic/Tags/TagsOutline';

const BillboardInfo = React.memo(
  ({ billboardData, scaleAnimation, timer, titleDetailPage, isMobile }: any) => {
    const { shortDescription, rankingText, categoryTags } = billboardData || {};
    if (!billboardData?.isMasterBanner) return null;
    const defaultStyle =
      scaleAnimation && timer === BILLBOARD.TITLE_CARD_TIMER
        ? styles.rankingScaled
        : styles.default;
    const descriptionStyle =
      scaleAnimation && timer === BILLBOARD.TITLE_CARD_TIMER ? styles.scaled : styles.default;

    return (
      <div
        className={`billboard__info__meta${isMobile ? ' m-b2' : ''}`}
        id="billboardInfoMeta"
        style={defaultStyle}
      >
        <div className="billboard__info__meta__fade relative" id="billboardInfoMetaFade">
          {rankingText && (
            <div className="title title-white line-clamp" data-line-clamp="2">
              <Icon iClass="vie-top-ten-o text-white" />
              <span>{rankingText}</span>
            </div>
          )}
          {!rankingText && billboardData?.isMasterBanner && (categoryTags || []).length > 0 && (
            <div className="tags-group child-spacing-x middle-v">
              <TagsOutline
                className="tags--outline-dot"
                tagArray={categoryTags}
                txtClass="text-medium-up-13 text-large-up-18 text-medium"
              />
            </div>
          )}
          {shortDescription && !titleDetailPage && (
            <div
              className="billboard__desc text text-white line-clamp"
              data-line-clamp="3"
              style={descriptionStyle}
            >
              {shortDescription}
            </div>
          )}
        </div>
      </div>
    );
  }
);
const styles = {
  scaled: {
    opacity: 0,
    transitionDuration: '1.3s',
    // transitionDelay: '0s',
    transformOrigin: 'bottom left',
    transform: `scale(0.35,0.35)`
  },
  rankingScaled: {
    transform: `scale(1) translate3d(0, 77%, 0)`,
    transitionDuration: '1.3s',
    // transitionDelay: '0s',
    transformOrigin: 'bottom left'
  },
  rankingDefault: {
    // transform: ' scale(1) translate3d(0, 0, 0)',
    // transitionDuration: '1.3s',
    // transitionDelay: '0s',
    // transformOrigin: 'bottom left',
  },
  default: {
    opacity: 1,
    transitionDuration: '1.3s',
    // transitionDelay: '0s',
    transformOrigin: 'bottom left'
  }
};
export default BillboardInfo;
