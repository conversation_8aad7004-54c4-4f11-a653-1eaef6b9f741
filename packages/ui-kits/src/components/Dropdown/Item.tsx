import React, { useMemo } from 'react';
import get from 'lodash/get';
import IconChecked from '../components/Icons/IconChecked';
import Style from './Dropdown.module.scss';

const Item = ({ data, selected, onSelected }: any) => {
  const isSelected = useMemo(
    () => get(data, 'value', '') === get(selected, 'value', ''),
    [data, selected]
  );
  return (
    <div className={Style.itemDropdown} onClick={() => onSelected(data)}>
      <span className={Style.iconChecked}>{isSelected && <IconChecked />}</span>
      <span className={Style.country}>{get(data, 'title', '')}</span>
      <span>{get(data, 'key', '')}</span>
    </div>
  );
};

export default Item;
