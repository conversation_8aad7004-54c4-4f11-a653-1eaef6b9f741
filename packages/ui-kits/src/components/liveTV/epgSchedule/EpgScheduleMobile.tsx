import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperInstance } from 'swiper';
import { isMobileOnly } from 'react-device-detect';
import isEmpty from 'lodash/isEmpty';
import ConfigImage from '@vieon/core/config/ConfigImage';
import EpgItem from './EpgItem';

const EpgScheduleMobile = React.forwardRef(
  (
    {
      listEpgs,
      listFilterDate,
      activeFilterDate,
      isFullscreen,
      activeEpg,
      handleSelectFilterDate,
      handleClickEpgItem,
      onScrollToActive,
      epgHeaderRef,
      heightContainerEPG
    }: any,
    ref: any
  ) => (
    <>
      <Swiper
        spaceBetween={isMobileOnly ? 20 : 32}
        slidesPerView="auto"
        onSwiper={(swiper: SwiperInstance) => {
          if (swiper) {
            swiper.slideTo(4);
            // Store the swiper instance in the ref if needed
            if (epgHeaderRef && typeof epgHeaderRef === 'object') {
              (epgHeaderRef as React.RefObject<SwiperInstance | null>).current = swiper;
            }
          }
        }}
        containerModifierClass="slider slider--menu-broadcast tabs broadcast-category "
        data-item-view={6}
        slideToClickedSlide
      >
        {listFilterDate.map((item: any) => (
          <SwiperSlide
            className={activeFilterDate?.id === item.id ? 'slider__item active' : 'slider__item'}
            key={item.id}
          >
            <button
              id={item.id}
              onClick={() => handleSelectFilterDate && handleSelectFilterDate(item)}
              title={item.titleM || item.title}
              className="category-item"
              type="button"
            >
              {item.titleM || item.title}
            </button>
          </SwiperSlide>
        ))}
      </Swiper>
      <div className="epg epg--schedule" style={{ height: heightContainerEPG, overflow: 'auto' }}>
        <div className="epg-inner">
          <div className="epg-container" ref={ref} style={{ height: '100%' }}>
            <div className="card-group">
              {!isEmpty(listEpgs) &&
                listEpgs.map((item: any) => (
                  <EpgItem
                    key={item.id}
                    data={item}
                    isFullscreen={isFullscreen}
                    activeEpg={activeEpg}
                    handleClickEpgItem={handleClickEpgItem}
                    onScrollToActive={onScrollToActive}
                  />
                ))}
            </div>
            {isEmpty(listEpgs) && (
              <div className="empty">
                <div className="content">
                  <img src={ConfigImage.commentFirst} alt="Lịch phát sóng chưa cập nhật" />
                  <p className="text">Lịch phát sóng chưa cập nhật</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  )
);
export default EpgScheduleMobile;
