import React, { useRef, useEffect, useMemo } from 'react';
import isEmpty from 'lodash/isEmpty';
import Button from '../components/basic/Buttons/Button';
import Image from '../components/basic/Image/Image';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { createTimeout } from '@vieon/core/utils/common';

const EpgItem = React.memo(
  ({ data, isFullscreen, activeEpg, handleClickEpgItem, onScrollToActive }: any) => {
    const itemsRef = useRef<any>(null);
    const clickTimer = useRef<any>(null);
    const { id, isLive, title, isComingSoon, time, seo, isCatchUp, isNotifyComingSoon, images } =
      data;
    const classItem = useMemo(() => {
      let strClass = 'card card--broadcast';
      if (activeEpg?.id === id) {
        strClass += ' active';
      }
      if (!isComingSoon && !isLive) {
        strClass += ' premiered';
      }
      return strClass;
    }, [activeEpg?.id, id, isComingSoon, isLive]);
    useEffect(() => () => clearTimeout(clickTimer.current), []);

    useEffect(() => {
      if (activeEpg?.id && activeEpg.id === id && typeof onScrollToActive === 'function') {
        onScrollToActive(itemsRef?.current);
      }
    }, [isFullscreen, isLive, activeEpg, id]);

    const onClickItem = (e: any) => {
      e.preventDefault();
      e.stopPropagation();
      if (isEmpty(data)) return;
      clearTimeout(clickTimer.current);
      clickTimer.current = createTimeout(() => {
        handleClickEpgItem(data);
      }, 500);
    };

    return (
      <div className={classItem} ref={itemsRef} id={id} onClick={onClickItem}>
        {isComingSoon && !isLive && (
          <Button
            className={
              isNotifyComingSoon
                ? 'button button--replay absolute checked'
                : 'button button--replay absolute'
            }
            onClick={onClickItem}
            iconName={isNotifyComingSoon ? 'vie-tick !text-vo-green' : 'vie-bell-o-rc-light'}
            iconClass="!text-[1.125rem]"
          />
        )}
        {isCatchUp && !isComingSoon && !isLive && (
          <Button
            className="button button--replay absolute"
            onClick={onClickItem}
            iconName="vie-replay-o-medium"
            iconClass="!text-[1.125rem]"
          />
        )}
        <div className="time">
          {time}
          {isLive && (
            <label className="tags tags--box tags--live small tags--red-gradient">LIVE </label>
          )}
        </div>
        {images?.thumbnail && (
          <div className="card__thumbnail">
            <Image
              className="player__poster-image"
              src={images?.thumbnail}
              defaultSrc={ConfigImage.defaultBanner16x9}
              alt={title}
              title={title || 'epg livetv'}
            />
          </div>
        )}
        {activeEpg?.id === id && (
          <span className="icon icon--live-img absolute">
            <img src={ConfigImage.liveTVActiveIcon} alt="animate-livetv" />
          </span>
        )}
        <div className="card__title line-clamp" data-line-clamp="2">
          {title}
        </div>
      </div>
    );
  }
);

export default EpgItem;
