import React, { useEffect, useRef } from 'react';
import isEmpty from 'lodash/isEmpty';
import { createTimeout } from '@vieon/core/utils/common';

const EpgDropDownItems = ({
  data,
  heightContainerEPG,
  activeFilter,
  alignClass,
  onClick,
  isFullscreen
}: any) => {
  const ref = useRef<any>(null);
  const itemsRef = useRef<any>([]);

  useEffect(() => {
    if (!isEmpty(activeFilter)) {
      const id = activeFilter?.id;
      const activeItem = itemsRef.current[id];
      if (activeItem && ref.current) {
        createTimeout(() => {
          const { offsetTop } = activeItem;
          if (ref?.current?.scrollTo) {
            ref.current.scrollTo({ left: 0, top: offsetTop, behavior: 'smooth' });
          }
        }, 1000);
      }
    }
  }, [data, activeFilter]);

  return (
    (data && (
      <div
        className="filter__pane filter__pane-outline filter__pane-vertical absolute"
        data-alignment={alignClass}
        style={{ zIndex: 9999 }}
      >
        <div
          className="filter__pane__wrap scrollable-y over-scroll-contain"
          style={{ maxHeight: isFullscreen ? 'calc(100vh - 196px)' : heightContainerEPG }}
          ref={ref}
        >
          <div className="filter__list size-w-full">
            {(data || []).map((item: any, index: any) => (
              <div
                className={`filter__item filter__item--backdrop${
                  activeFilter.id === item.id ? ' active' : ''
                }`}
                ref={(el: any) => (itemsRef.current[item?.id] = el)}
                onClick={() => onClick(item, index)}
                key={index}
              >
                <button className="button" type="button">
                  <span className="text ellipsis">{item.title || ''}</span>
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    )) ||
    null
  );
};
export default EpgDropDownItems;
