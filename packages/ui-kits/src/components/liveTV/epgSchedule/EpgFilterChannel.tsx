import React, { useState, useEffect, useRef } from 'react';
import get from 'lodash/get';
import find from 'lodash/find';
import Button from '../components/basic/Buttons/Button';
import { TAB_LISTS, CONTENT_TYPE } from '@vieon/core/constants/constants';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import EpgDropDownItems from './EpgDropDownItems';

const EpgFilterChannel = ({
  listAllChannels,
  activeChannel,
  heightContainerEPG,
  onGetListAllChannels,
  isFullscreen,
  handleSelectChannel,
  activeFilterDate,
  listFilterDate,
  handleSelectFilterDate
}: any) => {
  const ref = useRef<any>(null);
  const [activeDropdownChannel, setActiveDropDownChannel] = useState(false);
  const [activeDropdownDate, setActiveDropDownDate] = useState(false);

  useEffect(() => {
    document.addEventListener('click', onClickOutside, true);
    return () => {
      document.removeEventListener('click', onClickOutside, true);
    };
  }, []);

  useEffect(() => {
    if (activeDropdownDate) {
      setActiveDropDownChannel(false);
    }
  }, [activeDropdownDate]);

  const onClickOutside = (event: any) => {
    if (ref.current && !ref.current.contains(event.target)) {
      setActiveDropDownChannel(false);
      setActiveDropDownDate(false);
    }
  };

  const onClickBtnToShowDropdownChannels = () => {
    if (typeof onGetListAllChannels === 'function') onGetListAllChannels();
    setActiveDropDownChannel(!activeDropdownChannel);
  };

  const onSelectChannel = (item: any, index: any) => {
    const category = find(TAB_LISTS, ['id', 'LPS']);
    TrackingApp.contentSelected({
      data: {
        ...item,
        type: CONTENT_TYPE.LIVETV,
        index,
        categoryText: category?.name,
        seasonThumb: item?.images?.thumbnail,
        seasonGenre: item?.genreText
      },
      clickType: VALUE.DIRECT,
      category,
      isLiveTv: true
    });
    setActiveDropDownChannel(!activeDropdownChannel);
    if (typeof handleSelectChannel === 'function') handleSelectChannel(item);
  };

  const onClickBtnToShowDropdownDate = () => {
    setActiveDropDownDate(!activeDropdownDate);
  };

  const onSelectFilterDate = (item: any) => {
    setActiveDropDownDate(!activeDropdownDate);
    if (typeof handleSelectFilterDate === 'function') handleSelectFilterDate(item);
  };

  return (
    (activeChannel && (
      <div className="sort sort--list" ref={ref}>
        <div className="filter filter--sort filter--sort-epg filter--dark">
          <Button
            customizeClass="flex max-w-[9.89583vw] items-center justify-center !border !border-solid !border-[#9b9b9b]/70 hover:!border-vo-green h-9 px-3 bg-transparent !text-white hover:!text-vo-green space-x-1 md:space-x-2 xl:space-x-3 transition-colors"
            iconNameSlotRight="vie-chevron-down-red-medium"
            title={activeChannel?.title || ''}
            onClick={onClickBtnToShowDropdownChannels}
            textClass="max-v-full whitespace-nowrap line-clamp-1 text-ellipsis block !text-[.875rem] !font-medium"
          />
          {activeDropdownChannel && (
            <EpgDropDownItems
              isFullscreen={isFullscreen}
              heightContainerEPG={heightContainerEPG}
              data={get(listAllChannels, 'items', [])}
              onClick={onSelectChannel}
              activeFilter={activeChannel}
              alignClass="bottom-left"
            />
          )}
        </div>
        <div className="filter filter--sort filter--dark">
          <Button
            customizeClass="flex max-w-[9.89583vw] items-center justify-center !border !border-solid !border-[#9b9b9b]/70 hover:!border-vo-green h-9 px-3 bg-transparent !text-white hover:!text-vo-green space-x-3 transition-colors"
            iconNameSlotRight="vie-chevron-down-red-medium"
            title={activeFilterDate?.title || ''}
            onClick={onClickBtnToShowDropdownDate}
            textClass="max-v-full whitespace-nowrap line-clamp-1 text-ellipsis block !text-[.875rem] !font-medium"
          />
          {activeDropdownDate && (
            <EpgDropDownItems
              heightContainerEPG={heightContainerEPG}
              data={listFilterDate}
              activeFilter={activeFilterDate}
              onClick={onSelectFilterDate}
              alignClass="bottom-right"
            />
          )}
        </div>
      </div>
    )) ||
    null
  );
};
export default EpgFilterChannel;
