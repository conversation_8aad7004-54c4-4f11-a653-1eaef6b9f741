import React, { useEffect, useRef } from 'react';
import { isMobileOnly } from 'react-device-detect';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Navigation } from 'swiper/core';
import isEmpty from 'lodash/isEmpty';
import FilterDropDown from '../components/basic/FilterDropDown';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import classNames from 'classnames';

SwiperCore.use([Navigation]);

const ChannelTabs = ({
  isMobile,
  isFullscreen,
  data,
  category,
  listFilter,
  activeFilter,
  activeSubCategory,
  onChangeFilter,
  onChangeActiveSubCategory
}: any) => {
  const swiperRef = useRef<any>(null);

  useEffect(() => {
    if (isFullscreen && swiperRef.current && typeof swiperRef.current.update === 'function') {
      swiperRef.current.update();
    }
  }, [data, isFullscreen]);

  const handleChangeFilter = (e: any, i: any, data: any) => {
    if (typeof onChangeFilter === 'function') onChangeFilter(data);
  };

  const onClickTab = (tab: any, i: any) => {
    if (!isEmpty(tab)) {
      TrackingApp.ribbonSelected({
        data: tab,
        ribbonOrder: i,
        title: tab.name,
        isLiveTv: true,
        category
      });
      if (typeof onChangeActiveSubCategory === 'function') {
        onChangeActiveSubCategory(tab, category?.id);
      }
    }
  };

  // render El
  if (!data || data.length === 0) return null;

  return (
    <div className="flex flex-col md:flex-row !py-5">
      <FilterDropDown
        id="CHANNEL_PAGE_LIST"
        className="filter filter--sort filter--dark"
        buttonClass="flex lg:max-w-[9.89583vw] items-center justify-center !border !border-solid !border-vo-gray-200 hover:!border-vo-green h-9 px-3 bg-vo-dark-gray-900/50 !text-white hover:!text-vo-green space-x-3 transition-colors"
        iconNameSlotRight="vie-chevron-down-red-medium r"
        changeFilter={handleChangeFilter}
        filterName={activeFilter?.name || ''}
        filterList={listFilter}
        itemIconClass="vie-tick"
        nowrap
        textClass="max-v-full block whitespace-nowrap line-clamp-1 !text-[.875rem] text-ellipsis"
      />
      <Swiper
        spaceBetween={isMobileOnly ? 20 : 32}
        slidesPerView="auto"
        containerModifierClass="slider tabs slider--menu-broadcast broadcast-category non-hover overflow "
        data-item-view="auto"
        allowTouchMove={!!isMobile}
        slideToClickedSlide={!!isMobileOnly}
        centeredSlides={!!isMobileOnly}
        centeredSlidesBounds={!!isMobileOnly}
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        navigation={
          !isMobile && isFullscreen
            ? {
                prevEl: '.slider-navigate.slider-navigate-prev',
                nextEl: '.slider-navigate.slider-navigate-next'
              }
            : false
        }
      >
        {data.map((item: any, idx: any) => (
          <SwiperSlide
            className={`slider__item${activeSubCategory?.id === item.id ? ' active' : ''}`}
            key={item.id}
          >
            <button
              id={item.id}
              onClick={() => onClickTab(item, idx)}
              title={item.name || item.title}
              className={classNames(
                'category-item',
                activeSubCategory?.id === item.id ? '!text-white' : 'hover:!text-vo-green'
              )}
              type="button"
            >
              {item.name || item.title}
            </button>
          </SwiperSlide>
        ))}
        {!isMobile && isFullscreen && (
          <div className="slider-navigate-group align-right horizontal layer-1 absolute right !space-x-3">
            <div className="slider-navigate slider-navigate-prev size-16 relative" />
            <div className="slider-navigate slider-navigate-next size-16 relative" />
          </div>
        )}
      </Swiper>
    </div>
  );
};

export default ChannelTabs;
