import React, { useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import sortBy from 'lodash/sortBy';
import classNames from 'classnames';
import {
  getListChannels,
  setActiveCategory,
  setActiveFilterChannelPage,
  setActiveSubCategory
} from '@vieon/core/store/actions/liveTV';
import { CONTENT_TYPE, RIBBON_TYPE, TYPE_TRIGGER_ALWAYS } from '@vieon/core/constants/constants';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { createTimeout } from '@vieon/core/utils/common';
import { getDataTriggerPoint } from '@vieon/core/store/actions/trigger';
import HeaderTabsLivetv from './HeaderTabsLivetv';
import BodyTabsLivetv from './BodyTabsLivetv';

const Categories = React.memo(() => {
  const dispatch = useDispatch();
  const ref = useRef<any>(null);
  const timer = useRef<any>(null);
  const clickTimerRef = useRef<any>(null);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { isMobile, isTablet } = useSelector((state: any) => state?.App || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const listItemAdsLiveTv = useSelector(
    (state: any) => state.App?.outStreamAds?.listItemLiveTv || []
  );
  const { isFullscreen } = useSelector((state: any) => state?.Player || {});
  const { dataTriggerLiveTv } = useSelector((state: any) => state?.Trigger);

  const {
    activeCategory,
    activeCategoryFullscreen,
    activeFilterChannelPage,
    activeFilterChannelPageFullscreen,
    activeSubCategoryDSK,
    activeSubCategoryDSKFullscreen,
    activeSubCategoryDPS,
    activeSubCategoryDPSFullscreen,
    detailChannel,
    listChannels,
    listFavoriteChannels,
    listFilterChannelPage,
    listSubCategoriesDPS,
    listSubCategoriesDSK,
    listWatchedChannels
  } = useSelector((state: any) => state?.LiveTV || {});
  const categoryActive = useMemo(
    () => (isFullscreen ? activeCategoryFullscreen : activeCategory),
    [isFullscreen, activeCategory, activeCategoryFullscreen]
  );
  const filterActive = useMemo(
    () => (isFullscreen ? activeFilterChannelPageFullscreen : activeFilterChannelPage),
    [isFullscreen, activeFilterChannelPage, activeFilterChannelPageFullscreen]
  );
  const subCategoryDSKActive = useMemo(
    () => (isFullscreen ? activeSubCategoryDSKFullscreen : activeSubCategoryDSK),
    [isFullscreen, activeSubCategoryDSK, activeSubCategoryDSKFullscreen]
  );
  const subCategoryDPSActive = useMemo(
    () => (isFullscreen ? activeSubCategoryDPSFullscreen : activeSubCategoryDPS),
    [isFullscreen, activeSubCategoryDPS, activeSubCategoryDPSFullscreen]
  );
  const dataSubCategoriesDSK = useMemo(
    () => get(listSubCategoriesDSK, filterActive?.id, []),
    [filterActive, listSubCategoriesDSK]
  );
  const firstIdSubCategoryOfFirstFilterPage = useMemo(
    () => get(listSubCategoriesDSK, `${get(listFilterChannelPage, '[0].id', '')}[0].id`, ''),
    [listFilterChannelPage, listSubCategoriesDSK]
  );
  const listChannelsToShow = useMemo(() => {
    let data: any = {};
    if (categoryActive?.id === 'DSK') {
      const bannerTrigger = {
        id: 'banner-trigger',
        type: CONTENT_TYPE.BANNER_TRIGGER
      };
      const listAdsItemSorted = sortBy(listItemAdsLiveTv, ['position']);
      if (subCategoryDSKActive?.type === RIBBON_TYPE.FAVORITE_LIVE_TV) {
        data = { items: listFavoriteChannels || [] };
      } else if (subCategoryDSKActive?.type === RIBBON_TYPE.WATCHED_LIST) {
        let list = [...listWatchedChannels];
        if (list?.length > 0 && !profile?.isPremium && !isFullscreen && !isKid) {
          if (!isMobile && !isEmpty(listAdsItemSorted)) {
            listAdsItemSorted.map((item: any) => {
              const positionInArr = item.position - 1;
              list = [
                ...list.slice(0, positionInArr),
                { ...item, type: CONTENT_TYPE.ADS },
                ...list.slice(positionInArr)
              ];
            });
          }
        }
        if (!isEmpty(dataTriggerLiveTv)) {
          list.splice(isMobile ? (isTablet ? 4 : 2) : 6, 0, bannerTrigger);
        }
        data = { items: list || [] };
      } else {
        data = get(listChannels, `${subCategoryDSKActive?.id}`, {});
        if (
          firstIdSubCategoryOfFirstFilterPage &&
          subCategoryDSKActive?.id === firstIdSubCategoryOfFirstFilterPage &&
          !isFullscreen &&
          !isEmpty(data?.items) &&
          !isKid
        ) {
          let listItemChannel = [...data?.items];
          if (!profile?.isPremium) {
            if (!isMobile && !isEmpty(listAdsItemSorted)) {
              listAdsItemSorted.map((item: any) => {
                const positionInArr = item.position - 1;
                listItemChannel = [
                  ...listItemChannel.slice(0, positionInArr),
                  { ...item, type: CONTENT_TYPE.ADS },
                  ...listItemChannel.slice(positionInArr)
                ];
              });
            }
          }
          if (!isEmpty(dataTriggerLiveTv)) {
            listItemChannel.splice(isMobile ? (isTablet ? 4 : 2) : 6, 0, bannerTrigger);
          }
          data = {
            ...data,
            items: listItemChannel
          };
        }
      }
    } else if (categoryActive?.id === 'DPS') {
      data = get(listChannels, `${subCategoryDPSActive?.id}`, {});
    }
    return data;
  }, [
    categoryActive,
    subCategoryDSKActive,
    subCategoryDPSActive,
    listChannels,
    listFavoriteChannels,
    listWatchedChannels,
    isFullscreen,
    isMobile,
    isTablet,
    profile?.isPremium,
    listItemAdsLiveTv,
    firstIdSubCategoryOfFirstFilterPage,
    isKid,
    dataTriggerLiveTv
  ]);

  const firstChannelForSeo = useMemo(() => {
    const firstIdFilterChannelPage = get(listFilterChannelPage, '[0].id', '');
    if (firstIdFilterChannelPage) {
      const subCategories = get(listSubCategoriesDSK, firstIdFilterChannelPage, []);
      const firstSubCategory =
        subCategories.find((subCategory: any) => subCategory.name === 'Nổi bật') || {};
      if (!isEmpty(firstSubCategory)) {
        if (firstSubCategory.type === RIBBON_TYPE.FAVORITE_LIVE_TV) {
          return listFavoriteChannels?.[0] || {};
        }
        if (firstSubCategory.type === RIBBON_TYPE.WATCHED_LIST) {
          return listWatchedChannels?.[0] || {};
        }
        return get(listChannels, `${firstSubCategory?.id}.items`, []);
      }
    }
  }, [
    listFilterChannelPage,
    listSubCategoriesDSK,
    listFavoriteChannels,
    listWatchedChannels,
    listChannels
  ]);

  useEffect(() => {
    if (isFullscreen && ref.current) {
      ref.current.addEventListener('scroll', handleScrollContainer, false);
    } else if (!isFullscreen) {
      window.addEventListener('scroll', handleScrollContainer, false);
    }
    return () => {
      clearTimeout(timer.current);
      if (ref.current) ref.current.removeEventListener('scroll', handleScrollContainer, false);
      window.removeEventListener('scroll', handleScrollContainer, false);
    };
  }, [listChannelsToShow, isFullscreen]);

  useEffect(() => {
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.LIVE_TV }));
    }, 400);
    return () => {
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    };
  }, []);

  const onChangeCategory = (category: any) => {
    if (!isEmpty(category)) {
      TrackingApp.categorySelected({ data: category, isLiveTv: true });
      dispatch(setActiveCategory(category, isFullscreen));
    }
  };

  const onChangeFilter = (data: any) => {
    dispatch(setActiveFilterChannelPage(data, isFullscreen));
  };

  const onChangeActiveSubCategory = (data: any, category: any) => {
    dispatch(setActiveSubCategory(data, category, isFullscreen));
  };

  const onLoadMoreListChannels = () => {
    const metadata = get(listChannelsToShow, 'metadata', {});
    const { total, limit, page } = metadata;
    if (total > limit * (page + 1)) {
      dispatch(
        getListChannels({
          type: listChannelsToShow.type,
          id: listChannelsToShow.id,
          ribbonOrder: listChannelsToShow.index,
          page: page + 1,
          limit,
          isGlobal
        })
      );
    }
  };

  const handleScrollContainer = () => {
    clearTimeout(timer.current);
    timer.current = setTimeout(() => {
      const footerHeight = isMobile ? 500 : 100;
      if (
        (isFullscreen &&
          ref.current &&
          ref.current.offsetHeight + ref.current.scrollTop >= ref.current.scrollHeight) ||
        window.innerHeight + window.scrollY + footerHeight >= document.body.offsetHeight
      ) {
        onLoadMoreListChannels();
      }
    }, 700);
  };

  return (
    <section
      className={classNames(
        'section section--channels',
        isFullscreen ? 'section--television' : 'canal-v'
      )}
    >
      <div
        className={classNames(
          'section__header',
          !isFullscreen && isMobile ? 'overflow-hidden' : ''
        )}
      >
        <HeaderTabsLivetv
          isMobile={isMobile}
          isFullscreen={isFullscreen}
          activeCategory={categoryActive}
          activeFilterInDSK={filterActive}
          activeSubCategoryDSK={subCategoryDSKActive}
          activeSubCategoryDPS={subCategoryDPSActive}
          listFilterChannelPage={listFilterChannelPage}
          dataSubCategoriesDPS={listSubCategoriesDPS}
          dataSubCategoriesDSK={dataSubCategoriesDSK}
          onChangeCategory={onChangeCategory}
          onChangeFilter={onChangeFilter}
          onChangeActiveSubCategory={onChangeActiveSubCategory}
          isGlobal={isGlobal}
        />
      </div>
      <div className={classNames('section__body', isFullscreen && 'scrollable-y')} ref={ref}>
        <BodyTabsLivetv
          isFullscreen={isFullscreen}
          activeCategory={categoryActive}
          listChannels={listChannelsToShow}
          detailChannel={detailChannel}
          activeSubCategoryDSK={subCategoryDSKActive}
          firstChannelForSeo={firstChannelForSeo}
        />
      </div>
    </section>
  );
});

export default Categories;
