import React, { useEffect, useState } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { RIBBON_TYPE } from '@vieon/core/constants/constants';
import CardListRibbon from './CardListRibbon';
import ChannelItems from './ChannelItems';
import EmptyFavoriteChannel from '../../empty/EmptyFavoriteChannel';
import EpgSchedule from '../epgSchedule/EpgSchedule';

const BodyTabsLivetv = ({
  isFullscreen,
  activeCategory,
  activeSubCategoryDSK,
  listChannels,
  detailChannel,
  firstChannelForSeo
}: any) => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (
    isEmpty(get(listChannels, 'items', [])) &&
    activeSubCategoryDSK?.type === RIBBON_TYPE.FAVORITE_LIVE_TV
  ) {
    return <EmptyFavoriteChannel />;
  }

  if (activeCategory?.id === 'DPS') {
    return (
      <CardListRibbon
        isFullscreen={isFullscreen}
        data={get(listChannels, 'items', [])}
        activeCategory={activeCategory}
      />
    );
  }
  return (
    <>
      {!isClient &&
        !isEmpty(firstChannelForSeo) && ( // ssr render SEO
          <div className="rocopa">
            <div className="rocopa__body">
              <h3 style={{ color: '#fff', padding: '12px 0', fontSize: '31px' }}>Nổi bật</h3>
              <div
                className={`group epr-view-2 epr-view-medium-up-4 margin-fce-4 horizontal ${
                  isFullscreen ? 'epr-view-large-up-3' : 'epr-view-large-up-6'
                } `}
              >
                <ChannelItems
                  notLazy
                  data={firstChannelForSeo}
                  activeChannel={firstChannelForSeo}
                />
              </div>
            </div>
          </div>
        )}
      {isClient && activeCategory?.id === 'DSK' && !isEmpty(get(listChannels, 'items', [])) && (
        <div className="rocopa" suppressHydrationWarning>
          <div className="rocopa__body">
            <div
              className={`group epr-view-2 epr-view-medium-up-4 margin-fce-4 horizontal ${
                isFullscreen ? 'epr-view-large-up-3' : 'epr-view-large-up-6'
              } `}
            >
              <ChannelItems
                data={get(listChannels, 'items', [])}
                activeCategory={activeCategory}
                activeChannelId={detailChannel?.id}
              />
            </div>
          </div>
        </div>
      )}
      {activeCategory?.id === 'LPS' && <EpgSchedule isFullscreen={isFullscreen} />}
    </>
  );
};
export default BodyTabsLivetv;
