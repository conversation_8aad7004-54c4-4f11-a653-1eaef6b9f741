import { RECONNECT_SOCKET } from '@vieon/core/config/ConfigEnv';
import ConfigSocket from '@vieon/core/config/ConfigSocket';
import ConfigCookie from '@vieon/core/config/ConfigCookie';

// Declare the global socketClusterClient variable for TypeScript
declare const socketClusterClient: any;

class SocketCluster {
  static channelId: any;
  static channelRoom: any;
  static channelUsiRoom: any;
  static handleSocketResult: any;
  static socket: any;
  static usi: any;

  constructor(props: any) {
    Object.assign(this, props);
  }

  static connectSocket = ({ usi, channelId, handleSocketResult }: any) => {
    this.channelId = channelId;
    this.usi = usi;
    this.handleSocketResult = handleSocketResult;
    if (!usi || typeof socketClusterClient === 'undefined') {
      // Todo: Cancel Socket
      return;
    }
    const option = {
      hostname: ConfigSocket.SERVER,
      secure: true,
      connectTimeout: 10000, // milliseconds
      ackTimeout: 10000, // milliseconds
      autoReconnect: !!RECONNECT_SOCKET
    };
    this.socket = socketClusterClient.create(option);
    (async () => {
      for await (const event of this.socket.listener('error')) {
        // console.log('SOCKET ERROR')
        this.handleSocketResult({
          data: { action: ConfigSocket.EVENTS.ERROR, type_msg: ConfigSocket.MSG_TYPE.ERROR },
          event
        });
      }
    })();
    (async () => {
      for await (const event of this.socket.listener('connect')) {
        // console.log('SOCKET CONNECTED')
        this.handleSocketResult({ data: { action: ConfigSocket.EVENTS.CONNECTED }, event });
        this.connectChannel(usi);
      }
    })();
    (async () => {
      for await (const event of this.socket.listener('disconnect')) {
        // console.log('SOCKET DISCONNECTED')
        this.handleSocketResult({ data: { action: ConfigSocket.EVENTS.DISCONNECTED }, event });
      }
    })();
  };

  static connectChannel = (usi: any) => {
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    const { channelId } = this;
    this.channelRoom = `LIVETV_${channelId}`;
    this.channelUsiRoom = `PLAYER_${channelId}_${usi}`;

    if (usi !== '') {
      (async () => {
        const channelRoom = this.socket.subscribe(this.channelRoom);
        for await (const res of channelRoom) {
          if (res.status === 200) {
            this.handleResult({ data: res?.data });
          }
        }
      })();
      (async () => {
        const channelUsiRoom = this.socket.subscribe(this.channelUsiRoom);
        for await (const res of channelUsiRoom) {
          if (res.status === 200) {
            this.handleResult({ data: res?.data });
          }
        }
      })();
      (async () => {
        await this.socket.invoke('fingering_room', {
          token: accessToken,
          channel_id: channelId,
          usi
        });
      })();
    } else {
      this.unsubscribe();
    }
  };

  static handleResult = ({ data, usi }: any) => {
    if (this.handleSocketResult) this.handleSocketResult({ data, usi });
  };

  static unsubscribe = async () => {
    if (!this.socket || this.socket?.state === 'closed') return;
    try {
      if (this.socket?.state === 'closed') return;
      // console.log('DISCONNECT')
      if (typeof this.socket?.disconnect === 'function') {
        await this.socket.disconnect();
      }
    } catch (e) {
      // console.log("DISCONNECT FAIL")
    }
  };
}

export default SocketCluster;
