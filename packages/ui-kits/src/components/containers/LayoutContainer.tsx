import React from 'react';

interface LayoutContainerProps {
  children: React.ReactNode;
  [key: string]: any;
}

const LayoutContainer: React.FC<LayoutContainerProps> = ({ children, ...props }) => {
  return (
    <div className="layout-container" {...props}>
      {children}
    </div>
  );
};

// Add getInitialProps for SSR compatibility
LayoutContainer.getInitialProps = async (ctx: any) => {
  // Return empty props for now - you can add your layout logic here
  return {};
};

export default LayoutContainer;
