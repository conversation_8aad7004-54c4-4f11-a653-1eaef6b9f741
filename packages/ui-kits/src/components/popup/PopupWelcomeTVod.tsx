import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@vieon/core/store/actions/popup';
import { useVieRouter } from '@customHook';
import { addParamToUrlVieON } from '@vieon/core/utils/common';
import { PAGE, TVOD } from '@vieon/core/constants/constants';
import { setStatusDialogOnBoarding, setStatusOnBoarding } from '@vieon/core/store/actions/app';
import { TYPE_TRIGGER_AUTH } from '@vieon/core/constants/types';
import { getDataRibbonsId } from '@vieon/core/store/actions/page';
import {
  tvodWelcomeTurorialLoaded,
  tvodWelcomeTurorialLoadedMulti,
  tvodWelcomeTurorialSelectedContent,
  tvodWelcomeTurorialLoadedMultiSelect,
  tvodWelcomeTurorialLoadedMultiClose,
  tvodWelcomeTurorialSelectedClose,
  liveEventWelcomeTurorialLoaded,
  liveEventWelcomeTurorialSelectedContent,
  liveEventWelcomeTurorialSelectedClose,
  liveEventWelcomeTurorialSelectedPreOrder
} from '@vieon/tracking/functions/TrackingTVodOnboarding';
import Button from '../basic/Buttons/Button';
import Modal from '../basic/Modal';

const PopupWelcomeTVod = () => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { tVod } = useSelector((state: any) => state?.App?.webConfig);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const dataKey = profile?.id ? 'userFlow' : 'guestFlow';
  const tVodDialog = tVod?.dialog?.[dataKey];
  const { id, imgUrl, desc, title, primaryButton, type, secondaryButton, benefits, url, price } =
    tVodDialog || {};
  useEffect(() => {
    if (tVodDialog) {
      switch (type) {
        case TVOD.TYPE.MOVIE:
        case TVOD.TYPE.TV_SERIES: {
          tvodWelcomeTurorialLoaded({
            data: tVodDialog,
            userType: userType?.userType,
            flowName: TVOD.TVOD_ON_BOARDING
          });
          break;
        }
        case TVOD.TYPE.RIBBON: {
          tvodWelcomeTurorialLoadedMulti({
            data: tVodDialog,
            userType: userType?.userType,
            flowName: TVOD.TVOD_ON_BOARDING_MULTI
          });
          break;
        }
        case TVOD.TYPE.LIVE_EVENT:
        case TVOD.TYPE.SIMULCAST_SINGLE:
        case TVOD.TYPE.SIMULCAST_MULTI: {
          liveEventWelcomeTurorialLoaded({
            data: tVodDialog,
            userType: userType?.userType,
            flowName: TVOD.TVOD_ON_BOARDING_PRE_ORDER
          });
          break;
        }
        default:
          break;
      }
    }
    if (isMobile) {
      dispatch(setStatusDialogOnBoarding(true));
    }
  }, [userType, type, tVodDialog]);

  const onContinue = () => {
    onClosed();

    switch (type) {
      case TVOD.TYPE.MOVIE:
      case TVOD.TYPE.TV_SERIES: {
        const queryParams = addParamToUrlVieON(router?.query, { vid: id });
        router.push(
          {
            pathname: router.pathname,
            query: queryParams
          },
          {
            pathname: PAGE.HOME,
            query: queryParams
          }
        );
        tvodWelcomeTurorialSelectedContent({
          userType: userType?.userType,
          data: tVodDialog,
          flowName: TVOD.TVOD_ON_BOARDING,
          contentOrder: 0
        });
        break;
      }
      case TVOD.TYPE.RIBBON:
      case TVOD.TYPE.SIMULCAST_MULTI: {
        dispatch(getDataRibbonsId({ id, isGlobal })).then((res: any) => {
          const url = res?.data?.seo?.url;
          const queryParams = addParamToUrlVieON(router?.query);
          if (type === TVOD.TYPE.RIBBON) {
            tvodWelcomeTurorialLoadedMultiSelect({
              userType: userType?.userType,
              flowName: TVOD.TVOD_ON_BOARDING_MULTI
            });
          } else {
            liveEventWelcomeTurorialSelectedPreOrder({
              userType: userType?.userType,
              data: tVodDialog,
              flowName: TVOD.TVOD_ON_BOARDING_PRE_ORDER
            });
          }
          router.push(
            {
              pathname: PAGE.COLLECTION,
              query: queryParams
            },
            {
              pathname: url,
              query: queryParams
            }
          );
        });
        break;
      }
      case TVOD.TYPE.LIVE_EVENT:
      case TVOD.TYPE.SIMULCAST_SINGLE: {
        const isSimulcast = type === TVOD.TYPE.SIMULCAST_SINGLE;
        const isLiveEvent = type === TVOD.TYPE.LIVE_EVENT;
        const queryParams = addParamToUrlVieON(router?.query, {
          // type: isSimulcast ? 3 : isLiveEvent ? 0 : 0,
          type: isSimulcast ? 3 : 0,
          id: id || 0,
          isSimulcast,
          isLiveEvent,
          fromPrice: price || 0
        });
        if (!profile?.id) {
          const destinationPath = `${PAGE.RENTAL_CONTENT}/?${new URLSearchParams(
            queryParams
          ).toString()}`;
          const queryString = new URLSearchParams({
            trigger: TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE_PRE_ORDER
          }).toString();

          const finalQueryString = `${queryString}&destination=${encodeURIComponent(
            destinationPath
          )}`;
          router.push(`${PAGE.AUTH}?${finalQueryString}`);
        } else {
          router.push(
            {
              pathname: PAGE.RENTAL_CONTENT,
              query: queryParams
            },
            {
              pathname: PAGE.RENTAL_CONTENT,
              query: queryParams
            }
          );
        }
        liveEventWelcomeTurorialSelectedPreOrder({
          userType: userType?.userType,
          data: tVodDialog,
          flowName: TVOD.TVOD_ON_BOARDING_PRE_ORDER
        });
        break;
      }
      default:
        break;
    }
  };
  const onClosePopup = () => {
    switch (type) {
      case TVOD.TYPE.MOVIE:
      case TVOD.TYPE.TV_SERIES: {
        tvodWelcomeTurorialSelectedClose({
          data: tVodDialog,
          userType: userType?.userType,
          flowName: TVOD.TVOD_ON_BOARDING,
          contentOrder: 0
        });
        break;
      }
      case TVOD.TYPE.RIBBON: {
        tvodWelcomeTurorialLoadedMultiClose({
          userType: userType?.userType,
          flowName: TVOD.TVOD_ON_BOARDING_MULTI
        });
        break;
      }
      case TVOD.TYPE.LIVE_EVENT:
      case TVOD.TYPE.SIMULCAST_SINGLE:
      case TVOD.TYPE.SIMULCAST_MULTI: {
        liveEventWelcomeTurorialSelectedClose({
          data: tVodDialog,
          userType: userType?.userType,
          flowName: TVOD.TVOD_ON_BOARDING_PRE_ORDER
        });
        break;
      }
      default:
        break;
    }
    dispatch(setStatusOnBoarding(true));
    onClosed();
  };

  const onDetail = () => {
    const pathname = router?.pathname;
    switch (type) {
      case TVOD.TYPE.LIVE_EVENT: {
        router.push(`${PAGE.LIVE_STREAM}/${url}`);
        break;
      }
      case TVOD.TYPE.SIMULCAST_SINGLE: {
        const queryParams = addParamToUrlVieON(router?.query, { vid: id });
        router.push(
          {
            pathname,
            query: queryParams
          },
          {
            pathname: PAGE.HOME,
            query: queryParams
          }
        );
        break;
      }
      default:
        break;
    }
    liveEventWelcomeTurorialSelectedContent({
      data: tVodDialog,
      userType: userType?.userType,
      flowName: TVOD.TVOD_ON_BOARDING_PRE_ORDER
    });
    onClosed();
  };
  const onClosed = () => {
    dispatch(openPopup());
    if (isMobile) dispatch(setStatusDialogOnBoarding(false));
  };

  const renderContent = () => (
    <div className="block block--for-dark block--notify">
      <h2 className="title text-center">{title}</h2>
      <p className="text text-center text-muted margin-small-up-bottom-12 margin-large-up-bottom-32">
        {desc}
      </p>
      {(type === TVOD.TYPE.LIVE_EVENT ||
        type === TVOD.TYPE.SIMULCAST_SINGLE ||
        type === TVOD.TYPE.SIMULCAST_MULTI) && (
        <div className="list divide-y-1 padding-medium-up-top-16 padding-medium-up-bottom-12 padding-medium-down-top-8 padding-medium-down-bottom-4 margin-large-up-bottom-32 margin-large-down-bottom-12">
          {(benefits || []).map((item: any, i: any) => (
            <div className="list__item flex-box align-middle margin-small-up-bottom-4" key={i}>
              <span className="icon icon--tiny">
                <i className={`${item?.iconClass} text-gold`} />
              </span>
              <span className="text text-gray239 text-large-up-16 text-medium-down-14 padding-small-up-left-4 padding-small-up-top-2 p-b">
                {item?.description}
              </span>
            </div>
          ))}
        </div>
      )}
      <div className="button-group child-auto">
        {(type === TVOD.TYPE.LIVE_EVENT || type === TVOD.TYPE.SIMULCAST_SINGLE) && (
          <Button
            className="button button--light hollow button--large-up button--xlarge-up m-b"
            onClick={onDetail}
            title={secondaryButton}
            textClass="!text-white"
          />
        )}
        <Button
          className="button button--light button--large-up button--xlarge-up m-b"
          onClick={onContinue}
          title={primaryButton}
        />
      </div>
    </div>
  );

  const renderCustom = () => (
    <div className="mask mask--overlay">
      <div className="mask-inner text-center">
        <img src={imgUrl} alt="livestream-image" />
      </div>
    </div>
  );

  return (
    <Modal
      className="modal--notify"
      renderCustom={renderCustom}
      renderBody={renderContent}
      onClosed={onClosePopup}
    />
  );
};

export default React.memo(PopupWelcomeTVod);
