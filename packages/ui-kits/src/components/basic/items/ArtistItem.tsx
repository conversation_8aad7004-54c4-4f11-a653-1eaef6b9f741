import React from 'react';
import VieLink from '../components/VieLink';
import { PAGE } from '@vieon/core/constants/constants';
import Image from '../components/basic/Image/Image';
const ArtistItem = React.memo(({ link, avatar, name, onClick }: any) => (
  <div className="card card--vod card--artist card--artist-related" onClick={onClick}>
    <div
      className={`avatar avatar--box overflow circle large-x${
        !avatar ? ' default light border--green' : ''
      }`}
    >
      <VieLink as={link} href={PAGE.ARTIST}>
        <div className="avatar__wrap">
          {avatar ? (
            <Image src={avatar} alt={`Nghệ sĩ ${name}`} />
          ) : (
            <span className="absolute alluvion-right" />
          )}
        </div>
      </VieLink>
    </div>

    <div className="card__section">
      <VieLink as={link} href={PAGE.ARTIST}>
        <h2 className="card__title text-white text-center" title={name}>
          {name || ''}
        </h2>
      </VieLink>
    </div>
  </div>
));

export default ArtistItem;
