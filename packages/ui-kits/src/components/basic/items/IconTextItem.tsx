import React from 'react';
import Icon from '../components/basic/Icon/Icon';

const IconTextItem = ({ className, icSpanClass, icClass, text }: any) => (
  <div className={`${className || ''} flex-box align-middle margin-small-up-bottom-4 text-gray239`}>
    <Icon
      spClass={icSpanClass || 'icon--tiny text-gold'}
      iClass={icClass || 'vie-clock-o-rc-medium'}
    />
    <span
      className="text text-large-up-16 text-medium-down-14 padding-small-up-left-4 padding-small-up-top-2 p-b p-t"
      dangerouslySetInnerHTML={{ __html: text || '' }}
    />
  </div>
);

export default React.memo(IconTextItem);
