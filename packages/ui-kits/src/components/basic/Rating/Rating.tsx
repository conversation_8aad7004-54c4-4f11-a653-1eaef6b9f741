import React, { useEffect, useState } from 'react';
import Tooltip from '../components/Tooltip';
import ConfigCookie from '@vieon/core/config/ConfigCookie';
import { EL_CLASS, POPUP } from '@vieon/core/constants/constants';
import { TEXT } from '@vieon/core/constants/text';
import { ratingButtonSelected } from '@vieon/tracking/functions/TrackingSegmentedUser';
import { getContentTypeText } from '@/helpers/utils';
import { useSelector } from 'react-redux';

const STARS = [
  { id: 1, label: 'Cần cải thiện nhiều' },
  { id: 2, label: 'Chưa hay lắm' },
  { id: 3, label: 'Ổn' },
  { id: 4, label: 'Hay' },
  { id: 5, label: 'Không thể rời mắt' }
];

const Rating = (props: any) => {
  const [point, setPoint] = useState(5);
  const [total, setTotal] = useState(props.total);
  const [increased, setIncreased] = useState(false);
  const [oldPoint, setOldPoint] = useState(5);
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const {
    avg,
    notUser,
    className,
    containerClass,
    selectionClass,
    summaryClass,
    isDisableToolTip,
    isMobile,
    cardData
  } = props;
  const avgNum = (avg || 0).toFixed(1);
  let temp = notUser ? props.point : point;
  temp = Math.round(temp * 2) / 2;

  const onClickRating = () => {
    localStorage.setItem('currentAuthFlow', 'registration_feature');
    localStorage.setItem('currentAuthFeature', 'rating');
    const dataTracking = {
      contentId: cardData?.id || '',
      contentName: cardData?.title,
      contentType: getContentTypeText(cardData?.type),
      isBlockVip: cardData?.isPremium || false,
      userType: userType?.userType || 'guest'
    };
    ratingButtonSelected(dataTracking);
    const { onClickRating } = props || {};
    if (typeof onClickRating === 'function') {
      onClickRating({
        name: POPUP.NAME.POPUP_RATING,
        title: TEXT.DO_YOU_LIKE_THIS_CONTENT,
        contentId: props.cardData?.id
      });
    }
  };

  const onRating = (e: any) => {
    if (props.disabled) return;
    onClickRating();
    const value = e?.target?.dataset?.value || e?.target?.dataset?.alt || 0;

    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    if (!accessToken) {
      const { requestLogin } = props || {};
      if (typeof requestLogin === 'function') requestLogin(true);
      return;
    }
    if (props.onRating) props.onRating({ data: e?.target });
    setOldPoint(value);
    if (props.point === 0 && !increased) {
      setPoint(value);
      setTotal(total + 1);
      setIncreased(!increased);
    } else setPoint(value);
  };

  const onMouseOver = (e: any) => {
    if (props.disabled) return;
    const value = e?.target?.dataset?.value || e?.target?.dataset?.alt || 0;
    const { onMouseOver } = props || {};
    if (typeof onMouseOver === 'function') onMouseOver(value);
    setPoint(value);
  };

  const onMouseOut = () => {
    setPoint(oldPoint || 5);
    const { onMouseOut } = props || {};
    if (typeof onMouseOut === 'function') onMouseOut();
  };

  useEffect(() => {
    setPoint(props.point || 5);
    setTotal(props.total);
    setOldPoint(+props.point || 5);
  }, [props.id]);

  return (
    <div className={className || 'rating rating--dark p-t1'}>
      <div className={containerClass || 'rating__wrap'}>
        {parseInt(avgNum) > 0 && (
          <span className={summaryClass || 'rating__summary'}>{avgNum || ''}</span>
        )}
        <div className={selectionClass || 'rating__selection'}>
          {STARS.map((star) => {
            let className = 'star-on-png';
            if (star.id <= temp) {
              className = 'star-on-png';
            } else if (star.id > temp && star.id - temp <= 0.5) {
              className = 'star-half-png';
            } else {
              className = 'star-off-png';
            }

            return (
              <Tooltip
                key={star.id}
                title={star.label}
                triggerEvent={isDisableToolTip || isMobile ? 'dismiss' : 'hover'}
                className="max-w-[212px] animate-fade-in"
                size={EL_CLASS.SMALL}
                isDarkBackground
              >
                <i
                  className={className}
                  title={star.label}
                  onClick={onRating}
                  onMouseEnter={onMouseOver}
                  onMouseOut={onMouseOut}
                  onBlur={() => {}}
                  data-alt={star.id}
                />
              </Tooltip>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Rating;
