import React from 'react';
import { PERMISSION } from '@vieon/core/constants/constants';

const WaitingSpin = React.memo(
  ({
    playerError,
    playingAds,
    permission,
    isLoading,
    isLimited,
    cancelPlayer,
    trialDuration
  }: any) => {
    if (
      !!cancelPlayer ||
      playerError ||
      playingAds ||
      isLimited ||
      (permission !== PERMISSION.CAN_WATCH && !trialDuration)
    ) {
      return null;
    }
    if (!isLoading) return null;
    return (
      <div className="loading transparent animate-spin absolute full flex-box align-center align-middle layer-1">
        <span className="spinner rounded big" />
      </div>
    );
  }
);

export default WaitingSpin;
