import React, { useEffect, useMemo, useRef } from 'react';
import Button from '../components/basic/Buttons/Button';
import { ICON_KEY, POSITION_ACTION } from '@vieon/core/constants/constants';
import classnames from 'classnames';
import { moveToPosition } from '@vieon/core/services/videoIndexingService';
import TrackingVieIndexing from '@vieon/tracking/functions/TrackingVieIndexing';
import { useDispatch, useSelector } from 'react-redux';
import { getItemIndicator } from '@vieon/core/store/actions/detail';
import isEmpty from 'lodash/isEmpty';
import styles from './InfoBloxItem.module.scss';
import InfoBoxItem from './InfoBoxItem';

const InfoBox = ({
  isDisplayInfoBox,
  isShowListProduct,
  data,
  item,
  isShowIndicator,
  handleActionVideoIndexing,
  contentDetail,
  currentTime,
  handleClickIndicator,
  idClick
}: any) => {
  const dispatch = useDispatch();
  const indicatorRef = useRef<any>(null);
  const { itemIndicator, sessionId, GET_CONTENT } = useSelector(
    (state: any) => state?.Detail || {}
  );
  useEffect(() => {
    if (!isEmpty(itemIndicator)) {
      const itemIndex =
        (itemIndicator || []).findIndex((ite: any) => ite?.objectId === item?.objectId) !== -1;
      if (!itemIndex) {
        dispatch(getItemIndicator({ data: item }));
        TrackingVieIndexing.showIndicator({
          data: GET_CONTENT,
          sessionId
        });
      }
    }
  }, [item?.objectId, itemIndicator]);

  useEffect(() => {
    if (isEmpty(itemIndicator)) {
      dispatch(getItemIndicator({ data: item }));
      TrackingVieIndexing.showIndicator({
        data: contentDetail,
        id: item?.objectId,
        sessionId
      });
    }
  }, []);

  useEffect(() => {
    const indicatorEl = indicatorRef.current;
    const currentItem = (item.groupedItems || []).find(
      (gItem: any) => gItem.startAt <= currentTime && gItem.endAt >= currentTime
    );

    if (currentItem && indicatorEl) {
      const { xPos, yPos } = currentItem || {};
      moveToPosition(indicatorEl, {
        topPos: yPos,
        leftPos: xPos
      });
    }
  }, [currentTime]);

  const handleClickDisplay = () => {
    if (!isDisplayInfoBox) {
      TrackingVieIndexing.indicatorSelected({
        data: GET_CONTENT,
        item: metaDataById,
        sessionId
      });
    }
    if (typeof handleClickIndicator === 'function') handleClickIndicator(item);
    if (typeof handleActionVideoIndexing === 'function') {
      handleActionVideoIndexing({ positionClick: POSITION_ACTION.INDICATOR });
    }
  };

  const { objects } = data?.meta || {};
  const metaDataById = useMemo(() => {
    if (objects) return objects?.find((obj: any) => obj?.id === item.objectId);
  }, [objects, item.objectId]);

  return (
    <div
      key={item?.objectId}
      id={item?.objectId}
      ref={indicatorRef}
      className={classnames(
        'absolute layer-1000 bg-blue flex-box animate-fade-in',
        styles.box,
        styles['position-indicator'],
        styles['move-position-indicator']
      )}
      style={{
        top: item?.yPos || 0,
        left: item?.xPos || 0
      }}
    >
      {!isShowListProduct && isShowIndicator && (
        <Button
          iconType={ICON_KEY.CART_INDICATOR}
          className="button p-x"
          isActiveIcon={idClick === item?.objectId && isDisplayInfoBox}
          onClick={() => handleClickDisplay()}
        />
      )}
      {!isShowListProduct &&
        isDisplayInfoBox &&
        metaDataById &&
        idClick === item?.objectId &&
        isShowIndicator && (
          <InfoBoxItem
            isHover
            data={metaDataById}
            item={item}
            contentDetail={contentDetail}
            objectId={item.objectId}
            handleClickIndicator={handleClickIndicator}
            indicatorRef={indicatorRef}
            currentTime={currentTime}
          />
        )}
    </div>
  );
};

export default React.memo(InfoBox);
