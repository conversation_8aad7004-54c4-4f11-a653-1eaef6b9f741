import React from 'react';
import get from 'lodash/get';
import { EL_ID } from '@vieon/core/constants/constants';
import { HLS_CONFIG } from '@vieon/core/constants/player';
import { createTimeout } from '@vieon/core/utils/common';
import { isSafari } from 'react-device-detect';
import Hls from 'hls.js';

class HlsPlayerComponent extends React.PureComponent<any> {
  levelLoaded: any;
  linkPlay: any;
  loadError: any;
  loadFail: any;
  loadSuccess: any;
  onStalled: any;
  player: any;
  resumeTime: any;
  video: any;
  videoPlayer: any;
  videoRef: any;
  stalledTimer: any = null;

  componentDidMount() {
    const {
      loadSuccess,
      loadFail,
      linkPlay,
      initAd,
      isAdPlay,
      onStalled,
      isPremiere,
      levelLoaded,
      setPlayer,
      onPlaying,
      onPaused,
      onWaiting,
      canplay,
      onTimeUpdate,
      onCheckPlayer,
      onSeeked
    }: any = this.props;
    this.video = this.videoRef;
    this.loadSuccess = loadSuccess || (() => {});
    this.levelLoaded = levelLoaded || (() => {});
    this.loadFail = loadFail || (() => {});
    this.onStalled = onStalled;
    this.loadError = 0;
    if (typeof setPlayer === 'function') {
      setPlayer({
        video: this.video,
        player: this.player,
        setQuality: this.onSetQuality,
        setAudio: this.onSetAudio,
        setSubtitle: this.onSetSubtitle,
        removePlayer: this.removePlayer
      });
    }

    if (this.stalledTimer) clearTimeout(this.stalledTimer);
    this.setupHlsPlayer(linkPlay, isPremiere);
    initAd();
    if (this.video) {
      this.video.addEventListener('playing', (e: any) => {
        if (typeof onPlaying === 'function') onPlaying(e);
      });
      this.video.addEventListener('pause', (e: any) => {
        if (typeof onPaused === 'function') onPaused(e);
      });
      this.video.addEventListener('ended', this.handlePlayerEnded, false);
      this.video.addEventListener('waiting', (e: any) => {
        if (typeof onWaiting === 'function') onWaiting(e);
      });
      this.video.addEventListener('canplay', () => {
        if (typeof canplay === 'function') canplay(this.video);
      });
      this.video.addEventListener('timeupdate', () => {
        const { currentTime } = this.video;
        const { duration } = this.video;
        if (typeof onTimeUpdate === 'function') onTimeUpdate({ currentTime, duration });
        if (typeof onCheckPlayer === 'function') onCheckPlayer(this.player);
      });
      this.video.addEventListener('error', this.loadFail, true);
      this.video.addEventListener('stalled', (e: any) => {
        if (isAdPlay) return;
        if (this.stalledTimer) clearTimeout(this.stalledTimer);
        this.resetStateVideoStalled();
        this.stalledTimer = createTimeout(() => {
          if (typeof this.onStalled === 'function') this.onStalled(e);
        }, 10000);
      });
      this.video.addEventListener('seeked', () => {
        if (typeof onSeeked === 'function') onSeeked();
      });
      this.video.addEventListener('progress', () => {
        if (this.stalledTimer) clearTimeout(this.stalledTimer);
        this.resetStateVideoStalled();
      });
    }
  }

  componentWillUnmount() {
    this.removePlayer();
  }

  handlePlayerEnded = (e: any) => {
    const { onEnded }: any = this.props;
    if (typeof onEnded === 'function') onEnded(e);
  };

  resetStateVideoStalled = () => {
    const { onResetStateVideoStalled }: any = this.props;
    if (typeof onResetStateVideoStalled === 'function') {
      onResetStateVideoStalled();
    }
  };

  setupHlsPlayer = (link: any, resumeTime: any) => {
    const linkPlay: any = link || this.linkPlay;
    this.resumeTime = resumeTime;
    const isMp4 = (linkPlay || '').includes('.mp4');
    const { isLiveStream, isIOS, isPremiere, setPlayer }: any = this.props;
    if (isMp4) {
      this.video.src = linkPlay;
      this.video.load();
      this.video.loop = !!isLiveStream;
      this.loadSuccess();
      return;
    }
    this.video.loop = false;
    const isCanPlayType = this.video.canPlayType('application/vnd.apple.mpegurl');
    if (isIOS && this.video && isCanPlayType) {
      this.video.src = linkPlay;
      this.video.load();
      this.loadSuccess();
      return;
    }
    if (!Hls.isSupported()) return;
    if (this.player) this.player.stopLoad();

    const hlsConfig: any = HLS_CONFIG;
    if (isPremiere && isSafari) hlsConfig.liveDurationInfinity = true;
    this.player = new Hls(hlsConfig);
    if (typeof setPlayer === 'function') {
      setPlayer({
        video: this.video,
        player: this.player,
        setQuality: this.onSetQuality,
        setAudio: this.onSetAudio,
        setSubtitle: this.onSetSubtitle,
        removePlayer: this.removePlayer
      });
    }

    if (this.video) this.video.pause();
    this.video.src = '';
    this.player.attachMedia(this.video);
    this.player.on(Hls.Events.MEDIA_ATTACHED, () => {
      this.loadSource(linkPlay);
    });
    this.player.on(Hls.Events.ERROR, (event: any, data: any) => {
      this.onError(event, data);
    });
    this.player.on(Hls.Events.MANIFEST_LOADED, () => {
      this.loadSuccess();
    });
    this.player.on(Hls.Events.LEVEL_LOADED, (event: any, data: any) => {
      this.levelLoaded(event, data);
    });
  };

  loadSource = (linkPlay: any) => {
    this.linkPlay = linkPlay;
    if (!linkPlay || !this.player) return;
    this.player.loadSource(linkPlay);
  };

  onError = (event: any, data: any) => {
    const { fatal } = data || {};
    if (fatal) {
      this.loadFail(event, data);
      if (this.video) this.video.pause();
      // OFF Flow Retry -> Go to water fall
    }
  };

  onSetSubtitle = (subName: any) => {
    if (this.player) {
      const listKeys = Object.keys(this.player.subtitleTracks);
      for (let i = 0; i < listKeys.length; i += 1) {
        if (
          get(this.player, `subtitleTracks[${listKeys[i]}].lang`, '').toUpperCase() ===
          subName.toUpperCase()
        ) {
          this.player.subtitleTrack = listKeys[i];
          break;
        }
      }
    }
  };

  onSetQuality = (item: any) => {
    if (!this.player || !item) return;
    const qualityLevels = this.player.levels;
    const qualityIndex = (qualityLevels || []).findIndex((qua: any) => qua.width === item.width);
    this.player.loadLevel = qualityIndex;
  };

  onSetAudio = (item: any) => {
    if (!item) return;
    const playerEl = this.player || this.video;
    const audioTracks = playerEl?.audioTracks;
    let audio = null;
    for (let i = 0; i < audioTracks?.length; i += 1) {
      if (
        audioTracks?.[i]?.language === item?.codeName ||
        audioTracks?.[i]?.lang === item?.codeName
      ) {
        audio = audioTracks?.[i];
        audioTracks[i].enabled = true;
      } else {
        audioTracks[i].enabled = false;
      }
    }
    if (audio) {
      playerEl.audioTrack = audio?.id;
    }
  };

  stopLoad = () => {
    if (!this.player) return;
    this.player.stopLoad();
  };

  removePlayer = () => {
    if (!this.player) return;
    try {
      this.video.pause();
      this.player.detachMedia(this.videoPlayer);
      this.stopLoad();
      this.player.destroy();
    } catch (e) {
      // handle error
    }
  };

  render() {
    return (
      <video
        id={this.props.playerId || EL_ID.VIE_PLAYER}
        autoPlay
        ref={(el: any) => (this.videoRef = el)}
        playsInline={!!(this.props.isMobile && this.props.isIOS)}
      />
    );
  }
}

export default HlsPlayerComponent;
