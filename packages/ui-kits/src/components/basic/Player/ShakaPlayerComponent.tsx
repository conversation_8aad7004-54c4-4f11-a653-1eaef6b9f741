import React from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import muxjs from 'mux.js';
import { EL_ID, HTTP_CODE } from '@vieon/core/constants/constants';
import { DRM, DRM_CERT, DRM_PROVIDER, DRM_SERVER, QNET } from '@vieon/core/constants/player';
import { setPlayerErrorLog } from '@vieon/core/services/playerServices';

declare const window: any;

const shaka = require('shaka-player/dist/shaka-player.ui.js');

class ShakaPlayerComponent extends React.PureComponent<any, any> {
  adsStartTime: any;
  assetId: any;
  drmQnetInfo: any;
  handleHttpCode: any;
  linkPlay: any;
  liveTVCCU: any;
  loadFail: any;
  loadSuccess: any;
  offVideoTimeout: any;
  onLoadStart: any;
  onStalled: any;
  operatorId: any;
  paramsValidDrm: any;
  player: any;
  playerDisposed: any;
  uri: any;
  variantId: any;
  video: any;
  videoRef: any;
  stalledTimer: any;

  componentDidMount() {
    window.muxjs = muxjs;
    const {
      loadSuccess,
      onStalled,
      loadFail,
      qnetInfo,
      liveTVCCU,
      onLoadStart,
      handleHttpCode,
      drmMerchant
    } = this.props || {};
    this.liveTVCCU = liveTVCCU;
    this.handleHttpCode = handleHttpCode;
    this.drmQnetInfo = {
      userId: qnetInfo?.userId || QNET.USER_ID,
      sessionId: qnetInfo?.sessionId || QNET.SESSION_ID,
      merchant: drmMerchant
    };
    this.operatorId = qnetInfo?.operatorId;
    this.video = this.videoRef;
    this.onLoadStart = onLoadStart;
    this.loadSuccess = loadSuccess;
    this.onStalled = onStalled;
    this.loadFail = loadFail;

    this.initShakaPlayer();
  }

  componentWillUnmount() {
    this.removePlayer();
    window.removeEventListener('beforeunload', this.onBrowserChange);

    if (this.offVideoTimeout) {
      clearTimeout(this.offVideoTimeout);
    }
  }

  initShakaPlayer = () => {
    if (typeof shaka !== 'undefined') {
      shaka.polyfill.installAll();
      shaka.polyfill.PatchedMediaKeysApple.install();
      if (shaka.Player.isBrowserSupported()) {
        // Everything looks good!
        this.initPlayer();
      } else {
        // This browser does not have the minimum set of APIs we need.
        console.error('Browser not supported!');
      }
    }
  };

  initPlayer = () => {
    this.player = new shaka.Player(this.video);
    window.viePlayer = this.player;
    window.addEventListener('beforeunload', this.onBrowserChange);
    if (this.player) {
      // console.log("addEventListener error")
      this.player.addEventListener('error', this.handlePlayerError, true);
    }
    if (this.props.setPlayer) {
      this.props.setPlayer({
        video: this.video,
        player: this.player,
        setQuality: this.onSetQuality,
        setAudio: this.onSetAudio,
        setSubtitle: this.onSetSubtitle,
        removePlayer: this.removePlayer,
        loadSource: this.loadSource
      });
    }
    if (this.stalledTimer) clearTimeout(this.stalledTimer);
    this.setupShakaPlayer();
    if (typeof this.props.initAd === 'function') this.props.initAd();
    if (this.video) {
      this.video.addEventListener('playing', (e: any) => {
        if (this.props.onPlaying) this.props.onPlaying(e);
      });
      this.video.addEventListener('pause', (e: any) => {
        if (this.props.onPaused) this.props.onPaused(e);
      });
      this.video.addEventListener('ended', this.handlePlayerEnded, false);
      this.video.addEventListener('waiting', (e: any) => {
        if (this.props.onWaiting) this.props.onWaiting(e);
      });
      this.video.addEventListener('stalled', this.handleStalled, true);
      this.video.ontimeupdate = this.props.onTimeUpdate || (() => {});
      this.video.addEventListener('canplay', (e: any) => {
        if (this.props.canplay) {
          this.props.canplay(e);
        }
      });
      this.video.addEventListener('seeking', () => {
        if (typeof this.props.onSeeking === 'function') this.props.onSeeking();
      });
      this.video.addEventListener('seeked', () => {
        if (typeof this.props.onSeeked === 'function') this.props.onSeeked();
      });
      this.video.addEventListener('progress', this.resetStateVideoStalled, true);
    }
  };

  handleStalled = (e: any) => {
    if (this.props.isAdPlay) return;
    if (this.stalledTimer) clearTimeout(this.stalledTimer);
    this.resetStateVideoStalled();
    this.stalledTimer = setTimeout(() => {
      if (typeof this.onStalled === 'function') this.onStalled(e);
    }, 10000);
  };

  resetStateVideoStalled = () => {
    if (this.stalledTimer) clearTimeout(this.stalledTimer);
    if (typeof this.props.onResetStateVideoStalled === 'function') {
      this.props.onResetStateVideoStalled();
    }
  };

  checkErrorLastChunk = () => {
    const isLive = this.player.isLive();
    const info = this.player.getBufferedInfo();
    const buffer = get(info, 'total[0].end', 0);
    const duration = this.video ? this.video.duration : 0;
    if (!isLive && buffer + 8 > duration) return true;
    return false;
  };

  handlePlayerEnded = (e: any) => {
    // SET END SCREEN
    if (this.props.onEnded) this.props.onEnded(e);
  };

  handlePlayerError = (e: any) => {
    const errorDetail = e?.detail || e;
    console.log('PlayerError', errorDetail);
    setPlayerErrorLog({ errorDetail });

    const bufferedRanges = this.video.buffered;

    if (bufferedRanges.length === 0) {
      if (errorDetail?.severity === 2 || errorDetail?.data?.[1] === HTTP_CODE.ERROR_404) {
        const errorLastChunk = this.checkErrorLastChunk();
        if (errorLastChunk && this.props.onEnded) {
          this.props.onEnded();
        } else {
          this.unload();
          this.loadFail(errorDetail);
        }
      }
    } else {
      let waitingTime = Math.max(bufferedRanges.end(0) - bufferedRanges.start(0), 0) * 1000;

      if (this.offVideoTimeout) {
        clearTimeout(this.offVideoTimeout);
      }

      this.offVideoTimeout = setTimeout(() => {
        if (errorDetail?.severity === 2 || errorDetail?.data?.[1] === HTTP_CODE.ERROR_404) {
          const errorLastChunk = this.checkErrorLastChunk();
          if (errorLastChunk && this.props.onEnded) {
            this.props.onEnded();
          } else {
            this.unload();
            this.loadFail(errorDetail);
          }
        }
      }, waitingTime);
    }
  };

  onBrowserChange = () => {
    if (!this.playerDisposed) {
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
    }
    this.playerDisposed = true;
    return undefined;
  };

  setupShakaPlayer = () => {
    const {
      drmInfo,
      linkPlay,
      isDrm,
      usingSigmaPacker,
      qnetInfo,
      qnetDrm,
      variantId,
      drmProvider,
      drmMerchant
    } = this.props || {};
    if (this.props.isSafari || this.props.isIOS) {
      this.handleDrmFairPlay({
        isDrm,
        drmInfo: qnetDrm ? this.drmQnetInfo : drmInfo,
        linkPlay,
        drmProvider,
        qnetDrm,
        qnetInfo,
        variantId,
        drmMerchant,
        operatorId: this.operatorId
      });
    } else {
      this.handleDrmWideVine({
        isDrm,
        drmInfo: qnetDrm ? this.drmQnetInfo : drmInfo,
        linkPlay,
        drmProvider,
        usingSigmaPacker,
        qnetDrm,
        qnetInfo,
        drmMerchant,
        operatorId: this.operatorId
      });
    }
  };

  handleDrmFairPlay = async ({
    drmProvider,
    isDrm,
    drmInfo,
    linkPlay,
    qnetDrm,
    qnetInfo,
    drmMerchant
  }: any) => {
    if (isDrm) {
      const drmCert =
        drmProvider === DRM_PROVIDER.SIGMA_DRM
          ? `${DRM_CERT.SIGMA_FAIR_PLAY}${drmMerchant}/${DRM.APP_ID}`
          : `${DRM_CERT.DRM_TODAY_FAIR_PLAY}${drmMerchant}`;
      const req = await fetch(drmCert);
      if (!req.ok) return console.log('server not response');
      const cert = await req.arrayBuffer();
      this.configDRMFairPlay({
        drmProvider,
        drmInfo,
        cert,
        drmMerchant
      });
    } else {
      this.configNoDRM();
    }
    const { userId, sessionId } = qnetInfo || {};
    if (qnetDrm) {
      this.liveTVCCU.onStart({
        userId,
        sessionId,
        loadSource: this.loadSource,
        closePlayer: this.removePlayer,
        operatorId: this.operatorId
      });
    } else {
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
      this.loadSource(linkPlay);
    }
  };

  configNoDRM = () => {
    if (isEmpty(this.player)) return;
    this.player.configure({
      streaming: {
        rebufferingGoal: 0,
        bufferingGoal: 24,
        bufferBehind: 10,
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        alwaysStreamText: true, // relevant subtitle function
        useNativeHlsOnSafari: true,
        ignoreTextStreamFailures: true
      },
      manifest: {
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        dash: {
          ignoreMinBufferTime: true
        }
      },
      abr: {
        defaultBandwidthEstimate: 250000
      }
    });
  };

  configDRMFairPlay = ({ drmProvider, drmInfo, cert, drmMerchant }: any) => {
    if (!this.player || !drmInfo) return;
    this.uri = null;
    const licenseURL =
      drmProvider === DRM_PROVIDER.SIGMA_DRM
        ? DRM_SERVER.SIGMA_FAIR_PLAY
        : DRM_SERVER.TODAY_FAIR_PLAY;
    this.player.configure({
      drm: {
        servers: {
          'com.apple.fps.1_0': licenseURL
        },
        advanced: {
          'com.apple.fps.1_0': {
            serverCertificate: new Uint8Array(cert)
          }
        },
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 2, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        }
      },
      abr: {
        enabled: false,
        defaultBandwidthEstimate: 250000
      },
      streaming: {
        rebufferingGoal: 0,
        bufferingGoal: 24,
        bufferBehind: 10,
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        alwaysStreamText: true, // relevant subtitle function
        useNativeHlsOnSafari: true,
        ignoreTextStreamFailures: true
      },
      manifest: {
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        dash: {
          ignoreMinBufferTime: true
        }
      }
    });
    this.player.configure('drm.initDataTransform', (initData: any, initDataType: any) => {
      if (initDataType !== 'skd') return initData;
      const skdUri = shaka.util.StringUtils.fromBytesAutoDetect(initData);
      const licenseUrl = new URL(skdUri);
      licenseUrl.protocol = 'https';
      let contentId =
        drmProvider === DRM_PROVIDER.SIGMA_DRM
          ? shaka.util.FairPlayUtils.defaultGetContentId(initData)
          : shaka.util.StringUtils.toUTF16(skdUri);
      if (drmProvider === DRM_PROVIDER.SIGMA_DRM) {
        this.assetId = licenseUrl.searchParams.get('assetId');
        this.paramsValidDrm = licenseUrl.search;
      } else {
        this.assetId = this.props.assetId || licenseUrl.searchParams.get('assetId');
        this.variantId = this.props.variantId || '';
        if (drmMerchant === QNET.MERCHANT) {
          contentId = shaka.util.FairPlayUtils.defaultGetContentId(initData);
        }
      }
      const cert = this.player.drmInfo().serverCertificate;
      return shaka.util.FairPlayUtils.initDataTransform(initData, contentId, cert);
    });
    this.player.getNetworkingEngine().registerRequestFilter((type: any, request: any) => {
      if (type !== shaka.net.NetworkingEngine.RequestType.LICENSE) {
        return;
      }
      const { Uint8ArrayUtils } = shaka.util;
      if (drmProvider === DRM_PROVIDER.SIGMA_DRM) {
        request.uris[0] += this.paramsValidDrm;
        request.headers['Content-type'] = 'application/json';
        request.headers['custom-data'] = btoa(JSON.stringify(drmInfo));
        const originalPayload = new Uint8Array(request.body);
        const base64Payload = Uint8ArrayUtils.toStandardBase64(originalPayload);
        const params: any = JSON.stringify({
          spc: base64Payload,
          assetId: encodeURIComponent(this.assetId)
        });
        request.body = params;
      } else {
        const { StringUtils } = shaka.util;
        const originalPayload = new Uint8Array(request.body);
        const base64Payload = Uint8ArrayUtils.toStandardBase64(originalPayload)
          .replace(/\+/g, '-')
          .replace(/\//g, '_');
        const params: any = `spc=${base64Payload}&assetId=${encodeURIComponent(
          this.assetId
        )}&variantId=${encodeURIComponent(this.variantId)}`;
        const wrapdrm = JSON.stringify(drmInfo);
        const wrapped = Uint8ArrayUtils.toBase64(new Uint8Array(StringUtils.toUTF8(wrapdrm)));
        request.headers['x-dt-custom-data'] = wrapped;
        request.headers['Content-Type'] = 'application/x-www-form-urlencoded';
        request.body = StringUtils.toUTF8(params);
      }
    });
    this.player.getNetworkingEngine().registerResponseFilter((type: any, response: any) => {
      if (type !== shaka.net.NetworkingEngine.RequestType.LICENSE) {
        return;
      }
      if (drmProvider === DRM_PROVIDER.SIGMA_DRM) {
        const { StringUtils } = shaka.util;
        const { Uint8ArrayUtils } = shaka.util;
        try {
          const wrappedString = StringUtils.fromUTF8(response.data);
          // Parse the JSON string into an object.
          const wrapped = JSON.parse(wrappedString);

          // This is a base64-encoded version of the raw license.
          const rawLicenseBase64 = wrapped.license;
          // Decode that base64 string into a Uint8Array and replace the response
          // data.  The raw license will be fed to the Widevine CDM.
          response.data = Uint8ArrayUtils.fromBase64(rawLicenseBase64);
        } catch (err) {
          console.log(err);
        }
      } else {
        let responseText = shaka.util.StringUtils.fromUTF8(response.data);
        // Trim whitespace.
        responseText = responseText.trim();
        // Look for <ckc> wrapper and remove it.
        if (responseText.substr(0, 5) === '<ckc>' && responseText.substr(-6) === '</ckc>') {
          responseText = responseText.slice(5, -6);
        }
        // Decode the base64-encoded data into the format the browser expects.
        response.data = shaka.util.Uint8ArrayUtils.fromBase64(responseText).buffer;
      }
    });
  };

  handleDrmWideVine = ({
    usingSigmaPacker,
    drmProvider,
    isDrm,
    drmInfo,
    linkPlay,
    qnetDrm,
    qnetInfo,
    drmMerchant
  }: any) => {
    if (isDrm) {
      this.configDRMWideVine({
        usingSigmaPacker,
        drmInfo,
        drmProvider,
        drmMerchant
      });
    } else {
      this.configNoDRM();
    }
    const { userId, sessionId } = qnetInfo || {};
    if (qnetDrm) {
      this.liveTVCCU.onStart({
        userId,
        sessionId,
        loadSource: this.loadSource,
        closePlayer: this.removePlayer,
        operatorId: this.operatorId
      });
    } else {
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
      this.loadSource(linkPlay);
    }
  };

  configDRMWideVine = ({ usingSigmaPacker, drmInfo, drmProvider }: any) => {
    if (isEmpty(this.player) || isEmpty(drmInfo)) return;
    this.player.configure({
      drm: {
        servers: {
          'com.widevine.alpha':
            drmProvider === DRM_PROVIDER.SIGMA_DRM
              ? DRM_SERVER.SIGMA_WIDE_VINE
              : DRM_SERVER.TODAY_WIDE_VINE,
          'com.microsoft.playready':
            drmProvider === DRM_PROVIDER.SIGMA_DRM ? DRM_SERVER.SIGMA_PLAY_READY : ''
        },
        advanced: {
          'com.widevine.alpha': {
            videoRobustness: 'SW_SECURE_CRYPTO',
            audioRobustness: 'SW_SECURE_CRYPTO'
          }
        },
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 2, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        }
      },
      streaming: {
        rebufferingGoal: 2,
        bufferingGoal: 12,
        bufferBehind: 12,
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        alwaysStreamText: true,
        ignoreTextStreamFailures: true
      },
      abr: {
        defaultBandwidthEstimate: 250000
      },
      manifest: {
        retryParameters: {
          timeout: 10000, // timeout in ms, after which we abort; 0 means never
          maxAttempts: 3, // the maximum number of requests before we fail
          baseDelay: 1000, // the base delay in ms between retries
          backoffFactor: 2, // the multiplicative backoff factor between retries
          fuzzFactor: 0.5 // the fuzz factor to apply to each retry delay
        },
        dash: {
          ignoreMinBufferTime: true
        }
      }
    });
    this.player.getNetworkingEngine().registerRequestFilter((type: any, request: any) => {
      // Only add headers to license requests:
      if (type === shaka.net.NetworkingEngine.RequestType.LICENSE) {
        // This is the specific header name and value the server wants:
        if (drmProvider === DRM_PROVIDER.SIGMA_DRM) {
          const drmRequest = drmInfo;
          if (usingSigmaPacker) {
            const packInfo =
              typeof window !== 'undefined' ? window.sigmaPacker.getDataPacker() || {} : {};
            drmRequest.reqId = packInfo.requestId;
            drmRequest.deviceInfo = packInfo.deviceInfo;
          }
          request.headers['Content-Type'] = 'application/octet-stream';
          request.headers['custom-data'] = btoa(JSON.stringify(drmRequest));
        } else {
          const { StringUtils } = shaka.util;
          const { Uint8ArrayUtils } = shaka.util;
          const wrapdrm = JSON.stringify(drmInfo);
          const wrapped = Uint8ArrayUtils.toBase64(new Uint8Array(StringUtils.toUTF8(wrapdrm)));
          request.headers['x-dt-custom-data'] = wrapped;
        }
      }
    });
    this.player.getNetworkingEngine().registerResponseFilter((type: any, response: any) => {
      // Alias some utilities provided by the library.
      const { StringUtils } = shaka.util;
      const { Uint8ArrayUtils } = shaka.util;
      if (type === shaka.net.NetworkingEngine.RequestType.LICENSE) {
        if (drmProvider === DRM_PROVIDER.SIGMA_DRM) {
          try {
            const wrappedString = StringUtils.fromUTF8(response.data);
            // Parse the JSON string into an object.
            const wrapped = JSON.parse(wrappedString);
            if (usingSigmaPacker) {
              if (response.headers['client-info']) {
                window.sigmaPacker.update(atob(response.headers['client-info']));
              } else if (wrapped.clientInfo) {
                window.sigmaPacker.update(JSON.stringify(wrapped.clientInfo));
              }
            }
            // This is a base64-encoded version of the raw license.
            const rawLicenseBase64 = wrapped.license;
            // Decode that base64 string into a Uint8Array and replace the response
            // data.  The raw license will be fed to the Widevine CDM.
            response.data = Uint8ArrayUtils.fromBase64(rawLicenseBase64);
          } catch (err) {
            console.log(err);
          }
        } else {
          const keySystem = this.player.keySystem();
          // Only manipulate license responses:
          if (keySystem === 'com.widevine.alpha') {
            // This is the wrapped license, which is a JSON string.
            let wrappedString = '';
            try {
              wrappedString = StringUtils.fromUTF8(response.data);
            } catch (e) {
              console.log('xxxxxxxx ===> e', e);
            }
            if (wrappedString) {
              // Parse the JSON string into an object.
              let wrapped = JSON.parse(wrappedString);
              // This is a base64-encoded version of the raw license.
              let rawLicenseBase64 = wrapped.license;
              // Decode that base64 string into a Uint8Array and replace the response
              // data.  The raw license will be fed to the Widevine CDM.
              response.data = Uint8ArrayUtils.fromBase64(rawLicenseBase64);
              // Read additional fields from the server.
              // The server we are using in this tutorial does not send anything useful.
              // In practice, you could send any license metadata the client might need.
              // Here we log what the server sent to the JavaScript console for
              // inspection.
            }
          }
        }
      }
    });
  };

  loadStart = () => {
    if (this.onLoadStart) this.onLoadStart();
  };

  loadSource = (linkPlay: any) => {
    this.linkPlay = linkPlay || this.props.linkPlay;
    this.adsStartTime = 0;
    if (!this.linkPlay || typeof this.linkPlay !== 'string') {
      this.player.unload();
      return;
    }
    this.loadStart();
    const isMp4 = (linkPlay || '').includes('.mp4');
    this.video.loop = isMp4 && !!this.props?.isLiveStream;
    this.player.load(this.linkPlay).then(this.onTechSupport).catch(this.handlePlayerError);
  };

  onTechSupport = () => {
    window.viePlayer = this.player;
    this.player.seekRange();
    this.loadSuccess(this.linkPlay);
  };

  onLoadFail = (e: any) => {
    console.log('onLoadFail', e); // show error
    this.handleError(e);
  };

  handleError = (e: any) => {
    if (e?.code === 7000 && e?.severity !== 2) return;
    this.loadFail(e);
  };

  unload = () => {
    if (!this.player) return;
    this.video.pause();
    this.video.removeAttribute('src'); // empty source
    this.video.load();
    this.player.unload();
  };

  onSetQuality = (item: any) => {
    if (!this.player || !item) return;
    const checkExistQuality =
      (this.props?.dataQuality || this.player?.getVariantTracks() || []).findIndex(
        (it: any) => it?.width >= item.width
      ) > -1;
    if (item && item.id) {
      this.player.selectVariantTrack(item);
      if (item.width === 0) {
        const arrayVariantTracks = (this.props?.dataQuality || []).find(
          (it: any) => it.active === true
        );
        this.player.configure({
          restrictions: {
            maxWidth: arrayVariantTracks?.width,
            minWidth: arrayVariantTracks?.width
          }
        });
      } else if (checkExistQuality) {
        this.player.configure({
          restrictions: {
            maxWidth: item.width,
            minWidth: item.width
          }
        });
      }
    }
  };

  onSetAudio = (item: any) => {
    if (!this.player || !item) return;
    this.player.selectAudioLanguage(item?.codeName);
  };

  onSetSubtitle = (subName: any) => {
    const textTracks = this.player.getTextTracks();
    for (let i = 0; i < textTracks.length; i += 1) {
      if (textTracks[i].language === subName) {
        this.player.selectTextTrack(textTracks[i]);
        break;
      }
    }
  };

  removePlayer = (httpCode?: any) => {
    if (this.handleHttpCode && httpCode) this.handleHttpCode(httpCode);
    if (!this.player) return;
    this.unload();
    this.player.removeEventListener('error');
    this.video.pause();
    this.player.destroy();
  };

  render() {
    return (
      <video
        id={this.props.playerId || EL_ID.VIE_PLAYER}
        autoPlay
        ref={(el: any) => (this.videoRef = el)}
        playsInline={!!(this.props.isMobile && this.props.isIOS)}
      />
    );
  }
}

export default ShakaPlayerComponent;
