import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { parseInfoDebug, copyText } from '@vieon/core/utils/common';
import style from './InfoDebugPlayer.module.scss';
import Button from '../Buttons/Button';

const GetInfoDebugPlayer = ({
  player,
  video,
  playerName,
  linkPlay,
  onCheckInfoDebugPlayer
}: any) => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const [infoDebugPlayer, setInfoPlayer] = useState<any>(null);
  const [isShowInfo, setShowInfo] = useState(false);
  useEffect(() => {
    if (linkPlay && playerName && (player || video)) {
      setInfoPlayer(parseInfoDebug({ playerName, player, video, linkPlay }));
    }
  }, [player, video, playerName, linkPlay]);

  useEffect(() => {
    setShowInfo(!isShowInfo);
    if (infoDebugPlayer?.length > 0 && typeof onCheckInfoDebugPlayer === 'function') {
      onCheckInfoDebugPlayer();
    }
  }, [linkPlay]);

  const onCLick = (data: any) => {
    const textValue = (data || []).map((item: any) => {
      const textInfo = item?.text;
      return textInfo;
    });
    copyText(textValue);
  };

  return (
    isShowInfo &&
    infoDebugPlayer?.length > 0 && (
      <div className={`${style.infoPlayer} absolute ${isMobile ? style.mobiInfo : ''}`}>
        {(infoDebugPlayer || []).map((item: any, index: number) => (
          <div className="text text-white p-1 p-l2" key={index}>
            {item?.text}
          </div>
        ))}
        <div className="button-group justify-content-center p-b3 p-t2">
          <Button
            title="Copy Text"
            className="button button--light hollow button--small-up button--medium-up m-b"
            onClick={() => onCLick(infoDebugPlayer)}
          />
        </div>
      </div>
    )
  );
};
export default GetInfoDebugPlayer;
