import React, { useEffect, useRef, useState } from 'react';
import styles from '../components/basic/Player/warningScreen.module.scss';
import classNames from 'classnames';
import { TEXT } from '@vieon/core/constants/text';
import { useSelector } from 'react-redux';

const WarningScreen = ({
  isLiveTV,
  contentId,
  isFullscreen,
  handleSeekbarContent,
  controlWarningMessage,
  warningScreen,
  video
}: any) => {
  const timeRef = useRef<any>(null);
  const [enable, setEnable] = useState(true);
  const contentConfig = useSelector((state: any) => state?.AppConfig?.content) || {};
  const warningContent = contentConfig?.warningScreen?.[warningScreen];

  useEffect(() => {
    if (typeof controlWarningMessage === 'function') controlWarningMessage(false);
    if (timeRef.current) clearTimeout(timeRef.current);
    timeRef.current = setTimeout(() => {
      if (video) {
        video.play();
      }
      setEnable(false);
      if (typeof controlWarningMessage === 'function') controlWarningMessage(true);
      if (typeof handleSeekbarContent === 'function') handleSeekbarContent({ isWarning: true });
    }, 5000);
    return () => {
      clearTimeout(timeRef.current);
    };
  }, []);

  useEffect(() => {
    if (video) {
      video.pause();
    }
  }, [video]);

  useEffect(() => {
    if (contentId) {
      setEnable(true);
    }
  }, [contentId]);

  if (!enable) return null;
  return (
    <div
      className={classNames(
        `${styles.warning} layer-9 animate-fade-out ${styles.delay2500} `,
        !isFullscreen && isLiveTV && styles.screenLiveTv
      )}
    >
      <div className="modal modal--large">
        <h2
          className={`title text-center ${styles.title}`}
          dangerouslySetInnerHTML={{ __html: warningContent?.title || TEXT.TITLE_WARNING_SCREEN }}
        />
        <p
          className="text text-muted text-center padding-small-up-16 text-large-up-24 text-14"
          dangerouslySetInnerHTML={{
            __html: warningContent?.description || ''
          }}
        />
      </div>
    </div>
  );
};
export default React.memo(WarningScreen);
