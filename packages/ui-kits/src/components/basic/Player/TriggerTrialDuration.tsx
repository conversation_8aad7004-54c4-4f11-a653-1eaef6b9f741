import React, { useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import { CONTENT_TYPE, PAGE, PERMISSION, POPUP } from '@vieon/core/constants/constants';
import { motion } from 'framer-motion';
import Button from '@/components/basic/Buttons/Button';
import { TEXT } from '@vieon/core/constants/text';
import { openPopup } from '@vieon/core/store/actions/popup';
import classNames from 'classnames';
import { segmentRegistrationEventsPopupAuth } from '@vieon/tracking/functions/TrackingContentForceLoginPopupAuth';
import { trackingAuth } from '@vieon/tracking/functions/TrackingAuthentication';
import TrackingPayment from '@vieon/tracking/functions/payment';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import LocalStorage from '@vieon/core/config/LocalStorage';

const trackingPayment = new TrackingPayment();
const TriggerTrialDuration = ({
  currentProfile,
  showControllerForPlayer,
  contentDetail = {},
  isTrialDurationOverText = false,
  content,
  episodeData
}: any) => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const router = useVieRouter();
  const { pathname } = router || {};
  const isPathLiveTV =
    pathname === PAGE.LIVE_TV || pathname === PAGE.LIVE_TV_EPG || pathname === PAGE.LIVE_TV_SLUG;
  const isPathLiveStream = pathname?.includes(PAGE.LIVE_STREAM);

  const dispatch = useDispatch();
  const isForceLogin = contentDetail?.forceLogin === PERMISSION.FORCE_LOGIN;

  const trackingDataContent = useMemo(() => {
    return episodeData || content || contentDetail || {};
  }, [episodeData, content, contentDetail]);

  useEffect(() => {
    segmentRegistrationEventsPopupAuth({
      contentType: trackingDataContent?.type,
      contentId: trackingDataContent?.id,
      contentTitle: trackingDataContent?.title || content?.title,
      is30SecondsWatchTrial: !isTrialDurationOverText
    });
    return () => {
      segmentRegistrationEventsPopupAuth({ contentType: trackingDataContent?.type, cancel: true });
    };
  }, [contentDetail, content]);

  const handleAuth = useCallback(() => {
    localStorage.setItem('currentAuthFlow', 'registration_trigger');
    dispatch(
      openPopup({ name: POPUP.NAME.PLAYER_TRIGGER_AUTH, contentDetail, data: content, episodeData })
    );

    const isLiveTV =
      (trackingDataContent?.type === CONTENT_TYPE.LIVE_TV ||
        trackingDataContent?.type === CONTENT_TYPE.EPG) &&
      isPathLiveTV;
    const isLivestream = trackingDataContent?.type === CONTENT_TYPE.LIVESTREAM || isPathLiveStream;

    // Tracking
    localStorage.setItem(
      'currentAuthFlow',
      'registration_trigger'
      /*isLiveTV
        ? 'registration_for_livetv_coming_soon'
        : isLivestream
        ? 'registration_for_livestream_coming_soon'
        : 'registration_trigger'*/
    );
    trackingAuth({
      event: 'LOGIN_BUTTON_SELECTED',
      flowName: isLiveTV
        ? VALUE.REGISTRATION_FOR_LIVETV
        : isLivestream
        ? VALUE.REGISTRATION_FOR_LIVESTREAM
        : isTrialDurationOverText
        ? VALUE.REGISTRATION_FOR_VOD
        : VALUE.REGISTRATION_TRIAL,
      triggerFrom: isTrialDurationOverText
        ? VALUE.FORCE_LOGIN_NOTIFICATION
        : VALUE.FORCE_LOGIN_BUTTON,
      content: trackingDataContent,
      isAuto: false
    });
  }, [dispatch, contentDetail, content, isTrialDurationOverText]);

  const handleSubscriptionClick = () => {
    router.push(PAGE.PAYMENT);
    ConfigLocalStorage.set('PREVIOUS_URL', JSON.stringify(window.location.href) || '/');
    ConfigLocalStorage.set(LocalStorage.FROM_URL_TRIGGER_TRIAL, window.location.pathname);

    ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);

    trackingPayment.globalPaymentButtonSelected({
      contentId: episodeData?.id || content?.id || contentDetail?.id,
      contentTitle: episodeData?.title || content?.title || contentDetail?.title,
      flowName: VALUE.FORCE_LOGIN
    });
  };

  const handleLoginClick = useCallback(() => {
    handleAuth();
  }, [handleAuth]);

  const positionWithController = useMemo(
    () =>
      showControllerForPlayer
        ? 'bottom-[54px] md:bottom-6 lg:bottom-24'
        : 'bottom-2 md:bottom-6 lg:bottom-8',
    [showControllerForPlayer]
  );

  const containerClass = useMemo(
    () =>
      classNames(
        'flex flex-col items-center absolute space-y-3',
        !isTrialDurationOverText
          ? 'right-4 z-[6] transition-all duration-300'
          : 'w-full h-full p-3 md:p-8 justify-center items-center top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[3] bg-black/70',
        !isTrialDurationOverText && positionWithController
      ),
    [isTrialDurationOverText, positionWithController]
  );

  const renderTrialOverContent = () => (
    <div className="flex w-auto max-w-full items-center space-x-2 py-4 px-5 bg-vo-dark-gray-950/60 text-white text-[1rem] md:text-[1.125rem] rounded-lg">
      {isForceLogin ? TEXT.CONTENT_FORCE_LOGIN : TEXT.TRIAL_IS_OVER}
    </div>
  );

  return (
    <motion.div
      className={containerClass}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.25, ease: 'easeInOut', delay: 0.1 }}
    >
      {isTrialDurationOverText && renderTrialOverContent()}
      <div className="flex items-center space-x-2">
        <Button
          theme="primaryOutlineGlass"
          size={isMobile ? 'Md' : 'LgVariant'}
          customizeClass="min-w-[74px] md:min-w-[84px]"
          title={TEXT.SUBSCRIBE_PACKAGE}
          onClick={handleSubscriptionClick}
        />
        {!currentProfile?.id && (
          <Button
            theme="primarySolid"
            size={isMobile ? 'Md' : 'LgVariant'}
            customizeClass="min-w-[120px]"
            title={TEXT.LOGIN}
            onClick={handleLoginClick}
          />
        )}
      </div>
    </motion.div>
  );
};

export default React.memo(TriggerTrialDuration);
