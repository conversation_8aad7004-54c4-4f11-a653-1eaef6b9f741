import React from 'react';
import get from 'lodash/get';
import find from 'lodash/find';
import isEmpty from 'lodash/isEmpty';
import without from 'lodash/without';
import isEqual from 'lodash/isEqual';
import cookie from 'react-cookies';
import User<PERSON>pi from '@vieon/core/api/userApi';
import LiveStreamApi from '@vieon/core/api/LiveStream';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import LocalStorage from '@vieon/core/config/LocalStorage';
import ConfigCookie from '@vieon/core/config/ConfigCookie';
import ConfigSocket from '@vieon/core/config/ConfigSocket';
import { sentryCatError } from '@vieon/tracking/sentry';
import {
  ADS_URL,
  CONTENT_TYPE,
  EL_ID,
  ERROR_CODE,
  HTTP_CODE,
  KEY_CODE,
  PERMISSION,
  POPUP,
  POSITION,
  POSITION_ACTION,
  TOAST,
  USER_TYPE
} from '@vieon/core/constants/constants';
import {
  DRM,
  DRM_PROVIDER,
  ERROR_PLAYER,
  PLAYER_NAME,
  PLAYER_STATUS,
  PLAYER_TYPE
} from '@vieon/core/constants/player';
import { TEXT } from '@vieon/core/constants/text';
import {
  checkIsFullscreen,
  createTimeout,
  getDeviceId,
  onOpenPayment,
  parseSecond,
  removeGlobalPlayer,
  setGlobalPlayer
} from '@vieon/core/utils/common';
import dynamic from 'next/dynamic';
import WarningScreen from '../components/basic/Player/WarningScreen';
import { getInfoVideoCodec, setPlayerErrorLog } from '@vieon/core/services/playerServices';
import VideoIndexing from '../components/basic/Player/videoIndexing/VideoIndexing';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import ContentTags from '../components/basic/Player/PlayerTip/ContentTags';
import AdsSlot from '../components/basic/Player/ads/AdsSlot';
import { SIGMA_DRM_WINDOWS } from '@vieon/core/config/ConfigEnv';
import { isIOS, isMobile, isSafari } from 'react-device-detect';
import ContentRelated from '../components/livestream/ContentRelated';
import Image from '../components/basic/Image/Image';
import PlayerEpisodeListMobile from './PlayerControl/ControlBottom/Episode/PlayerEpisodeListMobile';
import CCUQnet from '../../liveTV/CCUQnet';
import Button from '../Buttons/Button';
import PlayerDraw from './PlayerDraw/PlayerDraw';
import PlayerNotification from './PlayerNotification';
import GetInfoDebugPlayer from './GetInfoDebugPlayer';
import ControlLiveBottom from './PlayerControl/ControlBottom/ControlLiveBottom';
import WaitingSpin from './WaitingSpin';
import Subtitles from './PlayerControl/Subtitles';
import SoftLogo from './SoftLogo/SoftLogo';
import PlayerPoster from '../PlayerPoster/PlayerPoster';
import PlayerControl from './PlayerControl/PlayerControl';
import SkipAds from './ads/SkipAds';
import { initAd } from './ads/Ads';
import PiPAds from '../components/OutstreamAds/PiPAds';
import styles from './InfoDebugPlayer.module.scss';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { getPopupTriggerDialog } from '@vieon/core/store/actions/popup';
import { connect } from 'react-redux';
import { getSession, removeSession, setSession } from '@vieon/core/config/ConfigSessionStorage';

declare const window: any;

const ShakaPlayerComponent = dynamic(() => import('./ShakaPlayerComponent'), {
  ssr: false
});

const MOUSE_MOVE = 5000;
const MAX_HOUR = 48; // 2 days

class Player extends React.PureComponent<any, any> {
  adContainer: any;
  adDisplayContainer: any;
  adEvent: any;
  adPoints: any;
  adsCount: any;
  adsLoader: any;
  adsManager: any;
  adsSlot: any;
  autoLoadLiveStream: any;
  autoLoadTimer: any;
  bufferTime: any;
  bufferingTime: any;
  dispatch: any;
  firstPlay: any;
  getNextEpisode: any;
  intervalSeekTV: any;
  isCanPlay: any;
  isDisconnected: any;
  isFirstInitSubtitleOnSafariMacOS: any;
  isSeeking: any;
  linkPlaysToRetry: any;
  liveTVCCU: any;
  loadSource: any;
  mouserMoveTimer: any;
  offVideoTimeout: any;
  onSkipAd: any;
  player: any;
  playerUnmoungted: any;
  playerUnmounted: any;
  requestAds: any;
  requestingAds: any;
  setAudio: any;
  setContinueProgress: any;
  setQuality: any;
  setSubtitle: any;
  subtitleRef: any;
  timeoutSetSubtitleOnSafari: any;
  timerAutoShowControler: any;
  totalPlayedData: any;
  video: any;
  videoContainer: any;
  videoContainerRef: any;
  videoRef: any;
  webkitDisplayingFullscreen: any;

  constructor(props: any) {
    super(props);
    this.dispatch = props.dispatch;
    this.state = {
      isDrm: false,
      isMaxCCU: false,
      isPaused: false,
      seekValue: 0,
      volumeValue: 100,
      durationText: '00:00',
      currentTimeText: '00:00',
      hoverTimeText: '00:00',
      displayControl: true,
      isMuted: false,
      isLoading: false,
      isAdPlay: false,
      isAdsEnd: false,
      isAdsPaused: false,
      selectSubName: 'off',
      playerReady: false,
      isOnEnded: false,
      playerName: '',
      isPremiereLive: true,
      dataSuccess: false,
      videoStalled: null,
      infoDebugPlayer: null,
      isShowInfoDebug: false,
      playerError: false,
      subtitleSelected: null,
      isOpenEpisodeMobile: false,
      isAdsEndRegistration: false,
      dataQuality: null,
      isDisplayInfoBox: false,
      showController: true,
      warningMessageState: true,
      behindLive: false,
      isCollapseVisible: true,
      isDisconnected: false,
      timeInDisconnected: 0,
      historyPath: undefined,
      typeShowAds: 0,
      hasCalledTrackingVipFeature: React.createRef(),
      isAdError: false,
      isShowEndScreenWithTrialContent: false,
      isPopupDismissed: false
    };
    this.intervalSeekTV = 0;
    this.bufferingTime = 0;
    this.totalPlayedData = [];
    this.video = null;
    this.videoContainerRef = React.createRef();
    this.subtitleRef = React.createRef();
    this.isFirstInitSubtitleOnSafariMacOS = false;
    this.timeoutSetSubtitleOnSafari = null;
    this.adContainer = null;
    this.isCanPlay = false;
    this.webkitDisplayingFullscreen = false;
    this.setContinueProgress = false;
    this.autoLoadLiveStream = false;
    this.autoLoadTimer = null;
    this.linkPlaysToRetry = [];
    this.requestingAds = false;
    this.adsCount = 0;
    this.adsSlot = 0;
    this.adPoints = {
      pre: null,
      mid: 0,
      post: null,
      currentAds: {
        urlKey: ADS_URL.URL1,
        ads: null
      }
    };
    this.adDisplayContainer = null;
    this.adsLoader = null;
    this.adsManager = null;
    this.player = null;
    this.liveTVCCU = new CCUQnet();

    this.firstPlay = false;
    this.isDisconnected = false;
  }

  componentDidMount() {
    this.video = this.videoRef?.current;
    this.videoContainer = this.videoContainerRef?.current;
    document.addEventListener('fullscreenchange', this.handleFullscreen, false);
    document.addEventListener('mozfullscreenchange', this.handleFullscreen, false);
    document.addEventListener('msfullscreenchange', this.handleFullscreen, false);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenIOS, false);
    document.addEventListener('keydown', this.handleKeydown, false);
    window.addEventListener('online', this.hanldeStatusConnected);
    window.addEventListener('offline', this.hanldeStatusDisconnected);
    this.handleLoadNewContent();
    if (this.props?.router?.asPath) {
      this.setState({ historyPath: this.props?.router?.asPath || undefined });
    }
    const dataTrial = this.props?.contentDetail || this.props?.detailChannel || {};
    const isHasMoreMinuteTrialContent = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    const isNonPremium =
      !dataTrial?.isPremiumTVod &&
      !dataTrial?.isPremium &&
      !dataTrial?.isPremiumPVod &&
      !this.props?.profile?.id;
    const isTrialDurationDisplay =
      (isNonPremium &&
        this.props?.permission === PERMISSION.NON_LOGIN &&
        !this.props?.isGlobal &&
        (isHasMoreMinuteTrialContent
          ? this.video?.currentTime > +isHasMoreMinuteTrialContent
          : this.video?.currentTime >= dataTrial?.triggerLoginDuration)) ||
      (dataTrial?.forceLogin === PERMISSION.FORCE_LOGIN &&
        !this.props?.profile?.id &&
        !dataTrial?.triggerLoginDuration);

    if (isTrialDurationDisplay) {
      this.setState({ isShowEndScreenWithTrialContent: true });
    }
    const searchParams = new URLSearchParams(window.location.search);
    const isTriggerAuth = searchParams.get('isTriggerAuth');

    if (isTriggerAuth === 'true') {
      searchParams.delete('isTriggerAuth');

      const currentPath = window.location.pathname;
      const hasHtml = currentPath.includes('.html');

      const basePath = hasHtml ? currentPath.split('.html')[0] + '.html' : currentPath;

      const newSearch = searchParams.toString();
      const newUrl = newSearch ? `${basePath}?${newSearch}` : basePath;

      window.history.replaceState(null, '', newUrl);
      this.props?.setToast({
        message: TEXT.TRIGGER_AUTH_LOGIN_SUCCESS
      });
    }
  }

  componentDidUpdate(prevProps: any, prevState: any) {
    const {
      router,
      isIntro,
      isSafari,
      isIOS,
      contentId,
      socketConnected,
      blockPlayer,
      handlePlayerReady,
      tokenAnonymous,
      deviceId,
      popupName,
      contentDetail,
      detailChannel,
      profile
    } = this.props || {};
    const { playerReady, isFullscreen, isAdsEnd, isOnEnded, isCollapseVisible } = this.state || {};
    const { asPath } = router || {};
    if (asPath && asPath !== prevProps?.router?.asPath) {
      this.setState({ isOnEnded: false });
    }
    if (isIntro !== prevProps.isIntro && !isIntro && (isSafari || isIOS)) {
      this.handleSubtitleOnSafariWhenSeekProgress();
    }
    if (this.props.notVideo !== prevProps.notVideo) {
      this.video = this.props.notVideo ? null : this.videoRef?.current;
    }
    if (contentId !== prevProps?.contentId) {
      this.setState({ isAdsEnd: false, isAdError: false });
      this.prepareInitAds();
    }
    if (
      contentId !== prevProps?.contentId ||
      socketConnected !== prevProps.socketConnected ||
      blockPlayer !== prevProps?.blockPlayer ||
      deviceId !== prevProps?.deviceId ||
      tokenAnonymous !== prevProps?.tokenAnonymous
    ) {
      this.autoLoadLiveStream = false;
      this.handleLoadNewContent();
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
    }

    if (playerReady !== prevState.playerReady && typeof handlePlayerReady === 'function') {
      handlePlayerReady(playerReady);
    }
    if (isIOS && !checkIsFullscreen() && this.video) {
      try {
        this.video.playsInline = true;
        this.video.controls = false;
      } catch (error) {
        console.log(error);
      }
      if (isFullscreen) {
        this.setState({ isFullscreen: false });
      }
      this.webkitDisplayingFullscreen = this.video.webkitDisplayingFullscreen;
    }

    if (prevProps.popupName === POPUP.NAME.PLAYER_TRIGGER_AUTH) {
      if (this.video) {
        this.video.pause();
        this.setState({ isPaused: true, isPopupDismissed: true });
      }
    }

    const currentTime = this.video?.currentTime || 0;
    const isGlobal = this.props?.isGlobal || false;
    const dataTrial = contentDetail || detailChannel || {};
    const isHasMoreMinuteTrialContent = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    const isNonPremiumNonLogin =
      !dataTrial?.isPremiumTVod &&
      !dataTrial?.isPremium &&
      !dataTrial?.isPremiumPVod &&
      dataTrial?.permission === PERMISSION.NON_LOGIN &&
      !this.props?.profile?.id &&
      !isGlobal;

    const isHasTrialContentForceLogin =
      dataTrial?.forceLogin === PERMISSION.FORCE_LOGIN &&
      !profile?.id &&
      !dataTrial?.triggerLoginDuration;

    const hasReachedTrialEnd =
      !!dataTrial?.triggerLoginDuration &&
      (isHasMoreMinuteTrialContent
        ? currentTime > +isHasMoreMinuteTrialContent
        : currentTime > dataTrial?.triggerLoginDuration);

    const isTrialDurationDisplay =
      isNonPremiumNonLogin &&
      (hasReachedTrialEnd || this.state.isOnEnded || isHasTrialContentForceLogin);
    if (isTrialDurationDisplay) {
      this.setState({ isShowEndScreenWithTrialContent: true });
      if (hasReachedTrialEnd) {
        if (this.video) this.video?.pause();
        this.setState({
          isPaused: true
        });
      }
    } else {
      this.setState({ isShowEndScreenWithTrialContent: false });
    }

    if (
      (isAdsEnd && document.getElementById('MODAL')) ||
      this.props?.popupName === POPUP.NAME.KID_LIMITED_CONTENT_DIALOG ||
      this.props?.popupName === POPUP.NAME.PLAYER_TRIGGER_AUTH
    ) {
      if (this.video) this.video?.pause();
      this.setState({
        isPaused: true
      });
    }
    if (
      typeof this.props.handleBehindLive === 'function' &&
      this.state?.behindLive !== prevState.behindLive
    ) {
      this.props?.handleBehindLive(this.state?.behindLive);
    }
    if (
      this.props.statusLiveEvent !== prevProps?.statusLiveEvent &&
      this.props.statusLiveEvent === 2
    ) {
      if (this.video) this.video?.pause();
      this.setState({
        isPaused: true
      });
    }
    if (!isCollapseVisible && isOnEnded && isOnEnded !== prevState.isOnEnded) {
      this.setState({ isCollapseVisible: true });
    }
    if (popupName === POPUP.NAME.POPUP_RATING && this.video && this.video.paused) {
      this.setState({
        isPaused: true
      });
    }

    if (popupName === POPUP.NAME.TRIGGER_FIRSTPAY) {
      if (this.video) this.video?.pause();
      this.setState({
        isPaused: true
      });
    }

    if (
      !isEmpty(prevState.historyPath) &&
      router?.asPath !== prevState.historyPath &&
      !navigator.onLine
    ) {
      this.props.router.reload();
    }
    const triggerSource = this.props?.contentDetail || this.props?.detailChannel;

    if (triggerSource) {
      const isContentDetail = !!this.props?.contentDetail;

      const isTriggerLogin =
        triggerSource.permission === PERMISSION.NON_LOGIN &&
        (!isContentDetail ||
          (triggerSource.triggerLoginDuration > 0 &&
            this.video?.currentTime <= triggerSource.triggerLoginDuration));

      if (isTriggerLogin) {
        ConfigLocalStorage.set(
          LocalStorage.RE_LOGIN_PARAMS,
          JSON.stringify({
            contentData: triggerSource,
            pathname: router?.pathname,
            url: `${router?.asPath}?isTriggerAuth=true&destination=${router?.asPath}`
          })
        );
      }
    }
  }

  componentWillUnmount() {
    clearInterval(this.intervalSeekTV);
    if (this.offVideoTimeout) {
      clearTimeout(this.offVideoTimeout);
    }
    const isHasMoreMinuteTrialContent = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    if (isHasMoreMinuteTrialContent) {
      removeSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT);
    }

    document.removeEventListener('keyup', this.handleKeydown, false);
    document.removeEventListener('fullscreenchange', this.handleFullscreen, false);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreen, false);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreen, false);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenIOS, false);

    window.removeEventListener('online', this.hanldeStatusConnected);
    window.removeEventListener('offline', this.hanldeStatusDisconnected);
    if (this.props.isIOS && this.video) this.video.src = '';
    this.playerUnmounted = true;
    this.liveTVCCU.onEnd();
    if (this.autoLoadTimer) clearTimeout(this.autoLoadTimer);
  }

  openErrorPopup = (errorData: any) => {
    console.log('openErrorPopup', errorData);
    const { handleOpenCatError } = this.props || {};
    const retryAction = this.handleRetry;
    if (typeof handleOpenCatError === 'function') {
      handleOpenCatError({
        retryAction,
        segmentParams: this.props,
        errorData
      });
    }
  };

  handleFullscreen = (e: any) => {
    if (this.playerUnmounted) return;
    const fullscreen = !!checkIsFullscreen() || false;
    const { onFullscreenChange, handleStatusFullscreenOfPlayer } = this.props || {};
    if (typeof onFullscreenChange === 'function') onFullscreenChange(e);
    if (typeof handleStatusFullscreenOfPlayer === 'function') {
      handleStatusFullscreenOfPlayer(fullscreen);
    }
    this.setState({ isFullscreen: fullscreen });
  };

  hanldeStatusConnected = () => {
    TrackingApp.offlineDetect({
      currentPage: 'video_player',
      contentId: this.props?.contentId,
      contentTitle: this.props?.playerTitle,
      contentType: this.props?.typePlayer || null
    });
    const { setToast, popupData } = this.props;

    setToast({
      message: TEXT.ONLINE,
      kind: TOAST.KIND.WARNING,
      position: POSITION.TOP_RIGHT
    });

    if (popupData?.dataError) {
      sentryCatError(popupData.dataError);
      this.handleAutoRetry(this.video, popupData.dataError);
    }
    this.onPlayPause();
    this.video.currentTime = this.state.timeInDisconnected;

    this.setState({
      isDisconnected: false
    });

    if (this.offVideoTimeout) {
      clearTimeout(this.offVideoTimeout);
    }
  };

  hanldeStatusDisconnected = async () => {
    let contentTitle = '';
    switch (this.props?.typePlayer) {
      case 'livetv':
        contentTitle = this.props?.detailChannel?.title;
        break;
      case 'livestream':
        contentTitle = this.props?.playerTitle;
        break;
      default:
        contentTitle = this.props?.playerSubTitle || this.props?.playerTitle;
        break;
    }
    const dataTrackingOfflineContent = {
      currentPage: 'video_player',
      contentId: this.props?.contentId,
      contentTitle: contentTitle,
      contentType: this.props?.typePlayer || null
    };
    const cache = await caches.open('offline-cache-v1');
    await cache.put('/tracking-data', new Response(JSON.stringify(dataTrackingOfflineContent)));
    const { setToast } = this.props;
    setToast({
      message: (
        <p>
          Mất kết nối mạng!
          <br />
          Vui lòng kiểm tra đường truyền
        </p>
      ),
      kind: TOAST.KIND.WARNING,
      position: POSITION.TOP_RIGHT
    });

    if (!this.video) {
      return;
    }

    const bufferedRanges = this.video.buffered;

    let waitingTime = Math.max(bufferedRanges.end(0) - bufferedRanges.start(0), 0) * 1000;

    if (waitingTime === 0) {
      console.warn('No buffered data available for the current playback position.');
    }

    if (this.offVideoTimeout) {
      clearTimeout(this.offVideoTimeout);
    }

    this.offVideoTimeout = setTimeout(() => {
      this.setState({ isDisconnected: true });
      this.onPlayPause();
      this.setState({ timeInDisconnected: this.video.currentTime });
    }, waitingTime);
  };

  handleFullscreenIOS = () => {
    const fullscreen = !!checkIsFullscreen() || false;
    if (this.props.isIOS) {
      if (!fullscreen) {
        try {
          this.video.playsInline = true;
          this.video.controls = false;
        } catch (error) {
          console.log(error);
        }
      }
    }
    this.setState({ isFullscreen: fullscreen });
    if (typeof this.props.handleStatusFullscreenOfPlayer === 'function') {
      this.props.handleStatusFullscreenOfPlayer(fullscreen);
    }
  };

  onPlaying = (e: any) => {
    const { isIOS, isPremiere, isConvertMWebToApp, isSafari, onPlaying } = this.props || {};
    const { isMainLinkStarted, isAdPlay, subtitleSelected } = this.state || {};
    if (this.bufferingTime > 0) this.onBufferEnded();
    this.isSeeking = false;

    if (isConvertMWebToApp) {
      return this.setState({
        isPaused: true,
        isLoading: false,
        playerReady: false,
        isOnEnded: false
      });
    }

    if (isIOS && isPremiere && isMainLinkStarted && !isAdPlay) {
      const { duration } = this.video;
      // TODO Shaka + dash of livestream
      if (duration > 0 && duration !== Number.POSITIVE_INFINITY) {
        console.log('fail api');
        this.loadFail();
        this.video.muted = true;
        this.video.pause();
        return;
      }
    }
    if (isIOS) {
      const subtitle = isEmpty(subtitleSelected)
        ? find(get(this.props, 'settingData.subtitles', []), ['isDefault', true])
        : subtitleSelected;
      if (!isEmpty(subtitle)) {
        const codeName = get(subtitle, 'codeName', 'off');
        const videoTag: any = document.getElementById(EL_ID.VIE_PLAYER);
        const tracks = videoTag?.textTracks || [];
        const codeNameShowing = get(find(tracks, ['mode', 'showing']), 'language', 'off');
        if (codeNameShowing !== codeName) {
          this.onChangeSubtitleOnIOS(codeName);
        }
      }
    } else if (isSafari && this.isFirstInitSubtitleOnSafariMacOS) {
      this.onSetSubtitle(subtitleSelected, true);
    }

    if (typeof onPlaying === 'function') onPlaying(e);
    this.setState({
      isPaused: false,
      isLoading: false,
      playerReady: true,
      isOnEnded: false
    });
    this.updateTotalPlayedData({ status: PLAYER_STATUS.PLAYING });
  };

  onPaused = (e: any) => {
    const { onPaused } = this.props || {};
    if (typeof onPaused === 'function') onPaused(e);
    this.updateTotalPlayedData({ status: PLAYER_STATUS.PAUSED });
  };

  onEnded = (e: any) => {
    const {
      contentDetail,
      isLiveStream,
      isPremiere,
      openPopup,
      onEnded,
      handleEndSessionPlay,
      concurrentScreen,
      dataRefreshSession,
      isGlobal
    } = this.props || {};
    const { isAdPlay } = this.state || {};
    const { triggerLoginDuration, permission } = contentDetail || {};
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (isAdPlay) return;
    if (isLiveStream && isPremiere) {
      this.loadFail();
      return;
    }
    const moreMinuteTrialAfterAds = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    if (
      permission === PERMISSION.NON_LOGIN &&
      triggerLoginDuration > 0 &&
      !isGlobal &&
      (moreMinuteTrialAfterAds
        ? this.video?.currentTime >= +moreMinuteTrialAfterAds
        : this.video?.currentTime >= triggerLoginDuration)
    ) {
      if (typeof openPopup === 'function') {
        openPopup({
          name: POPUP.NAME.PLAYER_TRIGGER_AUTH,
          noControl: true,
          isEndTimeWatchTrial: true,
          data: this.props?.content,
          contentDetail: this.props?.contentDetail,
          episodeData: this.props?.currentEpisode,
          triggerFrom: 'force_login_notification'
        });
      }
      this.setState({ isPaused: true });
      return;
    }
    if (sessionToken) {
      handleEndSessionPlay(sessionToken);
    }
    // SET END SCREEN
    this.setState({
      isOnEnded: true,
      isPaused: true,
      isLoading: false
    });
    if (typeof openPopup === 'function') onEnded(e);
    if (this.liveTVCCU) {
      this.liveTVCCU.onEnd();
    }
  };

  onWaiting = () => {
    this.updateTotalPlayedData({ status: PLAYER_STATUS.WAITING });
    this.onBuffering();
  };

  onBuffering = () => {
    const { onBuffering } = this.props || {};
    this.bufferingTime = new Date().getTime();
    if (typeof onBuffering === 'function') onBuffering();
  };

  onBufferEnded = () => {
    const bufferingTime = new Date().getTime() - this.bufferingTime;
    const { onBufferEnded } = this.props || {};
    if (typeof onBufferEnded === 'function') {
      onBufferEnded({
        bufferingTime,
        isSeeking: this.isSeeking
      });
    }
    this.bufferingTime = 0;
  };

  updateTotalPlayedData = ({ status }: any) => {
    const timestamp = Math.floor(new Date().getTime() / 1000);
    const { updateTotalPlayedData } = this.props || {};
    if (status === PLAYER_STATUS.CLEAR) {
      this.totalPlayedData = [];
    } else {
      this.totalPlayedData.push({
        status,
        timestamp
      });
    }
    if (typeof updateTotalPlayedData === 'function') {
      updateTotalPlayedData(this.totalPlayedData);
    }
  };

  onError = (e?: any) => {
    /* const categoryOfError = e?.detail?.category
      if (categoryOfError === ERROR_PLAYER.CATEGORY.DRM && this.props.drmProvider === DRM_PROVIDER.SIGMA_DRM) {
        // TODO: Handle DRM error and switch to non-drm
      } */

    this.openErrorPopup({
      playerData: e,
      errorType: ERROR_PLAYER.TYPE.PLAYER
    });
    this.setState({
      playerError: true,
      playerReady: false
    });
    const { onError } = this.props || {};
    const { playerName, linkPlay } = this.state || {};
    const streamingProtocol = linkPlay
      ? linkPlay.includes('.mpd')
        ? VALUE.DASH
        : VALUE.HLS
      : null;
    const getInfoTrack = getInfoVideoCodec({
      player: this.player,
      playerName
    });
    if (typeof onError === 'function') {
      onError({
        event: e,
        playerName,
        linkPlay,
        streamingProtocol,
        getInfoTrack
      });
    }
  };

  handleLoadNewContent = (isNotLoadNew?: any) => {
    setPlayerErrorLog({ isReset: true });
    if (this.intervalSeekTV) clearInterval(this.intervalSeekTV);
    if (this.subtitleRef.current) {
      this.subtitleRef.current.innerHTML = '';
    }
    this.setContinueProgress = false;
    this.isCanPlay = false;
    const {
      isAutoLoadMainLink,
      isLiveStream,
      permission,
      timeStartLiveStream,
      trailerLinkPlay,
      profile,
      deviceId,
      blockPlayer,
      drmProvider,
      drmMerchant,
      isGlobal
    } = this.props;

    const fullscreen = !!checkIsFullscreen() || false;
    this.setState(
      {
        isOnEnded: false,
        subtitleSelected: null,
        initPlayer: false,
        playerReady: false,
        playerError: false,
        playingAds: false,
        isLoading: false,
        warningMessageState: true,
        isMainLinkStarted: !!isNotLoadNew, // if not load new, isMain = true to open logic onPlayPause
        isPaused: !this.props.linkPlay,
        playerName: '',
        seekValue: 0,
        durationText: '00:00',
        currentTimeText: '00:00',
        hoverTimeText: '00:00',
        isAdError: false
      },
      () => {
        if (blockPlayer && fullscreen) document.exitFullscreen();
        if (isNotLoadNew || blockPlayer) {
          return;
        }

        this.handleSeekBar({
          currentTime: 0,
          duration: 100
        });

        this.handleAutoShowEpisode();
        if (this.autoLoadTimer) clearTimeout(this.autoLoadTimer);
        if (isAutoLoadMainLink && isLiveStream && !this.autoLoadLiveStream) {
          const userId = profile?.id || `anonymous_${deviceId || getDeviceId()}`;
          const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
          const tokenAnonymous = cookie.load(ConfigCookie.KEY.ANONYMOUS_TOKEN);
          const drmInfo =
            drmProvider === DRM_PROVIDER.SIGMA_DRM
              ? {
                  userId,
                  sessionId: accessToken || tokenAnonymous || this.props?.tokenAnonymous,
                  merchantId: drmMerchant,
                  appId: DRM.APP_ID
                }
              : {
                  userId,
                  sessionId: accessToken || tokenAnonymous || this.props?.tokenAnonymous,
                  merchant: drmMerchant
                };
          this.setState({
            playerName: PLAYER_NAME.SHAKA_PLAYER,
            linkPlay: trailerLinkPlay?.hls,
            drmInfo,
            initPlayer: permission === PERMISSION.CAN_WATCH || isLiveStream
          });
          const timeStart = timeStartLiveStream;
          const currentTime = Math.floor(new Date().getTime());
          const timeLive = timeStart * 1000 - currentTime;
          this.autoLoadLiveStream = true;
          this.autoLoadTimer = createTimeout(async () => {
            if (isGlobal && !profile?.isPremium) {
              this.setCancelPlayer();
              if (this.props.openModalPayment) this.props.openModalPayment();
              this.setState({ isMainLinkStarted: true });
            } else if (permission === PERMISSION.CAN_WATCH) {
              this.handleLoadNewContent();
            } else if (this.props.handleCheckTVodLivestream) {
              this.handleLoadNewContent(true);
              this.props.handleCheckTVodLivestream();
            }
            if (this.props.autoLoadLiveStream) this.props.autoLoadLiveStream();
          }, timeLive);
        } else {
          this.onLoadPlayer();
        }
      }
    );
  };

  setPlayer = ({
    video,
    player,
    setQuality,
    setAudio,
    setSubtitle,
    removePlayer,
    loadSource
  }: any) => {
    this.video = video || {};
    this.player = player || {};
    this.setQuality = setQuality;
    this.setAudio = setAudio;
    this.setSubtitle = setSubtitle;
    this.loadSource = loadSource;
    const { setupPlayer, isLiveTV, isLiveStream, contentDetail } = this.props || {};
    const { isDVR, progress } = contentDetail || {};
    const { playerName } = this.state || {};
    let playerVolume: any = ConfigLocalStorage.get(LocalStorage.VOLUME);
    const isLive = isLiveTV || isLiveStream;
    if (this.video && playerVolume) {
      playerVolume = Number.parseFloat(playerVolume) > 1 ? 1 : Number.parseFloat(playerVolume);
      this.video.volume = playerVolume;
      this.setState({
        volumeValue: playerVolume * 100,
        isMuted: false
      });
    }
    if (typeof setupPlayer === 'function') {
      setupPlayer({
        video: this.video,
        videoContainer: this.videoContainer,
        removePlayer,
        setCancelPlayer: this.setCancelPlayer,
        liveTVCCU: this.liveTVCCU,
        player,
        playerName
      });
    }
    if (!isLive && !isDVR) {
      setGlobalPlayer({
        id: PLAYER_TYPE.VOD,
        player: video
      });
    }
    if (this.video) {
      this.video.addEventListener('loadedmetadata', () => {
        if (!isDVR && !isLiveTV && progress > 0 && !this.setContinueProgress) {
          this.setContinueProgress = true;
          this.isSeeking = true;
          this.video.currentTime = progress;
        }
      });
      window.video = this.video;
    }
  };

  setCancelPlayer = () => {
    this.setPlayerReady(false);
    this.setState({ isPaused: true });
    if (this.video) this.video.pause();
    this.video.muted = true;
    if (this.player) this.player.destroy();
  };

  handleKeydown = (e: any) => {
    if (this.playerUnmoungted || !this.video) return;
    const { permission, trialDuration, popupName, isLiveStream } = this.props;
    const { isFullscreen } = this.state || {};
    switch (e?.keyCode) {
      case KEY_CODE.ARROW_RIGHT: {
        // if (isLiveStream && !isPremiere) return;
        if (this.video.currentTime === this.video.duration || this.onHandleConcurrentScreen()) {
          return;
        }
        const seekValue = this.video.currentTime + 10;
        this.checkSeekAds({
          currentTime: this.video.currentTime,
          seekValue
        });
        this.isSeeking = true;
        this.video.currentTime = seekValue;
        break;
      }
      case KEY_CODE.ARROW_LEFT:
        if (
          (permission !== PERMISSION.CAN_WATCH &&
            trialDuration > 0 &&
            this.video.currentTime === this.video.duration &&
            popupName) ||
          this.onHandleConcurrentScreen()
        ) {
          return;
        }
        // if ((isLiveStream && !isPremiere) || this.video.currentTime === 0) return;
        this.video.currentTime = this.video.currentTime > 10 ? this.video.currentTime - 10 : 0;
        this.isSeeking = true;
        break;
      case KEY_CODE.ESC:
        if (isFullscreen) {
          this.setState({ isFullscreen: false });
        }
        break;
      case KEY_CODE.SPACE:
        if (
          document?.activeElement?.id === EL_ID?.DETAIL_COMMENT ||
          document?.activeElement?.id === EL_ID?.SEARCH_INPUT ||
          isLiveStream ||
          document?.activeElement?.localName === 'textarea' ||
          document?.activeElement?.localName === 'input'
        ) {
          return;
        }
        e.preventDefault();
        // if (isLive || (isLiveStream && isPremiere)) return;
        this.onPlayPause();
        break;
      default:
        break;
    }
  };

  checkLoadPlayer = () => {
    const { permission, trialDuration, isConvertMWebToApp } = this.props;
    const { triggerLoginDuration } = this.props?.contentDetail || {};
    if (
      permission !== PERMISSION.CAN_WATCH &&
      (trialDuration === 0 || triggerLoginDuration === 0)
    ) {
      this.setState({ playerReady: false });
    }
    if (permission === PERMISSION.CAN_WATCH && isConvertMWebToApp) {
      this.setState({
        playerReady: false,
        isPaused: true
      });
    }
  };

  onLoadPlayer = () => {
    this.autoLoadLiveStream = false;
    this.updateTotalPlayedData({ status: PLAYER_STATUS.CLEAR });
    this.totalPlayedData = [];
    this.bufferTime = new Date().getTime();
    const {
      profile,
      permission,
      trialDuration,
      drmServiceName,
      deviceId,
      isLiveStream,
      socketConnected,
      drmProvider,
      drmMerchant
    } = this.props;
    this.firstPlay = false;
    const { triggerLoginDuration } = this.props?.contentDetail || {};
    if (drmServiceName === DRM.K_PLUS && !socketConnected) return;
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    const tokenAnonymous = cookie.load(ConfigCookie.KEY.ANONYMOUS_TOKEN);
    this.checkLoadPlayer();
    const { linkPlay, isDrm } = this.props;
    const userId = profile?.id || `anonymous_${deviceId || getDeviceId()}`;
    const drmInfo =
      drmProvider === DRM_PROVIDER.SIGMA_DRM
        ? {
            userId,
            sessionId: accessToken || tokenAnonymous || this.props?.tokenAnonymous,
            merchantId: drmMerchant,
            appId: DRM.APP_ID
          }
        : {
            userId,
            sessionId: accessToken || tokenAnonymous || this.props?.tokenAnonymous,
            merchant: drmMerchant
          };
    if (permission !== PERMISSION.CAN_WATCH) {
      if (!trialDuration || trialDuration <= 0) removeGlobalPlayer({ id: PLAYER_TYPE.VOD });
    }
    const playerName = PLAYER_NAME.SHAKA_PLAYER;
    this.linkPlaysToRetry = without(get(this.props, 'linkPlaysToRetry', []), linkPlay);

    this.setState({
      isDrm,
      playerName,
      linkPlay,
      drmInfo,
      initPlayer:
        permission === PERMISSION.CAN_WATCH ||
        (permission !== PERMISSION.CAN_WATCH &&
          (trialDuration > 0 || triggerLoginDuration > 0) &&
          !isLiveStream),
      isPaused: linkPlay ? permission !== PERMISSION.CAN_WATCH : true,
      isLiveTV: this.props.isLiveTV,
      playerReady: false,
      isMainLinkStarted: true
    });
  };

  loadSuccess = () => {
    let isPaused = false;
    if (this.props.isMobile && this.video?.paused) isPaused = true;
    this.setInitPlay();
    this.setState({
      playerError: false,
      isPaused
    });
  };

  levelLoaded = (event: any, data: any) => {
    if (this.props.isPremiere && this.state.isMainLinkStarted && !data?.details?.live) {
      this.loadFail();
      if (this.video) this.video.pause();
    }
  };

  onStalled = () => {
    if (this.props.isIOS) {
      if (this.props.isLiveStream && this.video) {
        this.video.load(); // Reload player if turn off signal or stalled too long
      }
    }
  };

  onResetStateVideoStalled = () => {
    if (!isEmpty(this.state.videoStalled)) {
      this.setState({ videoStalled: null });
    }
  };

  handleAutoRetry = async (e?: any, errorData?: any) => {
    const { isEndStream, isLiveStream, openModalEndStreamTVod, contentId } = this.props || {};
    const linkPlayUseRetry = this.linkPlaysToRetry.shift();
    let streamStatus = null;
    let livestreamEnded = false;
    if (linkPlayUseRetry && isLiveStream && !isEndStream) {
      streamStatus = await LiveStreamApi.checkStatus(contentId);
      if (streamStatus?.status === 2) livestreamEnded = true;
    }
    if (livestreamEnded) {
      if (typeof openModalEndStreamTVod === 'function') openModalEndStreamTVod();
    } else if (!linkPlayUseRetry) {
      this.onError(e);
    } else if (linkPlayUseRetry) {
      this.setState(
        {
          isOnEnded: false,
          initPlayer: false,
          playerReady: false,
          playerError: false,
          playingAds: false,
          isLoading: true,
          isPaused: false,
          durationText: '00:00',
          currentTimeText: '00:00',
          hoverTimeText: '00:00',
          linkPlay: ''
        },
        () => {
          setTimeout(() => {
            this.setState({
              linkPlay: linkPlayUseRetry,
              initPlayer: true
            });
          }, 1000);
        }
      );
    }
  };

  loadFail = async (e?: any, errorData?: any) => {
    if (navigator.onLine) {
      await this.handleAutoRetry(e, errorData);
    } else {
      this.setState({ isDisconnected: true });
      this.handleAutoRetry(e, errorData);
    }
  };

  handleAutoShowEpisode = () => {
    this.setState({ displayControl: true });
    if (this.timerAutoShowControler) clearTimeout(this.timerAutoShowControler);
    createTimeout(() => {
      if (this.playerUnmounted) return;
      this.setState({ displayControl: false });
    }, 6000);
  };

  prepareInitAds = () => {
    this.setState({ isAdPlay: false, isAdError: false });
    this.adPoints = {
      pre: null,
      mid: [],
      post: null
    };
    if (this.adsManager) {
      this.adsManager.pause();
      this.adsManager.stop();
      this.adsManager.destroy();
    }
  };

  setPlayerReady = (playerReady: any) => {
    this.setState({ playerReady });
  };

  setPlayerError = (playerError: any) => {
    this.setState({ playerError });
  };

  setInitPlay = () => {
    if (this.subtitleRef.current) {
      this.subtitleRef.current.innerHTML = '';
    }
    if (!this.video) return;
    const { ads, isSafari, isIOS, setInitPlay } = this.props || {};
    const { isMuted } = this.state || {};
    this.continueProgress();
    const preAds = (ads || []).find((ad: any) => ad?.type === 'pre');
    if (!preAds || this.adPoints.pre || this.video.paused) {
      this.setAutoPlay({ isPre: true });
    }
    this.video.muted = !!isMuted;
    this.onSetAudio();
    if (isSafari && !isIOS) {
      this.isFirstInitSubtitleOnSafariMacOS = true;
    }
    this.onSetSubtitle();
    const bufferTime = new Date().getTime() - this.bufferTime;
    if (typeof setInitPlay === 'function') setInitPlay({ bufferTime });
    this.bufferingTime = 0;
  };

  setAutoPlay = (params?: any) => {
    const { isPre } = params || {};
    this.setState({
      isLoading: true,
      isPaused: false
    });
    const playPromise = this.video.play();
    this.video.muted = !!this.state.isMuted;
    if (isPre && this.props.isDVR) this.continueProgress();
    if (playPromise !== undefined) {
      const { isLiveTV, isSeekAllow, activeEpg, epg, isDVR } = this.props || {};
      const { isLive, isComingSoon, isCatchUp } = activeEpg || {};
      const isCatchUpContent = epg && isCatchUp && !isComingSoon;
      return playPromise
        .then(() => {
          // Automatic playback started!
          // Show playing UI.
          if (isPre) this.continueProgress();
          this.setState({ isLoading: false });
          if (
            (isLiveTV && isSeekAllow && (!isCatchUpContent || (isCatchUpContent && isLive))) ||
            isDVR
          ) {
            this.video.currentTime = this.player.seekRange().end;
            this.setState({ seekValue: 100 });
            this.handleTimerSeekLiveTV();
          }
          return true;
        })
        .catch((err: any) => {
          if (err) {
            // Auto-play was prevented
            // Show paused UI.
            console.log('Auto-play was prevented');
            if (
              (isLiveTV && isSeekAllow && (!isCatchUpContent || (isCatchUpContent && isLive))) ||
              isDVR
            ) {
              this.video.currentTime = this.player.seekRange().end;
              this.setState({ seekValue: 100 });
            }
            this.setState({
              isLoading: false,
              isPaused: true
            });
          }
          return false;
        });
    }
  };

  handleTimerSeekLiveTV = () => {
    if (this.intervalSeekTV) clearInterval(this.intervalSeekTV);
    const { isLiveTV, isSeekAllow, activeEpg, epg, isDVR, isPremiere } = this.props || {};
    const { isLive, isComingSoon, isCatchUp } = activeEpg || {};
    const isCatchUpContent = epg && isCatchUp && !isComingSoon;
    if (
      (isLiveTV && isSeekAllow && (!isCatchUpContent || (isLive && isCatchUpContent))) ||
      isDVR ||
      isPremiere
    ) {
      this.intervalSeekTV = setInterval(() => {
        this.onTimeUpdateSeekLiveTV();
      }, 1000);
    }
  };

  continueProgress = () => {
    const { contentDetail } = this.props;
    const { isDVR, progress } = contentDetail || {};
    if (!isDVR && progress > 0 && this.video) {
      this.isSeeking = true;

      this.video.currentTime = progress;
    }
    // TODO: [Forest] to do check
    /*  else if (this.props.isDVR) {
                                                                                  const { currentTime } = this.video;
                                                                                  let { duration } = this.video;

                                                                                  try {
                                                                                    if (duration === Infinity && typeof this.video.seekable === 'function') {
                                                                                      duration = this.video.seekable.end(0);
                                                                                    } else if (
                                                                                      typeof this.player?.isLive === 'function' &&
                                                                                      this.player.isLive() &&
                                                                                      typeof this.player.seekRange === 'function'
                                                                                    ) {
                                                                                      duration = this.player.seekRange()?.end;
                                                                                    }
                                                                                  } catch (e) {
                                                                                    console.log(e);
                                                                                  }

                                                                                  if (duration > 0 && duration > currentTime) {
                                                                                    this.isSeeking = true;

                                                                                    this.video.currentTime = duration - 8;
                                                                                  }
                                                                                } */
  };

  onLiveButton = () => {
    const { isLiveTV, isPaused } = this.state;
    const { isDVR } = this.props;
    if ((isLiveTV || isDVR) && this.video && this.player) {
      this.video.currentTime = this.player.seekRange()?.end;
      if (isPaused) this.video.play();
      this.setState({ behindLive: false });
    }
  };

  onPlayPause = (key?: any) => {
    const {
      preventPauseLive,
      onControlPlay,
      permission,
      trialDuration,
      cancelPlayer,
      isLiveStream,
      contentDetail,
      content,
      isLiveTV,
      openPopup,
      isConvertMWebToApp,
      onOpenPopupFreeTrial,
      isDVR,
      isGlobal,
      profile
    } = this.props || {};
    const { isMovieTrialInApp } = contentDetail || {};
    const { isMaxCCU, playerError, isOnEnded, isMainLinkStarted, isAdPlay, linkPlay } =
      this.state || {};
    if (playerError) {
      if (isLiveStream) {
        this.handleRetry();
        return;
      }
    }

    if (isGlobal && !profile?.isPremium && isMainLinkStarted) {
      // only for livestream
      if (this.props.openModalPayment) {
        this.props.openModalPayment();
        return;
      }
    }

    if (isMaxCCU || cancelPlayer?.action === ConfigSocket.EVENTS.CANCEL_PLAYER) {
      this.handleRetry();
      this.setState({
        isMaxCCU: false,
        initPlayer: false
      });
    }
    const isPaused = this.video?.paused;
    if (key !== 'NOT_CONTROL' && onControlPlay) {
      if (permission !== PERMISSION.CAN_WATCH && trialDuration === 0) {
        this.setState({ isFullscreen: false });
      }
      if (!isLiveStream || (isLiveStream && isMainLinkStarted)) {
        onControlPlay({ playerError });
      }
    }
    if (this.video) {
      const { triggerLoginDuration } = contentDetail || {};
      const moreMinuteTrialAfterAds = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
      if (
        permission === PERMISSION.NON_LOGIN &&
        triggerLoginDuration > 0 &&
        (moreMinuteTrialAfterAds
          ? this.video?.currentTime >= +moreMinuteTrialAfterAds
          : this.video.currentTime >= triggerLoginDuration) &&
        typeof openPopup === 'function'
      ) {
        openPopup({
          name: isGlobal
            ? profile?.id
              ? POPUP.NAME.USER_VOD_TRIAL_GLOBAL
              : POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL
            : POPUP.NAME.PLAYER_TRIGGER_AUTH,
          noControl: true,
          isEndTimeWatchTrial: true,
          data: this.props?.content,
          contentDetail: this.props?.contentDetail,
          episodeData: this.props?.currentEpisode,
          triggerFrom: 'force_login_notification'
        });
        this.setState({
          isPaused: true
        });
        return;
      }
      if (permission !== PERMISSION.CAN_WATCH) {
        // TODO: [Forest] todo check
        if (isLiveStream) {
          this.setAutoPlay({ isPre: true });
          return;
        }
        if (trialDuration === 0 && triggerLoginDuration === 0) return;
        if (
          trialDuration > 0 &&
          typeof onOpenPopupFreeTrial === 'function' &&
          this.video.duration === this.video.currentTime
        ) {
          onOpenPopupFreeTrial();
          return;
        }
      } else if (permission === PERMISSION.CAN_WATCH) {
        if (this.onHandleConcurrentScreen()) return;
        if (isConvertMWebToApp) {
          this.setState({
            isPaused: true,
            isLoading: false,
            playerReady: false,
            isOnEnded: false
          });
          return;
        }
        if (
          isMovieTrialInApp &&
          content?.type === CONTENT_TYPE.MOVIE &&
          typeof onOpenPopupFreeTrial === 'function' &&
          this.video.duration === this.video.currentTime
        ) {
          onOpenPopupFreeTrial();
          return;
        }
      }
      if (isPaused && !isAdPlay && linkPlay) {
        if (isOnEnded) {
          this.isSeeking = true;

          this.video.currentTime = 0;
          this.setState({
            isOnEnded: false,
            currentTimeText: '00:00'
          });
        }
        // TODO: [Forest] todo check
        /* if (isDVR) {
                                                                                                                                                                  let { duration } = this.video;
                                                                                                                                                                  try {
                                                                                                                                                                    if (duration === Infinity && typeof this.video.seekable === 'function') {
                                                                                                                                                                      duration = this.video.seekable.end(0);
                                                                                                                                                                    } else if (
                                                                                                                                                                      typeof this.player?.isLive === 'function' &&
                                                                                                                                                                      this.player.isLive() &&
                                                                                                                                                                      typeof this.player.seekRange === 'function'
                                                                                                                                                                    ) {
                                                                                                                                                                      duration = this.player.seekRange()?.end;
                                                                                                                                                                    }
                                                                                                                                                                  } catch (e) {
                                                                                                                                                                    console.log(e);
                                                                                                                                                                  }
                                                                                                                                                                  this.setState({ isPremiereLive: this.video.currentTime >= duration - 8 });
                                                                                                                                                                  if (!this.firstPlay && this.video.duration > 8 && duration > this.video.currentTime) {
                                                                                                                                                                    this.isSeeking = true;

                                                                                                                                                            this.video.currentTime = duration - 8;
                                                                                                                                                          }
                                                                                                                                                        } else  */
        // if (((isLiveTV && isSeekAllow) || isDVR) && !this.firstPlay) {
        //   this.video.currentTime = this.player.seekRange().end;
        // }
        this.setState({ isDisplayInfoBox: false });
        this.video.play();

        if (!this.intervalSeekTV) {
          this.handleTimerSeekLiveTV();
        }
      } else {
        if (preventPauseLive && !isDVR && !isLiveTV && key !== 'OFF') {
          // cho phép user pause khi bật dsk ở fullscreen livetv
          return;
        }
        // if (isLiveStream && isPremiere && !isMainLinkStarted) return;
        if (!isMainLinkStarted) return;
        this.video.pause();
        this.setState({
          isPaused: true,
          behindLive: true
        });
      }
    }
  };

  handleActionVideoIndexing = ({ positionClick }: any) => {
    if (positionClick === POSITION_ACTION.TOAST) {
      this.video.pause();
      this.setState({ isPaused: true });
    } else if (positionClick === POSITION_ACTION.INDICATOR) {
      if (!this.state.isDisplayInfoBox) {
        this.video.pause();
        this.setState({
          isDisplayInfoBox: true,
          isPaused: true
        });
      } else {
        this.setAutoPlay();
        this.setState({ isDisplayInfoBox: false });
      }
    }
  };

  handleShowController = (showController: any) => {
    this.setState({ showController });
  };

  onClickVolume = () => {
    const { onMuted } = this.props || {};
    const { volumeValue, isMuted } = this.state || {};
    this.video.muted = !isMuted;
    if (volumeValue === 0 && this.video.muted) {
      this.video.volume = 1;
      this.video.muted = false;
      ConfigLocalStorage.set(LocalStorage.VOLUME, 1);
      this.setState({
        volumeValue: 100,
        isMuted: false
      });
    } else {
      this.setState({ isMuted: !isMuted });
    }
    if (typeof onMuted === 'function') onMuted(!isMuted);
  };

  onVoluming = (value: any) => {
    if (!this.video) return;
    const volumeValue = value / 100;
    this.video.volume = volumeValue;
    this.video.muted = false;
    ConfigLocalStorage.set(LocalStorage.VOLUME, volumeValue);
    this.setState({
      volumeValue: value,
      isMuted: false
    });
  };

  onControlSeekChange = (value: any) => {
    if (this.onHandleConcurrentScreen()) return;
    const { duration }: any = this.handlePlayerTime() || {};
    const temp = value || 0;
    const { onSeekChange, isDVR, isSafari, isIOS, isLiveTV, isSeekAllow, activeEpg, isLive } =
      this.props || {};
    let seekValue = (temp * duration) / 100;

    if ((isLiveTV && isSeekAllow && (!activeEpg?.id || (activeEpg?.id && isLive))) || isDVR) {
      this.setState({ behindLive: true });
      seekValue = (temp * (this.player.seekRange().end - this.player.seekRange().start)) / 100;
    }

    if (!duration) return;
    this.checkSeekAds({
      currentTime: this.video.currentTime,
      seekValue
    });
    this.isSeeking = true;
    if ((isLiveTV && isSeekAllow && (!activeEpg?.id || (activeEpg?.id && isLive))) || isDVR) {
      this.video.currentTime = seekValue + this.player.seekRange().start;
      if (this.video.currentTime >= this.player.seekRange().end) {
        this.setState({ behindLive: false });
        this.video.currentTime = this.player.seekRange().end;
      }
    } else {
      this.video.currentTime = seekValue;
    }

    this.video.play();
    if (typeof onSeekChange === 'function') onSeekChange(seekValue);
    //  TODO: [Forest] check
    /*  if (isDVR) {
                                                                                  seekValue = seekValue < 30 ? 30 : seekValue;
                                                                                  let { duration } = this.video;

                                                                                  try {
                                                                                    if (duration === Infinity && typeof this.video.seekable === 'function') {
                                                                                      duration = this.video.seekable.end(0);
                                                                                    } else if (
                                                                                      typeof this.player?.isLive === 'function' &&
                                                                                      this.player.isLive() &&
                                                                                      typeof this.player.seekRange === 'function'
                                                                                    ) {
                                                                                      duration = this.player.seekRange()?.end;
                                                                                    }
                                                                                  } catch (e) {
                                                                                    console.log(e);
                                                                                  }

                                                                                  this.setState({
                                                                                    isPremiereLive: seekValue > duration - 8
                                                                                  });
                                                                                }
                                                                            */
    if (isSafari || isIOS) {
      this.handleSubtitleOnSafariWhenSeekProgress();
    }
  };

  onHandleConcurrentScreen = () => {
    const { concurrentScreen, openPopup, contentDetail, detailChannel } = this.props || {};
    const { isHBO, contentConcurrentGroup } = contentDetail || detailChannel || {};
    const isConcurrentScreenLimit = concurrentScreen?.code === ERROR_CODE.CODE_2;
    if (isConcurrentScreenLimit) {
      if (typeof openPopup === 'function') {
        openPopup({
          name: POPUP.NAME.CONCURRENT_SCREEN,
          sessionList: concurrentScreen?.sessionList,
          contentConcurrentGroup,
          isHBO
        });
      }
    }
    return isConcurrentScreenLimit;
  };

  onControlSeeking = (value: any) => {
    if (this.onHandleConcurrentScreen()) return;
    this.isSeeking = true;
    const { isSafari, isIOS } = this.props || {};
    this.setState({ seekValue: value });
    if (isSafari || isIOS) {
      this.handleSubtitleOnSafariWhenSeekProgress();
    }
  };

  onSeeking = () => {
    this.isSeeking = true;

    const isHasMoreMinuteTrialContent = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    if (isHasMoreMinuteTrialContent) {
      removeSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT);
    }

    if (
      this.props?.permission === PERMISSION.NON_LOGIN &&
      this.props?.contentDetail?.triggerLoginDuration &&
      (isEmpty(this.props.ads) || !!this.adPoints?.pre || !this.requestingAds)
    ) {
      setTimeout(() => {
        this.handleRegistrationTrigger({});
      }, 50);
    }
  };

  onSeeked = () => {
    const { permission, contentDetail, handleSeekbarContent, isIOS, warningScreen } =
      this.props || {};
    const { seekValue, isAdsMid } = this.state || {};
    if (
      permission === PERMISSION.NON_LOGIN &&
      contentDetail?.triggerLoginDuration &&
      this.video.currentTime < contentDetail?.triggerLoginDuration
    ) {
      this.setState({ isAdsEndRegistration: false });
    }

    if (warningScreen && !!seekValue) {
      if (typeof handleSeekbarContent === 'function') {
        handleSeekbarContent({ curTime: this.video?.currentTime });
      }
      if (isAdsMid) this.setState({ isAdsMid: false });
    }
    if (isIOS && !!checkIsFullscreen()) {
      this.handleSeekedInSafariIOSFullscreen();
    }
  };

  handleSeekedInSafariIOSFullscreen = () => {
    this.handleSubtitleOnSafariWhenSeekProgress();
  };

  handleSubtitleOnSafariWhenSeekProgress = () => {
    clearTimeout(this.timeoutSetSubtitleOnSafari);
    this.timeoutSetSubtitleOnSafari = setTimeout(() => {
      this.onChangeSubtitleOnIOS('off');
      this.onSetSubtitle(this.state.subtitleSelected, true);
    }, 700);
  };

  checkSeekAds = ({ currentTime, seekValue }: any) => {
    if (this.props.isIOS && this.props.isSafari) return;
    const mid = find(get(this.props, 'ads', []), ['type', 'mid']);
    const timeCodeAds = get(this.props, 'timeCodeAds', []);
    if (!isEmpty(mid)) this.setState({ isAdsMid: true });
    if (isEmpty(mid) || seekValue < currentTime || seekValue === 0) return;

    if (isEmpty(timeCodeAds)) {
      if (!mid?.repeat) return;
      const adsPoint = Number.parseInt(String(seekValue / (mid.repeat * 60))) * (mid.repeat * 60);
      if (adsPoint > currentTime && adsPoint < Number.parseInt(seekValue)) {
        this.adPoints.currentAds = {
          urlKey: ADS_URL.URL1,
          ads: mid
        };
        if (this.requestAds) this.requestAds(mid?.[ADS_URL.URL1]?.url, 1);
      }
    } else {
      let adsToShow: any = {};
      timeCodeAds.map((item: any) => {
        if (
          item.secondsStartAds > currentTime &&
          item.secondsStartAds < Number.parseInt(seekValue)
        ) {
          adsToShow = find(get(this.props, 'ads', []), ['type', item.typeAds]);
        }
      });
      if (!isEmpty(adsToShow)) {
        this.adPoints.currentAds = {
          urlKey: ADS_URL.URL1,
          ads: adsToShow
        };

        if (this.requestAds) this.requestAds(adsToShow?.[ADS_URL.URL1]?.url);
      }
    }
  };

  setupAds = ({ requestAds }: any) => {
    this.requestAds = requestAds;
  };

  onControlFullscreen = () => {
    const videoTag: any = document.getElementById(EL_ID.VIE_PLAYER);
    if (videoTag) {
      const tracks = videoTag?.textTracks;
      this.setState({ isFullscreen: !checkIsFullscreen() });
      if (checkIsFullscreen()) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (videoTag.webkitExitFullScreen) {
          videoTag?.webkitExitFullScreen();
        }
        if (this.props.isMobile && this.props.isIOS && typeof tracks.onchange === 'function') {
          tracks.onchange = null;
        }
      } else {
        const { videoContainer } = this;
        if (this.props.isMobile && this.props.isIOS) {
          try {
            this.video.playsInline = false;
            this.video.controls = true;
          } catch (error) {
            console.log(error);
          }
          videoTag.webkitEnterFullscreen();
          if (!tracks.onchange) tracks.onchange = this.handleFullscreenIOSTrackChange;
        } else if (videoContainer) {
          if (videoContainer.webkitRequestFullScreen) {
            videoContainer.webkitRequestFullScreen();
          } else if (videoContainer.requestFullscreen) {
            videoContainer.requestFullscreen();
          }
        }
      }
    }
  };

  handleFullscreenIOSTrackChange = (e: any) => {
    const tracks = e.target;
    let codeNameSelected = 'off';
    for (let i = 0; i < tracks.length; i += 1) {
      if (tracks[i].mode === 'showing') {
        codeNameSelected = tracks[i].language;
        break;
      }
    }
    const subtitleSelected =
      codeNameSelected === 'off'
        ? find(get(this.props, 'settingData.subtitles', []), ['id', 'OFF'])
        : find(get(this.props, 'settingData.subtitles', []), ['codeName', codeNameSelected]);
    this.onSetSubtitle(subtitleSelected);
  };

  handlePlayerTime = () => {
    if (!this.video || !this.player) return;
    const seekRange = this.player.seekRange();
    let currentTime = this.video?.currentTime || 0;
    let duration = 0;

    if (typeof this.player.seekRange === 'function') {
      if (this.video.duration === Number.POSITIVE_INFINITY && (isSafari || isIOS)) {
        duration = seekRange.end - seekRange.start;
        currentTime = this.video?.currentTime - seekRange.start;
      } else {
        // For DASH
        duration = seekRange.end;
      }
    }
    return {
      currentTime,
      duration
    };
  };

  onTimeUpdateSeekLiveTV = () => {
    if (this.playerUnmounted || this.props.playingAds || !this.player) return;
    this.firstPlay = true;
    const { onTimeUpdate } = this.props || {};
    const seekRange = this.player.seekRange();
    const { currentTime, duration }: any = this.handlePlayerTime();
    if (typeof this.props?.handleGetPlayerTime === 'function') {
      this.props?.handleGetPlayerTime({
        currentTime,
        duration
      });
    }
    if (this.video.paused && !this.player.seekRange()?.end && !this.player.seekRange()?.start) {
      this.video.currentTime = 0;
    }
    if (this.video.paused && currentTime > seekRange.end) {
      this.video.currentTime = seekRange.start;
    }

    if (typeof onTimeUpdate === 'function') {
      onTimeUpdate({
        currentTime,
        duration
      });
    }
    if (!this.isSeeking) {
      this.handleSeekBar({
        currentTime,
        duration
      });
    }
    this.handleCurrentTime({
      currentTime,
      duration
    });
  };

  onTimeUpdate = () => {
    if (
      this.playerUnmounted ||
      this.props.playingAds ||
      !this.player ||
      this.state.isPopupDismissed
    )
      return;
    this.firstPlay = true;
    const { isDVR, onTimeUpdate, isLiveTV, isSeekAllow, activeEpg, epg } = this.props || {};
    const { isLive, isComingSoon, isCatchUp } = activeEpg || {};
    const isCatchUpContent = epg && isCatchUp && !isComingSoon;
    const currentTime = this.video?.currentTime || 0;
    const duration = this.video?.duration || 0;

    if ((isLiveTV && isSeekAllow && (!isCatchUpContent || (isCatchUpContent && isLive))) || isDVR) {
      return;
    }

    if (!this.requestingAds && this.handleRegistrationTrigger({ curTime: currentTime })) {
      this.handleRegistrationTrigger();
      if (isEmpty(this.props.ads) || !!this.adPoints?.pre) {
        return;
      }
    }
    this.handleSeekBar({
      currentTime,
      duration
    });
    this.handleCurrentTime({
      currentTime,
      duration
    });
    if (typeof onTimeUpdate === 'function') {
      onTimeUpdate({
        currentTime,
        duration
      });
    }
  };

  shouldSkipRegistrationTriggerForAds = ({ currentTime }: any) => {
    const mid = find(get(this.props, 'ads', []), ['type', 'mid']);
    if (!mid || !mid.repeat) return false;
    const repeatSeconds = mid.repeat * 60;
    const adsPoint = Math.floor(currentTime / repeatSeconds) * repeatSeconds;

    const isNearAdsPoint = Math.abs(currentTime - adsPoint) <= 2;
    return isNearAdsPoint;
  };

  handleRegistrationTrigger = (data?: any) => {
    const { curTime } = data || {};
    const currentTime = curTime || this.video.currentTime;
    const { contentDetail, openPopup, isGlobal, profile } = this.props || {};
    const { triggerLoginDuration, permission } = contentDetail || {};
    const moreMinuteTrialAfterAds = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;
    const shouldSkip = this.shouldSkipRegistrationTriggerForAds({ currentTime });

    const registrationTrigger =
      permission === PERMISSION.NON_LOGIN &&
      triggerLoginDuration &&
      (moreMinuteTrialAfterAds
        ? currentTime >= +moreMinuteTrialAfterAds
        : currentTime >= triggerLoginDuration);

    if (shouldSkip && !this.requestingAds) {
      return false;
    }

    if (registrationTrigger && !this.requestingAds) {
      if (this.video.pause) this.video.pause();
      this.setState({ isPaused: true });

      if (typeof openPopup === 'function') {
        openPopup({
          name: isGlobal
            ? profile?.id
              ? POPUP.NAME.USER_VOD_TRIAL_GLOBAL
              : POPUP.NAME.NON_LOGIN_TRIAL_GLOBAL
            : POPUP.NAME.PLAYER_TRIGGER_AUTH,
          noControl: true,
          isEndTimeWatchTrial: true,
          data: this.props?.content,
          contentDetail: this.props?.contentDetail,
          episodeData: this.props?.currentEpisode,
          triggerFrom: 'force_login_notification'
        });
      }

      return true;
    }

    return false;
  };

  canplay = (video: any) => {
    if (this.isCanPlay) return;
    const { isIOS, isSafari, setInitPlay } = this.props || {};
    const { isAdPlay } = this.state || {};
    if (isIOS) {
      const bufferTime = new Date().getTime() - this.bufferTime;
      if (typeof setInitPlay === 'function') setInitPlay({ bufferTime });
      if (video && !isAdPlay) video.muted = false;
      if (this.video?.paused) {
        this.setState({ isPaused: true });
      }
    }
    if (isSafari && !isIOS && this.video?.textTracks?.length > 0) {
      for (let i = 0; i < this.video.textTracks.length; i += 1) {
        this.video.textTracks[i].mode = 'hidden';
      }
    }
    this.isCanPlay = true;
  };

  handleHttpCode = (httpCode: any) => {
    const {
      concurrentScreen,
      contentDetail,
      dataRefreshSession,
      handleEndSessionPlay,
      detailChannel
    } = this.props || {};
    const { contentConcurrentGroup, drmServiceName } = contentDetail || detailChannel || {};
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (httpCode === HTTP_CODE.MAX_CCU) {
      this.setState({
        playerReady: false,
        isPaused: true,
        isLoading: false,
        isMaxCCU: true,
        initPlayer: false
      });
      if (this.onHandleConcurrentScreen()) return;
      this.props.openPopup({
        name: POPUP.NAME.LIMIT_CCU,
        retryAction: this.handleRetry,
        contentConcurrentGroup,
        drmServiceName
      });
    } else if (httpCode) {
      if (sessionToken) {
        handleEndSessionPlay(sessionToken);
      }
      this.openErrorPopup({
        qnetData: { httpCode },
        errorType: ERROR_PLAYER.TYPE.QNET
      });
      this.setPlayerError(true);
    }
  };

  handleSeekBar = ({ currentTime, duration }: any) => {
    if (!duration) return;
    const { isOnEnded, isLiveTV, isSeekAllow, behindLive } = this.state || {};
    const { isDVR } = this.props || {};
    const seekValue = ((currentTime / duration) * 100).toFixed(2) || 0;
    // if (!isSafari && !isIOS && (isLiveTV || isDVR) && behindLive) {
    //   seekValue =
    //     ((currentTime - this.player.seekRange().start) /
    //       (this.player.seekRange().end - this.player.seekRange().start)) *
    //     100;
    // }
    if (!this.player?.paused && isDVR && currentTime.toFixed(0) === duration.toFixed(0)) {
      this.setState({ behindLive: false });
    }
    if (
      (!this.isSeeking && Number(seekValue) !== Number(this.state.seekValue)) ||
      isLiveTV ||
      isDVR
    ) {
      this.setState({
        seekValue:
          isOnEnded || (((isLiveTV && isSeekAllow) || isDVR) && !behindLive) ? 100 : seekValue
      });
    }
  };

  handleCurrentTime = ({ currentTime, duration }: any) => {
    if (!currentTime || !duration) return;
    this.parseCurrentTime(duration, currentTime);
  };

  parseCurrentTime = (duration: any, currentTime: any) => {
    const durationObject = parseSecond(duration);
    const currentTimeObject = parseSecond(Math.abs(duration - currentTime));
    const { isDVR, isLiveTV, isSeekAllow, activeEpg, isLive } = this.props || {};
    const { durationText, currentTimeText, isOnEnded, behindLive } = this.state || {};
    if (!isLiveTV && !isDVR && durationObject?.hours > MAX_HOUR) {
      return this.setState({ globalLimited: true });
    }

    if (durationObject.time !== durationText && !isLiveTV && !isDVR) {
      this.setState({ durationText: durationObject.time });
    }
    if (currentTimeObject.time !== currentTimeText) {
      const currentTimeTextFormat =
        (isLiveTV && isSeekAllow && (!activeEpg?.id || (activeEpg.id && isLive))) || isDVR
          ? `-${currentTimeObject.time}`
          : currentTimeObject.time;

      this.setState({
        currentTimeText:
          isOnEnded ||
          (isLiveTV &&
            isSeekAllow &&
            !behindLive &&
            (!activeEpg?.id || (activeEpg?.id && isLive))) ||
          (isDVR && !behindLive)
            ? '00:00'
            : currentTimeTextFormat
      });
    }
  };

  onCheckInfoDebugPlayer = () => {
    const { isShowInfoDebug } = this.state || {};
    this.setState({ isShowInfoDebug: !isShowInfoDebug });
  };

  initAd = async () => {
    this.video = document.getElementById(EL_ID.VIE_PLAYER);
    this.adContainer = document.getElementById(EL_ID.ADS_CONTAINER);
    const params: any = {
      props: this.props,
      videoElement: this.video,
      adContainer: this.adContainer,
      playerThis: this,
      subtitleElement: this.subtitleRef.current,
      adsMsgHandler: this.adsMsgHandler,
      handleShowSkipAds: this.handleShowSkipAds,
      setupAds: this.setupAds,
      linkPlay: this.props.linkPlay,
      isIOS: this.props.isIOS,
      isSafari: this.props.isSafari,
      isFullscreen: checkIsFullscreen(),
      startupLoadPreroll: this.props.startupLoadPreroll,
      contentId: this.props.contentId
    };
    if (this.adContainer) {
      this.adContainer.innerHTML = '';
    }
    const { ads } = this.props;
    if (!ads || ads?.length === 0) {
      return;
    }
    initAd(params);
  };

  onSkipAds = () => {
    if (this.adsManager) {
      this.adsManager.stop();
      if (this.onSkipAd) {
        this.onSkipAd(this.adEvent);
      }
    }
  };

  handleShowSkipAds = ({ isSkipAds, currentAd, onSkipAd, adEvent, adsManager, adsLoader }: any) => {
    // console.log('handleShowSkipAds', isSkipAds, currentAd);
    const currentUrl = currentAd?.ads?.[currentAd?.urlKey];
    const { timeSkip, skip } = currentUrl || {};
    this.adsManager = adsManager || null;
    this.onSkipAd = onSkipAd || null;
    this.adEvent = adEvent || null;
    this.adsLoader = adsLoader || null;

    // type 1 use skip of config
    // type 2 use skip of gg ads
    // type 0 un-use skip
    // console.log('handleShowSkipAds', isSkipAds, skip);
    this.setState({
      typeShowAds: !isMobile ? (skip ? 1 : isSkipAds ? 2 : 0) : isSkipAds ? 2 : skip ? 1 : 0
    });

    if ((!skip && !isSkipAds) || (isSkipAds && isMobile) || (!skip && !isMobile)) {
      this.setState({ timeSkip: 0 });
      return;
    }
    this.setState({ timeSkip });
  };

  onControlThumbnail = (value: any) => {
    const { isLiveTV, isSeekAllow, isDVR } = this.props || {};
    if (isLiveTV && isSeekAllow) return;
    let duration = this.video?.duration || 0;
    if (!isDVR) {
      try {
        if (
          duration === Number.POSITIVE_INFINITY &&
          typeof this.video?.seekable?.end === 'function'
        ) {
          duration = this.video.seekable.end(0);
        } else if (
          typeof this.player?.isLive === 'function' &&
          this.player.isLive() &&
          typeof this.player.seekRange === 'function'
        ) {
          duration = this.player.seekRange().end;
        }
      } catch (e) {
        console.log(e);
      }
    }
    if (isDVR && this.player) {
      duration = this.player?.seekRange().end - this.player?.seekRange().start;
    }
    const hoverTime = (value * duration) / 100;
    const temp = hoverTime > duration ? duration : hoverTime < 0 ? 0 : hoverTime;
    const hoveTimeSecond = isDVR ? parseSecond(duration - temp) : parseSecond(temp);
    const { thumbData } = this.props || {};
    const thumbMetadata = this.getMetadata(thumbData, hoverTime);
    this.setState({
      hoverTimeText: hoveTimeSecond?.time,
      thumbMetadata,
      hoverSeerValue: value
    });
  };
  getMetadata = (data: any, time: any) => {
    const thumbData = data || [];
    return thumbData.find(
      (t: any) =>
        Number.parseFloat(time) >= Number.parseFloat(t.start) &&
        Number.parseFloat(time) <= Number.parseFloat(t.end)
    );
  };

  setupEpisode = ({ getNextEpisode }: any) => {
    this.getNextEpisode = getNextEpisode || (() => {});
  };

  onMouseMove = () => {
    if (this.props.isPrevent) return;
    const { displayControl } = this.state || {};
    if (!displayControl) {
      this.setState({ displayControl: true });
    }
    if (this.mouserMoveTimer) clearTimeout(this.mouserMoveTimer);
    this.mouserMoveTimer = createTimeout(() => {
      if (this.playerUnmounted) return;
      if (displayControl) {
        this.setState({ displayControl: false });
      }
    }, MOUSE_MOVE);
  };

  checkPermission = (item: any, trigger: any) => {
    if (!item) return true;
    const { contentDetail, currentProfile, profile, openPopup, router, isGlobal } =
      this.props || {};
    const permission = item?.permission;
    let result = false;
    let name = '';
    let packageId = contentDetail?.packageId || [];
    const isKid = currentProfile?.isKid || false;
    if (permission === PERMISSION.PAYMENT) {
      name = POPUP.NAME.LOGIN_NON_VIP_QUALITY;
      if (!profile?.id) {
        name = POPUP.NAME.NON_LOGIN_QUALITY;
      }
      if (trigger === VALUE.SUBTITLE) {
        name = POPUP.NAME.LOGIN_NON_VIP_SUB_AUDIO;
        if (!profile?.id) {
          name = POPUP.NAME.NON_LOGIN_SUB_AUDIO;
        }
        packageId = item?.packageId;
      } else if (trigger === VALUE.AUDIO) {
        name = POPUP.NAME.LOGIN_NON_VIP_SUB_AUDIO;
        if (!profile?.id) {
          name = POPUP.NAME.NON_LOGIN_SUB_AUDIO;
        }
        packageId = item?.packageId;
      }
      if (isKid) name = POPUP.NAME.KID_LIMITED_VIP_DIALOG;
      if (this.video) this.video.pause();
      const action = {
        func: () =>
          onOpenPayment(router, {
            returnUrl: window?.location?.href,
            pkg: packageId,
            newTriggerPaymentBuyPackage: {
              isGlobal,
              profileId: profile?.id
            }
          }),
        closed: () => {
          if (this.video) this.video.play();
        }
      };
      if (typeof openPopup === 'function') {
        if (profile?.type !== USER_TYPE.VIP && !isGlobal) {
          openPopup({
            name: POPUP.NAME.SVOD_TRIGGER,
            id: this.props.contentId,
            action,
            trigger: true,
            data: this.props?.content,
            trackingData: {
              triggerFrom:
                trigger === VALUE.RESOLUTION ? VALUE.QUALITY_DIALOG : VALUE.SUB_AUDIO_DIALOG,
              userType: !profile?.id
                ? 'guest'
                : profile?.type === USER_TYPE.NON_VIP
                ? 'free'
                : 'vip',
              contentId: this.props?.content?.id,
              contentName: this.props?.content?.title
            }
          });
        } else {
          openPopup({
            name,
            id: this.props.contentId,
            action,
            trigger: true,
            data: this.props?.content,
            trackingData: {
              triggerFrom:
                trigger === VALUE.RESOLUTION ? VALUE.QUALITY_DIALOG : VALUE.SUB_AUDIO_DIALOG,
              userType: !profile?.id
                ? 'guest'
                : profile?.type === USER_TYPE.NON_VIP
                ? 'free'
                : 'vip',
              contentId: this.props?.content?.id,
              contentName: this.props?.content?.title
            }
          });
        }
      }
    } else {
      result = true;
    }
    return result;
  };
  onChangeSubtitleOnIOS = (codeName: any) => {
    const videoTag: any = document.getElementById(EL_ID.VIE_PLAYER);
    const tracks = videoTag?.textTracks || [];
    for (let i = 0; i < tracks.length; i += 1) {
      if (codeName === 'off') {
        tracks[i].mode = 'disabled';
      } else if (tracks[i].language === codeName) {
        tracks[i].mode = 'showing';
      } else {
        tracks[i].mode = 'disabled';
      }
    }
  };

  onSetSubtitle = (selectSub?: any, isNotCheckSameSub?: any) => {
    const { isIOS, isSafari, profile, requestLogin } = this.props || {};
    const userId = profile?.id;
    const { subtitleSelected, playerName } = this.state || {};
    // SHOOT GA
    const subtitle = isEmpty(selectSub)
      ? find(get(this.props, 'settingData.subtitles', []), ['isDefault', true]) || null
      : selectSub;
    if (isEqual(subtitle, subtitleSelected) && !isNotCheckSameSub) {
      return;
    }
    if (!this.checkPermission(subtitle, VALUE.SUBTITLE)) {
      if (isIOS && !!checkIsFullscreen()) {
        if (!isEqual(subtitle, subtitleSelected)) {
          this.onControlFullscreen();
        }
        this.onChangeSubtitleOnIOS(get(this.state, 'subtitleSelected.codeName', 'off'));
      }
      const contentId = this.props.contentId;

      if (profile?.isPremium) return;
      this.dispatch(getPopupTriggerDialog({ type: 'audio_sub', contentId }));
      if (isEmpty(subtitle)) return;
    }
    if (!userId && subtitle?.permission !== PERMISSION.CAN_WATCH) {
      const requestLoginTitle =
        'Hãy đăng nhập và đăng ký gói VieON VIP để xem với chất lượng tốt nhất';
      if (typeof requestLogin === 'function') {
        requestLogin(true, requestLoginTitle, false, true);
      }
      // if (this.video) this.video.pause();
      if (isIOS && !!checkIsFullscreen()) {
        this.onControlFullscreen();
        this.onChangeSubtitleOnIOS(get(this.state, 'subtitleSelected.codeName', 'off'));
      }
      return;
    }
    const codeName = subtitle?.codeName || 'off';
    if (typeof this.setSubtitle === 'function') this.setSubtitle(codeName);
    this.setState({ subtitleSelected: subtitle });
    if (isNotCheckSameSub && isSafari && !isIOS) {
      this.isFirstInitSubtitleOnSafariMacOS = false;
    }
    if (this.video) {
      const tracks = this.video?.textTracks || [];
      if (isIOS) {
        this.onChangeSubtitleOnIOS(codeName);
      } else if (playerName === PLAYER_NAME.SHAKA_PLAYER) {
        if (codeName === 'off') {
          // Turn Off text track handler
          for (let i = 0; i < tracks.length; i += 1) {
            if (tracks[i].oncuechange && typeof tracks[i].oncuechange === 'function') {
              tracks[i].oncuechange = null;
            }
          }
          if (this.subtitleRef.current) {
            this.subtitleRef.current.innerHTML = '';
          }
          return;
        }
        // Check and turn on text track handler
        for (let i = 0; i < tracks.length; i += 1) {
          if (isSafari) {
            if (tracks[i].language === codeName) {
              tracks[i].oncuechange = this.cueChangeHandler;
              tracks[i].mode = 'hidden';
            } else {
              tracks[i].oncuechange = null;
            }
          } else if (tracks[i].label === 'Shaka Player TextTrack') {
            tracks[i].oncuechange = this.cueChangeHandler;
          } else {
            tracks[i].oncuechange = null;
          }
        }
      } else {
        for (let i = 0; i < tracks.length; i += 1) {
          const textTrack = tracks[i];
          if (textTrack.language === codeName) {
            textTrack.oncuechange = this.cueChangeHandler;
          } else {
            textTrack.oncuechange = null;
          }
        }
        if (codeName === 'off' && this.subtitleRef.current) {
          this.subtitleRef.current.innerHTML = '';
        }
      }
    }
  };

  cueChangeHandler = (e: any) => {
    if (this.subtitleRef.current) {
      const cues = e.target.activeCues;
      const text = get(cues, '[0].text', '');
      this.subtitleRef.current.innerHTML = text.replace(/\n\r?/g, '<br />');

      const align = get(cues, '[0].align', 'center');
      const line = get(cues, '[0].line', 'auto');
      const position = get(cues, '[0].position', 'auto');
      const size = get(cues, '[0].size', '100');
      const snapToLines = get(cues, '[0].snapToLines', true);
      if (text && text !== '') {
        this.subtitleRef.current.innerHTML = `<span style='-webkit-text-stroke: 1px black; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;'>${get(
          cues,
          '[0].text',
          ''
        ).replace(/\n\r?/g, '<br />')}</span>`;
        const textAlign = align;
        let left = '';
        let width = '';
        let top = '88%';
        let translate = `translate(-${0}%, -${top})`;
        if (line !== 'auto') {
          top = `${line}%`;
        }
        if (align === 'left') {
          const positionInt = position === 'auto' ? 0 : Number.parseInt(position);
          const sizeInt = Number.parseInt(size);
          left = `${Math.max(positionInt)}%`;
          width = `${Math.max(sizeInt - positionInt, 0)}%`;
          if (!snapToLines) translate = `translate(-${left}, -${top})`;
        } else if (align === 'right') {
          const positionInt = position === 'auto' ? 100 : Number.parseInt(position);
          const sizeInt = Number.parseInt(size);
          const temp = positionInt - sizeInt;
          left = `${Math.max(temp, 0)}%`;
          width = `${sizeInt - Math.abs(temp)}%`;
          if (!snapToLines) translate = `translate(-${left}, -${top})`;
        } else {
          const positionInt = position === 'auto' ? 50 : Number.parseInt(position);
          const sizeInt = Number.parseInt(size);
          const temp = (positionInt - 50) * 2;
          left = `${Math.max(temp, 0)}%`;
          width = `${sizeInt - Math.abs(temp)}%`;
          if (!snapToLines) {
            translate = `translate(-${left}, -${top})`;
          }
        }
        this.subtitleRef.current.style.textAlign = textAlign;
        this.subtitleRef.current.style.top = top;
        this.subtitleRef.current.style.left = left;
        this.subtitleRef.current.style.width = width;
        this.subtitleRef.current.style.transform = translate;
      } else {
        this.subtitleRef.current.innerHTML = ``;
        this.subtitleRef.current.style.left = `0%`;
        this.subtitleRef.current.style.width = `100%`;
      }
    }
  };
  onSetAudio = (item?: any) => {
    const audio = item || (this.props.settingData?.audios || []).find((au: any) => !!au.isDefault);
    const { profile } = this.props || {};
    if (!this.checkPermission(audio, VALUE.AUDIO)) {
      const contentId = this.props.contentId;

      if (profile?.isPremium) return;
      this.dispatch(getPopupTriggerDialog({ type: 'audio_sub', contentId }));
      if (!audio) return;
    }
    if (this.setAudio) this.setAudio(audio);
  };

  onSetQuality = (item: any) => {
    const height = item?.height || 0;
    const name = item?.name || 0;
    const permission = item?.permission;
    const { profile } = this.props;
    const userId = profile?.id;
    const loginQuality = {
      height,
      name,
      contentId: this.props.contentId,
      userId: profile?.id
    };

    if (userId && permission === PERMISSION.PAYMENT) {
      ConfigLocalStorage.set(LocalStorage.LOGIN_QUALITY, JSON.stringify(loginQuality));
    }
    if (!this.checkPermission(item, VALUE.RESOLUTION) || !item) {
      const contentId = this.props.contentId;
      this.dispatch(getPopupTriggerDialog({ type: 'vip_quality', contentId }));
      return;
    }
    if (isEmpty(this.state.dataQuality)) {
      this.setState({
        dataQuality: this.player?.getVariantTracks()
      });
    }
    if (this.setQuality) this.setQuality(item);
  };

  onCheckPlayer = (player: any) => {
    this.player = player;
  };

  onSendReport = (dataState: any, dataTextArea: any, settingData: any) => {
    const dataUserReportId: any = [];
    dataState.forEach((element: any, key: any) => {
      if (element) {
        dataUserReportId.push(key);
      }
    });
    const { currentEpisode, content, setToast, contentId, linkPlay } = this.props || {};
    const { subtitle, audio } = settingData;
    const activeSub =
      subtitle || (this.props.settingData?.subtitles || []).find((st: any) => st.is_default === 1);
    const selectedAudio =
      audio || (this.props.settingData?.audios || []).find((au: any) => au.is_default === 1);
    const timeSeeker = Math.ceil(this.video?.currentTime);
    const entinyType = currentEpisode?.type > 0 ? currentEpisode?.type : content?.type;
    let osVersion: any = null;
    const getOSVersion = () => {
      if (typeof window === 'undefined') return '';
      const { userAgent } = window.navigator;
      const { platform } = window.navigator;
      const macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'];
      const windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'];
      const iosPlatforms = ['iPhone', 'iPad', 'iPod'];
      if (macosPlatforms.indexOf(platform) !== -1) {
        osVersion = 'Mac OS';
      } else if (iosPlatforms.indexOf(platform) !== -1) {
        osVersion = 'iOS';
      } else if (windowsPlatforms.indexOf(platform) !== -1) {
        osVersion = 'Windows';
      } else if (/Android/.test(userAgent)) {
        osVersion = 'Android';
      } else if (!osVersion && /Linux/.test(platform)) {
        osVersion = 'Linux';
      }
      return osVersion;
    };
    return UserApi.postUserReport(
      contentId,
      dataUserReportId,
      dataTextArea,
      linkPlay,
      selectedAudio?.code_name,
      activeSub?.uri,
      getOSVersion(),
      timeSeeker,
      entinyType
    ).then((resp) => {
      if (!resp.success) {
        if (typeof setToast === 'function') setToast({ message: TEXT.MSG_ERROR });
      } else {
        this.setState({ dataSuccess: true });
      }
    });
  };

  adsMsgHandler = (
    msg: any,
    adTagUrl: any,
    adEvent: any,
    adsPlayedDuration: any,
    currentAd: any,
    isAdIOScomplete: any,
    isDoneAds: any
  ) => {
    const { controlPlayingAds, onSkipAds, isIOS, isSafari, isMobile, contentDetail } =
      this.props || {};
    const isHasTrialMoreMinute =
      (this.video?.currentTime > contentDetail?.triggerLoginDuration ||
        contentDetail?.triggerLoginDuration > 0 ||
        contentDetail?.permission === PERMISSION.NON_LOGIN) &&
      (this.video?.currentTime > contentDetail?.triggerLoginDuration - 60 ||
        this.video?.duration - this.video?.currentTime < 60);

    if (isHasTrialMoreMinute && isDoneAds) {
      setSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT, this.video?.currentTime + 60);
    }
    switch (msg) {
      case 'pausedAds': {
        this.setState({ isAdsPaused: true });
        break;
      }
      case 'resumed': {
        this.setState({ isAdsPaused: false });
        break;
      }
      case 'pause': // content pause requested
        this.video.pause();
        if (typeof controlPlayingAds === 'function') {
          controlPlayingAds(true, adTagUrl, adEvent, adsPlayedDuration);
        }
        break;
      case 'play': {
        this.requestingAds = false;
        const isRegistrationTrigger = this.handleRegistrationTrigger({});
        if (!isRegistrationTrigger) {
          if (isIOS && currentAd?.type === 'pre' && this.loadSource) {
            this.loadSource();
          } else {
            this.setAutoPlay({ isPre: currentAd?.type === 'pre' });
          }
        }
        if (isAdIOScomplete) this.setState({ isAdPlay: false });
        if (typeof controlPlayingAds === 'function') {
          controlPlayingAds(false, adTagUrl, adEvent, adsPlayedDuration);
        }
        break;
      }
      case 'skip': {
        this.requestingAds = false;
        if (typeof controlPlayingAds === 'function') {
          controlPlayingAds(false, adTagUrl, adEvent, adsPlayedDuration);
        }
        if (typeof onSkipAds === 'function') {
          onSkipAds(true, adTagUrl, adEvent, adsPlayedDuration);
        }
        break;
      }
      case 'error': {
        this.requestingAds = false;
        this.setState({ isAdError: true });
        if (isIOS && isSafari && isMobile) {
          if (this.loadSource) this.loadSource();
        } else {
          this.setAutoPlay({ isPre: currentAd?.type === 'pre' });
        }
        break;
      }
      default:
        this.requestingAds = false;
        break;
    }
  };

  onNextEpisode = async (e: any) => {
    const nextEpisode = await this.getNextEpisode();
    if (this.props.onNextEpisode) this.props.onNextEpisode(e, nextEpisode);
  };

  handleRetry = () => {
    this.setPlayerError(false);
    const { handleRetry, isLiveStream } = this.props || {};
    if (isLiveStream) {
      this.handleLoadNewContent();
    } else if (typeof handleRetry === 'function') handleRetry();
  };

  onClickLive = () => {
    if (this.video) {
      let duration = this.video?.duration || 0;

      try {
        if (duration === Number.POSITIVE_INFINITY && typeof this.video.seekable === 'function') {
          duration = this.video.seekable.end(0);
        } else if (
          typeof this.player?.isLive === 'function' &&
          this.player.isLive() &&
          typeof this.player.seekRange === 'function'
        ) {
          duration = this.player.seekRange()?.end;
        }
      } catch (e) {
        console.log(e);
      }
      this.isSeeking = true;
      this.video.currentTime = duration - 8;
    }
    this.setState({ isPremiereLive: true });
  };

  controlSettingScreen = (screen: any) => {
    this.setState({ screen });
  };

  controlWarningMessage = (value: any) => {
    const { contentDetail } = this.props || {};
    if (contentDetail?.progress > 0) {
      this.setState({ warningMessageState: true });
    } else {
      this.setState({ warningMessageState: value });
    }
  };

  controlOpenEpisode = (isOpen: any) => {
    this.setState({ isOpenEpisode: isOpen });
  };

  onBackward = () => {
    if (!this.video || this.onHandleConcurrentScreen()) return;
    let time = this.video.currentTime - 10;
    const { isLiveTV, isSeekAllow, isDVR } = this.props;
    time = time < 0 ? 0 : time;
    this.isSeeking = true;
    this.video.currentTime = time;
    if ((isLiveTV && isSeekAllow) || isDVR) {
      this.setState({ behindLive: true });
    }
  };

  onForward = () => {
    if (!this.video || this.onHandleConcurrentScreen()) return;
    let time = this.video.currentTime + 10;
    const { isLiveTV, isSeekAllow, isDVR } = this.props;
    time = time > this.video.duration ? this.video.duration : time;
    this.isSeeking = true;
    this.video.currentTime = time;
    if ((isLiveTV && isSeekAllow) || isDVR) {
      if (this.video.currentTime >= this.player.seekRange().end) {
        this.setState({ behindLive: false });
        this.video.currentTime = this.player.seekRange().end;
      }
    }
  };

  createPlayerData = () => {
    const {
      linkPlay,
      drmInfo,
      playerName,
      initPlayer,
      isMuted,
      isDrm,
      isShowInfoDebug,
      isAdPlay,
      dataQuality
    } = this.state;
    const {
      isLiveTV,
      qnetInfo,
      qnetDrm,
      assetId,
      variantId,
      permission,
      trialDuration,
      isLiveStream,
      isPremiere,
      playerId,
      ads,
      deviceId,
      isConvertMWebToApp,
      drmMerchant,
      contentDetail
    } = this.props;
    const { triggerLoginDuration } = contentDetail || {};

    if (
      !linkPlay ||
      !drmInfo ||
      !playerName ||
      !initPlayer ||
      (!qnetInfo && qnetDrm) ||
      (permission !== PERMISSION.CAN_WATCH &&
        trialDuration === 0 &&
        triggerLoginDuration === 0 &&
        !isLiveStream) ||
      (permission === PERMISSION.CAN_WATCH && isConvertMWebToApp)
    ) {
      if (isConvertMWebToApp) this.setState({ isPaused: true });
      return null;
    }
    const isLive = isLiveTV;
    const { liveTVCCU } = this;
    const { onStalled } = this;
    const { onResetStateVideoStalled } = this;
    const { loadSuccess } = this;
    const { levelLoaded } = this;
    const { loadFail } = this;
    const { setPlayer } = this;
    const { onPlaying } = this;
    const { onPaused } = this;
    const { onEnded } = this;
    const { onWaiting } = this;
    const { onError } = this;
    const { onTimeUpdate } = this;
    const { onSeeked } = this;
    const { onSeeking } = this;
    const { canplay } = this;
    const { handleHttpCode } = this;
    const { initAd } = this;
    const { onCheckPlayer } = this;
    const params: any = {
      deviceId,
      isDrm,
      linkPlay,
      drmInfo,
      playerName,
      isMuted,
      isLive,
      assetId,
      playerId,
      liveTVCCU,
      qnetDrm,
      qnetInfo,
      variantId,
      initPlayer,
      isLiveStream,
      isShowInfoDebug,
      isPremiere,
      isAdPlay,
      dataQuality,
      drmMerchant
    };
    const events = {
      onStalled,
      onResetStateVideoStalled,
      loadSuccess,
      levelLoaded,
      setPlayer,
      onPlaying,
      onPaused,
      onEnded,
      onWaiting,
      onError,
      onTimeUpdate,
      onSeeking,
      onSeeked,
      canplay,
      loadFail,
      handleHttpCode,
      initAd,
      onCheckPlayer,
      ads
    };
    return { ...params, ...events };
  };

  renderPlayer = (namePlayer: any, containerClass: any, eventRelated: any) => {
    if (this.props.isPrevent) return null;
    const playerData = this.createPlayerData();
    const { content } = this.props || {};
    if (isEmpty(playerData)) {
      if (content?.linkPlay?.hlsLinkPlay) {
        // Support render video tag for video object SEO
        return <video src={content.linkPlay.hlsLinkPlay} />;
      }
      if (!isEmpty(eventRelated) && this.props?.isEndStream) {
        return <video id={EL_ID.VIE_PLAYER} />;
      }
      return null;
    }
    const { selectSubName } = this.state;
    return (
      <ShakaPlayerComponent
        containerClass={containerClass}
        selectSubName={selectSubName}
        {...playerData}
        isSafari={this.props.isSafari}
        isIOS={this.props.isIOS}
        usingSigmaPacker={
          this.props.isAndroid ||
          (this.props.isWindows && SIGMA_DRM_WINDOWS) ||
          (this.props.isMacOs && !this.props.isSafari)
        }
        isMobile={this.props.isMobile}
        drmProvider={this.props.drmProvider}
      />
    );
  };

  setCollapseVisible = (value: any) => {
    this.setState({ isCollapseVisible: value });
  };

  setOpenEpisode = () => {
    const { isOpenEpisodeMobile } = this.state || {};
    this.setState({ isOpenEpisodeMobile: !isOpenEpisodeMobile });
  };

  render() {
    const {
      playerReady,
      playerName,
      isAdPlay,
      isOnEnded,
      playerError,
      isLoading,
      isPremiereLive,
      isFullscreen,
      timeSkip,
      globalLimited,
      isAdsEnd,
      isAdsPaused,
      isShowInfoDebug,
      subtitleSelected,
      isAdsMid,
      showController,
      warningMessageState,
      adsCount,
      adsSlot,
      behindLive,
      isMainLinkStarted,
      isDisconnected,
      typeShowAds,
      isShowEndScreenWithTrialContent
    } = this.state;

    const {
      poster,
      adsEnable = true,
      playingAds,
      softLogo,
      isLiveTV,
      permission,
      trialDuration,
      onSkipIntro,
      onSkipIntroTouch,
      isIntro,
      isOuttro,
      isPaused,
      currentEpisode,
      recommendData,
      content,
      altSEOImg,
      settingData,
      isLiveStream,
      isDVR,
      cancelPlayer,
      contentDetail,
      isPrevent,
      preventNote,
      totalCCU,
      isAgeRestricted,
      contentId,
      handleSeekbarContent,
      currentProfile,
      sessionId,
      warningScreen,
      warningMessage,
      warningLocation,
      warningTag,
      isHBO,
      isMobile,
      positionLiveCCU,
      buttonLive,
      showCCU,
      isEndStream,
      eventRelated,
      statusLiveEvent,
      isPremiere,
      isGlobal,
      profile
    } = this.props;
    const isFreeTrial = permission !== PERMISSION.CAN_WATCH && trialDuration > 0;
    const displayControl = this.props.displayControl || this.state.displayControl;
    const isLive = isLiveTV || isLiveStream;
    const isTrailer = !this.props.isLive;
    const endScreenData = recommendData?.bodyData?.slickData || {};

    const isShowEndScreen =
      !isFreeTrial &&
      (isOuttro || isOnEnded) &&
      !isAdPlay &&
      !isEmpty(endScreenData) &&
      !showController;
    const isWarningScreen =
      isAgeRestricted &&
      (((isAdsEnd || (!!this.adPoints?.pre && !this.requestingAds)) && !isAdsMid) ||
        isEmpty(this.props?.ads));

    let isEndScreen = false;
    if (
      (currentEpisode?.isEnd || content?.type === CONTENT_TYPE.MOVIE) &&
      this.state?.isCollapseVisible
    ) {
      isEndScreen = isOnEnded;
    }
    if (isEmpty(endScreenData)) isEndScreen = false;

    let containerClass = `player-container overflow relative${
      playerReady ? ' is-ready' : ' not-ready'
    }`;
    if (playerError || (isEndScreen && !isFreeTrial && !this.state?.isCollapseVisible)) {
      containerClass = 'player-container not-ready overflow relative';
    }
    const innerClass = 'player-inner';
    let isController = !isAdPlay && (!isEndScreen || !!isFreeTrial);
    if (this.props.isMobile) isController = true;
    if (this.props.isMobile && isAdPlay && this.props.isIOS) isController = false;
    const triggerSource = this.props?.contentDetail || this.props?.detailChannel;
    const moreMinuteTrialContent = getSession(LocalStorage.MORE_MINUTE_TRIAL_CONTENT) || 0;

    const isNonPremium =
      !triggerSource?.isPremiumTVod && !triggerSource?.isPremium && !triggerSource?.isPremiumPVod;

    const isNonLoginPermission = triggerSource?.permission === PERMISSION.NON_LOGIN;
    const triggerLoginDuration = triggerSource?.triggerLoginDuration || 0;
    const forceLogin = triggerSource?.forceLogin === PERMISSION.FORCE_LOGIN;
    const currentTime = this.video?.currentTime || 0;
    const videoDuration = this.video?.duration || 0;

    const trialDisplayThreshold = moreMinuteTrialContent
      ? +moreMinuteTrialContent >= videoDuration
        ? videoDuration - 30
        : +moreMinuteTrialContent - 30
      : triggerLoginDuration - 30;

    const isTrialDurationDisplay =
      isNonPremium && isNonLoginPermission && currentTime > trialDisplayThreshold;

    const isTriggerTrialLoginDuration =
      (isNonPremium &&
        isNonLoginPermission &&
        triggerLoginDuration > 0 &&
        (moreMinuteTrialContent
          ? currentTime >= +moreMinuteTrialContent
          : currentTime >= triggerLoginDuration)) ||
      forceLogin;

    return (
      <div className={innerClass} id={EL_ID.PLAYER_INNER}>
        <div
          className={containerClass}
          ref={this.videoContainerRef}
          onMouseMove={this.onMouseMove}
          onTouchStart={this.onMouseMove}
          onBlur={() => {}}
          id={EL_ID.PLAYER_CONTAINER}
        >
          <div
            className={`${
              !isDisconnected
                ? 'hidden z-[-3]'
                : 'block z-50 layer-8 w-full h-full relative bg-black'
            } `}
          >
            <div
              className={`${
                (isController && isLiveStream) || isLiveTV
                  ? 'flex-col-reverse justify-center'
                  : 'flex-col-reverse justify-center lg:flex-row lg:justify-between lg:pl-[104px] lg:pr-[120px] lg:pt-[104px] lg:pb-[144px]'
              } flex items-center w-full h-full space-y-[12px]`}
            >
              <div className="flex flex-col flex-[3/2] text-white space-y-[12px]">
                <p
                  className={`${
                    (isController && isLiveStream) || isLiveTV
                      ? 'text-center !text-[20px] md:!text-[28px] leading-[10px] md:leading-[40px] font-[500]'
                      : 'text-center lg:text-left !text-[20px] md:!text-[28px] lg:!text-[58px] leading-[10px] md:leading-[40px] lg:leading-[81.2px] font-[700]'
                  } `}
                >
                  Không có kết nối mạng
                </p>
                <p
                  className={`${
                    (isController && isLiveStream) || isLiveTV
                      ? 'text-center !text-[16px] md:!text-[24px] font-[400] md:leading-[33.6px]'
                      : 'text-center lg:text-left !text-[16px] md:!text-[24px] lg:!text-[36px] font-[400] md:leading-[33.6px] lg:leading-[57.6px]'
                  } `}
                >
                  Vui lòng kiểm tra lại kết nối mạng và thử lại
                </p>
              </div>
              <div
                className={`${
                  (isController && isLiveStream) || isLiveTV
                    ? 'max-w-[320px] max-h-[320px] w-[27%] h-[47.5%]'
                    : 'lg:max-w-[600px] lg:max-h-[600px] max-w-[27%] max-h-[47.5%] md:max-w-[320px] md:max-h-[320px]'
                }`}
              >
                <Image
                  src="/assets/images/lost_connect.svg"
                  width="600"
                  height="600"
                  className="aspect-[600/600]"
                />
              </div>
            </div>
          </div>
          {contentDetail?.hasObjectDetection &&
            !currentProfile?.isKid &&
            playerReady &&
            sessionId && (
              <VideoIndexing
                currentTime={this.video?.currentTime}
                handleActionVideoIndexing={this.handleActionVideoIndexing}
                isDisplayInfoBox={this.state.isDisplayInfoBox}
                onPlayPause={this.onPlayPause}
                showController={showController}
                contentDetail={contentDetail}
                isAdPlay={isAdPlay}
              />
            )}

          {isWarningScreen &&
            playerReady &&
            (!isLiveStream || (isLiveStream && isMainLinkStarted)) && (
              <WarningScreen
                isLiveTV={isLiveTV}
                contentId={contentId}
                isFullscreen={isFullscreen}
                handleSeekbarContent={handleSeekbarContent}
                controlWarningMessage={this.controlWarningMessage}
                warningScreen={warningScreen}
                video={this.video}
              />
            )}
          {this.state.isOpenEpisodeMobile && this.props.isMobile && (
            <PlayerEpisodeListMobile setOpenEpisode={this.setOpenEpisode} />
          )}
          {this.renderPlayer(playerName, containerClass, eventRelated)}
          {!isEmpty(eventRelated) &&
            (isEndStream || statusLiveEvent === 2) &&
            isLiveStream &&
            !this.props.isMobile && <ContentRelated isFullscreen={isFullscreen} isEventRelated />}
          {((isController && isLiveStream) || isLiveTV) && (
            <ControlLiveBottom
              {...this.props}
              {...this.state}
              totalCCU={totalCCU}
              isPaused={isPaused || this.state.isPaused}
              isOnEnded={isOnEnded}
              isAdPlay={isAdPlay}
              displayControl={displayControl}
              isFullscreen={isFullscreen}
              videoContainerHeight={this.videoContainerRef?.current?.offsetHeight}
              isTablet={this.props.isTablet}
              isPremiereLive={isPremiereLive}
              behindLive={behindLive}
              isEndStream={isEndStream}
              // Events
              onLiveButton={this.onLiveButton}
              onPlayPause={this.onPlayPause}
              onClickVolume={this.onClickVolume}
              onVoluming={this.onVoluming}
              onControlSeeking={this.onControlSeeking}
              onControlSeekChange={this.onControlSeekChange}
              onFullscreen={this.onControlFullscreen}
              onForward={this.onForward}
              onBackward={this.onBackward}
              onCheckInfoDebugPlayer={this.onCheckInfoDebugPlayer}
              onControlThumbnail={this.onControlThumbnail}
              onClickLive={this.onClickLive}
              statusLiveEvent={statusLiveEvent}
              trialDuration={trialDuration}
              currentProfile={currentProfile}
              triggerSource={triggerSource}
              isTriggerTrialLoginDuration={isTriggerTrialLoginDuration}
              isTrialDurationDisplay={isTrialDurationDisplay}
              currentEpisode={currentEpisode}
            />
          )}
          <PlayerPoster poster={poster} alt={altSEOImg} />
          {isPrevent && <PlayerNotification text={preventNote} />}
          <SoftLogo imgSrc={softLogo} alt={altSEOImg} />
          <WaitingSpin
            playerReady={playerReady}
            playerError={playerError}
            playingAds={playingAds}
            permission={permission}
            trialDuration={trialDuration}
            isLoading={isLoading}
            cancelPlayer={cancelPlayer}
          />
          {((warningTag && warningMessageState && playerReady && !isMobile) ||
            (currentEpisode?.hasPVOD && contentDetail?.permission === PERMISSION.CAN_WATCH)) && (
            <ContentTags
              warningTag={warningTag}
              warningMessage={isLiveStream && isTrailer ? '' : warningMessage}
              warningLocation={warningLocation}
              currentTime={this.video?.currentTime}
              duration={this.video?.duration}
              isHBO={isHBO}
              progress={contentDetail?.progress}
              isLiveStream={isLiveStream}
              positionLiveCCU={positionLiveCCU}
              warningMessageState={warningMessageState}
              buttonLive={buttonLive}
              isFullscreen={isFullscreen}
              showCCU={showCCU}
              playerReady={playerReady}
              showController={showController}
              content={content}
              contentDetail={contentDetail}
              currentEpisode={currentEpisode}
              isSeeking={this.isSeeking}
              isAdsEnd={isAdsEnd}
              hasAds={!isEmpty(this.props.ads)}
              isAdError={this.state.isAdError}
            />
          )}
          {(isController || (isShowEndScreenWithTrialContent && isOnEnded)) &&
            !isLive &&
            !isPrevent && (
              <PlayerControl
                {...this.props}
                {...this.state}
                isMobile={this.props.isMobile}
                subtitle={subtitleSelected}
                warningMessageState={warningMessageState}
                isLiveTV={isLiveTV}
                isAdsEnd={isAdsEnd}
                isLiveStream={isLiveStream}
                isDVR={isDVR}
                isFreeTrial={isFreeTrial}
                settingData={settingData}
                isOuttro={isOuttro}
                isOnEnded={isOnEnded}
                endScreenData={endScreenData}
                currentEpisode={currentEpisode}
                content={content}
                contentDetail={contentDetail}
                // Props
                isPaused={isPaused || this.state.isPaused}
                displayControl={displayControl}
                isPremiereLive={isPremiereLive}
                isFullscreen={isFullscreen}
                // Events
                controlSettingScreen={this.controlSettingScreen}
                onPlayPause={this.onPlayPause}
                onClickVolume={this.onClickVolume}
                onVoluming={this.onVoluming}
                onControlSeeking={this.onControlSeeking}
                onControlSeekChange={this.onControlSeekChange}
                onFullscreen={this.onControlFullscreen}
                onControlFullscreen={this.onControlFullscreen}
                onControlThumbnail={this.onControlThumbnail}
                onSetSubtitle={this.onSetSubtitle}
                onSetAudio={this.onSetAudio}
                onSetQuality={this.onSetQuality}
                onSendReport={this.onSendReport}
                handleShowController={this.handleShowController}
                onClickLive={this.onClickLive}
                setupEpisode={this.setupEpisode}
                controlOpenEpisode={this.controlOpenEpisode}
                onBackward={this.onBackward}
                onForward={this.onForward}
                onCheckInfoDebugPlayer={this.onCheckInfoDebugPlayer}
                setOpenEpisode={this.setOpenEpisode}
                currentTime={this.video?.currentTime}
                video={this.video}
                duration={this.video?.duration}
                endScreenItems={recommendData?.bodyData?.slickData}
                isShowEndScreen={isShowEndScreen}
                isAdPlay={isAdPlay}
                dataEventDetails={contentDetail}
                isEndScreenVod={!isFreeTrial && (isOuttro || isOnEnded) && !isAdPlay}
                isWarningScreen={isWarningScreen}
                triggerSource={triggerSource}
              />
            )}
          {playerReady && !isLive && !isPremiereLive && isDVR && (
            <div className="player__status position absolute top-left" id={EL_ID.TAG_PREMIERE}>
              <label className="tags tags--red-gradient tags--live tags--box style--uppercase">
                <span className="text">PREMIERE</span>
              </label>
            </div>
          )}
          {!isFreeTrial && !globalLimited && isIntro && !isAdPlay && (
            <Button
              className="player__button player__button--light player__button--skip player__button--skip-intro absolute bottom-right layer-4"
              theme="primarySolid"
              size={isMobile ? 'medium' : 'large'}
              customizeClass="px-3 md:px-5 xl:px-8 !text-[1rem] md:!text-[1.125rem]"
              title={TEXT.SKIP_INTRO}
              onClick={this.props.isMobile ? onSkipIntroTouch : onSkipIntro}
              onTouchStart={onSkipIntroTouch}
            />
          )}
          {!isFreeTrial &&
            (isOuttro || isOnEnded || isShowEndScreenWithTrialContent) &&
            !isAdPlay && (
              <PlayerDraw
                isAdPlay={isAdPlay || playingAds}
                isMobile={this.props.isMobile}
                isOuttro={isOuttro}
                endScreenData={endScreenData}
                onSkipIntro={onSkipIntro}
                isOnEnded={isOnEnded}
                video={this.video}
                contentDetail={contentDetail}
                showController={showController}
                isFullscreen={isFullscreen}
                isCollapseVisible={this.state?.isCollapseVisible}
                setCollapseVisible={this.setCollapseVisible}
                isShowEndScreenWithTrialContent={isShowEndScreenWithTrialContent}
              />
            )}
          {(!isEndScreen || isFreeTrial) && !playingAds && <Subtitles ref={this.subtitleRef} />}
          {adsEnable && (
            <>
              <div
                ref={(el: any) => (this.adContainer = el)}
                id={EL_ID.ADS_CONTAINER}
                className={`absolute transition-all animate-fade-in full control-ads player__ads layer-7 ${
                  typeShowAds !== 2 && !isMobile ? styles.hFull : '!h-auto'
                }`}
                style={{
                  display: isAdPlay ? 'block' : 'none'
                }}
              />
              {isShowInfoDebug && (this.player || this.video) && playerName && (
                <GetInfoDebugPlayer
                  player={this.player}
                  video={this.video}
                  playerName={playerName}
                  linkPlay={this.state.linkPlay}
                  onCheckInfoDebugPlayer={this.onCheckInfoDebugPlayer}
                />
              )}

              {isAdPlay && (
                <div
                  id={EL_ID.PLAY_ADS_BUTTON}
                  className={`player__controls-highway absolute layer-8${
                    isAdsPaused ? '' : ' hide'
                  }`}
                  style={{
                    height: '20%',
                    top: '40%'
                  }}
                >
                  <Button
                    className="player__button player__button-play"
                    iconName="vie vie-play-solid-rc"
                  />
                </div>
              )}
              {isAdPlay > 0 && <AdsSlot adsCount={adsCount} adsSlot={adsSlot} />}

              {isAdPlay && timeSkip > 0 && (
                <SkipAds
                  adsSlot={adsSlot}
                  isAdsPaused={isAdsPaused}
                  skipSecond={timeSkip}
                  onSkipAds={this.onSkipAds}
                  isMobile={this.props.isMobile}
                />
              )}
            </>
          )}
          {!isLive &&
            !isPremiere &&
            !isLiveStream &&
            playerReady &&
            (!profile?.id || (profile?.id && !profile.isPremium && !currentProfile?.isKid)) &&
            !isGlobal &&
            content?.category !== CONTENT_TYPE.SHORT_CONTENT &&
            !isMobile && (
              <PiPAds
                videoCurrentTime={this.video?.currentTime}
                dataEventDetails={contentDetail}
                isPaused={isPaused || this.state.isPaused}
                isAdPlay={isAdPlay}
                isAdsPaused={isAdsPaused}
                isEndscreen={isOuttro || isOnEnded}
                isIntro={isIntro}
              />
            )}
        </div>
      </div>
    );
  }
}

const mapDispatchToProps = (dispatch: any) => ({
  dispatch
});

export default connect(null, mapDispatchToProps)(Player);
