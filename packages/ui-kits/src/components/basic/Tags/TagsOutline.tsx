import React from 'react';
import { TEXT } from '@vieon/core/constants/text';
import Tags from './Tags';
import Styles from './Tag.module.scss';
import { EL_SIZE_CLASS, EL_THEME_CLASS } from '@vieon/core/constants/constants';

const TagsOutline = React.memo(
  ({ isVideoIndexing, tagArray, className, txtClass, arrayLimit, theme, size }: any) => {
    if ((tagArray || []).length === 0) return null;

    const arrayHandle = arrayLimit ? (tagArray || []).slice(0, arrayLimit) : tagArray || [];
    if (isVideoIndexing) arrayHandle?.push({ name: TEXT.CONTENT_INTERACT });
    return arrayHandle.map((item: any, index: any) => (
      <Tags
        className={className}
        key={index}
        subClass={Styles.divide}
        description={item?.title || item?.name}
        tagKey={item?.tagKey}
        spClass={item?.spClass}
        txtClass={txtClass}
        iClass={item?.iClass || ''}
        theme={theme || EL_THEME_CLASS.DEFAULT}
        size={size || EL_SIZE_CLASS.MEDIUM}
      />
    ));
  }
);

export default TagsOutline;
