import React, { useEffect, useRef } from 'react';
import { EL_PROPERTY, EL_THEME_CLASS } from '@vieon/core/constants/constants';
import classNames from 'classnames';
import Styles from './Tag.module.scss';

const positionClasses = {
  [EL_PROPERTY.TOP_LEFT]: 'absolute top left',
  [EL_PROPERTY.TOP_RIGHT]: 'absolute top right',
  [EL_PROPERTY.BOTTOM_LEFT]: 'absolute bottom left',
  [EL_PROPERTY.BOTTOM_RIGHT]: 'absolute bottom right',
  [EL_PROPERTY.BOTTOM_CENTER]: 'absolute middle'
};

const TagContent = ({ size, position, text, theme, className, isUpperCase, rounded }: any) => {
  const isClient = useRef(false);
  useEffect(() => {
    isClient.current = true;
  }, []);

  if (!isClient || !text) return null;

  return (
    <label
      className={classNames(
        Styles.Base,
        Styles[size],
        Styles[theme],
        positionClasses[position],
        className,
        Styles[rounded]
      )}
    >
      <span
        className={classNames(theme === EL_THEME_CLASS.DEFAULT ? 'font-medium' : 'font-semibold')}
      >
        {isUpperCase ? text.toUpperCase() : text}
      </span>
    </label>
  );
};

export default React.memo(TagContent);
