import React from 'react';
import {
  CURRENCY,
  EL_PROPERTY,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  TAG_KEY,
  TAG_VIP,
  TAG_VIP_LABEL
} from '@vieon/core/constants/constants';
import ConfigImage from '@vieon/core/config/ConfigImage';
import Icon from '../components/basic/Icon/Icon';
import NewIcon from '../components/basic/Icon/NewIcon';
import Image from '../components/basic/Image/Image';
import TagContent from '../components/basic/Tags/TagContent';
import classNames from 'classnames';
import { isMobile } from 'react-device-detect';
import Styles from './Tag.module.scss';
import { TEXT } from '@vieon/core/constants/text';

const positionClasses = {
  [EL_PROPERTY.TOP_LEFT]: 'absolute top left',
  [EL_PROPERTY.TOP_RIGHT]: 'absolute top right',
  [EL_PROPERTY.BOTTOM_LEFT]: 'absolute bottom left',
  [EL_PROPERTY.BOTTOM_RIGHT]: 'absolute bottom right',
  [EL_PROPERTY.BOTTOM_CENTER]: 'absolute left-1/2 bottom -translate-x-1/2 max-w-[85%]'
};

const Tags = ({
  title,
  isPremiumDisplay,
  unit,
  tagKey,
  subClass,
  iClass,
  iImage,
  content,
  price,
  txtClass,
  description,
  position,
  size: externalSize,
  spClass,
  tagContentClass,
  theme: externalTheme,
  rounded: externalRounded,
  isNewIcon,
  iconName
}: any) => {
  if (Object.values(TAG_VIP).includes(isPremiumDisplay)) {
    return (
      <TagContent
        className={classNames(tagContentClass)}
        position={position}
        size={externalSize || EL_SIZE_CLASS.MEDIUM}
        theme={externalTheme || EL_THEME_CLASS.GOLD}
        text={TAG_VIP_LABEL[isPremiumDisplay] || ''}
        rounded={externalRounded || ''}
      />
    );
  }

  const getTagAttributes = (key: any) => {
    const attributes = {
      iconClass: iClass,
      tagTitle: title,
      className: '',
      iconImage: iImage,
      tagAlt: '',
      textClass: '',
      theme: externalTheme || EL_THEME_CLASS.DEFAULT,
      liveEvent: '',
      size: externalSize || EL_SIZE_CLASS.MEDIUM,
      rounded: externalRounded || ''
    };

    switch (key) {
      case TAG_KEY.VIP:
        attributes.theme = EL_THEME_CLASS.GOLD;
        attributes.iconClass = 'vie-vip';
        break;
      case TAG_KEY.PRICE:
        attributes.theme = EL_THEME_CLASS.BLUE_TVOD;
        break;
      case TAG_KEY.WATCH_SOON:
      case TAG_KEY.PARALLEL:
      case TAG_KEY.NEW:
        attributes.theme = EL_THEME_CLASS.GREEN;
        break;
      case TAG_VIP.VIP_K:
        attributes.iconImage = ConfigImage.kplusICO;
        attributes.theme = EL_THEME_CLASS.GOLD;
        attributes.tagAlt = 'Kplus iCO';
        break;
      case TAG_VIP.VIP_HBO:
        attributes.iconImage = ConfigImage.hboGoICO;
        attributes.theme = EL_THEME_CLASS.GOLD;
        attributes.tagAlt = 'HBO Go';
        break;
      case TAG_VIP.VIP_PRO:
        attributes.theme = EL_THEME_CLASS.GOLD;
        attributes.iconClass = 'vie-pro';
        break;
      case TAG_KEY.LIVE:
        attributes.tagTitle = (title || 'Live').toUpperCase();
        attributes.theme = EL_THEME_CLASS.RED_LIVE;
        break;
      case TAG_KEY.TOP_VIEW:
        attributes.theme = EL_THEME_CLASS.IMAGE;
        break;
      case TAG_KEY.REMIND:
        attributes.theme = EL_THEME_CLASS.DEFAULT;
        break;
      default:
        break;
    }

    return attributes;
  };

  const { iconClass, tagTitle, className, iconImage, tagAlt, theme, size, rounded } =
    getTagAttributes(tagKey);

  return (
    <label
      className={classNames(
        Styles.Base,
        className,
        Styles[externalTheme || theme],
        Styles[externalSize || size],
        Styles.Truncate,
        subClass,
        positionClasses[position],
        Styles[externalRounded || rounded],
        // (tagKey === TAG_KEY.TOP_VIEW || tagKey === TAG_KEY.NEW) && '!h-max !p-0'
        tagKey === TAG_KEY.TOP_VIEW && '!h-max !p-0'
      )}
    >
      {!isNewIcon && !tagKey && iconClass && <Icon spClass={spClass} iClass={iconClass} />}
      {!tagKey && iconName && isNewIcon && (
        <NewIcon iCustomizeClass={spClass} iconName={iconName} />
      )}
      {unit && <sup>{unit}</sup>}
      {tagKey === TAG_KEY.TOTAL_CCU && iconClass && (
        <span className="icon icon--tiny !flex">
          <i className={`vie ${iconClass}`} />
        </span>
      )}
      {iconClass && tagKey && tagKey !== TAG_KEY.TOTAL_CCU && tagKey !== TAG_KEY.REMIND && (
        <i className={`vie ${iconClass}`} />
      )}
      {iconImage && (
        <img
          src={iconImage}
          alt={tagAlt || tagTitle}
          title={tagAlt || tagTitle}
          width="..."
          height="..."
        />
      )}
      {tagTitle && (
        <span
          className={classNames(theme === EL_THEME_CLASS.DEFAULT ? 'font-normal' : 'font-semibold')}
        >
          {tagTitle}
        </span>
      )}
      {tagKey === TAG_KEY.TOP_VIEW && (
        <Image
          className={classNames(size === EL_SIZE_CLASS.LARGE && !isMobile ? 'w-full' : 'w-[35px]')}
          src={ConfigImage.top10Tag}
          alt="Top 10 Tag"
          notWebp
        />
      )}
      {tagKey === TAG_KEY.NEW && <span className="font-semibold uppercase">{TEXT.NEW}</span>}
      {tagKey === TAG_KEY.PARALLEL && <span className="font-semibold">{content}</span>}
      {(tagKey === TAG_KEY.TOTAL_CCU || price || description) && (
        <span
          className={classNames(
            txtClass,
            theme === EL_THEME_CLASS.DEFAULT ? 'font-medium' : 'font-semibold'
          )}
        >
          {tagKey === TAG_KEY.TOTAL_CCU && <span>{content}</span>}
          {price && (
            <span className="pr-0.5">
              Thuê {price}
              {CURRENCY.D}
            </span>
          )}
          {description ?? ''}
        </span>
      )}
    </label>
  );
};

export default Tags;
