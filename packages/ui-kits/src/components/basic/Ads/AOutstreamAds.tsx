import React, { useEffect, useState } from 'react';
import Image from '../components/basic/Image/Image';
import isEmpty from 'lodash/isEmpty';
import { aiActiveThirdTracking } from '@vieon/core/api/aiactiv-third-tracking';

const AOutstreamAds = ({
  inventoryId,
  onSlotRenderEnded,
  renderCustomAds,
  adId,
  renderCustomAdsClass = '',
  allowPath
}: any) => {
  const [adsData, setAdsData] = useState<any>(null);

  useEffect(() => {
    if (inventoryId) {
      setTimeout(requestAds, 100);
    }
  }, [inventoryId, allowPath]);

  const requestAds = async () => {
    if (!window.AiactivSDK) {
      return;
    }
    try {
      await window.AiactivSDK.requestAds([
        {
          inventoryId: parseInt(inventoryId),
          placementId: `div-${inventoryId}`
        }
      ]).then((res: any) => {
        handleLoaded({ res });
      });
    } catch (error) {
      handleLoaded({ error });
    }
  };

  const handleLoaded = ({ res }: any) => {
    const data = res?.[0];
    const type = data?.type;
    const success = type === TYPE.NATIVE ? !isEmpty(data?.native) : !isEmpty(data?.seatbid);
    if (onSlotRenderEnded) {
      onSlotRenderEnded({
        ...data,
        success
      });
    }

    setAdsData({
      ...data,
      type,
      success
    });
  };

  const handleClickBanner = () => {
    if (adsData?.native?.link?.clickTrackers?.[0]) {
      window.open(adsData?.native?.link?.clickTrackers?.[0], '_blank');
    }
  };

  const renderNative = () => {
    if (adsData?.type === TYPE.NATIVE && adsData && adsData?.success) {
      if (renderCustomAds) {
        return <div id={`div-${inventoryId}`}>{renderCustomAds(adsData)}</div>;
      }
      if (adsData?.native?.previewImage?.url) {
        aiActiveThirdTracking({
          vadeUrl: adsData?.vade,
          events: adsData?.native?.events
        });
        return (
          <div id={`div-${inventoryId}`}>
            <Image
              id={adId}
              className="cursor-pointer w-full"
              src={adsData?.native?.previewImage?.url || ''}
              alt=""
              notWebp
              onClick={handleClickBanner}
            />
          </div>
        );
      }
    }
    return null;
  };

  return (
    <div
      className={!adsData?.success ? 'hidden' : renderCustomAdsClass || ''}
      id={`div-${inventoryId}`}
    >
      {renderNative()}
    </div>
  );
};

const TYPE = {
  BANNER: 'banner',
  NATIVE: 'native'
};

export default AOutstreamAds;
