import React, { useEffect, useState } from 'react';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { createTimeout } from '@vieon/core/utils/common';
import Slider from '../Slider/Slider';
import CardVodIntro from '../Card/CardVodIntro';

const RibbonVodIntro = ({
  title,
  id,
  data,
  ribbonType,
  currentEpisode,
  page,
  filterDropDownDetail,
  onClickNextEpisode,
  onClickPrevEpisode
}: any) => {
  const [state, setState] = useState<{ swiper: any; isReady: any }>({ swiper: null, isReady: 1 });
  useEffect(() => {
    setState({ ...state, isReady: false });
    const timer = createTimeout(() => {
      setState({ ...state, isReady: true });
    }, 200);
    return () => clearTimeout(timer);
  }, [id]);

  const swiperParams: any = {
    slidesPerView: 4,
    slidesPerGroup: 4
  };
  const renderSliderItem = () => {
    const isPoster = ribbonType === 1 || ribbonType === 8;
    const thumbClass = isPoster
      ? 'card__thumbnail-loader overflow ratio-1-2'
      : 'card__thumbnail-loader overflow ratio-16-9';
    return (
      <>
        {(data || []).map((item: any, idx: any) => {
          const itemData = {
            title: item?.title,
            imgSrc: isPoster
              ? item?.images?.poster_v4
              : item?.images.thumbnail_v4 || item?.images.thumbnail,
            defaultImgSrc: isPoster ? ConfigImage.defaultBanner16x9 : ConfigImage.defaultPoster,
            href: item?.seo?.url,
            url: item?.seo?.url,
            description: item?.seo?.description,
            releaseYear: item?.release_year,
            country: item?.tags?.[0]?.name,
            relatedSeason: item?.related_season,
            isPremium: item?.is_premium
          };
          return (
            <div className="swiper-slide slider__item" key={item?.id || idx}>
              <CardVodIntro {...itemData} thumbClass={thumbClass} id={id} page={page} />
            </div>
          );
        })}
      </>
    );
  };
  if (!data?.length || data?.length === 0) return null;
  if (!state.isReady) return null;

  return (
    <div className="rocopa">
      <div className="rocopa__header">
        <div className="rocopa__header-left">
          <h2 className="rocopa__title">{title}</h2>
          {id === `idEpisode_${page}` && (
            <ul className="list episodes-list text-white">
              <li>
                <span className="tags tags--outline tags--outline-v" title="2020">
                  <span>{currentEpisode}</span>
                </span>
              </li>
              <li>
                <span className="tags tags-item">Phát sóng 20h thứ 4, 5 hàng tuần</span>
              </li>
            </ul>
          )}
        </div>
        <div className="rocopa__header-right">{filterDropDownDetail && filterDropDownDetail()}</div>
      </div>
      <div className="rocopa__body">
        <Slider
          onClickNextEpisode={onClickNextEpisode}
          onClickPrevEpisode={onClickPrevEpisode}
          renderSliderItem={renderSliderItem}
          data={data}
          setting={swiperParams}
          id={id}
          className=" "
          dataViewItem={4}
        />
      </div>
    </div>
  );
};
export default RibbonVodIntro;
