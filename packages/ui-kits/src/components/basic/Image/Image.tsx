import React, { useEffect, useState } from 'react';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { parseWebpSrc } from '@vieon/core/utils/common';
import classnames from 'classnames';
import { useInView } from 'react-intersection-observer';

const Image = (props: any) => {
  const {
    id,
    src: srcProp,
    defaultSrc,
    alt,
    className,
    notWebp = false,
    title,
    imageLazyLoad,
    imageInView,
    width,
    height,
    style,
    onClick
  } = props || {};

  const [loaded, setLoaded] = useState(false);
  const imageDefault = defaultSrc || ConfigImage.defaultBanner16x9;
  const [ref, inView] = useInView({ threshold: 0 });
  // const { isWebPSupported } = useSelector((state: any) => state?.App);
  const [src, setSrc] = useState(!notWebp ? parseWebpSrc(srcProp || imageDefault) : srcProp);
  // Cover image to webp on safari
  useEffect(() => {
    if (!notWebp) {
      setSrc(parseWebpSrc(srcProp));
    } else {
      setSrc(srcProp);
    }
  }, [notWebp, srcProp]);

  // handle onLoad, onError
  const handleLoaded = () => setLoaded(true);
  const handleError = () => {
    setSrc(imageDefault);
  };

  // render style
  const styleImage = imageLazyLoad ? (loaded ? '' : { display: 'none' }) : '';

  // Render image
  const renderImage = () => (
    <img
      id={id}
      className={rootClass}
      style={{ ...styleImage, ...style }}
      src={src}
      onLoad={handleLoaded}
      onError={handleError}
      alt={alt || 'VieON Image'}
      title={title}
      width={width}
      height={height}
      onClick={onClick}
    />
  );

  // use case image view
  const renderImageView = () => {
    if (imageLazyLoad) {
      return (
        <span ref={ref} className={`imag-lazy-load ${!loaded ? 'skeleton pulse' : ''}`}>
          {(inView || loaded) && renderImage()}
        </span>
      );
    }
    if (imageInView && !imageLazyLoad) {
      return (
        <span ref={ref} className="imag-lazy-load">
          {inView && renderImage()}
        </span>
      );
    }
    return renderImage();
  };

  // Define class
  const rootClass = classnames(
    'image-block',
    {
      'animate-fade-in duration': loaded && imageLazyLoad
    },
    className
  );

  return renderImageView();
};

export default React.memo(Image);
