import React from 'react';
import { POSITION, TOAST } from '@vieon/core/constants/constants';
import ToastItem from '../components/basic/Toast/ToastItem';
import classNames from 'classnames';
import styles from './toast.module.scss';

const ToastGroup = ({ position, data }: any) => {
  if ((data || []).length === 0) return null;
  return (
    <div
      className={classNames(
        data[0]?.kind === TOAST.KIND.TIMER && styles.toast__timer__group,
        'toast-group layer-max',
        POSITION_CLASS[position || POSITION.TOP_RIGHT]
      )}
    >
      {(data || []).map((item: any, idx: any) =>
        item?.renderCustom ? item?.renderCustom(item) : <ToastItem key={idx} {...item} />
      )}
    </div>
  );
};

const POSITION_CLASS = {
  [POSITION.TOP_LEFT]: 'top-left',
  [POSITION.TOP_RIGHT]: 'top-right',
  [POSITION.BOTTOM_LEFT]: 'bottom-left',
  [POSITION.BOTTOM_RIGHT]: 'bottom-right'
};

export default React.memo(ToastGroup);
