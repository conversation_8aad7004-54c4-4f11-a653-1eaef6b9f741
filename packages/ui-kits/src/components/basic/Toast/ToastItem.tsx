import React, { useEffect, useMemo, useState } from 'react';
import Button from '../components/basic/Buttons/Button';
import Icon from '../components/basic/Icon/Icon';
import {
  toastEventHappeningBrowsingLoaded,
  toastEventHappeningBrowsingSelect,
  playerEventHappeningBrowsingLoaded,
  playerEventHappeningBrowsingSelect
} from '@vieon/tracking/functions/TrackingTVodToastReminder';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import { useVieRouter } from '@customHook';
import { EL_CLASS, LOYALTY_STATUS, PAGE, TIER, TOAST, TOAST_KEY, TVOD } from '@vieon/core/constants/constants';
import ConfigImage from '@vieon/core/config/ConfigImage';
import Image from '../Image/Image';
import styles from './toast.module.scss';

const ToastItem = ({
  title,
  content,
  image,
  type,
  message,
  btnText,
  btnClick,
  noIcon,
  toastKey,
  spClass,
  iClass,
  duration,
  animation,
  kind,
  className,
  messageClassName,
  beforeClass,
  imgSrcRight,
  btnClassname
}: any) => {
  const router = useVieRouter();
  const preorderReminder = useSelector((state: any) => state?.User?.preorderReminder);
  const { USER_TYPE } = useSelector((state: any) => state?.User);
  const [progress, setProgress] = useState(0);
  const userType = USER_TYPE;

  let strokeArray = 300; // stroke array

  const pathnameHavePlayer =
    router.pathname === PAGE.VOD ||
    router.pathname === PAGE.LIVE_STREAM ||
    router.pathname === PAGE.LIVE_STREAM_SLUG ||
    router.pathname === PAGE.LIVE_TV ||
    router.pathname === PAGE.LIVE_TVOD_SLUG ||
    router.pathname === PAGE.LIVE_TV_EPG ||
    router.pathname === PAGE.LIVE_TV_SLUG;

  // Functions
  const renderTimerToast = () => {
    const loyaltyImage = useMemo(() => {
      const map = {
        [LOYALTY_STATUS.SUCCESS]: ConfigImage.exchangeSuccessfully,
        [LOYALTY_STATUS.FAILED]: ConfigImage.exchangeFailed,
        [TIER.SILVER]: ConfigImage.promoteToSilver,
        [TIER.GOLD]: ConfigImage.promoteToGold,
        [TIER.DIAMOND]: ConfigImage.promoteToDiamond
      };
      return map[type] || ConfigImage.exchangeFailed;
    }, [type]);

    return (
      <div className={`relative grid-x align-items-center ${styles.toast__timer__info}`}>
        <div className={`absolute right-2 top-2 ${styles.toast__timer__button}`}>
          <button className={`button relative ${styles.toast__timer__button}`} onClick={onClickBtn}>
            <i className="vie vie-times-medium relative layer-max font-size-12" />
            <div className={styles['toast__timer__button--icon']}>
              <div className="relative">
                <svg viewBox="0 0 100 100">
                  <path
                    d="M 50,50 m 0,-47 a 47,47 0 1 1 0,94 a 47,47 0 1 1 0,-94"
                    stroke="#3AC882"
                    strokeWidth="8"
                    fillOpacity="0"
                    style={{ strokeDasharray: `${progress || 300}, 300` }}
                  />
                </svg>
              </div>
            </div>
          </button>
        </div>
        <div className="cell large-auto small-5">
          <Image src={image || loyaltyImage} notWebp />
        </div>
        <div
          className={`cell large-shrink small-7 text text-white ${styles.toast__timer__info__content}`}
        >
          <h1 className="margin-bottom-6 font-size-24 font-size-sm-down-14 text-bold">{title}</h1>
          <div
            className={`font-size-16 text-gray239 ${styles['toast__timer__info__content--description']}`}
            dangerouslySetInnerHTML={{
              __html: content
            }}
          />
        </div>
      </div>
    );
  };

  const timerInterval = (loadingTime?: any) =>
    setInterval(() => {
      strokeArray -= 3;
      setProgress(strokeArray);
    }, loadingTime);

  const onClickBtn = () => {
    if (typeof btnClick === 'function') btnClick();

    if (toastKey === TOAST_KEY.REMINDER) {
      if (pathnameHavePlayer) {
        playerEventHappeningBrowsingSelect({
          contentData: preorderReminder[0] || {},
          userType: userType?.userType,
          flowName: TVOD.TOAST.EVENT_HAPPENING_REMINDER
        });
      } else {
        toastEventHappeningBrowsingSelect({
          contentData: preorderReminder[0] || {},
          userType: userType?.userType,
          flowName: TVOD.TOAST.EVENT_HAPPENING_REMINDER
        });
      }
    }
  };

  // Hooks
  useEffect(() => {
    if (toastKey === TOAST_KEY.REMINDER) {
      if (pathnameHavePlayer) {
        playerEventHappeningBrowsingLoaded({
          contentData: preorderReminder[0] || {},
          userType: userType?.userType,
          flowName: TVOD.TOAST.EVENT_HAPPENING_REMINDER
        });
      } else {
        toastEventHappeningBrowsingLoaded({
          contentData: preorderReminder[0] || {},
          userType: userType?.userType,
          flowName: TVOD.TOAST.EVENT_HAPPENING_REMINDER
        });
      }
    }
  }, []);

  useEffect(() => {
    const loadingTime = duration / 100;
    if (kind === TOAST.KIND.TIMER) timerInterval(loadingTime);
    return () => {
      clearInterval(timerInterval());
    };
  }, [kind]);

  const animateClass = useMemo(() => EL_CLASS[animation] || EL_CLASS.SLIDE_IN_RIGHT, [animation]);

  let iconClass = 'vie-bell-o-rc';
  let spanClass = 'icon-extend';
  if (spClass) spanClass = spClass;
  if (iClass) iconClass = iClass;

  return (
    <div
      className={classNames(
        'overflow toast size-mw-550 size-min-w-300 toast-line-vertical toast--custom dark box-shadow-lv1 m-b1',
        kind === TOAST.KIND.TIMER && styles.toast__timer,
        type,
        animateClass,
        className
      )}
    >
      {!noIcon && <Icon spClass={`${spanClass} absolute text-white`} iClass={iconClass} />}

      {kind === TOAST.KIND.TIMER ? (
        renderTimerToast()
      ) : (
        <div className={classNames('toast__body grid-x', noIcon && 'p-l4', beforeClass)}>
          {message && (
            <div className="cell auto">
              <div className={classNames('text text-white', messageClassName)}>{message}</div>
            </div>
          )}
          {(btnText || imgSrcRight) && (
            <div className="cell shrink">
              <Button
                className={classNames(
                  'button button--light button--medium-up button--small-up',
                  btnClassname
                )}
                title={btnText || ''}
                onClick={onClickBtn}
                imgSrcRight={imgSrcRight}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

ToastItem.defaultProps = {
  type: 'info'
};

export default React.memo(ToastItem);
