import React from 'react';
import { useSelector } from 'react-redux';
import ToastGroup from '../components/basic/Toast/ToastGroup';
import { POSITION } from '@vieon/core/constants/constants';

const ToastHandler = () => {
  const toastData = useSelector((state: any) => state?.App?.toastData);
  const { topLeft, topRight, bottomLeft, bottomRight } = toastData || {};

  return (
    <>
      <ToastGroup data={topLeft} position={POSITION.TOP_LEFT} />
      <ToastGroup data={topRight} position={POSITION.TOP_RIGHT} />
      <ToastGroup data={bottomLeft} position={POSITION.BOTTOM_LEFT} />
      <ToastGroup data={bottomRight} position={POSITION.BOTTOM_RIGHT} />
    </>
  );
};

export default React.memo(ToastHandler);
