import { TEXT } from '@vieon/core/constants/text';
import React from 'react';
import LogoVieSvg from './LogoVieSvg';
import LogoVieSvgForLight from './LogoVieSvgForLight';
import ConfigCookie from '@/config/ConfigCookie';
import { destinationLogin } from '@/services/multiProfileServices';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@/customHook';
import { PAGE } from '@/constants/constants';

const Logo = ({ className, title, onClickLogo, forLight }: any) => {
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { currentProfile } = useSelector((state: any) => state?.MultiProfile || {});
  const router = useVieRouter();
  const dispatch = useDispatch();
  const destination = router?.query?.destination || '/';
  const pathname = router?.pathname;

  const clickLogo = (e: any) => {
    if (onClickLogo) onClickLogo(e);
    else if (!currentProfile?.id) {
      if (pathname?.includes(PAGE.PAYMENT_RESULT)) {
        const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

        destinationLogin({
          dataLogin: {
            profile,
            accessToken
          },
          destination,
          router,
          dispatch
        });
      }
      window.location.href = '/';
    } else {
      window.location.href = '/';
    }
  };

  return (
    <a
      className={className || 'logo logo--top'}
      onClick={clickLogo}
      tabIndex={-1}
      title={title || TEXT.SLOGAN}
    >
      {forLight ? <LogoVieSvgForLight /> : <LogoVieSvg />}
    </a>
  );
};

export default React.memo(Logo);
