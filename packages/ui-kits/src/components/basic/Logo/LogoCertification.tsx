import React from 'react';
import { TEXT } from '@vieon/core/constants/text';

const LogoCertification = ({
  className = '',
  title,
  href,
  target = '_blank',
  src,
  onClickLogo,
  altImg = '',
  style
}: any) => (
  <a
    className={className}
    onClick={onClickLogo}
    title={title || TEXT.SLOGAN}
    href={href}
    target={target}
    rel="noopener noreferrer"
  >
    <img className={className} src={src} alt={altImg || title} style={style} />
  </a>
);

export default React.memo(LogoCertification);
