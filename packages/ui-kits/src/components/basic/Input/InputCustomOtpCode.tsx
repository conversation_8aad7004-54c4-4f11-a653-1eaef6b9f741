import React, { useEffect, useRef, useState } from 'react';
import { KEY_CODE } from '@vieon/core/constants/constants';

const InputCustomOtpCode = React.memo(
  ({
    id,
    type,
    onEnter,
    onChangeOtp,
    handleInput,
    autoComplete,
    inputClass,
    isPinCode,
    onFocusBack,
    onFocusJump,
    keyLimit
  }: any) => {
    const [values, setValues] = useState<any>([]);
    const [currentInputIndex, setCurrentInputIndex] = useState<any>(1);
    const timerRef = useRef<any>(null);
    const focusFirstInputRef = useRef<any>(null);

    useEffect(() => {
      window.addEventListener('keydown', handleKeyDown);
      const firstInputOtp = document?.getElementById('otpPinCode1');
      clearTimeout(focusFirstInputRef.current);
      focusFirstInputRef.current = setTimeout(() => {
        if (firstInputOtp && !onFocusBack && !onFocusJump) {
          firstInputOtp?.focus();
        }
      }, 100);

      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        if (isPinCode) {
          clearTimeout(timerRef.current);
          clearTimeout(focusFirstInputRef.current);
        }
      };
    }, []);

    useEffect(() => {
      const firstInput = document?.getElementById(id + 1);
      const lastInputOtp = document?.getElementById('otpPinCode4');
      const firstInputOtpConfirm = document?.getElementById('otpPinCodeConfirm1');
      if (isPinCode) {
        if (lastInputOtp && onFocusBack && !onFocusJump) {
          lastInputOtp?.focus();
          return;
        }
        if (firstInputOtpConfirm && onFocusJump) {
          firstInputOtpConfirm?.focus();
          return;
        }
      } else {
        firstInput?.focus();
      }

      if (typeof handleInput === 'function') handleInput({ resetInput });
    }, [id, onFocusBack, onFocusJump]);

    const handleKeyDown = (e: any) => {
      if (e?.keyCode === KEY_CODE.BACK_SPACE) {
        const element = e?.target;
        if ((element?.id || '').includes('smartTVCODE')) {
          if (!element?.value && element?.previousSibling) {
            e.target.previousSibling.focus();
          }
        }
      }
    };

    const handleKeyPress = (e: any) => {
      const keyLength = e?.target?.value?.length;
      if (e.charCode === 13 && typeof onEnter === 'function') {
        onEnter(e);
      }
      if (keyLength >= keyLimit) {
        e.preventDefault();
      }
    };

    const resetInput = () => {
      setValues([]);
    };

    const handleChange = ({ e, index, inputId, number }: any) => {
      const { value } = e.target;
      const regexNumber = /\d/;
      const newValues: any = [...values];

      if (!value || (value && (regexNumber.test(value) || inputId === `smartTVCODE${number}`))) {
        newValues[index] = value || '';
      }
      if (
        value &&
        e.target.nextSibling &&
        (regexNumber.test(value) || inputId === `smartTVCODE${number}`)
      ) {
        e.target.nextSibling.focus();
      }

      if (e.target.previousSibling && !value) {
        e.target.previousSibling.focus();
      }

      setValues(newValues);
      if (typeof onChangeOtp === 'function') onChangeOtp(newValues.join(''));

      if (isPinCode) {
        setCurrentInputIndex(index);
        clearTimeout(timerRef.current);

        timerRef.current = setTimeout(() => {
          setCurrentInputIndex(null);
        }, 500);
      }
    };

    const handleBlur = () => {
      clearTimeout(timerRef.current);

      timerRef.current = setTimeout(() => {
        setCurrentInputIndex(null);
      }, 200);
    };

    return (
      <div className="input-group-custom">
        <div className="input-group input-group-digit">
          {[1, 2, 3, 4].map((number, index) => (
            <input
              className={inputClass}
              key={id + number}
              id={id + number}
              ref={timerRef}
              type={
                type || isPinCode ? (currentInputIndex === index ? 'text' : 'password') : 'text'
              }
              value={(values || [])[index] || ''}
              onChange={(e) => handleChange({ e, index, inputId: id + number, number })}
              maxLength={keyLimit || 1}
              onKeyPress={(e) => handleKeyPress(e)}
              onFocus={(e) => e.target.select()}
              onBlur={handleBlur}
              autoComplete={autoComplete || 'off'}
            />
          ))}
        </div>
      </div>
    );
  }
);

export default InputCustomOtpCode;
