import React from 'react';
import classNames from 'classnames';
import { EL_ID } from '@vieon/core/constants/constants';

const Input = React.forwardRef(
  (
    {
      handleOnChange,
      handleOnKeyPress,
      handleOnFocus,
      handleKeyDown,
      valInput,
      placeholder,
      inputClass,
      id,
      type,
      disabled,
      size,
      readOnly,
      icon,
      title,
      autoFocus
    }: any,
    ref: any
  ) => {
    return (
      <div className="relative flex items-center w-full">
        {icon && <span className="absolute top-1/2 left-[8px] -translate-y-1/2">{icon}</span>}
        <input
          ref={ref}
          className={classNames('shadow-none', inputClass || 'input-group')}
          id={id || EL_ID.SEARCH_INPUT}
          autoComplete="off"
          type={type || 'text'}
          value={valInput}
          placeholder={placeholder || ''}
          onChange={handleOnChange}
          onKeyPress={handleOnKeyPress}
          onKeyDown={handleKeyDown}
          onFocus={handleOnFocus}
          disabled={disabled || ''}
          readOnly={readOnly || ''}
          size={size || ''}
          title={title}
          autoFocus={autoFocus}
        />
      </div>
    );
  }
);

export default React.memo(Input);
