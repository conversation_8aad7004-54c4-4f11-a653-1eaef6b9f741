import React, { useState } from 'react';
import { TEXT } from '@vieon/core/constants/text';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import SvgIcon from '../components/basic/Icon/SvgIcon';

const InputCustom = ({
  label,
  id,
  type,
  icon,
  error,
  valueOutput,
  classInput,
  inputTagsClass,
  className,
  placeholder,
  onEnter,
  warning,
  errorOtp,
  onFocus,
  onBlur,
  autoComplete,
  onKeyUp,
  isErrOtpClassAbsolute,
  isErrClassAbsolute,
  errorClassName,
  defaultValue,
  autoFocus,
  isRequiredLabel,
  requiredLabelClassName,
  handleKeyDown,
  iconSvg,
  ...restProps
}: any) => {
  const [isFocus, setFocus] = useState(false);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);

  const inputGroupClass = classNames('input-group-custom', autoFocus && 'focus', className, {
    focus: (isFocus || valueOutput?.value !== '') && id !== 'otpCode',
    error,
    warning
  });
  const errorClass = classNames(isErrClassAbsolute && ` absolute bottom${isMobile ? '' : '-1'}`);
  const errorOtpClass = classNames(isErrOtpClassAbsolute && 'absolute bottom-2');
  const inputClass = classNames('text-white', inputTagsClass || 'input-group-field');

  const handleClickInput = () => {
    if (valueOutput?.value !== '' && isFocus) {
      setFocus(true);
      return;
    }
    setFocus(true);
  };

  const handleBlurInput = (e: any) => {
    if (valueOutput?.value !== '' && isFocus) {
      if (typeof onBlur === 'function') onBlur(e);
      setFocus(true);
      return;
    }
    setFocus(false);
  };
  const handleFocusInput = () => {
    if (valueOutput?.value !== '' && isFocus) {
      if (typeof onFocus === 'function') onFocus();
      setFocus(true);
      return;
    }
    setFocus(false);
  };

  const [isToggleDisplayPW, setToggleDisplayPW] = useState({
    display: false,
    type
  });

  const toggleDisplayPassword = (e: any) => {
    e.preventDefault();
    setToggleDisplayPW({
      display: !isToggleDisplayPW.display,
      type: !isToggleDisplayPW.display ? 'text' : type
    });
  };

  const handleKeyPressInput = (e: any) => {
    if (e.charCode === 13) {
      if (typeof onEnter === 'function') onEnter(e);
      e.preventDefault();
    }
  };
  const handleKeyDownInput = (e: any) => {
    if (typeof handleKeyDown === 'function') handleKeyDown(e);
  };

  const handleKeyUpInput = (e: any) => {
    if (typeof onKeyUp === 'function') onKeyUp(e);
  };

  return (
    <div className={inputGroupClass}>
      <div className={`input-group ${classInput || ''}`}>
        {icon && (
          <span className="icon absolute left icon--small p-t3">
            <i className={icon} />
          </span>
        )}
        {iconSvg && (
          <span className="icon absolute left icon--small p-t2 margin-small-up-left-2">
            <SvgIcon type={iconSvg} />
          </span>
        )}
        <label htmlFor={id} className="input-group-label pos pos-tl">
          {label}
          {isRequiredLabel && <span className={requiredLabelClassName}>*</span>}
        </label>
        <input
          className={inputClass}
          placeholder={placeholder}
          id={id}
          type={isToggleDisplayPW.type}
          defaultValue={defaultValue}
          {...valueOutput}
          onKeyPress={handleKeyPressInput}
          onClick={handleClickInput}
          onBlur={handleBlurInput}
          onFocus={handleFocusInput}
          onKeyUp={handleKeyUpInput}
          onKeyDown={handleKeyDownInput}
          autoComplete={autoComplete || 'new-password'}
          {...restProps}
        />
        {type === 'password' && (
          <div className="input-group-button action-show-pass">
            <button onClick={toggleDisplayPassword} className="button text-black">
              <i
                className={
                  isToggleDisplayPW.display
                    ? 'vie vie-eye-off-o-rc-medium'
                    : 'vie vie-eye-on-o-rc-medium'
                }
                style={{ fontSize: '1.1875rem' }}
              />
            </button>
          </div>
        )}
      </div>
      {errorOtp && (
        <label className={`form-error is-visible${errorOtpClass}`}>
          Mã không đúng. Vui lòng kiểm tra và thử lại
        </label>
      )}
      {error && (
        <label className={`form-error is-visible size-w-full${errorClass} ${errorClassName}`}>
          {error}
        </label>
      )}
      {warning && (
        <label id="capsLock" className="form-warning is-visible">
          {TEXT.CHECK_CAPS_LOCK}
        </label>
      )}
    </div>
  );
};

export default InputCustom;
