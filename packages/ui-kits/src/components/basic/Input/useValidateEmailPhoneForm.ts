import { useState } from 'react';
import { REGEX } from '@vieon/core/constants/constants';
import { TEXT } from '@vieon/core/constants/text';
import { validateEmail } from '@vieon/core/utils/common';

const useValidateEmailPhoneForm = (initialValue: any) => {
  const [value, setValue] = useState(initialValue);
  const [error, setError] = useState<any>('');
  const LENGTH_10 = 10;
  const regexPhone = REGEX.NUMBER;

  const handleChange = (event: any) => {
    const value = event.target?.value;

    setTimeout(() => {
      if (value.includes('@')) {
        if (!validateEmail(value)) {
          setError(TEXT.EMAIL_WRONG_SYNTAX);
          return;
        }
        return setError('');
      }
      if (!value) {
        setError('');
      } else {
        if (!regexPhone.test(value)) {
          setError(TEXT.PHONE_WRONG_SYNTAX);
          return;
        }
        if (value.length !== LENGTH_10) {
          setError(TEXT.PHONE_WRONG_TEN);
          return;
        }
        return setError('');
      }
    }, 300);

    setValue(value);
  };

  const onBlur = (event: any) => setValue(event.target?.value.trim());

  return { value, error, onChange: handleChange, onBlur };
};

export default useValidateEmailPhoneForm;
