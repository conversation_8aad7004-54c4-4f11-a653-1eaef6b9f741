import React, { useEffect, useState } from 'react';
import { PAGE } from '@vieon/core/constants/constants';
import { useVieRouter } from '@customHook';
import ButtonViewAll from '../components/basic/Buttons/ButtonViewAll';

const RibbonTitle = ({ title, as, redirectUrl, isViewAll, directURL, onSelectRibbon }: any) => {
  // directURL use for livetv, livestream go direct menu page
  const newTitle = (title || '').replace('"{movie_name}"', '');

  const [isClient, setClient] = useState(false);
  const router = useVieRouter();
  useEffect(() => {
    setClient(true);
  }, []);

  const onClickViewAll = () => {
    router.push(directURL || PAGE.COLLECTION, directURL ? `${directURL}/` : redirectUrl || as);
    onSelectRibbon();
  };

  const onClickRibbonTitle = (e: any) => {
    e.preventDefault();
    const href = directURL || PAGE.COLLECTION;
    const url = directURL ? `${directURL}/` : redirectUrl || as;
    if (url && href) {
      router.push(href, url);
    }
    if (typeof onSelectRibbon === 'function') onSelectRibbon(e);
  };

  if (isViewAll) {
    return (
      <>
        {isClient && (
          <ButtonViewAll
            title="Xem tất cả"
            iconName="vie-chevron-right-r-medium"
            onClick={onClickViewAll}
          />
        )}
        <h2 className="rocopa__title">
          <a
            href={directURL ? `${directURL}/` : redirectUrl || as}
            className="title--link"
            onClick={onClickRibbonTitle}
            tabIndex={-1}
          >
            {newTitle || title}
          </a>
        </h2>
      </>
    );
  }
  return <h2 className="rocopa__title">{newTitle || title}</h2>;
};
export default RibbonTitle;
