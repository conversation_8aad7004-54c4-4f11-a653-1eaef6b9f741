import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import LocalStorage from '@vieon/core/config/LocalStorage';
import { getTipData } from '@vieon/core/store/actions/page';
import {
  CONTENT_TYPE,
  EL_ID,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  PAGE,
  TAG_KEY,
  TAG_VIP,
  TAG_VIP_LABEL,
  EL_CLASS,
  TVOD
} from '@vieon/core/constants/constants';
import { PLAYER_STATUS, PLAYER_TYPE } from '@vieon/core/constants/player';
import { formatTimeTVodString } from '@vieon/core/services/contentService';
import { numberWithCommas, parseTagsData, setVideoPlay } from '@vieon/core/utils/common';
import { useVieRouter } from '@customHook';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import Tooltip from '../components/Tooltip';
import Button from '../Buttons/Button';
import MiniPlayer from '../MiniPlayer/MiniPlayer';
import TriggerAction from '../../trigger/TriggerAction';
import TagsOutline from '../Tags/TagsOutline';
import Tags from '../Tags/Tags';
import Image from '../Image/Image';
import NewIcon from '../Icon/NewIcon';
const CardAiring = React.memo(({ data, idRibbon }: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const tipData = useSelector((state: any) => state?.Page?.tipData);
  const { webConfig, isMobile } = useSelector((state: any) => state?.App || {});
  const expiredString = get(webConfig, 'tVod.text.expiredString', '');
  const {
    images,
    isPremium,
    altSEOImg,
    shortDescription,
    triggers,
    tvod,
    hasPVOD,
    type,
    isPremiumDisplay
  } = data || {};
  const { strTimeStandard, benefitType, price } = tvod || {};
  const tipDataItem = tipData?.[data?.id];
  const remainTimeText = useMemo(() => {
    if (!strTimeStandard) return '';
    return formatTimeTVodString({
      strConfig: expiredString,
      strTime: strTimeStandard
    });
  }, [strTimeStandard, expiredString]);

  useEffect(() => {
    if (data?.id && !tipDataItem) {
      dispatch(getTipData({ id: data?.id }));
    }
  }, [data?.id]);
  const cardRef = useRef<any>(null);

  const [playerStatus, setPlayerStatus] = useState(PLAYER_STATUS.ERROR);
  const [muted, setMuted] = useState(false);
  const [arrayLimit, setArrayLimit] = useState(5);

  useEffect(() => {
    window.addEventListener('scroll', checkScroll, false);
    return () => {
      window.removeEventListener('scroll', checkScroll, false);
    };
  }, []);

  const cardThumbId = `CARD_AIRING_${data?.id}`;

  const checkScroll = (e: Event) => {
    const scrollingEl = (e.target as Document)?.scrollingElement;
    const cardEl = cardRef?.current;
    if (!scrollingEl || !cardEl) return;

    const cardRect = cardEl.getBoundingClientRect();
    const screenHeight = document.documentElement.clientHeight;

    const airingVideo = document.getElementById(
      PLAYER_TYPE.CARD_AIRING + data?.id
    ) as HTMLVideoElement | null;

    if (!airingVideo) return;

    // Calculate visible height of the card
    const visibleHeight = Math.min(cardRect.bottom, screenHeight) - Math.max(cardRect.top, 0);
    const cardHeight = cardRect.height;
    const visibleRatio = cardHeight > 0 ? visibleHeight / cardHeight : 0;

    // Play if at least 2/3 of the card is visible
    const isTwoThirdsVisible = visibleRatio >= 2 / 3;

    // Pause if less than 1/3 is visible or completely out of view
    const isLessThanOneThirdVisible = visibleRatio < 1 / 3 || visibleHeight <= 0;

    if (isLessThanOneThirdVisible && !airingVideo.paused) {
      airingVideo.pause();
    } else if (isTwoThirdsVisible && airingVideo.paused && !airingVideo.ended) {
      setVideoPlay({ playerId: PLAYER_TYPE.CARD_AIRING + data?.id, noPauseOther: true });
    }
  };

  let ctrIconClass = 'vie-volume-mute-rc';
  if (playerStatus === PLAYER_STATUS.PLAYING) {
    if (muted) {
      ctrIconClass = 'vie-volume-mute-rc';
    } else {
      ctrIconClass = 'vie-volume-up-rc animate-fade-in';
    }
  } else if (playerStatus === PLAYER_STATUS.ENDED) ctrIconClass = 'vie-refresh';

  const toolTipContent = useMemo(() => {
    let tempToolTipContent = '';
    if (playerStatus === PLAYER_STATUS.ENDED) tempToolTipContent = 'Xem lại';
    else if (muted) tempToolTipContent = 'Bật tiếng';
    else tempToolTipContent = 'Tắt tiếng';
    return tempToolTipContent;
  }, [playerStatus, muted]);

  const onEnded = () => {
    setPlayerStatus(PLAYER_STATUS.ENDED);
  };
  const onPlay = ({ isMuted }: any) => {
    setMuted(!!isMuted);
  };
  const onPlaying = () => {
    setPlayerStatus(PLAYER_STATUS.PLAYING);
  };
  const onError = () => {
    setPlayerStatus(PLAYER_STATUS.ERROR);
  };

  const onClickControl = (e: any) => {
    e.preventDefault();

    if (playerStatus === PLAYER_STATUS.PLAYING) {
      setMuted(!muted);
      const airingVideo: any =
        window?.[PLAYER_TYPE.CARD_AIRING + data?.id] ||
        document.getElementById(PLAYER_TYPE.CARD_AIRING + data?.id);
      if (airingVideo) airingVideo.muted = !muted;
      ConfigLocalStorage.set(LocalStorage.TRAILER_MUTED, !muted || '');
    } else if (playerStatus === PLAYER_STATUS.ENDED) {
      setVideoPlay({ playerId: PLAYER_TYPE.CARD_AIRING + data?.id });
    }
  };

  const cardAiringClass = `card card--vod card--vod-variant card--dir-vertical-medium-down align-items-center${
    router.pathname === PAGE.SPORT
      ? ' card--dir-horizontal-xlarge-up vertical'
      : ' card--dir-horizontal-large-up'
  }`;

  let cardThumbnail = 'card__thumbnail-video ratio-16-9';
  if (playerStatus === PLAYER_STATUS.ENDED) cardThumbnail += ' hide';
  const newTagData = parseTagsData({ ...data, ...tipDataItem });

  useEffect(() => {
    if (
      isPremium ||
      hasPVOD ||
      (!!price && (benefitType === TVOD.USER_TYPE.EXPIRED || benefitType === TVOD.USER_TYPE.NONE))
    ) {
      if (isPremium && hasPVOD) return setArrayLimit(3);
      return setArrayLimit(4);
    }
    setArrayLimit(5);
  }, [isPremium, price, benefitType]);

  // const strTagVipLabel = `${TAG_VIP_LABEL[TAG_VIP.PVOD].charAt(0).toUpperCase()}${TAG_VIP_LABEL[
  //   TAG_VIP.PVOD
  // ]
  //   .slice(1)
  //   .toLowerCase()}`;

  return (
    <div className={cardAiringClass} ref={cardRef} data-grid="6-4">
      <div className="mask absolute middle">
        <div className="mask-inner ratio-16-9">
          <Image
            className="mask-img intrinsic absolute middle"
            src={images?.thumbnailBigNTC}
            alt={altSEOImg}
            title={altSEOImg}
          />
        </div>
      </div>
      <div className="card__thumbnail overflow layer-1" id={cardThumbId}>
        {data?.linkPlay?.hlsLinkPlay && !isMobile && (
          <>
            <div className={cardThumbnail}>
              <MiniPlayer
                id={PLAYER_TYPE.CARD_AIRING + data?.id}
                notAutoPlay
                notCheckScroll
                linkPlay={data?.linkPlay?.hlsLinkPlay}
                contentId={data?.id}
                muted={muted}
                onPlay={onPlay}
                onEnded={onEnded}
                onPlaying={onPlaying}
                onError={onError}
              />
            </div>
            <Tooltip
              title={toolTipContent}
              placement="top"
              triggerEvent={isMobile ? 'dismiss' : 'hover'}
              className="animate-fade-in text-start p-2 max-w-[14rem] min-w-[2.75rem]"
              size={EL_CLASS.SMALL}
              arrowPosition="top-end"
              sizeX={12}
              isDarkBackground
            >
              <Button
                className="button absolute right-5 bottom-5 button--geometry-circle !bg-black/50 !text-white hover:!text-vo-green !text-[1.125rem] xl:!text-[1.375rem]"
                onClick={onClickControl}
                iconName={ctrIconClass}
                subTitle={toolTipContent}
              />
            </Tooltip>
          </>
        )}

        <div className="card__thumbnail-loader overflow ratio-16-9">
          <Image className="card__thumb-img" src={images?.thumbnailBigNTC} alt={altSEOImg} />
        </div>
      </div>
      <div className="card__section layer-1">
        <div className="card__title">
          <Image src={images?.titleCardLight} alt={altSEOImg} />
        </div>
        <div className="card__section-wrap">
          <div className="flex flex-row flex-wrap space-x-[10px] lg:space-x-3">
            {price &&
              (benefitType === TVOD.USER_TYPE.EXPIRED || benefitType === TVOD.USER_TYPE.NONE) && (
                <Tags
                  tagKey={TAG_KEY.PRICE}
                  price={numberWithCommas(price)}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
            {isPremium && isPremiumDisplay && (
              <Tags isPremiumDisplay={isPremiumDisplay} size={EL_SIZE_CLASS.LARGE} />
            )}
            {hasPVOD && type === CONTENT_TYPE.SEASON && (
              <Tags
                title={TAG_VIP_LABEL[TAG_VIP.PVOD]}
                theme={EL_THEME_CLASS.GREEN}
                size={EL_SIZE_CLASS.LARGE}
              />
            )}
            <TagsOutline size={EL_SIZE_CLASS.LARGE} tagArray={newTagData} arrayLimit={arrayLimit} />
          </div>
          {shortDescription && <div className="card__desc">{shortDescription || ''}</div>}
        </div>
        <div className="button-group align-left space-x-2">
          {(triggers || []).map((tgg: any, index: any) => (
            <TriggerAction
              key={index}
              isAiring
              index={index}
              triggerItem={tgg}
              cardData={data}
              contentData={data}
              playerId={PLAYER_TYPE.CARD_AIRING + data?.id}
              buttonStyleCustom="w-1/3"
            />
          ))}
        </div>
        <div className="w-full justify-start flex items-baseline">
          {remainTimeText && (
            <>
              <NewIcon iCustomizeClass="text-[#EDC42D]" iconName={'vie-clock-o-rc-medium'} />
              <Tags
                tagKey={TAG_KEY.REMIND}
                description={remainTimeText}
                size={EL_SIZE_CLASS.LARGE}
                // iClass="icon--small icon--tiny-xs text-yellow91"
                iconName="vie-clock-o-rc-medium !text-[#EDC42D]"
                isNewIcon
                txtClass={'text-[#EDC42D]'}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
});
export default CardAiring;
