import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';
import { getTipData } from '@vieon/core/store/actions/page';
import { openPopup, previewCard } from '@vieon/core/store/actions/popup';
import LocalStorage from '@vieon/core/config/LocalStorage';
import { addParamToUrlVieON, createTimeout, parseQueryString } from '@vieon/core/utils/common';
import {
  EL_ID,
  PAGE,
  PERMISSION,
  POPUP,
  POSITION_TRIGGER,
  RIBBON_TYPE,
  TYPE_TRIGGER_ALWAYS,
  TVOD
} from '@vieon/core/constants/constants';
import { PLAYER_TYPE } from '@vieon/core/constants/player';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import { NAME, PROPERTY, VALUE } from '@vieon/core/config/ConfigSegment';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { useOutsideEvent, useVieRouter } from '@customHook';
import { getContentDetailByID } from '@vieon/core/store/actions/detail';
import TriggerTouchPoint from '../components/home/<USER>';
import { getTriggerConfig } from '@vieon/core/store/actions/appConfig';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import { handleCheckLocalGlobal } from '@vieon/core/services/popupServices';
import BannerVideoIntroBackgroundAds from '../components/OutstreamAds/BannerVideoIntroBackgroundAds';
import BannerVideoIntroAds from '../components/OutstreamAds/BannerVideoIntroAds';
import BannerTriggerPVod from '../components/home/<USER>';
import { getDataTriggerPoint } from '@vieon/core/store/actions/trigger';
import BannerPaymentConversion from '../../home/<USER>';
import { watchNowBehavior } from '../../trigger/triggerFunction';
import Transition from '../Transition';
import Button from '../Buttons/Button';
import Recommended from '../../vodIntro/Recommended';
import Related from '../../vodIntro/Related';
import EpisodeList from '../../vodIntro/EpisodeList';
import Comment from '../../vodIntro/Comment';
import CardInfo from './CardInfo';
import Billboard from '../../Billboard/Billboard';

const CardDetail = () => {
  const ref = useRef<any>(null);
  const router = useVieRouter();
  const {
    query: { vid },
    pathname
  } = router || {};
  const dispatch = useDispatch();
  const clickTimerRef = useRef<any>(null);
  const tipData = useSelector((state: any) => state?.Page?.tipData);
  const { dataTriggerVodIntro } = useSelector((state: any) => state?.Trigger);
  const { isGlobal, globalHost } = useSelector((state: any) => state?.App?.geoCheck || {});
  const previewCardSelector = useSelector((state: any) => state?.Popup?.previewCard);
  const profile = useSelector((state: any) => state?.Profile?.profile || {});
  const triggerConfig = useSelector((state: any) => state?.AppConfig?.trigger) || {};
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const { paymentConversion, outStreamAds, isMobile } = useSelector(
    (state: any) => state?.App || {}
  );
  const { bannerVideoIntro, bannerVideoIntroBackground } = outStreamAds || {};
  const { windowing, svodTrial, packageId } = paymentConversion || {};
  const dataIntroWindowing = windowing?.bannerIntro || {};
  const dataSVodTrial = svodTrial?.bannerIntro || {};
  const searchContents = useSelector((state: any) => state?.Search?.SEARCH_CONTENT);
  const masterBannerData = useSelector((state: any) => {
    const dataMenu = state?.Menu?.activeSubMenu || state?.Menu?.activeMenu || {};
    return state?.Page?.pageBanner?.[dataMenu?.seo?.url];
  });

  const [totalComment, setTotal] = useState<any>('');
  const cardData = previewCardSelector?.data;
  const cardDataEpisode = previewCardSelector?.dataEpisode;
  const detailCard = useMemo(
    () => (!isEmpty(cardData) ? cardData : cardDataEpisode || {}),
    [cardData, cardDataEpisode]
  );
  const isTriggerTrial = cardData?.tvod?.benefitType
    ? !profile?.isPremium &&
      cardData?.isVip &&
      cardData?.trialDuration > 0 &&
      (cardData?.tvod?.benefitType === TVOD.USER_TYPE.EXPIRED ||
        cardData?.tvod?.benefitType === TVOD.USER_TYPE.NONE)
    : !profile?.isPremium && cardData?.isVip && cardData?.trialDuration > 0;
  const tipDataItem = useMemo(() => tipData?.[vid], [tipData, vid]);
  const wrapperRef = useRef<any>(null);
  const animateClass = cardData?.id ? 'ohYeah-expand' : 'ohYeah-enter';
  const wrapperClass = classNames('intro__wrap layer-1', animateClass);
  const wrapStyle = cardData?.id
    ? {}
    : {
        ...(previewCardSelector?.offset || {}),
        height: 'auto'
      };
  const cardClassName = classNames(
    'intro intro--preview intro--preview-vod',
    cardData?.id && 'intro--preview-vod--modal',
    cardData?.isOriginal && 'intro--original',
    cardData?.isMain && 'intro--variant'
  );

  useEffect(() => {
    ConfigLocalStorage.set(LocalStorage.HIDE_MASTHEAD, 1);
  }, []);

  useEffect(() => {
    if (isKid) {
      dispatch(
        getContentDetailByID({ contentId: detailCard?.id, isCard: true }) as unknown as Promise<any>
      ).then((res: any) => {
        if (res?.payload?.success && res?.payload?.data?.permission === PERMISSION?.KID_LIMITED) {
          dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_CONTENT_DIALOG }));
        }
      });
    }
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(
        getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.VOD_INTRO, contentId: detailCard?.id })
      );
    }, 400);

    if (isEmpty(triggerConfig)) {
      dispatch(getTriggerConfig());
    }
    document.addEventListener('keydown', onKeyPress);
    return () => {
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
      document.removeEventListener('keydown', onKeyPress);
    };
  }, []);

  useEffect(() => {
    if (ref?.current) {
      ref.current.addEventListener('scroll', handleRedirect);
      return () => {
        if (ref?.current) {
          ref.current.removeEventListener('scroll', handleRedirect);
        }
      };
    }
    return () => {}; // Return empty function for the case when ref?.current is falsy
  }, [ref]);

  useEffect(() => {
    if (vid) {
      dispatch(getTipData({ id: vid }));
    }
  }, [vid]);

  const handleRedirect = () => {
    if (handleCheckLocalGlobal(pathname)) {
      if (isGlobal && !globalHost) {
        dispatch(openPopup({ name: POPUP.NAME.LOCAL_TO_GLOBAL }));
      } else if (!isGlobal && globalHost) {
        dispatch(openPopup({ name: POPUP.NAME.GLOBAL_TO_LOCAL }));
      }
    }
  };

  const handleSaveProgressBanner = () => {
    const player: any = document.getElementById(PLAYER_TYPE.CARD_DETAIL);
    const currentTime = player?.ended ? 0 : player?.currentTime || 0;
    ConfigLocalStorage.set(
      LocalStorage.BANNER_TRAILER_PROGRESS,
      JSON.stringify({ [cardData?.id]: currentTime })
    );
  };

  const onClose = () => {
    handleSaveProgressBanner();
    const { asPath } = router;
    let url = (asPath || '').split('?');
    url = url?.[0] || url;
    const id = parseQueryString(location.search)?.id;
    const isRecommend = (asPath || '').indexOf('?id') > -1;
    let params = {};
    if (id && !router?.query?.id && isRecommend) {
      params = { id };
    }
    const queryParams = addParamToUrlVieON(router?.query, params);
    router.push(
      { pathname: router?.pathname, query: queryParams },
      { pathname: url, query: queryParams },
      { scroll: false }
    );
    dispatch(
      previewCard({
        expand: false,
        data: null,
        offset: null,
        dataEpisode: null,
        tVodInfo: null
      })
    );
  };

  useOutsideEvent(wrapperRef, onClose);

  const onKeyPress = (e: any) => {
    switch (e.which) {
      case 27:
        onClose();
        break;
      case 36:
        scrollToBottom(36);
        break;
      case 35:
        scrollToBottom(35);
        break;
      case 40:
      case 38:
      case 34:
      case 33:
        break;
      default:
        break;
    }
  };

  const scrollToBottom = (key: any) => {
    const detailEl = document.getElementById('INTRO_WRAP');
    let top: any = '';
    if (key === 35) top = detailEl?.clientHeight;
    if (key === 36) top = detailEl?.offsetTop === 0;
    if (ref?.current) {
      ref?.current.scrollTo({
        left: 0,
        top,
        block: 'start'
      });
    }
  };

  const setTotalComment = (totalCm: any) => {
    setTotal(totalCm);
  };

  const handleScroll = (e: any) => {
    const video: any = document.getElementById(PLAYER_TYPE.CARD_DETAIL);
    if (!video) return;
    if (e?.target?.scrollTop > 300) {
      if (!video?.paused && !video.ended) video.pause();
    } else if (e?.target?.scrollTop < 200) {
      if (video?.paused && !video.ended) video.play();
    }
  };

  const handleScrollToComment = () => {
    const commentEl = document.getElementById(EL_ID.INTRO_COMMENT);
    const top = commentEl?.offsetTop || ref?.current?.offsetHeight;
    if (ref?.current) {
      ref?.current.scrollTo({
        left: 0,
        top: top - 200,
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const onClickBanner = async () => {
    await watchNowBehavior({
      profile,
      currentProfile,
      contentData: cardData,
      router,
      dispatch,
      expand: false,
      isMobile
    });

    const { contentTypeTvod } = cardData || {};
    if (typeof cardData?.onContentSelected === 'function') {
      cardData.onContentSelected({ cardData, contentTypeTvod });
    } else {
      TrackingApp.contentSelected({
        data: {
          ...cardData,
          seasonThumb: cardData?.images?.thumbnail,
          seasonGenre: cardData?.genreText
        },
        masterBannerData,
        clickType: VALUE.HOVER_CLICK,
        isLiveTv: cardData?.isLiveTv,
        category: cardData?.isLiveTv ? cardData?.categoryTracking : undefined
      });
    }
    if (router?.pathname === PAGE.SEARCH) {
      const keyword = router?.query?.q;
      const searchItems = searchContents?.[keyword]?.[-1]?.[0]?.items || [];
      const itemPosition = (searchItems || []).findIndex((it: any) => it?.id === cardData?.id);
      segmentEvent(NAME.SELECT_SEARCH_RESULT, {
        [PROPERTY.KEYWORD]: keyword,
        [PROPERTY.CONTENT_TITLE]: cardData?.title,
        [PROPERTY.CONTENT_TYPE]: cardData?.type,
        [PROPERTY.CONTENT_POSITION]: itemPosition || cardData?.index || 0,
        [PROPERTY.CURRENT_PAGE]: window?.location?.href
      });
    }
  };

  if (isEmpty(cardData)) return null;

  return (
    <div className={cardClassName} ref={ref} id="CardDetail" onScroll={handleScroll}>
      <Transition
        animateClass={animateClass}
        childrenId="INTRO_WRAP"
        onKeyPress={(e: any) => onKeyPress(e)}
      >
        <div
          className={
            isGlobal && !isMobile
              ? `${wrapperClass} !relative !top-0 !left-0 !my-0 !mx-auto !w-[60rem]`
              : wrapperClass
          }
          style={wrapStyle}
          id="INTRO_WRAP"
          ref={wrapperRef}
        >
          <Billboard
            expand
            isCardDetail
            billboardData={cardData}
            cardDataEpisode={cardDataEpisode}
            tipDataItem={tipDataItem}
            playerId={PLAYER_TYPE.CARD_DETAIL}
            className="billboard--intro"
            onClickBanner={onClickBanner}
            searchContents={searchContents}
            isAnimateText
          />
          {!isGlobal &&
            !currentProfile?.isKid &&
            !isEmpty(dataTriggerVodIntro) &&
            !isTriggerTrial && (
              <TriggerTouchPoint
                image={dataTriggerVodIntro?.image}
                imageMobile={dataTriggerVodIntro?.image}
                url={dataTriggerVodIntro?.navigateUrl}
                positionTrigger={POSITION_TRIGGER.VIDEO_INTRO}
                className="intro__info padding-small-up-bottom-36"
                content={detailCard}
              />
            )}
          {isTriggerTrial && (
            <BannerPaymentConversion
              data={dataSVodTrial}
              newSubstr={cardData?.trialDuration}
              regexp="{trialDuration}"
              packageId={packageId}
              profile={profile}
              isTrialDuration
              contentData={cardData}
            />
          )}
          <CardInfo
            cardData={cardData}
            cardDataEpisode={cardDataEpisode}
            totalComment={totalComment}
            tipDataItem={tipDataItem}
            searchContents={searchContents}
            profile={profile}
            expand
            handleScrollToComment={handleScrollToComment}
            playerId={PLAYER_TYPE.CARD_DETAIL}
          />
          {!profile?.isPremium && !isKid && !isGlobal && (
            <BannerVideoIntroAds data={bannerVideoIntro} />
          )}
          {!profile?.isPremium && cardData?.windowingMessage && (
            <BannerPaymentConversion
              packageId={packageId}
              data={dataIntroWindowing}
              profile={profile}
              newSubstr={cardData?.windowingMessage}
              regexp="{windowingMessage}"
              style={isMobile ? {} : { marginLeft: '2.08333vw', marginRight: '2.08333vw' }}
              contentData={cardData}
            />
          )}
          {!isEmpty(cardData?.pvod) && (
            <BannerTriggerPVod className="mx-[2.08333vw] p-t4 relative" data={cardData} />
          )}
          {cardData && (
            <>
              <section className="section section--intro" id="SECTION">
                <div className="section__body">
                  {cardData?.ribbonType !== RIBBON_TYPE.EPG && <EpisodeList content={cardData} />}
                  <Related content={cardData} />
                  <Recommended content={cardData} />
                </div>
              </section>
              {!isKid && (
                <Comment content={cardData} setTotalComment={setTotalComment} profile={profile} />
              )}
              <Button
                className="button close button--geometry-circle button--dark-glass absolute"
                iconClass="!text-[13px]"
                iconName="vie-times-medium"
                onClick={onClose}
                isFadeInForIcon
              />
            </>
          )}
        </div>
      </Transition>
      {!profile?.isPremium && !isKid && !isGlobal && (
        <BannerVideoIntroBackgroundAds data={bannerVideoIntroBackground} />
      )}
    </div>
  );
};

export default memo(CardDetail);
