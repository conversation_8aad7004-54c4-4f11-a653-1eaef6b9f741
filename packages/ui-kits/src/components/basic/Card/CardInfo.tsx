import React, { useEffect, useMemo, useState } from 'react';
import VieLink from '../components/VieLink';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';
import { openPopup } from '@vieon/core/store/actions/popup';
import { TEXT } from '@vieon/core/constants/text';
import {
  CONTENT_TYPE,
  EL_ID,
  EL_SIZE_CLASS,
  LIKE_VIDEO,
  PAGE,
  POPUP,
  RIBBON_TYPE,
  TAG_KEY,
  TRIGGER_KEY,
  TVOD,
  EL_THEME_CLASS,
  USER_TYPE
} from '@vieon/core/constants/constants';
import { PLAYER_TYPE } from '@vieon/core/constants/player';
import { TYPE_TRIGGER_AUTH } from '@vieon/core/constants/types';
import {
  encodeParamDestination,
  onNumberViews,
  onShareSocial,
  parseTagsData
} from '@vieon/core/utils/common';
import { formatTimeTVodString, parseTimeExpiredTVod } from '@vieon/core/services/contentService';
import { useVieRouter } from '@customHook';
import TrackingLoyalty from '@vieon/tracking/functions/TrackingLoyalty';
import TriggerAction from '../components/trigger/TriggerAction';
import findIndex from 'lodash/findIndex';
import dynamic from 'next/dynamic';
import { setLive } from '@vieon/models/epgItem';
import { setStartTimeLiveStream } from '@vieon/core/services/datetimeServices';
import TagsOutline from '../Tags/TagsOutline';
import CardProgress from './CardProgress';
import Rating from '../Rating/Rating';
import ContentApi from '@vieon/core/api/cm/ContentApi';
import NewIcon from '../Icon/NewIcon';
import { formatLikeCount } from '../../../helpers/utils';

const Tags = dynamic(import('@components/basic/Tags/Tags'), { ssr: false });

const CardInfo = React.memo(
  ({
    cardData,
    totalComment,
    tipDataItem,
    handleScrollToComment,
    profile,
    isVodDetail = false,
    playerId,
    currentEpisode,
    dataNewSEO,
    cardDataEpisode,
    playerReady = false,
    hasObjectDetection,
    searchContents
  }: any) => {
    const {
      isPremium,
      people,
      isLiveTv,
      isLiveStream,
      longDescription,
      shortDescription,
      knownAs,
      vodSchedule,
      seo,
      ribbonType,
      isComingSoon,
      dataLiveTV,
      defaultEpisode,
      moreInfo,
      isLive,
      isPremiumDisplay,
      hasPVOD,
      expiredDate,
      startTime,
      isPremiere,
      isMasterBanner,
      isViewCollection,
      ribbonOrder,
      type,
      isSvodTvod
    } = cardData || {};
    const tvodInfo = useSelector((state: any) => state?.App?.tVod);
    const config = useSelector((state: any) => state?.App?.webConfig);
    const expiredStringInfoBox = get(config, 'tVod.text.expiredStringInfoBox', '');
    let title = cardData?.title;
    const tvod = useMemo(() => {
      let dataTemp = {};
      if (!isEmpty(cardData?.tvod)) {
        dataTemp = cardData?.tvod;
      }
      if (!isEmpty(cardDataEpisode?.tvod)) {
        dataTemp = cardDataEpisode?.tvod;
      }
      return dataTemp;
    }, [cardDataEpisode?.tvod, cardData?.tvod]);
    const tVodInfo = useMemo(() => {
      let dataTemp = {};
      if (!isEmpty(cardData?.tVodInfo)) {
        dataTemp = cardData?.tVodInfo;
      }
      if (!isEmpty(cardDataEpisode?.tVodInfo)) {
        dataTemp = cardDataEpisode?.tVodInfo;
      }
      return dataTemp;
    }, [cardDataEpisode?.tVodInfo, cardData?.tVodInfo]);
    const { bizInfo }: any = tVodInfo || {};
    const { day, hour } = vodSchedule || {};
    const titleLiveTv = moreInfo?.programme?.title;
    const { isKid, id } = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});
    const previewCard = useSelector((state: any) => state?.Popup?.previewCard);
    const isInRemoveContentFlowRestriction =
      useSelector((state: any) => state?.MultiProfile?.titleRestriction?.isInRemoveContentFlow) ||
      false;
    const { webConfig, isMobile } = useSelector((state: any) => state?.App);
    const expiredString = get(webConfig, 'tVod.text.expiredString', '');
    const { expand } = previewCard || {};
    const { shareUrl } = seo || {};
    const dispatch = useDispatch();
    const router = useVieRouter();
    const { views, text, avgRate, remainText, progressPercent, progress, tags } = tipDataItem || {};
    const categoryFromTags = (tags || []).find((tag: any) => tag?.type?.search('genre') >= 0);
    const { consumingDurMsg, strTimeStandard, benefitType, price, isLiveEvent, isSimulcast }: any =
      tvod || {};

    const [arrayLimit, setArrayLimit] = useState(5);
    const { isOnLoyalty, isShowTitleRestriction }: any = useSelector(
      (state: any) => state?.App?.webConfig?.featureFlag || {}
    );
    const titleDetailPage = router?.pathname === PAGE.VOD ? title : '';
    const [isLike, setIsLike] = useState(false);
    const [totalLike, setTotalLike] = useState(0);

    const startTextLive = useMemo(() => {
      if (startTime) {
        if (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) {
          const { startText } = setLive(startTime);
          return startText;
        }
        if (type === CONTENT_TYPE.LIVESTREAM) {
          return setStartTimeLiveStream(startTime, isLive, isPremiere);
        }
      }
      return '';
    }, [startTime]);

    const triggers = useMemo(() => {
      let arrTriggers = [];
      if (!isEmpty(cardData?.triggers)) {
        arrTriggers = cardData?.triggers;
      }
      if (!isEmpty(cardDataEpisode?.triggers)) {
        arrTriggers = cardDataEpisode?.triggers;
      }
      if (isVodDetail && findIndex(arrTriggers, ['key', TRIGGER_KEY.MY_LIST]) === -1) {
        arrTriggers.push({ key: TRIGGER_KEY.MY_LIST });
      }
      if (isViewCollection) {
        arrTriggers = [{ key: TRIGGER_KEY.DETAIL }];
      }
      return arrTriggers;
    }, [isVodDetail, cardDataEpisode?.triggers, cardData?.triggers, isViewCollection]);

    const showTitleRestriction = useMemo(() => {
      if (profile?.id) {
        return id === profile?.id && isShowTitleRestriction && cardData.canLimit;
      }
    }, [id, profile?.id, isShowTitleRestriction, cardData.canLimit]);

    const remainTimeText = useMemo(() => {
      let timeText = '';
      if (
        isVodDetail &&
        playerReady &&
        benefitType === TVOD.USER_TYPE.RENTED &&
        !isEmpty(bizInfo)
      ) {
        const consumingDurTime = bizInfo?.consumingDur * 60 + Math.floor(Date.now() / 1000);
        const timeExpired = parseTimeExpiredTVod({ expiredTime: consumingDurTime });
        if (timeExpired?.strTimeStandard) {
          timeText = formatTimeTVodString({
            strConfig: expiredString,
            strTime: timeExpired?.strTimeStandard
          });
        }
      } else if (strTimeStandard) {
        timeText = formatTimeTVodString({ strConfig: expiredString, strTime: strTimeStandard });
      }
      return timeText;
    }, [
      strTimeStandard,
      consumingDurMsg,
      expiredString,
      isVodDetail,
      benefitType,
      playerReady,
      bizInfo,
      tvod
    ]);

    const textTSvod = () => {
      let timeText = '';
      if (tvodInfo && !isEmpty(tvodInfo?.bizInfo) && !isEmpty(tvodInfo?.benefitInfo)) {
        const { type, endAt } = tvodInfo?.benefitInfo;
        const consumingDurTime =
          type === TVOD.USER_TYPE.WATCHED
            ? endAt
            : tvodInfo?.bizInfo?.consumingDur * 60 + Math.floor(Date.now() / 1000);
        const timeExpired = parseTimeExpiredTVod({ expiredTime: consumingDurTime });
        if (timeExpired?.strTimeInfoBox) {
          timeText = formatTimeTVodString({
            strConfig: expiredStringInfoBox,
            strTime: timeExpired?.strTimeInfoBox
          });
        }
      }
      return timeText;
    };

    const isVideoIndexing = useMemo(
      () =>
        (hasObjectDetection ||
          cardData?.hasObjectDetection ||
          defaultEpisode?.hasObjectDetection) &&
        !isKid,
      [hasObjectDetection, cardData?.hasObjectDetection, defaultEpisode?.hasObjectDetection]
    );

    // Action
    const goToComment = (e: any) => {
      e.preventDefault();
      if (isKid) return;
      if (typeof handleScrollToComment === 'function') handleScrollToComment();
    };

    const shareOnSocial = (e: any) => {
      e.preventDefault();
      if (isKid) return;
      if (shareUrl) {
        onShareSocial({
          link: shareUrl,
          name: 'facebook',
          callback: callbackShare,
          callbackSuccess: shareSuccessfully
        });

        const video: any = document.getElementById(PLAYER_TYPE.CARD_DETAIL);
        if (video) video.pause();
      }
    };

    const handleOpenResctrictPopup = () => {
      dispatch(
        openPopup({
          name: POPUP.NAME.TITLE_RESCTRICTION,
          contentId: cardData?.id
        })
      );
    };
    const callbackShare = () => {
      const video: any = document.getElementById(PLAYER_TYPE.CARD_DETAIL);
      if (video) video.play();
    };
    const shareSuccessfully = () => {
      if (isOnLoyalty) {
        TrackingLoyalty.trackingShareContentToFacebook({
          contentId: cardData?.id || '',
          contentTitle: cardData?.movie?.title || ''
        });
      }
    };

    if (cardData?.isOriginal) return null;
    let nextWatchingTitle = '';
    if (progressPercent > 0 || progress === -1) {
      nextWatchingTitle = text || cardDataEpisode?.title || defaultEpisode?.title;
    } else if (cardDataEpisode) nextWatchingTitle = cardDataEpisode?.title;

    if ((day && ribbonType === RIBBON_TYPE.COMING_SOON) || cardData?.isComingSoon) {
      title = day;
    }
    const newTagData: any = parseTagsData({ ...cardData, ...tipDataItem });

    if (isLiveTv && newTagData && dataLiveTV?.programme?.id) {
      title = dataLiveTV?.programme?.title;
    }

    const onClickRating = () => {
      if (!profile?.id) {
        const remakeDestination = encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.RATING}`
        );
      } else {
        dispatch(
          openPopup({
            name: POPUP.NAME.POPUP_RATING,
            contentId: cardData?.id,
            playerId
          })
        );
      }
    };

    const handleLikeClick = () => {
      setIsLike(!isLike);
      toggleLike();
    };

    const toggleLike = async () => {
      const param = {
        content_id: cardData?.id,
        type: 2
      };
      const data = await ContentApi.likeToggle(param);
      setTotalLike(data?.result?.total_count);
      setIsLike(data?.result?.type !== 0);
    };

    const getTotalLike = async (content_id: any) => {
      const data = await ContentApi.getTotalLike(content_id);
      setTotalLike(data?.result?.total_count);
      setIsLike(data?.result?.type !== LIKE_VIDEO.UNLIKE);
    };

    const showPriceTag = useMemo(() => {
      if (profile?.isPremium || profile?.type === USER_TYPE.VIP) {
        return false;
      }
      if (price === 0 || tvodInfo?.bizInfo?.price === 0) {
        return false;
      }
      return (
        ((!isLiveEvent && !isSimulcast) || isSvodTvod) &&
        (price || tvodInfo?.bizInfo?.price) &&
        (tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.NONE ||
          tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.EXPIRED ||
          benefitType === TVOD.USER_TYPE.NONE ||
          benefitType === TVOD.USER_TYPE.EXPIRED)
      );
    }, [isLiveEvent, isSimulcast, price, benefitType, isSvodTvod, isPremium, tvodInfo]);

    useEffect(() => {
      if (
        (isPremium && !isSvodTvod) ||
        hasPVOD ||
        (!isLiveEvent &&
          !isSimulcast &&
          !!price &&
          (benefitType === TVOD.USER_TYPE.NONE || benefitType === TVOD.USER_TYPE.EXPIRED))
      ) {
        if (isPremium && hasPVOD) return setArrayLimit(3);
        return setArrayLimit(4);
      }
      setArrayLimit(5);
    }, [isPremium, isLiveEvent, isSimulcast, price, benefitType, isSvodTvod]);

    useEffect(() => {
      if (isInRemoveContentFlowRestriction) {
        dispatch(
          openPopup({
            name: POPUP.NAME.TITLE_RESCTRICTION,
            contentId: cardData?.id
          })
        );
      }
    }, [isInRemoveContentFlowRestriction]);

    useEffect(() => {
      if (cardData?.id) {
        getTotalLike(cardData?.id);
      }
    }, [cardData?.id]);

    const isShowRemainTime = () => {
      if (router?.route !== PAGE.VOD) {
        return false;
      }
      if (profile?.isPremium || profile?.type === USER_TYPE.VIP) {
        if (cardData?.isPremium && !isSvodTvod) {
          if (benefitType === TVOD.USER_TYPE.RENTED || benefitType === TVOD.USER_TYPE.WATCHED) {
            return true;
          }
          return false;
        } else {
          return false;
        }
      }
      return (
        tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.RENTED ||
        tvodInfo?.benefitInfo?.type === TVOD.USER_TYPE.WATCHED
      );
    };

    return (
      <div className="intro__info">
        <div className="intro__info__wrap">
          <div className="intro__info-left">
            {isVodDetail && <h1 className="title text-white">{dataNewSEO?.title || title}</h1>}
            {isVodDetail && knownAs && <h2 className="text-white font-size-18">{knownAs}</h2>}
            {isShowRemainTime() && (
              <div className="flex items-center">
                <Tags
                  description={
                    <span
                      dangerouslySetInnerHTML={{
                        __html: isSvodTvod ? textTSvod() : remainTimeText
                      }}
                    />
                  }
                  theme={EL_THEME_CLASS.YELLOW_SUBTLE}
                  iconName="vie-clock-o-rc-medium"
                  size={EL_SIZE_CLASS.LARGE_SUBTLE}
                  isNewIcon
                />
              </div>
            )}
            {((!isComingSoon && expand) || isVodDetail) && (
              <div className="viewer-rating align-middle">
                <div className="viewer viewer--light">
                  <span className="viewer__summary">{views || 0}</span>
                  <span className="viewer__muted"> {TEXT.VIEWS}</span>
                </div>
                <Rating
                  disabled={isKid}
                  avg={avgRate}
                  cardData={cardData}
                  onClickRating={onClickRating}
                  point={avgRate}
                  notUser
                  isDisableToolTip
                />
              </div>
            )}
            <div
              className={`flex flex-row flex-wrap${
                isLiveStream && !startTextLive ? '' : ' space-x-[10px] lg:space-x-3'
              }`}
            >
              {showPriceTag && (
                <Tags
                  tagKey={TAG_KEY.PRICE}
                  price={new Intl.NumberFormat('vi-VN').format(price || tvodInfo?.bizInfo?.price)}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
              {!isLiveTv && !isLiveStream && isPremiumDisplay && (
                <Tags isPremiumDisplay={isPremiumDisplay} size={EL_SIZE_CLASS.LARGE} />
              )}
              {isLiveTv && isPremiumDisplay && (
                <Tags
                  isPremiumDisplay={isPremiumDisplay}
                  tagKey={TAG_KEY.VIP}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
              {hasPVOD && type === CONTENT_TYPE.SEASON && (
                <Tags
                  tagKey={TAG_KEY.WATCH_SOON}
                  title={TEXT.TAG_WATCH_SOON}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
              {(isLive || isPremiere) && (
                <Tags
                  title={isPremiere ? TAG_KEY.PREMIERE : ''}
                  tagKey={TAG_KEY.LIVE}
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
              <TagsOutline
                isVideoIndexing={isVideoIndexing}
                tagArray={newTagData}
                arrayLimit={arrayLimit}
                size={EL_SIZE_CLASS.LARGE}
              />
              {startTextLive && isLiveStream && !isPremiere && (
                <Tags
                  tagKey={TAG_KEY.REMIND}
                  description={startTextLive}
                  iClass="icon--tiny text-green"
                  size={EL_SIZE_CLASS.LARGE}
                />
              )}
            </div>
            {expiredDate && expiredDate !== '' && (
              <h4 className="title text-white">{`${TEXT.CONTENT_LASTING}: ${expiredDate}`}</h4>
            )}
            {!expand && (cardData?.progress || progress) > 0 && (
              <CardProgress
                progressPercent={cardData?.progressPercent || progressPercent}
                remainText={cardData?.remainText || remainText}
              />
            )}
            {titleLiveTv && isLiveTv && ribbonType === RIBBON_TYPE.LIVE_TV && (
              <h3 className="intro__info__title">{titleLiveTv}</h3>
            )}
            {(ribbonType === RIBBON_TYPE.COMING_SOON ||
              ribbonType === RIBBON_TYPE.EPG ||
              isLiveStream ||
              isLive) && <h3 className="intro__info__title">{cardData?.title || title}</h3>}
            {isComingSoon && expand && <h5 className="card__title text-white">{day}</h5>}

            {nextWatchingTitle && !isVodDetail && (
              <h5
                className="card__title text-white"
                style={{ fontSize: '16px', fontWeight: 'bold' }}
              >
                {nextWatchingTitle}
              </h5>
            )}
            {isVodDetail && (
              <h2
                className="card__title text-white"
                style={{ fontSize: '16px', fontWeight: 'bold' }}
              >
                {currentEpisode?.title || nextWatchingTitle || title}
              </h2>
            )}

            {isMobile && !isVodDetail && (
              <div className="flex flex-col space-y-2">
                {(triggers || []).map((tgg: any, index: any) => {
                  if (
                    tgg?.key === TRIGGER_KEY.DETAIL &&
                    (isVodDetail || (expand && !isMasterBanner))
                  ) {
                    return null;
                  }
                  return (
                    <TriggerAction
                      key={index}
                      index={isViewCollection ? null : index}
                      expand={expand}
                      playerId={playerId}
                      triggerItem={tgg}
                      searchContents={searchContents}
                      cardData={cardData}
                      contentData={cardData}
                      ribbonOrder={ribbonOrder}
                      titleDetailPage={titleDetailPage}
                      isVodDetail={isVodDetail}
                      cardDataEpisode={cardDataEpisode}
                    />
                  );
                })}
              </div>
            )}

            {progress > 0 && !isVodDetail && expand && (
              <div
                className="intro__info__desc line-clamp"
                data-line-clamp="3"
                dangerouslySetInnerHTML={{
                  __html:
                    cardDataEpisode?.shortDescription ||
                    defaultEpisode?.shortDescription ||
                    shortDescription ||
                    ''
                }}
              />
            )}
            {shortDescription && !progress && !isVodDetail && expand && (
              <div
                className="intro__info__desc line-clamp"
                data-line-clamp="3"
                dangerouslySetInnerHTML={{
                  __html:
                    (cardDataEpisode?.shortDescription || shortDescription) +
                    (cardDataEpisode ? '' : day || hour ? ` | ${day}` || hour : '')
                }}
              />
            )}
            {isVodDetail && (
              <div
                className="intro__info__desc"
                dangerouslySetInnerHTML={{ __html: longDescription || shortDescription }} // dataNewSEO?.open_graph?.page_description
              />
            )}
          </div>
          {(isVodDetail || expand) && (
            <div className="intro__info-right !pl-0">
              <div
                className={classNames(
                  'flex space-x-4 md:space-x-2 lg:space-x-3 2xl:space-x-10',
                  isMobile ? 'm-t3' : '',
                  isKid ? 'disabled' : ''
                )}
              >
                {/* new icon */}
                <button
                  className="comment-counter !justify-start !text-white hover:!text-vo-green"
                  tabIndex={-1}
                  onClick={handleLikeClick}
                  type="button"
                >
                  <NewIcon
                    iCustomizeClass="!text-[1rem] md:!text-[1.5rem]"
                    iconName={isLike ? 'vie-heart-s' : ''}
                    imageSrc={!isLike ? '/assets/images/heart-o-rc-medium.svg' : ''}
                    spImageClass="w-[16px] h-[16px] md:w-[24px] md:h-[24px] flex items-center justify-center"
                  />
                  <span className="text-[.75rem] text-center mt-1.5">
                    {formatLikeCount(totalLike)}
                  </span>
                </button>

                <a
                  className="comment-counter !justify-start relative !text-white hover:!text-vo-green"
                  href={isKid ? undefined : `#${EL_ID.INTRO_COMMENT}`}
                  tabIndex={-1}
                  title="Comment"
                  onClick={goToComment}
                >
                  <NewIcon
                    iCustomizeClass="!text-[1rem] md:!text-[1.5rem]"
                    iconName="vie-comment-o-alt-rc"
                  />
                  {totalComment ? (
                    <div className="flex max-w-max h-3 px-0.5 absolute -top-1.5 !right-2 justify-center items-center leading-normal box-border rounded-[5px] !text-[.625rem] !text-center z-[1] bg-vo-dark-gray-950">
                      {parseInt(totalComment) > 0 ? onNumberViews(totalComment) : ''}
                    </div>
                  ) : (
                    ''
                  )}
                  <span className="text-[.75rem] text-center mt-1.5">{TEXT.COMMENT}</span>
                </a>
                {shareUrl && (
                  <a
                    className="shared shared--link !justify-start !text-white hover:!text-vo-green"
                    title={`${TEXT.SHARE} - ${title}`}
                    tabIndex={-1}
                    onClick={shareOnSocial}
                    href={isKid ? undefined : shareUrl}
                    target="_blank"
                    rel="noreferrer"
                  >
                    <NewIcon
                      iCustomizeClass="!text-[1rem] md:!text-[1.5rem]"
                      iconName="vie-location-share-o"
                    />
                    <span className="text-[.75rem] text-center mt-1.5">{TEXT.SHARE}</span>
                  </a>
                )}
                {showTitleRestriction && (
                  <button
                    className="comment-counter !justify-start !text-white hover:!text-vo-green"
                    tabIndex={-1}
                    title="Title Restriction"
                    onClick={handleOpenResctrictPopup}
                    type="button"
                  >
                    <NewIcon
                      iCustomizeClass="!text-[1rem] md:!text-[1.5rem]"
                      iconName="vie-ban-o"
                    />
                    <span className="text-[.75rem] text-center mt-1.5">
                      {TEXT.TITLE_RESTRICTION}
                    </span>
                  </button>
                )}
              </div>

              {(people?.actor || []).length > 0 && (
                <div className="tags-group">
                  <label className="tags tags--muted">Diễn viên:</label>
                  {(people?.actor || []).map((item: any, i: any) => {
                    const { seo, name } = item;
                    const isLastItem = i === people.actor.length - 1;
                    return (
                      <VieLink key={i} as={seo?.url || ''} href={PAGE.ARTIST}>
                        <a className="tags text-white" title={seo?.title} tabIndex={-1}>
                          {name + (isLastItem ? '' : ', ')}
                        </a>
                      </VieLink>
                    );
                  })}
                </div>
              )}
              {(people?.director || []).length > 0 && (
                <div className="tags-group">
                  <label className="tags tags--muted">Đạo diễn:</label>
                  {(people?.director || []).map((item: any, i: any) => {
                    const { seo, name } = item;
                    const isLastItem = i === people?.director.length - 1;
                    return (
                      <VieLink key={i} as={seo?.url || ''} href={PAGE.ARTIST}>
                        <a className="tags text-white" title={seo?.title} tabIndex={-1}>
                          {name + (isLastItem ? '' : ', ')}
                        </a>
                      </VieLink>
                    );
                  })}
                </div>
              )}

              {categoryFromTags?.name && (
                <div className="tags-group">
                  <label className="tags tags--muted">Thể loại: </label>
                  <VieLink as={categoryFromTags?.seo?.url || ''} href={PAGE.TAG}>
                    <a
                      className="tags text-white"
                      tabIndex={-1}
                      title={categoryFromTags?.seo?.title}
                    >
                      {categoryFromTags?.name}
                    </a>
                  </VieLink>
                </div>
              )}
            </div>
          )}
        </div>
        {remainTimeText &&
          !isMasterBanner &&
          !expand &&
          !(startTextLive && isLiveStream && !isPremiere) && (
            <Tags
              tagKey={TAG_KEY.REMIND}
              description={remainTimeText}
              iClass="icon--small icon--tiny-xs text-yellow91"
            />
          )}
      </div>
    );
  }
);
export default CardInfo;
