import React, { useEffect } from 'react';
import { createTimeout } from '@vieon/core/utils/common';

let scrollTimer: any = null;

const CardList = React.memo((props: any) => {
  const { numberItem, renderContent, heightCheckScroll, onScrollDown } = props;

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimer) clearTimeout(scrollTimer);
    };
  }, []);

  const handleScroll = (e: any) => {
    if (scrollTimer) clearTimeout(scrollTimer);
    scrollTimer = createTimeout(() => {
      onDetectScrollDown(e);
    }, 300);
  };

  const onDetectScrollDown = (e: any) => {
    const scrollingElement = e?.target?.scrollingElement;
    const { clientHeight } = scrollingElement;
    const { scrollTop } = scrollingElement;

    if (scrollTop + clientHeight > scrollingElement.scrollHeight - (heightCheckScroll || 100)) {
      if (typeof onScrollDown === 'function' && onScrollDown) {
        onScrollDown();
      }
    }
  };
  const className = 'card-group group-margin-y-6 group-margin-x-4 col-x-small-2 col-medium-4 ';

  return (
    <div
      className={className + (numberItem ? 'col-large-5' : 'col-large-6')}
      data-number-item={numberItem || '6'}
    >
      {renderContent && renderContent()}
    </div>
  );
});

export default CardList;
