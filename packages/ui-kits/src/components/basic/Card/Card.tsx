import React, { useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import get from 'lodash/get';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { getUserNotifyComingSoon } from '@vieon/core/store/actions/user';
import {
  closeAllTabFullScreen,
  expandPreview,
  openCardHover,
  openCardRelatedHover
} from '@vieon/core/store/actions/popup';
import {
  CARD_DETAIL,
  CONTENT_TYPE,
  EL_ID,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  EL_ROUNDED_CLASS,
  PAGE,
  POSITION,
  RIBBON_TYPE,
  TVOD,
  USER_TYPE
} from '@vieon/core/constants/constants';
import {
  addParamToUrlVieON,
  checkIsFullscreen,
  createTimeout,
  getLiveTime,
  getSLugTVodFromRouter,
  openAppMobile,
  parseParamsFromContent,
  parseQueryString,
  removeURLQueryParams
} from '@vieon/core/utils/common';
import PageApi from '@vieon/core/api/cm/PageApi';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import { TEXT } from '@vieon/core/constants/text';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { formatTimeTVodString } from '@vieon/core/services/contentService';
import { useVieRouter } from '@customHook';
import { setLive } from '@vieon/models/epgItem';
import { setStartTimeLiveStream } from '@vieon/core/services/datetimeServices';
import Tags from '../components/basic/Tags/Tags';
import classNames from 'classnames';
import CardInfoSeo from './CardInfoSeo';
import CardImage from './CardImage';
import CardTags from './CardTags';
import Styles from './Card.module.scss';
import DetailApi from '@/apis/detailApi';

declare global {
  interface Window {
    cardData?: any;
    mouseOverCardTimer?: any;
    cardTimer?: any;
  }
}

const Card = React.forwardRef((props: any, ref: any) => {
  const dispatch = useDispatch();
  const router = useVieRouter();
  const cardTimer = useRef<any>(null);

  const masterBannerData = useSelector((state: any) => {
    const dataMenu = state?.Menu?.activeSubMenu || state?.Menu?.activeMenu || {};
    return state?.Page?.pageBanner?.[dataMenu?.seo?.url];
  });
  const { appDownload } = useSelector((state: any) => state?.App?.webConfig || {});
  const config = useSelector((state: any) => state?.App?.webConfig);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const expiredString = get(config, 'tVod.text.expiredString', '');
  const mouseOverCardTimer = useRef<any>(null);
  const profile = useSelector((state: any) => state?.Profile?.profile);
  useEffect(
    () => () => {
      clearTimeout(mouseOverCardTimer.current);
      clearTimeout(cardTimer.current);
    },
    []
  );

  const {
    cardData,
    index,
    randomID,
    isMain,
    isSearchPage,
    onClick,
    activeLiveTvID,
    isLiveStreamFirstRibbonID,
    isLivetvBroadcasting,
    onSkipBanner,
    onContentSelected,
    isFullscreenPaymentTrigger,
    contentTypeTvod,
    notLazy,
    disableInteraction,
    enableBanner,
    isEventRelated,
    lengthEventRelated,
    isFullscreen,
    isEndScreenVod,
    onBackToPlayer
  } = (props as any) || {};
  const {
    id,
    title,
    images,
    ribbonType,
    href,
    url,
    seo,
    moreInfo,
    isTopView,
    tagDisPlay,
    isOriginal,
    altSEOImg,
    isWatchMoreRibbon,
    progressPercent,
    isLiveTv,
    shortDescription,
    ribbonId,
    tvod,
    isLiveStream,
    startTime,
    isLive,
    hasPVOD,
    type
  } = cardData || {};
  //filter expire tag with tsvod content
  const tagListDisplay = cardData?.isSvodTvod
    ? tagDisPlay.filter((tag: any) => tag !== 'Expired')
    : tagDisPlay;

  const totalTitleLength = useMemo(
    () => (tagDisPlay || []).reduce((sum: any, tag: any) => sum + tag.length, 0),
    [tagDisPlay]
  );

  const isMonopolyTag = useMemo(() => {
    if (!tagListDisplay) return false;
    const isMonopoly = tagListDisplay?.find((tag: any) => {
      if (tag.toUpperCase().includes(TEXT.MONOPOLY.toUpperCase())) return tag;
    });
    if (isMonopoly) {
      return isMonopoly;
    }
    return null;
  }, [tagListDisplay]);

  const startTextLive = useMemo(() => {
    if (startTime) {
      if (type === CONTENT_TYPE.EPG || type === CONTENT_TYPE.LIVE_TV) {
        const { startText } = setLive(startTime);
        return startText;
      }
      if (type === CONTENT_TYPE.LIVESTREAM) {
        return setStartTimeLiveStream(startTime, isLive);
      }
    }
    return '';
  }, [startTime, type, isLive]);

  const cardElId = `${ribbonId || 'RB_ID'}_${id}_${index}_rd_${randomID}`;
  const fetchDefaultEpisode = async () => {
    if (cardData?.type === CONTENT_TYPE.SEASON && !cardData?.defaultEpisode) {
      try {
        const isGlobal = false;
        const { data } = await DetailApi.getContentById({
          contentId: cardData?.id,
          isGlobal
        });

        if (data && data.defaultEpisode) {
          return {
            ...cardData,
            defaultEpisode: data.defaultEpisode
          };
        }
      } catch (error) {
        console.error('Error fetching default episode:', error);
      }
    }
    return null;
  };

  const onMouseEnter = async (e: any) => {
    let elOffset: any = null;
    let resultWithDefaultEpisodeData: any = null;

    if (cardData && cardData.type === CONTENT_TYPE.SEASON) {
      resultWithDefaultEpisodeData = await fetchDefaultEpisode();
    }

    if (e && e.target) {
      elOffset = e.target.getBoundingClientRect();
    }

    if (!cardData || (isLiveTv && checkIsFullscreen()) || isMobile) return;
    clearTimeout(window.mouseOverCardTimer);
    mouseOverCardTimer.current = createTimeout(() => {
      setHoverData({
        randomID,
        cardData:
          cardData.type === CONTENT_TYPE.SEASON && resultWithDefaultEpisodeData
            ? resultWithDefaultEpisodeData
            : cardData,
        elOffset
      });
    }, 250);
  };

  const handleMouseLeave = () => {
    clearTimeout(mouseOverCardTimer.current);
  };

  const getLinkPlayLiveStreamDetail = ({ cardData }: any) =>
    PageApi.getLivestreamEventsById({
      id: cardData?.id,
      dispatch,
      isCard: true
    }).then((res) => {
      if (res) {
        const linkPlay = {
          hlsLinkPlay: res?.trailerLinkPlay?.hls || '',
          dashLinkPlay: res?.trailerLinkPlay?.dash || ''
        };
        return {
          linkPlay,
          data: res
        };
      }
      return {
        linkPlay: {},
        data: null
      };
    });
  const handleSubcribedContent = async (data: any) => {
    // Cast the dispatch result to Promise<any> to resolve TypeScript error
    const result = dispatch(getUserNotifyComingSoon([data.id])) as unknown as Promise<any>;
    const res = await result;
    if (res?.data) {
      return res?.data[id];
    }
    return null;
  };

  const setHoverData = async ({ randomID, cardData }: any) => {
    if (disableInteraction) return;
    const currentElOffset = ref?.current?.[randomID]?.getBoundingClientRect();
    if (currentElOffset?.top > 0) {
      const cardWidth = currentElOffset?.width || 0;
      const cardHeight = currentElOffset?.height || 0;
      // new bubbleWidth
      const bubbleWidth = currentElOffset?.width || 0;
      const bubbleHeight = currentElOffset?.height || 0;

      let width = cardWidth * 1.2;
      const height = cardHeight;
      let bubbleContainerWidth = bubbleWidth * 1.2;
      const bubbleContainerHeight = bubbleHeight;
      if (isMain || isOriginal) {
        width = cardWidth * 1.2;
        bubbleContainerWidth = bubbleWidth * 1.2;

        if (isMain && width < CARD_DETAIL.MIN_WIDTH) {
          width = CARD_DETAIL.MIN_WIDTH;
          bubbleContainerWidth = CARD_DETAIL.MIN_WIDTH;
        } else {
          if (width < CARD_DETAIL.MIN_WIDTH_ORIGINAL) width = CARD_DETAIL.MIN_WIDTH_ORIGINAL;
          if (bubbleContainerWidth < CARD_DETAIL.MIN_WIDTH_ORIGINAL) {
            bubbleContainerWidth = CARD_DETAIL.MIN_WIDTH_ORIGINAL;
          }
        }
      } else {
        if (width < CARD_DETAIL.MIN_WIDTH) {
          width = CARD_DETAIL.MIN_WIDTH;
        }
        if (bubbleContainerWidth < CARD_DETAIL.MIN_WIDTH) {
          bubbleContainerWidth = CARD_DETAIL.MIN_WIDTH;
        }
      }

      let left = currentElOffset?.left - (width - cardWidth) / 2 + 4;
      left = left < 10 ? 10 : left;
      if (document.body.clientWidth - 450 > 0 && !isOriginal) {
        const maxLeft = document.body.clientWidth - 450;
        left = left > maxLeft ? maxLeft : left;
      }

      const top = window?.scrollY + currentElOffset?.top - 100;
      let bottom = isFullscreen
        ? currentElOffset?.top + height
        : window?.scrollY + currentElOffset?.top + height;
      const isWideScreen = document.body.clientWidth >= 2560;
      const isLastIndex = lengthEventRelated === index;

      const displayPosition = () => {
        const hasHeader: any = document.getElementById('primary-header');
        const safeZoneY = (bubbleHeight * 1.2) / 2 + (hasHeader?.clientHeight ?? 0);
        const safeZoneX = bubbleContainerWidth / 2 + 24;
        const id =
          document.getElementById('rv') ||
          document.getElementById('rankingBoard') ||
          document.getElementById('artistBoard');
        const leftBoardWidth = id?.clientWidth ?? 0;

        // bubble in safe zone top and right
        if (
          window.innerWidth - currentElOffset?.left - bubbleWidth <= safeZoneX &&
          currentElOffset?.top < safeZoneY
        ) {
          return POSITION.TOP_RIGHT;
        }
        // bubble in safe zone top and left
        if (
          currentElOffset?.left - leftBoardWidth < safeZoneX &&
          currentElOffset?.top < safeZoneY
        ) {
          return POSITION.TOP_LEFT;
        }
        // bubble in safe zone bottom and right
        if (
          window.innerWidth - currentElOffset?.left - bubbleWidth <= safeZoneX &&
          window.innerHeight - currentElOffset?.top - bubbleHeight < safeZoneY
        ) {
          return POSITION.BOTTOM_RIGHT;
        }
        // bubble in safe zone bottom and left
        if (
          currentElOffset?.left - leftBoardWidth < safeZoneX &&
          window.innerHeight - currentElOffset?.top - bubbleHeight < safeZoneY
        ) {
          return POSITION.BOTTOM_LEFT;
        }
        // bubble in safe zone with top and center
        if (
          currentElOffset?.left > safeZoneX &&
          window.innerWidth - currentElOffset?.left - bubbleWidth > safeZoneX &&
          currentElOffset?.top < safeZoneY
        ) {
          return POSITION.TOP_CENTER;
        }
        // bubble in safe zone bottom and center
        if (
          currentElOffset?.left > safeZoneX &&
          window.innerWidth - currentElOffset?.left - bubbleWidth > safeZoneX &&
          window.innerHeight - currentElOffset?.top - bubbleHeight < safeZoneY
        ) {
          return POSITION.BOTTOM_CENTER;
        }
        // bubble in safe zone middle and right
        if (
          window.innerWidth - currentElOffset?.left - bubbleWidth <= safeZoneX &&
          currentElOffset?.top > safeZoneY &&
          window.innerHeight - currentElOffset?.top - bubbleHeight > safeZoneY
        ) {
          return POSITION.MIDDLE_RIGHT;
        }
        // bubble in safe zone middle and left
        if (
          currentElOffset?.left - leftBoardWidth < safeZoneX &&
          currentElOffset?.top > safeZoneY &&
          window.innerHeight - currentElOffset?.top - bubbleHeight > safeZoneY
        ) {
          return POSITION.MIDDLE_LEFT;
        }

        return POSITION.CENTER;
      };

      if (isEventRelated) {
        if (isWideScreen) {
          if (isFullscreen) {
            left = isLastIndex ? left - 40 : left;
          } else {
            left = isLastIndex ? left - 130 : left - 80;
          }
          bottom += 40;
        } else if (isFullscreen) {
          left = isLastIndex ? left + 30 : left;
          bottom += 30;
        } else {
          left = isLastIndex ? left - 165 : left;
          bottom -= 15;
        }
      } else if (isEndScreenVod) {
        if (isWideScreen) {
          if (isFullscreen) {
            left = isLastIndex ? left - 40 : left;
          } else {
            left = isLastIndex ? left - 130 : left - 80;
          }
          bottom += 40;
        } else if (isFullscreen) {
          left = isLastIndex ? left + 30 : left;
          bottom += 30;
        } else {
          left = isLastIndex ? left + 30 : left;
          bottom += 25;
        }
      }

      if (cardData.type === CONTENT_TYPE.LIVESTREAM) {
        // LiveStream Item GET DETAIL
        const { linkPlay, data } = await getLinkPlayLiveStreamDetail({ cardData });
        let dataSubscribe = false;
        if (cardData.isComingSoon) {
          dataSubscribe = await handleSubcribedContent(cardData);
        }
        cardData.linkPlay = linkPlay;
        cardData.dataLiveStream = data;
        cardData.isSubcribed = dataSubscribe;
      }
      if (isSearchPage) {
        cardData.searchData = {
          id: cardData?.id,
          keyword: router?.query?.q,
          position: index,
          type: cardData?.type
        };
        if (cardData.dataLiveStream) {
          if (!cardData.timeLiveStream) {
            cardData.timeLiveStream = getLiveTime(cardData.dataLiveStream?.timeStart);
          }
          cardData.isLive = cardData.dataLiveStream?.isLive > 0;
        }
      }
      if (isEventRelated || isEndScreenVod) {
        dispatch(
          openCardRelatedHover({
            ...cardData,
            isMain,
            onContentSelected,
            contentTypeTvod,
            idHover: `${cardElId}_${index}`,
            isFullscreenPaymentTrigger,
            offset: {
              left,
              width,
              height,
              bottom
            },
            bubbleStyles: {
              width: bubbleWidth,
              height: bubbleHeight,
              top: currentElOffset?.top + window?.scrollY,
              left: currentElOffset?.left
            },
            bubbleContainerStyles: {
              width: bubbleContainerWidth,
              height: bubbleContainerHeight
            },
            onBackToPlayer: isEndScreenVod && onBackToPlayer,
            isEndScreenVod,
            enableBanner,
            displayPosition: displayPosition()
          })
        );
      } else {
        dispatch(
          openCardHover({
            ...cardData,
            idHover: `${cardElId}_${index}`,
            isMain,
            onContentSelected,
            contentTypeTvod,
            isFullscreenPaymentTrigger,
            offset: {
              left,
              top,
              width
            },
            bubbleStyles: {
              width: bubbleWidth,
              height: bubbleHeight,
              top: currentElOffset?.top + window?.scrollY,
              left: currentElOffset?.left
            },
            bubbleContainerStyles: {
              width: bubbleContainerWidth,
              height: bubbleContainerHeight
            },
            enableBanner,
            displayPosition: displayPosition()
          })
        );
      }
    }
  };

  const handleClick = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    if (disableInteraction) return;
    const contentId = cardData?.id;
    const type = cardData?.type;
    segmentContent(cardData);

    if (cardData?.id === EL_ID.ONLY_APP_ITEM) {
      clearTimeout(cardTimer.current);
      cardTimer.current = setTimeout(() => {
        openAppMobile(appDownload);
      }, 200);
      return;
    }

    if (isMobile) {
      const mHref = href || seo?.url || '';
      const mUrl = url || seo?.url || '';
      const mQueryParams = addParamToUrlVieON(router?.query);
      if (mUrl && mHref) {
        router.push(
          {
            pathname: mHref,
            query: mQueryParams
          },
          {
            pathname: mUrl,
            query: mQueryParams
          }
        );
      }
    } else {
      if (
        type === CONTENT_TYPE.SEASON ||
        type === CONTENT_TYPE.EPISODE ||
        type === CONTENT_TYPE.MOVIE
      ) {
        const pathname = router?.pathname;
        const isRecommendPage =
          pathname === PAGE.COLLECTION_RIB && (router?.asPath || '').indexOf('?id') > -1;
        const idRecommend = parseQueryString(location.search)?.id;
        const queryParams = addParamToUrlVieON(
          router?.query,
          isRecommendPage && idRecommend
            ? {
                id: idRecommend,
                vid: id
              }
            : { vid: contentId }
        );

        let url = (router?.asPath || '').split('?');
        url = url?.[0] || '/';
        if ((url || '').indexOf('#') > -1) url = removeURLQueryParams(url, '#');
        if (isEndScreenVod && typeof onBackToPlayer === 'function') {
          onBackToPlayer(cardData);
        } else {
          router.push(
            {
              pathname,
              query: queryParams
            },
            {
              pathname: url,
              query: queryParams
            }
          );
        }
        dispatch(expandPreview({ expand: true }));
      } else {
        const { page, params, link } = parseParamsFromContent(cardData);
        if (type === CONTENT_TYPE.LIVESTREAM && (seo?.url || '').includes('.html')) {
          const queryParams = getSLugTVodFromRouter(seo?.url);
          router.push(
            {
              pathname: PAGE.LIVE_TVOD_SLUG,
              query: queryParams
            },
            { pathname: seo?.url }
          );
        } else {
          router.push(
            {
              pathname: page,
              query: { ...params }
            },
            { pathname: link }
          );
        }

        if (type === CONTENT_TYPE.LIVE_TV || type === CONTENT_TYPE.LIVESTREAM) {
          if (typeof window === 'undefined') return;
          if (checkIsFullscreen()) {
            dispatch(closeAllTabFullScreen({ off: true }));
          }
        }
      }
      if (isSearchPage) {
        cardData.position = index;
        return (
          onClick &&
          onClick({
            id: contentId,
            keyword: router?.query?.q,
            type,
            item: cardData
          })
        );
      }
      if (onSkipBanner) onSkipBanner();
      if (checkIsFullscreen() && isEventRelated) {
        document.exitFullscreen();
      }
    }
  };

  const setActiveLiveTV = () => {
    if (
      (router?.pathname !== PAGE.LIVE_TV &&
        router?.pathname !== PAGE.LIVE_TV_SLUG &&
        router?.pathname !== PAGE.LIVE_TV_EPG) ||
      (cardData?.timeFutureActive > 0 && isLivetvBroadcasting) // Check future card item
    ) {
      return false;
    }
    let check = false;
    if (router?.pathname === PAGE.LIVE_TV) {
      check = activeLiveTvID === id;
    } else if (cardData?.seo?.url) {
      check = (router?.asPath || '').includes(cardData?.seo?.url);
      if (cardData?.type === CONTENT_TYPE.EPG) {
        check = check || activeLiveTvID === id;
        if (cardData?.isComingSoon) check = false;
      }
    }

    return check;
  };
  const setActiveLiveStream = () => {
    if (router?.pathname !== PAGE.LIVE_STREAM && router?.pathname !== PAGE.LIVE_STREAM_SLUG) {
      return false;
    }
    const path = ((router?.asPath || '').split('?') || [])?.[0];
    const isIncludedSlug = (path || '').split('/')?.length > 4;
    if (router?.pathname === PAGE.LIVE_STREAM && !router?.query?.slug && !isIncludedSlug) {
      return isLiveStreamFirstRibbonID === id || cardData?.isLiveStreamFirstRibbonID === id;
    }
    return path === seo?.url;
  };

  const segmentContent = (contentData: any) => {
    if (onContentSelected) {
      onContentSelected({
        cardData: contentData,
        contentTypeTvod
      });
    } else {
      TrackingApp.contentSelected({
        data: {
          ...contentData,
          seasonThumb: contentData?.images?.thumbnail,
          seasonGenre: contentData?.genreText
        },
        masterBannerData,
        clickType: VALUE.DIRECT,
        isLiveTv: contentData?.isLiveTv,
        category: contentData?.isLiveTv ? contentData?.categoryTracking : undefined
      });
    }
  };

  const { strTimeTag, benefitType } = tvod || {};
  const remainTimeText = useMemo(() => {
    if (!strTimeTag) return '';
    return formatTimeTVodString({
      strConfig: expiredString,
      strTime: strTimeTag
    });
  }, [strTimeTag, expiredString]);

  let cardClass = 'card card--vod';
  let ratioClass = ' ratio-16-9';
  if (isTopView) {
    cardClass += ' card--vod-rank';
    ratioClass = ' ratio-3-43';
  } else if (isOriginal) ratioClass = ' ratio-1-2';
  if (isMain) cardClass += ' card--vod-variant';

  const thumbClass = `card__thumbnail-loader overflow${ratioClass}`;
  const activeLiveTv = setActiveLiveTV();
  const activeLiveStream = setActiveLiveStream();

  const shouldDisplayTag = () => {
    if ((profile?.isPremium || profile?.type === USER_TYPE.VIP) && cardData?.isSvodTvod) return '';
    const conditionShowTag =
      (benefitType === TVOD.USER_TYPE.RENTED || benefitType === TVOD.USER_TYPE.WATCHED) &&
      !isLiveStream &&
      cardData?.type !== CONTENT_TYPE.EPG &&
      ribbonType !== RIBBON_TYPE.EPG;
    return conditionShowTag ? (
      !isMonopolyTag ? (
        <div className={classNames(Styles.tagGroup3)}>
          <Tags
            title={remainTimeText}
            size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
            theme={EL_THEME_CLASS.GREEN}
            subClass="!w-max !max-w-max"
          />
        </div>
      ) : (
        <div className={classNames(Styles.tagGroup3)}>
          <Tags
            title={isMonopolyTag}
            size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
            theme={EL_THEME_CLASS.GREEN}
            subClass="!w-max !max-w-max"
          />
        </div>
      )
    ) : (
      ''
    );
  };
  const getTopRankingImage = (index: number): string => {
    const key = `topRanking_${index}` as keyof typeof ConfigImage;
    const image: any = ConfigImage[key] || ConfigImage.defaultBanner16x9;
    return image;
  };

  return (
    <>
      <div
        id={`${cardElId}_${index}`}
        key={index}
        className={cardClass}
        onMouseEnter={onMouseEnter}
        onMouseLeave={handleMouseLeave}
        ref={(el) => (ref && ref?.current ? (ref.current[randomID] = el) : null) as any}
      >
        {isTopView && (
          <img src={getTopRankingImage(index)} className="card__thumb-img--rank" alt="VieON" />
        )}
        <div className="card__thumbnail">
          <a
            className={thumbClass || 'card__thumbnail-img'}
            tabIndex={-1}
            title={title}
            onClick={handleClick}
            href={url || seo?.url || ''}
            style={disableInteraction ? { cursor: 'initial' } : undefined}
          >
            <CardImage
              className="card__thumb-img swiper-lazy"
              ribbonType={ribbonType}
              images={images}
              alt={altSEOImg}
              isMain={isMain}
              notLazy={notLazy}
              title={seo?.title || title}
            />
          </a>
          {cardData?.images?.logoLight11 && cardData?.type === CONTENT_TYPE.EPG && (
            <div className={Styles.channelBrand}>
              <div className={Styles.channelBrandBackdrop}>
                <picture className="block w-full h-full">
                  <source media="(min-width:1024px)" srcSet={ConfigImage.channelBackdropForLarge} />
                  <img
                    className="w-full"
                    src={ConfigImage.channelBackdrop}
                    alt={seo?.title || title}
                    width={26}
                    height={26}
                    style={{ aspectRatio: '1/1' }}
                  />
                </picture>
              </div>
              <div className={Styles.channelBrandC}>
                <img src={cardData?.images?.logoLight11} alt={seo?.title || title} />
              </div>
            </div>
          )}
          <CardTags
            cardData={cardData}
            isEventRelated={isEventRelated || isEndScreenVod}
            ribbonType={ribbonType}
            hideMonopolyTag={
              !(hasPVOD && type === CONTENT_TYPE.SEASON) &&
              benefitType !== TVOD.USER_TYPE.RENTED &&
              benefitType !== TVOD.USER_TYPE.WATCHED &&
              !isEventRelated &&
              !isEndScreenVod &&
              cardData?.type !== CONTENT_TYPE.EPG &&
              ribbonType !== RIBBON_TYPE.EPG
            }
            // hideMonopolyTag to clear duplicate monopoly tag
          />
          {shouldDisplayTag()}
          {activeLiveTv && (
            <span className="icon icon--live-img absolute bottom-1 right-1" style={{ zIndex: 2 }}>
              <img src={ConfigImage.liveTVActiveIcon} alt="liveTv" />
            </span>
          )}
          {activeLiveStream && (
            <span className="icon icon--live-img absolute bottom-1 right-1" style={{ zIndex: 2 }}>
              <img src={ConfigImage.liveTVActiveIcon} alt="liveTv" />
            </span>
          )}
        </div>

        {(cardData?.type === CONTENT_TYPE.EPG || ribbonType === RIBBON_TYPE.EPG) && (
          <div className="card__section absolute bottom left make-overlay">
            <div className="card__section-wrap">
              {title && (
                <h4 className="card__title">
                  <a
                    className="link line-clamp"
                    title={title}
                    data-line-clamp="1"
                    tabIndex={-1}
                    onClick={handleClick}
                  >
                    {title}
                  </a>
                </h4>
              )}
              {cardData?.isLive && (
                <div className="card__live font-semibold">{TEXT.BROADCASTING}</div>
              )}{' '}
              {!cardData?.isLive && cardData?.isComingSoon && startTextLive && (
                <div className="card__remind font-semibold">{startTextLive || ''}</div>
              )}
            </div>
          </div>
        )}
        <div
          className={classNames(
            'card__section w-full absolute right bottom',
            !isTopView ? 'left' : '',
            isWatchMoreRibbon ? 'bottom-[3px]' : 'bottom'
          )}
        >
          <div className="card__section-wrap w-full !pb-0">
            <CardInfoSeo
              seo={{ ...seo, description: moreInfo?.programme?.seo?.description }}
              title={title || ''}
              shortDescription={shortDescription || ''}
            />
            {tagListDisplay?.length > 0
              ? !(hasPVOD && type === CONTENT_TYPE.SEASON) &&
                benefitType !== TVOD.USER_TYPE.RENTED &&
                benefitType !== TVOD.USER_TYPE.WATCHED &&
                !isEventRelated &&
                !isEndScreenVod &&
                cardData?.type !== CONTENT_TYPE.EPG &&
                ribbonType !== RIBBON_TYPE.EPG &&
                !(cardData?.categoryTracking?.id === 'DPS') && (
                  <div
                    className={classNames(
                      Styles.tagGroup3ForSection,
                      {
                        'max-w-[88%]': isOriginal || isTopView,
                        'xl:max-w-[85%] 2xl:max-w-[80%] max-w-[95%]': !isOriginal && !isTopView,
                        'flex-wrap 2xl:flex-nowrap':
                          tagDisPlay.length > 1 &&
                          totalTitleLength < 16 &&
                          (isOriginal || isTopView),
                        'flex-wrap': totalTitleLength > 15 && (isOriginal || isTopView),
                        '!w-full': totalTitleLength > 30,
                        '!w-max': totalTitleLength <= 30
                      },
                      'flex justify-center'
                    )}
                  >
                    {isMonopolyTag ? (
                      <Tags
                        title={isMonopolyTag}
                        size={
                          isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM
                        }
                        theme={EL_THEME_CLASS.GREEN}
                        rounded={EL_ROUNDED_CLASS.TOP_SM}
                      />
                    ) : (
                      tagListDisplay
                        .filter((tag: any) => tag.toUpperCase() !== TEXT.PARALLEL.toUpperCase())
                        .map((tag: any, i: any) => {
                          return (
                            <Tags
                              subClass={classNames({
                                '!w-1/2 !max-w-1/2': totalTitleLength > 30,
                                '!w-full !max-w-full 2xl:!w-max 2xl:!max-w-max':
                                  isOriginal || isTopView
                              })}
                              key={i}
                              size={
                                isOriginal
                                  ? EL_SIZE_CLASS.VARIANT_LARGE
                                  : EL_SIZE_CLASS.VARIANT_MEDIUM
                              }
                              theme={i === 0 ? EL_THEME_CLASS.GREEN : EL_THEME_CLASS.WHITE}
                              title={tag}
                            />
                          );
                        })
                    )}
                  </div>
                )
              : ''}
          </div>
        </div>
        {isWatchMoreRibbon && (
          <div
            className={Styles.Progress}
            role="progressbar"
            aria-valuenow={50}
            aria-valuemin={0}
            aria-valuetext="50 percent"
            aria-valuemax={100}
          >
            <div className={Styles.ProgressMeter} style={{ width: `${progressPercent || 0}%` }} />
          </div>
        )}
      </div>
    </>
  );
});

export default Card;
