import React from 'react';
import { setCardImage } from '@vieon/core/utils/common';
import Image from '../Image/Image';

const CardImage = React.memo(
  ({
    images,
    ribbonType,
    isMasterBanner,
    className,
    isMain,
    alt,
    onLoaded,
    isCardDetail,
    expand,
    isCollectionBanner,
    isMobile,
    title,
    notLazy,
    isSchedule
  }: any) => {
    const { src, defaultSrc } = setCardImage({
      images,
      ribbonType,
      isMain,
      isMasterBanner,
      isCardDetail,
      expand,
      isCollectionBanner,
      isMobile,
      isSchedule
    });

    return (
      <Image
        notWebp={images?.notWebp || false}
        className={className}
        // fadeIn={fadeIn}
        src={src || ''}
        defaultSrc={defaultSrc}
        alt={alt || 'VieON'}
        title={title || ''}
        imageLazyLoad={!notLazy}
        onLoaded={onLoaded}
      />
    );
  }
);

export default CardImage;
