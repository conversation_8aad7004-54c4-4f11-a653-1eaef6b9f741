import React, { useMemo } from 'react';
import { TEXT } from '@vieon/core/constants/text';
import { EL_PROPERTY, EL_ROUNDED_CLASS, EL_THEME_CLASS } from '@vieon/core/constants/constants';
import { isMobile } from 'react-device-detect';
import { useSelector } from 'react-redux';
import Tags from '../Tags/Tags';
import Button from '../Buttons/Button';
import Image from '../Image/Image';

const EpisodeCard = ({ item, active, onClickItem, currentEpisode }: any) => {
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { title, shortDescription, progressPercent, images, isPremium, isPremiumDisplay } =
    item || {};

  const isDisplayTagFree = useMemo(
    () =>
      isGlobal && !isPremium && !profile?.isPremium && isPremiumDisplay === TEXT.FREE.toUpperCase(),
    [isGlobal, isPremium, isPremiumDisplay, profile?.isPremium]
  );

  return (
    <div className={`player__card player__card--episode${active ? ' active' : ''}`}>
      <div className="player__card__wrap" onClick={() => onClickItem(item)}>
        <div className={`player__card__section${isMobile ? ' size-w-full' : ''}`}>
          <p className="player__card__title">{title || 'Tập 2. Nhà là tất cả'}</p>
          {isMobile && (
            <div className="progress m-t1" aria-label="progressbar" role="progressbar">
              <div className="progress-meter" style={{ width: `${progressPercent}%` }} />
            </div>
          )}
          <p className="player__card__desc line-clamp" data-line-clamp="3">
            {shortDescription || ''}
          </p>
        </div>
        {!isMobile && (
          <div className="player__card__thumbnail">
            <div className="progress" aria-label="progressbar" role="progressbar">
              <div className="progress-meter" style={{ width: `${progressPercent}%` }} />
            </div>
            <div className="player__card__thumbnail-loader ratio-16-9 overflow">
              <Image
                src={images?.thumbnail}
                className='className="player__card__thumbnail-img"'
                alt={title}
              />
            </div>

            {isPremium && isPremiumDisplay && active && (
              <Tags
                isPremiumDisplay={isPremiumDisplay}
                position={EL_PROPERTY.TOP_LEFT}
                rounded={EL_ROUNDED_CLASS.BOTTOM_RIGHT}
              />
            )}
            {isDisplayTagFree && active && (
              <Tags
                theme={EL_THEME_CLASS.GREEN}
                position={EL_PROPERTY.TOP_LEFT}
                rounded={EL_ROUNDED_CLASS.BOTTOM_RIGHT}
                title={TEXT.FREE}
              />
            )}
            {currentEpisode?.id !== item?.id && (
              <Button
                className="player__button player__button--play"
                subTitle={TEXT.PLAY}
                iconName="vie-play-solid-rc p-l1"
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EpisodeCard;
