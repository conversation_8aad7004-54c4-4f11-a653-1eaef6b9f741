import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import Input from '../components/Input';
import Button from '../components/Button';
import IconPhone from '../components/Icons/IconPhone';
import IconEmail from '../components/Icons/IconEmail';
import { TYPE_INPUT, TYPE_TRIGGER_AUTH, LOGIN_INPUT_TYPE } from '@vieon/core/constants/types';
import {
  DATA_DROPDOWN_COUNTRY,
  HTTP_CODE,
  PAGE,
  REGEX,
  FLOW_GLOBAL_AUTH
} from '@vieon/core/constants/constants';
import { TEXT } from '@vieon/core/constants/text';
import { validateEmail } from '@vieon/core/utils/common';
import QRForm from '../components/Authentication/QRForm';
import get from 'lodash/get';
import { useSelector } from 'react-redux';
import SocialPlugin from '../SocialPlugin';
import TextSupport from './TextSupport';
import Modal from './index';
import TrackingPayment from '@vieon/tracking/functions/payment';
import { useVieRouter } from '@customHook';
import MessageCasesInput from './MessageCasesInput';

const PhoneOrEmailSelected = ({
  isHasBtnSetupLaterBindAccount,
  onSkipBindAccount,
  isBindPhone,
  isUpdatePhoneNumber,
  userName,
  trigger,
  isOnlySupportVN,
  onSubmit,
  onChangeUserName,
  onChangeCountryCode,
  dataVerify,
  isPhoneNumber,
  geoCountry,
  countryCode,
  inputType,
  setInputType,
  titlePopupTriggerAuth,
  flow
}: any) => {
  const router = useVieRouter();
  const [error, setError] = useState<any>('');
  const [errorForm, setErrorForm] = useState<any>('');
  const [disabled, setDisabled] = useState(true);
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const social = useSelector((state: any) => state?.App?.social);
  const isAllowSocialLogin = useMemo(() => social?.allowLoginFB && social?.allowLoginGG, [social]);

  const dataDropdown = useMemo(() => {
    if (isPhoneNumber) {
      if (isOnlySupportVN) {
        return DATA_DROPDOWN_COUNTRY.filter((item) => item.value === 'VN');
      }
      if (geoCountry === 'US') {
        return DATA_DROPDOWN_COUNTRY.map((item: any) => ({
          ...item,
          isDefault: item.value === geoCountry
        }));
      }
      return DATA_DROPDOWN_COUNTRY.map((item: any) => ({
        ...item,
        isDefault: item.value === 'VN'
      }));
    }
    return [];
  }, [isPhoneNumber, isOnlySupportVN, geoCountry]);

  const title = useMemo(() => {
    if (titlePopupTriggerAuth && !(isBindPhone || isUpdatePhoneNumber)) {
      return TEXT[`AUTH_${titlePopupTriggerAuth}`];
    }
    if (isBindPhone || isUpdatePhoneNumber) {
      return TEXT.AUTH_UPDATE_PHONE_NUMBER;
    }
    if (trigger) {
      return TEXT[`AUTH_${trigger}`];
    }
    return TEXT.AUTH_CONTENT;
  }, [trigger, isUpdatePhoneNumber, isBindPhone]);

  const description = useMemo(() => {
    if (isUpdatePhoneNumber || isBindPhone) {
      return TEXT.AUTH_DESC_UPDATE_PHONE_NUMBER;
    }
    if (isPhoneNumber) {
      if (geoCountry === 'VN') {
        return TEXT.PLEASE_ENTER_YOUR_PHONE_TO_CONTINUE;
      }
      if (isOnlySupportVN) {
        return TEXT.AUTH_INPUT_PHONE_ONLY_VN_PLEASE;
      }
    }
    return TEXT.AUTH_INPUT_EMAIL_OR_PHONE_PLEASE;
  }, [isPhoneNumber, isOnlySupportVN, isUpdatePhoneNumber, isBindPhone]);

  const placeholder = useMemo(() => {
    if (isOnlySupportVN || inputType === LOGIN_INPUT_TYPE.PHONE) {
      return TEXT.PLACE_HOLDER_NUMBER_PHONE;
    }
    if (inputType === LOGIN_INPUT_TYPE.EMAIL) {
      return TEXT.PLACE_HOLDER_EMAIL;
    }
    return '';
  }, [isOnlySupportVN, inputType]);

  const destination = useMemo(() => get(router, 'query.destination', '/'), [router?.query]);
  const isRentedContent = useMemo(() => {
    if (destination) {
      return (
        decodeURIComponent(destination).includes(PAGE.RENTAL_CONTENT) ||
        decodeURIComponent(destination).includes(PAGE.PVOD_CONTENT)
      );
    }
    return;
  }, [destination]);

  const isTriggerAuth = useMemo(
    () =>
      (trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE ||
        trigger === TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE_PRE_ORDER ||
        trigger === TYPE_TRIGGER_AUTH.SETTING) &&
      !(
        flow.includes(FLOW_GLOBAL_AUTH.LINK_PHONE_NUMBER) ||
        flow.includes(FLOW_GLOBAL_AUTH.BIND_PHONE)
      ),
    [trigger, flow]
  );

  const handleChangeUserName = (value: any) => {
    onChangeUserName?.(value);
    setDisabled(!value.trim());
    if ((value && error) || (value && errorForm)) {
      setErrorForm('');
      setError('');
    }

    if (geoCountry === 'US') {
      setInputType(
        value.includes('@')
          ? LOGIN_INPUT_TYPE.EMAIL
          : /^\d{6,}$/.test(value) && value.length > 5
          ? LOGIN_INPUT_TYPE.PHONE
          : LOGIN_INPUT_TYPE.UNKNOWN
      );
    }
  };

  const onKeyDown = (e: any) => {
    if (e?.keyCode === 13 || e?.key === 'Enter') {
      handleSubmit();
    }
  };

  const onBlur = () => {
    if (userName && isPhoneNumber && countryCode !== 'VN' && userName.length < 10) {
      setError(TEXT.PHONE_WRONG_TEN);
    }
  };

  const handleSelectCountry = (data: any) => {
    setErrorForm('');
    setError('');
    if (typeof onChangeCountryCode === 'function') onChangeCountryCode(data);
  };

  const handleValidateInput = () => {
    let valid = false;
    if (isPhoneNumber) {
      let regexNumber = REGEX.NUMBER;
      if (countryCode === 'VN') {
        regexNumber = REGEX.VN_PHONE_NUMBER;
        valid = regexNumber.test(userName);
      }
      valid = regexNumber.test(userName);
    } else {
      valid = validateEmail(userName);
    }
    return valid;
  };

  const handleSubmit = () => {
    if (!disabled) {
      const valid = handleValidateInput();
      if (!valid) {
        setError(
          inputType === LOGIN_INPUT_TYPE.UNKNOWN
            ? TEXT.INVALID_PHONE_EMAIL
            : isPhoneNumber
            ? TEXT.INVALID_PHONE_NUMBER
            : TEXT.EMAIL_WRONG
        );
      } else if (isPhoneNumber && countryCode !== 'VN' && userName.length !== 10) {
        setError(TEXT.PHONE_WRONG_TEN);
      } else {
        setError('');
        if (typeof onSubmit === 'function') onSubmit();
      }
    }
  };

  const handleSkipBindAccount = () => {
    if (typeof onSkipBindAccount === 'function') {
      onSkipBindAccount();
    }
  };

  const handleGotoStep2 = () => {
    const path = isRentedContent ? decodeURIComponent(destination) : destination;
    const trackingPayment = new TrackingPayment();

    trackingPayment.laterButtonSelect();
    router.push(path);
  };

  useEffect(() => {
    if (userName) {
      setDisabled(!userName);
    }
  }, [userName]);

  const config = {
    title,
    ...(titlePopupTriggerAuth && !isMobile ? {} : { description }),
    inputs: (
      <Input
        id="userName"
        autoFocus
        className="!h-[42px]"
        value={userName}
        placeholder={placeholder}
        type={TYPE_INPUT.TEXT}
        label={
          geoCountry === 'US'
            ? TEXT.PHONE_OR_EMAIL
            : isPhoneNumber
            ? TEXT.USER_LABEL.PHONE_NUMBER
            : TEXT.USER_LABEL.EMAIL
        }
        dropdownData={inputType === LOGIN_INPUT_TYPE.UNKNOWN ? null : dataDropdown}
        onSelectDropdown={inputType === LOGIN_INPUT_TYPE.UNKNOWN ? null : handleSelectCountry}
        iconPrefix={
          inputType === LOGIN_INPUT_TYPE.UNKNOWN ? (
            ''
          ) : isPhoneNumber ? (
            <IconPhone size="small" />
          ) : (
            <IconEmail size="small" />
          )
        }
        error={error}
        onChange={handleChangeUserName}
        onKeyDown={onKeyDown}
        onBlur={onBlur}
        maxLength={50}
      />
    ),
    messageCasesInput: MessageCasesInput({ systemMessages: errorForm }),
    isSupportLongError: isBindPhone || isUpdatePhoneNumber,
    buttons: (
      <>
        <Button
          title={
            isUpdatePhoneNumber || isBindPhone
              ? TEXT.TITLE_BTN_SUBMIT_CONTINUE
              : TEXT.TITLE_BTN_SUBMIT_START
          }
          isPrimary
          disabled={disabled}
          onClick={handleSubmit}
        />
        {isTriggerAuth && <Button title="Để sau" onClick={handleGotoStep2} />}
      </>
    ),
    buttonSupport: isHasBtnSetupLaterBindAccount ? (
      <span className="text-white font-bold cursor-pointer" onClick={handleSkipBindAccount}>
        {TEXT.LATER}
      </span>
    ) : null,
    textSupport: isUpdatePhoneNumber || isBindPhone ? <TextSupport /> : null,
    footer: isUpdatePhoneNumber || isBindPhone ? null : <SocialPlugin />,
    textDivider: isUpdatePhoneNumber || isBindPhone ? null : TEXT.TEXT_DIVIDER,
    hasDivider: !(isUpdatePhoneNumber || isBindPhone),
    QRForm: isUpdatePhoneNumber || isBindPhone ? null : <QRForm />,
    isMobile,
    isAllowSocialLogin,
    isTriggerAuthPopup: !!titlePopupTriggerAuth
  };

  useEffect(() => {
    if (!isEmpty(dataVerify)) {
      const message =
        dataVerify?.data?.message ||
        dataVerify?.message ||
        TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE;
      if (!dataVerify?.success || dataVerify?.data?.code === HTTP_CODE.FAIL) {
        setErrorForm(message);
      } else {
        setErrorForm('');
      }
    }
  }, [dataVerify]);

  return <Modal {...config} />;
};

export default PhoneOrEmailSelected;
