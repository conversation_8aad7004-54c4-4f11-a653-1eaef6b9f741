import React, { cloneElement, useEffect, useRef, useState } from 'react';
import { arrow, autoUpdate, offset, shift } from '@floating-ui/react-dom';
import {
  autoPlacement,
  FloatingNode,
  FloatingPortal,
  useClick,
  useDismiss,
  useFloating,
  useFloatingNodeId,
  useHover,
  useInteractions,
  useRole
} from '@floating-ui/react-dom-interactions';
import classNames from 'classnames';
import { TEXT } from '@vieon/core/constants/text';
import isEmpty from 'lodash/isEmpty';
import styles from './Tooltip.module.scss';

const Tooltip = ({
  className,
  title,
  timeEL,
  placement = 'bottom',
  triggerEvent = isEmpty(timeEL) && 'click',
  children,
  disableToolTip,
  padding,
  onOPenTooltip,
  isDarkBackground,
  size,
  arrowPosition,
  sizeX,
  sizeY,
  visible
}: any) => {
  const positionMap: any = {
    top: 'bottom',
    right: 'left',
    bottom: 'top',
    left: 'right'
  } as const;
  const timeRef = useRef<any>(null);
  const arrowRef = useRef<any>(null);
  const [open, setOpen] = useState(!!timeEL && !disableToolTip);
  const nodeId = useFloatingNodeId();
  const {
    context,
    x,
    y,
    reference,
    floating,
    middlewareData: { arrow: { x: arrowX, y: arrowY } = {} }
  }: any = useFloating({
    open,
    onOpenChange: setOpen,
    middleware: [
      shift(),
      offset(padding || 8),
      arrow({
        element: arrowRef
      }),
      autoPlacement({
        allowedPlacements: [arrowPosition || 'bottom']
      })
    ],
    placement,
    whileElementsMounted: (reference, floating, update) =>
      autoUpdate(reference, floating, update, {
        animationFrame: true
      })
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    triggerEvent === 'hover' ? useHover(context) : undefined,
    triggerEvent === 'click' ? useClick(context) : undefined,
    triggerEvent === 'dismiss' ? useDismiss(context) : undefined,
    useRole(context)
  ]);

  useEffect(() => {
    if (open && onOPenTooltip) onOPenTooltip();
  }, [open]);

  useEffect(() => {
    if (visible) setOpen(visible);
    const timer = setTimeout(() => {
      setOpen(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, [visible]);

  useEffect(() => {
    if (!timeEL) return;
    if (timeRef.current) clearInterval(timeRef.current);
    if (visible) setOpen(visible);
    timeRef.current = setInterval(() => {
      setOpen(false);
    }, timeEL);
    return () => {
      clearInterval(timeRef.current);
    };
  }, [timeRef.current]);

  return (
    <FloatingNode id={nodeId}>
      {cloneElement(
        children,
        getReferenceProps({
          ref: reference,
          ...children.props
        })
      )}
      <FloatingPortal>
        {open && (
          <div>
            <div
              {...getFloatingProps({
                ref: floating,
                style: {
                  top: y + (sizeY || 0) || '',
                  left: x + (sizeX || 0) || ''
                },
                className: classNames(
                  className,
                  'text-white text-center line-height-1 absolute layer-max',
                  styles.floating,
                  isDarkBackground ? styles.dark : styles.blue,
                  isDarkBackground
                    ? 'padding-x-small-up-8 padding-y-small-up-4'
                    : 'padding-x-small-up-16 padding-y-small-up-8'
                )
              })}
            >
              <span className="text-14 p-b1">{title || TEXT.ADD_USER_PROFILE}</span>
              <div
                style={{
                  top: arrowY - (sizeY || 0) || '',
                  left: arrowX - (sizeX || 0) || '',
                  right: '',
                  bottom: '',
                  [positionMap[placement.split('-')[0]]]: size === 'small' ? '-3px' : '-6px'
                }}
                ref={arrowRef}
                className={classNames(
                  'absolute',
                  styles.arrow,
                  isDarkBackground ? styles.dark : styles.blue
                )}
              />
            </div>
          </div>
        )}
      </FloatingPortal>
    </FloatingNode>
  );
};
export default Tooltip;
