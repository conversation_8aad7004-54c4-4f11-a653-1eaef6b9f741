import React, { useEffect, useState } from 'react';
import { useVieRouter } from '@customHook';
import { useSelector } from 'react-redux';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { TEXT } from '@vieon/core/constants/text';
import { GOOGLE_STORE_ID } from '@vieon/core/config/ConfigEnv';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import { buildDeeplink } from '@vieon/core/utils/common';
import TrackingMWebToApp from '@vieon/tracking/functions/TrackingMWebToApp';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import { isAndroid } from 'react-device-detect';
import Button from '../basic/Buttons/Button';
import Icon from '../basic/Icon/Icon';

const SmartBanner = React.memo(() => {
  const router = useVieRouter();
  const dataSEOALLPAGE = useSelector((state: any) => state?.Page?.dataSEOAllPage?.seo);
  const isIOS = useSelector((state: any) => state?.App?.isIOS);
  const [shortenedLink, setShortenedLink] = useState<any>('');
  const [open, setOpen] = useState(true);
  const linkStore = `https://play.google.com/store/apps/details?id=${GOOGLE_STORE_ID}`;

  useEffect(() => {
    const { asPath } = router;
    if (asPath && dataSEOALLPAGE?.deeplink) {
      const deeplink = dataSEOALLPAGE?.deeplink;
      const link = buildDeeplink(router, deeplink);
      if (link) setShortenedLink(link);
    }
  }, [dataSEOALLPAGE]);

  useEffect(() => {
    if (isAndroid && open) {
      TrackingMWebToApp.smartBannerDownloadLoad({
        flowName: VALUE.TRIGGER_DIRECTLY_SMART_BANNER_OS
      });
    }
  }, [isAndroid, open]);

  const onDownloadNow = () => {
    TrackingMWebToApp.smartBannerDownloadAccept({
      flowName: VALUE.TRIGGER_DIRECTLY_SMART_BANNER_OS
    });
    window.open(shortenedLink || linkStore);
    segmentEvent('download_trigger');
  };

  const onCloseBanner = () => {
    TrackingMWebToApp.smartBannerDownloadClose({
      flowName: VALUE.TRIGGER_DIRECTLY_SMART_BANNER_OS
    });
    setOpen(false);
  };

  if (!open || isIOS) return null;

  return (
    <div className="download download--app layer-1005 fixed top size-w-full layer-max">
      <div className="download-wrap">
        <Button
          className="button button--close absolute left-1 middle-v p-x1"
          iconClass="icon--tiny"
          iconName="vie-times-medium text-gray117"
          onClick={onCloseBanner}
        />
        <div className="grid-x align-middle">
          <div className="cell shrink">
            <Icon imageSrc={ConfigImage.logoVieONSmartBanner} />
          </div>
          <div className="cell auto">
            <p className="text text-white m-0 line-height-1 text-12 padding-small-up-left-8 p-r2">
              Cài đặt ứng dụng VieON để giúp trải nghiệm tốt hơn!
            </p>
          </div>
          <div className="cell shrink">
            <Button
              className="button button--light button--variant-h44"
              title={TEXT.DOWNLOAD_NOW}
              onClick={onDownloadNow}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

export default SmartBanner;
