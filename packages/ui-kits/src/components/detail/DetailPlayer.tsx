import React from 'react';
import dynamic from 'next/dynamic';
import {
  addClassActive,
  checkIsFullscreen,
  checkNextEpisodeToWatch,
  createTimeout
} from '@vieon/core/utils/common';
import { videoTrackingCurrentTime } from '@vieon/tracking/video';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import UserServices from '@vieon/core/services/userServices';
import TrackingLog from '@vieon/core/services/trackingLog';
import { NAME, PROPERTY, VALUE } from '@vieon/core/config/ConfigSegment';
import ConfigUser from '@vieon/core/config/ConfigUser';
import LocalStorage from '@vieon/core/config/LocalStorage';
import UserApi from '@vieon/core/api/userApi';
import { CONTENT_TYPE, PERMISSION, POPUP } from '@vieon/core/constants/constants';
import { gtmShootEvent } from '@vieon/tracking/TrackingGTM';
import ConfigGTM from '@vieon/core/config/ConfigGTM';
import TrackingPlayer from '@vieon/tracking/functions/TrackingPlayer';
import isEmpty from 'lodash/isEmpty';
import { getInfoVideoCodec, setPlayerErrorLog } from '@vieon/core/services/playerServices';
import ConfigLocalStorage from '@vieon/core/config/ConfigLocalStorage';
import classNames from 'classnames';
import styles from './Styles.module.scss';

const Player = dynamic(import('../basic/Player/Player'), { ssr: false });

const AD_BLOCK = 'AD_BLOCK';

class DetailPlayer extends React.Component {
  adsStartTime: any;
  bufferTime: any;
  getLinkMoment: any;
  initId: any;
  initPlayerTime: any;
  liveTVCCU: any;
  offsetTime: any;
  oldPercent: any;
  partStartTime: any;
  player: any;
  playerDisposed: any;
  playerName: any;
  playingId: any;
  sessionId: any;
  spamPauseTimer: any;
  startedAdsPre: any;
  tracking: any;
  video: any;
  videoContainer: any;
  constructor(props: any) {
    super(props);
    this.state = {
      error: false,
      isNext: false,
      isChapterList: false,
      playerReady: false,
      isWatchLater: props.contentDetail?.isWatchlater,
      adBlock: false,
      isIntro: false,
      isOuttro: false,
      asPath: '',
      isTimeZero: false
    };
    this.player = null;
    this.video = null;
    this.videoContainer = null;
    this.offsetTime = 0;
    this.spamPauseTimer = 0;
    this.playingId = '';
    this.getLinkMoment = 0;
    this.bufferTime = 0;
    this.oldPercent = 0;
    this.startedAdsPre = false;
    this.sessionId = 0;
    // Tracking Object
    this.tracking = new TrackingLog();
    this.initId = '';
    this.initPlayerTime = 0;
  }

  componentDidMount() {
    const adBlock = document.getElementById(AD_BLOCK);
    if (adBlock) {
      this.setState({ adBlock: true });
    }
    window.addEventListener('beforeunload', this.onBrowserChange);

    this.handleTrialEpisode();
  }

  componentDidUpdate(prevProps: any) {
    const { contentDetail, popupName }: any = this.props || {};
    const contentId = contentDetail?.id;
    const prevContentId = prevProps?.contentDetail?.id;

    if (popupName && popupName !== prevProps?.popupName) {
      if (popupName === POPUP.NAME.PLAYER_ERROR_VOD) {
        this.playingId = contentId;
      }
    }

    if (contentId !== prevContentId) {
      if (!isEmpty(contentDetail) && contentDetail?.progress <= 0) {
        this.setState({ isTimeZero: true });
      }
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
      if (prevContentId) {
        this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP, props: prevProps });
      }
      this.setState({ isWatchLater: contentDetail?.isWatchlater });
      addClassActive(contentId, contentDetail?.isWatchlater);
      // Check ad block
      const ads = contentDetail?.ads;
      const isAds = ads?.length > 0;
      const adBlock = document.getElementById(AD_BLOCK);
      const { handleSetSessionId }: any = this.props;
      if (typeof handleSetSessionId === 'function') handleSetSessionId('');
      if (adBlock && this.video && isAds) {
        this.video.pause();
        this.video.muted = true;
      }

      this.setState({
        adBlock: isAds ? adBlock : false,
        playingAds: false,
        isWatchLater: contentDetail?.isWatchlater,
        isIntro: false,
        isOuttro: false
      });

      this.handleTrialEpisode();
      setPlayerErrorLog({ isReset: true });
    }
  }

  componentWillUnmount() {
    const { contentDetail }: any = this.props || {};
    const { contentId, contentName, playTrial, contentType, duration, currentTime } =
      this.getParamsForSegment({
        props: this.props
      });
    const data = [
      {
        action: 'offset',
        duration: parseInt(duration),
        progress: parseInt(currentTime),
        timestamp: Math.floor(new Date().getTime() / 1000)
      }
    ];

    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    UserApi.trackingWatch({
      contentId,
      contentName,
      contentType,
      data,
      playTrial,
      videoCodec: getInfoTrack?.videoCodec
    });
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
    window.removeEventListener('beforeunload', this.onBrowserChange);
    this.playerDisposed = true;

    if (contentDetail?.id && this.playingId !== contentDetail?.id) {
      const segmentParams = this.getParamsForSegment();
      TrackingPlayer.exitBeforeStarted(segmentParams);
    }
  }

  onBrowserChange = () => {
    const { contentDetail }: any = this.props || {};
    if (!this.playerDisposed) {
      this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
      if (this.liveTVCCU) {
        this.liveTVCCU.onEnd();
      }
    }
    if (contentDetail?.id && this.playingId !== contentDetail?.id) {
      const segmentParams = this.getParamsForSegment();
      TrackingPlayer.exitBeforeStarted(segmentParams);
    }
    return undefined;
  };

  setupPlayer = (params: any) => {
    const { player, liveTVCCU, video, videoContainer, playerName } = params || {};
    const { setupPlayer }: any = this.props || {};
    this.video = video;
    this.videoContainer = videoContainer;
    this.player = player;
    this.playerName = playerName;
    this.liveTVCCU = liveTVCCU;
    if (typeof setupPlayer === 'function') setupPlayer(params);

    // Tracking Init Player
    this.handleInitPlayer();
  };

  handleInitPlayer = () => {
    const { content, contentDetail }: any = this.props || {};
    if (contentDetail?.id && this.initId !== contentDetail?.id) {
      this.initId = contentDetail?.id;
      this.initPlayerTime = new Date().getTime();
      TrackingPlayer.initPlayer({
        contentId: contentDetail?.id,
        contentType: content?.type,
        contentTitle: content?.title
      });
    }
  };

  startupLoadPreroll = () => {
    const { content, contentDetail }: any = this.props || {};
    TrackingPlayer.videoStartupPrerollLoad({
      contentId: contentDetail?.id,
      contentType: content?.type,
      contentTitle: content?.title,
      totalTime: this.initPlayerTime ? new Date().getTime() - this.initPlayerTime : 0
    });
  };

  onBufferEnded = ({ bufferingTime, isSeeking }: any) => {
    const { contentDetail }: any = this.props || {};
    if (contentDetail?.id && this.playingId === contentDetail?.id) {
      const segmentParams = this.getParamsForSegment();
      TrackingPlayer.rebufferContent({ ...segmentParams, bufferingTime, isSeeking });
    }
  };

  isConvertMWebToApp = () => {
    const { contentDetail = {}, currentEpisode = {}, isMobile, content }: any = this.props;
    const { numberTrialEpisode, isEpisodeTrialInApp, isTriggerToApp, trialDuration } =
      contentDetail || {};
    const { episode } = currentEpisode || {};
    if (content?.type === CONTENT_TYPE?.MOVIE) {
      return !trialDuration && isTriggerToApp && isMobile;
    }
    return checkNextEpisodeToWatch({
      isEpisodeTrialInApp,
      numberTrialEpisode,
      episode,
      type: currentEpisode?.type,
      isMobile,
      content,
      isTriggerToApp,
      trialDuration
    });
  };

  handleTrialEpisode = () => {
    const isMWebToApp = this.isConvertMWebToApp();
    if (isMWebToApp) this.onOpenPopupFreeTrial();
  };

  onPlaying = () => {
    const { contentDetail }: any = this.props || {};
    if (this.playingId !== contentDetail.id) {
      this.playingId = contentDetail.id;
      const segmentParams = this.getParamsForSegment();
      TrackingPlayer.vodEvents(NAME.VIDEO_STARTED, segmentParams);
    }

    // GTM Facebook Pixel
    gtmShootEvent({ event: 'play_video' });
    // SEGMENT TRACKING
    const segmentParams = this.getParamsForSegment();
    const { hasAds } = segmentParams;
    const { adBlock }: any = this.state || {};
    if (adBlock && hasAds && this.video) {
      this.video.pause();
      this.video.mute = true;
      return;
    }

    if (this.partStartTime > 0) {
      this.partStartTime = 0;
    }
    // RECORD ACTION PLAY
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.PLAY });
  };

  onEnded = () => {
    const segmentParams = this.getParamsForSegment({ progress: VALUE.PROGRESS_100 });
    // SEGMENT TRACKING
    TrackingPlayer.vodEvents(NAME.VIDEO_PROGRESS, segmentParams);
    TrackingPlayer.vodEvents(NAME.VIDEO_COMPLETED, segmentParams);
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.STOP });
    const { contentDetail, currentEpisode, GET_CONTENT_EPISODE, GET_CONTENT, content }: any =
      this.props || {};
    const contentProps = content;
    const { permission, trialDuration, isMovieTrialInApp } = contentDetail;

    if (
      (permission !== PERMISSION.CAN_WATCH && trialDuration > 0) ||
      (permission === PERMISSION.CAN_WATCH && isMovieTrialInApp)
    ) {
      this.onOpenPopupFreeTrial();
    } else {
      const content = currentEpisode || GET_CONTENT_EPISODE || contentProps || GET_CONTENT;
      if (content?.type === CONTENT_TYPE.TRAILER) {
        this.backToIntro(content);
      }
    }
  };

  onOpenPopupFreeTrial = () => {
    const { contentDetail, handlePopupFreeTrial }: any = this.props;
    if (contentDetail && typeof handlePopupFreeTrial === 'function') {
      handlePopupFreeTrial(contentDetail, true);
    }
  };

  backToIntro = (content: any) => {
    const { router }: any = this.props || {};
    let introUrl: any = ConfigLocalStorage.get(LocalStorage.BACK_FROM_PLAYER);
    introUrl = JSON.parse(introUrl || '{}');
    ConfigLocalStorage.remove(LocalStorage.BACK_FROM_PLAYER);
    const queryString = (window?.location?.search || '').substring(1) || '';
    if (introUrl?.url) {
      let remakeUrl = introUrl.url;
      if (remakeUrl.includes('?')) remakeUrl += queryString ? `&${queryString}` : '';
      else remakeUrl += `?${queryString}`;
      router.push(remakeUrl);
    } else {
      const seasonId = content?.groupId || content?.id;
      router.push(`/?vid=${seasonId}${queryString ? `&${queryString}` : ''}`);
    }
  };

  onPaused = () => {
    if (this.spamPauseTimer) clearTimeout(this.spamPauseTimer);
    this.spamPauseTimer = createTimeout(() => {
      this.handleRecord({ action: ConfigUser.RECORD.ACTION.PAUSE });
      // SEGMENT TRACKING
      TrackingPlayer.vodEvents(NAME.PAUSE_VIDEO);
    }, 2000);
  };

  onFullscreenChange = () => {
    const { currentEpisode }: any = this.props || {};
    let isNext = false;
    let isChapterList = false;
    if (checkIsFullscreen()) {
      const type = currentEpisode?.type;
      const isEnd = currentEpisode?.is_end;
      if (type === CONTENT_TYPE.EPISODE && !isEnd) {
        isNext = true;
      }
      if (type === CONTENT_TYPE.EPISODE) {
        isChapterList = true;
      }
    }
    this.setState({ isNext, isChapterList });
  };

  onTimeUpdate = ({ currentTime, duration }: any) => {
    const { contentDetail }: any = this.props || {};
    this.handleIntro({ currentTime, duration });
    // HANDLE RECORD OFFSET
    this.handleRecordOffset(currentTime);
    // CHECK USER FEEDBACK
    if (!contentDetail?.isDVR) {
      this.checkUserFeedback(currentTime, duration);
    }
    // TRACKING PROGRESS
    videoTrackingCurrentTime({
      startTime: this.sessionId,
      currentTime,
      duration,
      segmentProgress: this.segmentProgress,
      watch95End: this.watch95End
    });
  };

  watch95End = () => {
    const {
      contentId,
      seasonName,
      contentTitle,
      playTrial,
      contentType,
      duration,
      currentTime
    }: any = this.getParamsForSegment({
      props: this.props
    });
    const contentName = `${seasonName} - ${contentTitle}`;
    const data = [
      {
        action: 'offset',
        duration: parseInt(duration),
        progress: parseInt(currentTime),
        timestamp: Math.floor(new Date().getTime() / 1000)
      }
    ];
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    UserApi.trackingWatch({
      contentId,
      contentName,
      contentType,
      playTrial,
      data,
      videoCodec: getInfoTrack?.videoCodec
    });
  };

  handleIntro = ({ currentTime }: any) => {
    const { contentDetail }: any = this.props || {};
    const intro = contentDetail?.intro;
    const outtro = contentDetail?.outtro;
    let isIntro = false;
    let isOuttro = false;
    if (
      parseInt(currentTime) > 0 &&
      parseInt(currentTime) > intro?.start &&
      parseInt(currentTime) <= intro?.end
    ) {
      isIntro = true;
    }
    if (
      parseInt(currentTime) > 0 &&
      parseInt(currentTime) > outtro?.start &&
      parseInt(currentTime) <= outtro?.end
    ) {
      isOuttro = true;
    }
    this.setState({ isIntro, isOuttro });
  };

  segmentProgress = (value: any) => {
    const playerQualities: any = [];
    const { quality }: any = this.state || {};
    const activeQuality = (playerQualities || []).find((qua: any) => qua.active);
    const bitrate = activeQuality?.bandwidth || 0;
    const qualityCustom = activeQuality ? activeQuality.height : quality || 0;
    const segmentParams = this.getParamsForSegment({ progress: value, bitrate, qualityCustom });
    // SEGMENT TRACKING
    TrackingPlayer.vodEvents(NAME.VIDEO_PROGRESS, segmentParams);
  };

  onError = ({ event, linkPlay, streamingProtocol, getInfoTrack }: any) => {
    const { content, currentEpisode, subtitles, subtitle, audios, audio }: any = this.props || {};
    TrackingPlayer.playerError({
      event,
      content,
      currentEpisode,
      subtitle: subtitle || subtitles?.[0] || null,
      audio: audio || audios?.[0] || null,
      streamingProtocol,
      getInfoTrack,
      linkPlay
    });
  };

  handlePlayCurrentTime = (contentId: any, startPosition: any) => {
    const { profile }: any = this.props || {};
    if (!profile?.id) return;
    const loginAt15M = ConfigLocalStorage.get(LocalStorage.LOGIN_AT_15M);
    let newStartPosition = startPosition;
    if (loginAt15M && loginAt15M === contentId) {
      newStartPosition = startPosition >= 900 ? startPosition : 900;
    }
    if (this.video) {
      this.video.currentTime = newStartPosition || 0;
    }
  };

  onNextChapter = () => {
    const { handleGotoChapter }: any = this.props || {};
    if (typeof handleGotoChapter === 'function') handleGotoChapter();
  };

  controlPlayingAds = (isAds: any, adTagUrl: any, adEvent: any, adsPlayedDuration: any) => {
    let playingAds = false;
    if (isAds) {
      playingAds = true;
    }
    this.setState({ playingAds });
    this.trackingWithADs(isAds, adTagUrl, adEvent, adsPlayedDuration);
  };

  onSkipAds = () => {
    const { contentDetail, currentEpisode, content }: any = this.props || {};
    segmentEvent(NAME.SKIP_ADS, {
      [PROPERTY.CURRENT_PAGE]: window.location.href,
      [PROPERTY.CONTENT_ID]: contentDetail?.id,
      [PROPERTY.CONTENT_NAME]:
        content?.title + (currentEpisode?.title ? ` - ${currentEpisode?.title}` : '')
    });
  };

  onSkipIntro = () => {
    const { isMobile }: any = this.props || {};
    if (isMobile) return;
    this.handleSkipIntro();
  };

  onSkipIntroTouch = () => {
    this.handleSkipIntro();
  };

  handleSkipIntro = () => {
    const { contentDetail, isIOS }: any = this.props || {};
    const { isIntro }: any = this.state || {};
    const intro = contentDetail?.intro;
    if (isIntro && intro?.end > 0) {
      let timer = intro?.end + 1;
      if (timer && isIOS) timer = Math.floor(+timer).toFixed(1);
      this.video.currentTime = timer + 2;
      this.setState({ isIntro: false });
    }
  };

  onNextEpisode = (e: any, nextEpisode: any) => {
    const { contentDetail }: any = this.props || {};
    const { isOuttro }: any = this.state || {};
    const outtro = contentDetail?.outtro;
    if (isOuttro && outtro?.end > 0) {
      this.video.currentTime = outtro?.end + 1;
      this.setState({ isOuttro: false });
    }
    this.onEnded();
  };

  setInitPlay = ({ bufferTime }: any) => {
    this.adsStartTime = 0;
    this.bufferTime = bufferTime;
    this.sessionId = Math.floor(new Date().getTime());
    const { handleSetSessionId, contentDetail, setInitPlay }: any = this.props || {};
    if (typeof handleSetSessionId === 'function') handleSetSessionId(this.sessionId);
    const segmentParams = this.getParamsForSegment();
    const { contentId, contentType, playTrial, hasAds, contentTitle, seasonName, duration } =
      segmentParams;
    const startPosition = contentDetail?.progress || 0;
    if (!contentDetail?.isDVR) {
      // INIT CURRENT TIME
      this.handlePlayCurrentTime(contentId, startPosition);
    }

    gtmShootEvent({ event: ConfigGTM.VIDEO_STARTED });
    if (!hasAds) {
      this.partStartTime = new Date().getTime();
    }
    // START TRACKING WATCH
    const getInfoPlayer = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const bufferData = this.handleTrackingBufferTime({
      contentId,
      playTrial,
      contentName: (seasonName || '') + (contentTitle ? ` - ${contentTitle}` : ''),
      contentType,
      bufferTime: bufferTime / 1000,
      duration
    });
    this.handleRecord({ action: ConfigUser.RECORD.ACTION.OFFSET });
    this.handleTracking({
      action: ConfigUser.TRACKING.START,
      contentId,
      bufferData: { ...bufferData, videoCodec: getInfoPlayer?.videoCodec }
    });
    if (typeof setInitPlay === 'function') setInitPlay({ sessionId: this.sessionId });
  };

  onControlPlay = ({ playerError }: any) => {
    const currentTime = this.video?.currentTime || 0;
    const { onControlPlay }: any = this.props || {};
    if (typeof onControlPlay === 'function') onControlPlay({ currentTime, playerError });
  };

  handleOpenCatError = () => {
    const { dataRefreshSession, handleEndSessionPlay, concurrentScreen }: any = this.props || {};
    const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
    if (sessionToken) {
      handleEndSessionPlay(sessionToken);
    }
    // NOTE: hide old UI popup lost connect
    // if (openPopup) {
    //   openPopup({
    //     name: POPUP.NAME.PLAYER_ERROR_VOD,
    //     ...catData,
    //     errorData: {
    //       ...catData?.errorData,
    //       vodData: { content, currentEpisode, contentDetail }
    //     }
    //   });
    // }
  };

  getPlayerData = () => {
    const {
      contentDetail,
      thumbData,
      thumbSrc,
      currentEpisode,
      content,
      profile,
      currentProfile,
      handleRetry,
      deviceId,
      setToast,
      blockPlayer,
      ACTION_GET_RECOMMEND_VOD,
      geoCheck
    }: any = this.props || {};
    const {
      permission,
      trialDuration,
      settingData,
      ads,
      timeCodeAds,
      drmServiceName,
      qnetDrm,
      assetId,
      variantId,
      warningLocation
    } = contentDetail || {};
    const { playerReady, isNext, playingAds, isLive, adBlock, isIntro, isRetry }: any =
      this.state || {};
    const noSeeker = currentEpisode?.noSeeker || content?.noSeeker;
    const softLogo = currentEpisode?.playerLogo || content?.playerLogo;
    let isChapterList = false;
    const contentType = content?.type;
    if (contentType === CONTENT_TYPE.SEASON) {
      isChapterList = true;
    }
    const videoId = 'DETAIL_PLAYER';
    const recommendData = ACTION_GET_RECOMMEND_VOD?.recommendData;
    const isDVR = contentDetail?.isDVR;
    const isDrm = contentDetail?.isDrm;
    const contentId = contentDetail?.id;
    const isShow = content?.category === 2;
    const linkPlaysToRetry = contentDetail?.linkPlaysToRetry || [];
    const linkPlay = contentDetail?.linkplay || '';
    const isHBO = contentDetail?.isHBO || '';
    const relatedSeason = content?.relatedSeason;
    const playerTitle = content?.title;
    const playerSubTitle = currentEpisode?.title;
    const qnetInfo = contentDetail?.qnetInfo;
    const {
      setupPlayer,
      onBufferEnded,
      onPlaying,
      controlPlayingAds,
      onSkipAds,
      onEnded,
      onNextChapter,
      onPaused,
      onFullscreenChange,
      onTimeUpdate,
      onError,
      onControlPlay,
      onSkipIntro,
      onSkipIntroTouch,
      onNextEpisode,
      setInitPlay,
      onOpenPopupFreeTrial,
      handleOpenCatError,
      startupLoadPreroll
    } = this;
    const isConvertMWebToApp = this.isConvertMWebToApp();
    const { updateTotalPlayedData }: any = this.props;
    const altSEOIMG = contentDetail?.altSEOIMG || '';
    const params: any = {
      isGlobal: geoCheck?.isGlobal,
      isHBO,
      deviceId,
      warningLocation,
      linkPlaysToRetry,
      isLive,
      isDVR,
      softLogo,
      playingAds,
      recommendData,
      playerTitle,
      playerSubTitle,
      playerReady,
      permission,
      trialDuration,
      isNext,
      isChapterList,
      noSeeker,
      contentId,
      linkPlay,
      ads,
      timeCodeAds,
      thumbData,
      thumbSrc,
      settingData,
      relatedSeason,
      currentEpisode,
      isShow,
      content,
      drmServiceName,
      isIntro,
      profile,
      currentProfile,
      videoId,
      isDrm,
      isRetry,
      altSEOIMG,
      qnetInfo,
      qnetDrm,
      assetId,
      variantId,
      blockPlayer
    };
    const events = {
      setupPlayer,
      startupLoadPreroll,
      onPlaying,
      controlPlayingAds,
      onEnded,
      adBlock,
      handleRetry,
      onBufferEnded,
      onNextChapter,
      onPaused,
      onFullscreenChange,
      onSkipIntro,
      onSkipIntroTouch,
      onNextEpisode,
      onTimeUpdate,
      onError,
      onControlPlay,
      setInitPlay,
      onSkipAds,
      onOpenPopupFreeTrial,
      updateTotalPlayedData,
      setToast,
      isConvertMWebToApp,
      handleOpenCatError
    };
    return { ...this.props, ...this.state, ...params, ...events };
  };

  // 1. RECORD PROGRESS
  // 2. TRACKING WATCH
  // 3. TRACKING GA
  // 4. TRACKING SEGMENT
  // 5. USER FEEDBACK
  // 6. USER FLOW
  getParamsForSegment = (segmentData?: any) => {
    const { props, adTagUrl, progress, bitrate, quality } = segmentData || {};
    const data = props || this.props;
    const midAds = (data.contentDetail?.ads || []).find((ad: any) => ad.type === 'mid');
    const repeat = midAds?.repeat || 0;
    const currentTime = this.video?.currentTime || 0;
    let partIndexTemp = 1;
    if (currentTime && repeat) {
      partIndexTemp = Math.round(currentTime / (repeat * 60));
    }

    const { sessionId } = this;
    const query = data?.router?.query || {};
    const categoryText = data?.content?.categoryText || '';
    const genreText = data?.content?.genreText || '';
    const shortDescription = data.currentEpisode?.shortDescription || '';
    const thumbnail = data?.content?.images?.thumbnail || '';
    const audio = data?.audio || (data?.audios || [])?.[0] || {};
    const subtitle = data?.subtitle || (data?.subtitles || [])?.[0] || {};
    const duration = this.video?.duration;
    const contentId = data.contentDetail?.id;
    const permission = data.contentDetail?.permission;
    const trialDuration = data.contentDetail?.trialDuration;
    const triggerLoginDuration = data.contentDetail?.triggerLoginDuration;
    const playTrial =
      permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0);
    const isEnd = data?.currentEpisode ? data.currentEpisode?.is_end : true;
    const contentTitle = data.currentEpisode?.title || data.content?.title;
    const seasonName = data.content?.title;
    const contentType = data.currentEpisode?.type || data.content?.type;
    const episodeName = data.currentEpisode?.title || '';
    const contentName = seasonName + (episodeName ? ` - ${episodeName}` : '');
    const partLength = repeat * 60;
    const partIndex = partIndexTemp;
    const hasAds = data.contentDetail?.ads?.length > 0;
    const videoPlayType = data.contentDetail?.videoPlayType;
    const lagPartStarted = new Date().getTime() - (this.partStartTime || new Date().getTime());
    const lagVideoStarted = new Date().getTime() - (this.sessionId || 0);
    const now = new Date().getTime();
    let playedDuration = Math.floor((now - this.sessionId) / 1000);
    if (this.adsStartTime > 0) {
      playedDuration = Math.floor((now - this.adsStartTime) / 1000);
    }
    return {
      sessionId,
      genreText,
      shortDescription,
      thumbnail,
      categoryText,
      contentId,
      playTrial,
      contentTitle,
      seasonName,
      contentType,
      partLength,
      duration,
      partIndex,
      hasAds,
      videoPlayType,
      playedDuration,
      lagVideoStarted,
      lagPartStarted,
      adTagUrl,
      progress,
      bitrate,
      quality,
      isEnd,
      currentTime,
      episodeName,
      contentName,
      audio,
      subtitle,
      query
    };
  };

  trackingWithADs = (isAds: any, adTagUrl: any, adEvent: any, adsPlayedDuration: any) => {
    const segmentParams = this.getParamsForSegment({ adTagUrl });
    if (!isAds) {
      // SEGMENT
      TrackingPlayer.vodEvents(NAME.VIDEO_AD_COMPLETED, {
        ...segmentParams,
        adEvent,
        adsPlayedDuration
      });
      this.partStartTime = new Date().getTime();
    } else {
      // SEGMENT
      this.adsStartTime = new Date().getTime();
      TrackingPlayer.vodEvents(NAME.VIDEO_AD_STARTED, {
        ...segmentParams,
        adEvent,
        adsPlayedDuration
      });
      if (!this.startedAdsPre) return;
      this.startedAdsPre = true;
    }
  };

  handleRecord = ({ action, props }: any) => {
    let currentTime = 0;
    let duration = 0;
    if (action === ConfigUser.RECORD.ACTION.STOP) {
      this.handleTracking({ action: ConfigUser.TRACKING.STOP });
    }
    try {
      currentTime = this.video?.currentTime || 0;
      duration = this.video?.duration || 0;
    } catch (e) {
      console.log('PLAYER ERROR ', e);
    }
    const contentDetailId = (props || this.props)?.contentDetail?.id;
    const permission = (props || this.props)?.contentDetail?.permission;
    const trialDuration = (props || this.props)?.contentDetail?.trialDuration;
    const triggerLoginDuration = (props || this.props)?.contentDetail?.triggerLoginDuration;
    const contentType = (props || this.props)?.content?.type;
    const contentTitle = (props || this.props)?.content?.title;
    const currentEpisodeTitle = (props || this.props)?.currentEpisode?.title;
    const contentName =
      (contentTitle || '') + (currentEpisodeTitle ? ` - ${currentEpisodeTitle?.title}` : '');
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const params: any = {
      contentId: contentDetailId,
      contentName,
      contentType,
      timeSecond: currentTime,
      playTrial:
        permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0),
      action,
      durationTime: duration,
      videoCodec: getInfoTrack?.videoCodec
    };
    UserServices.recordProgressOfUser(params);
  };

  handleTrackingBufferTime = ({
    contentId,
    contentName,
    contentType,
    playTrial,
    bufferTime
  }: any) => {
    const timestamp = Math.floor(new Date().getTime() / 1000);
    return {
      contentId,
      contentName,
      contentType,
      playTrial,
      data: [
        {
          action: ConfigUser.RECORD.ACTION.PLAY,
          duration: Math.floor(this.video?.duration || 0),
          timestamp,
          buffer_time: bufferTime
        }
      ]
    };
  };

  handleTracking = (params: any) => {
    const getInfoTrack = getInfoVideoCodec({ player: this.player, playerName: this.playerName });
    const videoCodec = getInfoTrack?.videoCodec;
    UserServices.handleTrackingLog({ ...params, videoCodec, tracker: this.tracking });
  };

  handleRecordOffset = (currentTime?: any) => {
    if (currentTime && currentTime - this.offsetTime >= 5) {
      if (!this.playerDisposed) {
        this.handleRecord({ action: ConfigUser.RECORD.ACTION.OFFSET });
      }
      this.offsetTime = currentTime;
    }
    this.offsetTime = this.offsetTime >= currentTime ? currentTime : this.offsetTime;
  };

  checkUserFeedback = (currentTime: any, duration: any) => {
    const { content, currentEpisode, showPopupUserFeedback, getUserFeedback }: any =
      this.props || {};
    const type = content?.type;
    const isEnd = currentEpisode?.is_end;
    const episodeType = currentEpisode?.type;
    if (type === CONTENT_TYPE.MOVIE || (isEnd && episodeType === CONTENT_TYPE.EPISODE)) {
      if (duration === 0 || currentTime === 0) return;
      const percent = (currentTime / duration) * 100;
      if (this.oldPercent < 95 && percent > 95) {
        if (typeof getUserFeedback === 'function') {
          getUserFeedback().then((res: any) => {
            if (res) {
              if (typeof showPopupUserFeedback === 'function') showPopupUserFeedback(true);
              if (this.video) this.video.pause();
              const isFullScreen = checkIsFullscreen();
              if (isFullScreen && this.videoContainer) {
                this.videoContainer.exitFullscreen();
              }
            }
          });
        }
      }
      this.oldPercent = percent;
    }
  };

  handleSeekbarContent = (data: any) => {
    const { curTime, isWarning } = data || {};
    if (isWarning) this.setState({ isTimeZero: false });
    else if (curTime < 0.12) {
      this.setState({ isTimeZero: true });
    }
  };

  render() {
    const playerData = this.getPlayerData();
    if (!playerData?.contentId) return null;
    const {
      isSafari,
      isMobile,
      isTablet,
      isIOS,
      blockPlayer,
      content,
      drmProvider,
      drmMerchant,
      sessionId,
      isHasCompanionBanner
    }: any = this.props || {};
    const { isTimeZero }: any = this.state || {};
    return (
      <div className="player player--vod">
        <div className={classNames('player-stage', isHasCompanionBanner && styles.playerStage)}>
          <Player
            {...playerData}
            blockPlayer={blockPlayer}
            isIOS={isIOS}
            isSafari={isSafari}
            isMobile={isMobile}
            isTablet={isTablet}
            isAgeRestricted={content?.warningScreen && isTimeZero}
            warningScreen={content?.warningScreen}
            warningMessage={content?.warningMessage}
            warningTag={content?.warningTag}
            handleSeekbarContent={this.handleSeekbarContent}
            drmProvider={drmProvider}
            drmMerchant={drmMerchant}
            sessionId={sessionId}
            typePlayer="vod"
          />
        </div>
      </div>
    );
  }
}

export default DetailPlayer;
