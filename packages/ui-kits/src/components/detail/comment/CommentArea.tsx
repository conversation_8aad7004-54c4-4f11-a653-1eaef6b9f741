import React, { useEffect, useState } from 'react';
import ConfigCookie from '@vieon/core/config/ConfigCookie';
import { PAGE } from '@vieon/core/constants/constants';
import { TYPE_TRIGGER_AUTH } from '@vieon/core/constants/types';
import { encodeParamDestination } from '@vieon/core/utils/common';
import Button from '../../basic/Buttons/Button';
import CustomTextArea from '../../basic/CustomTextarea/CustomTextArea';

const CommentArea = React.memo(
  ({
    router,
    textAreaId,
    profile,
    classButton,
    classButtonIco,
    disableAvatar,
    contentID,
    onFocus,
    onClick,
    onClickSend
  }: any) => {
    const [commentValue, setCommentValue] = useState<any>('');
    const [resetValueInputArea, setResetValueInputArea] = useState<any>(null);
    const { givenName, avatar } = profile || {};
    const onHandleChange = (value: any) => {
      setCommentValue(value);
    };
    const onHandleClickSend = () => {
      const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE);
      if (!accessToken) {
        if (classButton && typeof onFocus === 'function') {
          onFocus();
        } else {
          const remakeDestination = encodeParamDestination(router?.asPath);
          router.push(
            `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.COMMENT}`
          );
        }
        return;
      }
      if (typeof onClickSend === 'function') {
        onClickSend(commentValue);
        setCommentValue('');
        if (resetValueInputArea) resetValueInputArea();
      }
    };

    const onHandleClick = (e: any) => {
      if (typeof onClick === 'function') onClick(e);
    };

    const onHandleCallback = ({ callback }: any) => {
      if (callback?.onResetValue) setResetValueInputArea(() => callback.onResetValue);
    };
    useEffect(() => {
      setCommentValue('');
    }, [contentID]);
    return (
      <form className="form form--comment">
        {!disableAvatar &&
          (avatar ? (
            <div className="avatar overflow light circle normal gender">
              <img src={avatar} alt={givenName} />
            </div>
          ) : (
            <div className="media-object-section middle">
              <a className="avatar default overflow light circle normal" title={givenName}>
                {givenName}
              </a>
            </div>
          ))}
        <div className="field">
          <CustomTextArea
            placeholder="Thêm bình luận..."
            id={contentID || textAreaId}
            onFocus={onFocus}
            rows="1"
            onChange={onHandleChange}
            onKeyPress={onHandleClickSend}
            onClick={onHandleClick}
            callback={onHandleCallback}
          />
          {classButton && commentValue?.length > 0 && (
            <Button className={classButton} iconName={classButtonIco} onClick={onHandleClickSend} />
          )}
        </div>
      </form>
    );
  }
);

export default CommentArea;
