import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import get from 'lodash/get';
import { TEXT } from '@vieon/core/constants/text';
import { isAPartElmInView } from '@vieon/core/utils/common';
import { PAGE } from '@vieon/core/constants/constants';
import classNames from 'classnames';
import { debounce } from 'lodash';
import Promotion from '../Promotion';
import Button from '../../../basic/Buttons/Button';
import BillItem from './BillItem';
import { checkoutBill } from './checkoutBill';
import Styles from './Bill.module.scss';
import LogoCertificate from './LogoCertificate';

const BillTVod = ({
  onCheckOut,
  isNotSelectBank,
  extraError,
  isLiveEvent,
  isSimulcast,
  content,
  onHandleLogin
}: any) => {
  const [paddingBill, setPaddingBill] = useState<any>(null);
  const footerRef = useRef<any>(null);
  const router = useVieRouter();
  const { pathname } = router || {};
  const { isMobileViewPort, webConfig } = useSelector((state: any) => state?.App || {});
  const userProfile = useSelector((state: any) => state?.Profile?.profile);
  const paymentStore = useSelector((state: any) => state?.Payment) || {};
  const isChangeToVod = !!content?.contentId;
  const isEnableInputPromotionCode = get(webConfig, 'tVod.payment.enableInputPromotionCode', false);
  const { tvodOffer } = paymentStore || {};
  const {
    productNameMsg,
    durationMsg,
    startDateMsg,
    priceMsg,
    discountAmountMsg,
    waitingDurMsg,
    totalPriceMsg,
    consumingDurMsg,
    discountAmount
  } = tvodOffer || {};

  const inApp = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);
  const nameLabel = useMemo(() => {
    if (userProfile?.mobile) {
      return userProfile?.mobile;
    }
    if (!userProfile?.mobile && userProfile?.email && userProfile?.emailVerified) {
      return userProfile?.email;
    }
    return userProfile?.givenName;
  }, [userProfile]);
  const checkoutButton = useMemo(() => checkoutBill(paymentStore, inApp), [paymentStore, inApp]);

  const tvodNote = useMemo(() => {
    if (!waitingDurMsg || !consumingDurMsg) return '';
    let temp = webConfig?.tVod?.text?.paymentStep2Note || '';
    if (isLiveEvent) {
      if (isChangeToVod) {
        temp = webConfig?.tVod?.text?.paymentStep2NoteForLiveEvent || '';
      } else {
        temp = '';
      }
    }
    if (isSimulcast) {
      temp = webConfig?.tVod?.text?.paymentStep2NoteForSimulcast || '';
    }
    return (temp || '')
      .replace('{waitingDurMsg}', waitingDurMsg)
      .replace('{consumingDurMsg}', consumingDurMsg);
  }, [waitingDurMsg, consumingDurMsg, isLiveEvent, isChangeToVod, isSimulcast]);

  // Check footer view in Mobile
  const debounceCheckFooterInViewOnMobile = debounce(() => {
    if (!isMobileViewPort || typeof window === 'undefined') return;
    const footerElm = document.getElementById('footer');
    if (footerElm) {
      const footerIsInView = isAPartElmInView(footerElm);
      const billInfo: any = document.getElementById('bill-info');
      if (footerIsInView) {
        if (billInfo.classList.contains('is-fixed-resume') === true) {
          billInfo.classList.remove('is-fixed-resume');
          setPaddingBill(null);
        }
      } else if (billInfo.classList.contains('is-fixed-resume') === false) {
        billInfo.classList.add('is-fixed-resume');
        setPaddingBill(footerRef?.current?.clientHeight || null);
      }
    }
  }, 100);

  // Detect footer when scroll
  useEffect(() => {
    if (!isMobileViewPort) return;
    window.addEventListener('scroll', debounceCheckFooterInViewOnMobile);
    return () => window.removeEventListener('scroll', debounceCheckFooterInViewOnMobile);
  }, [isMobileViewPort]);

  const fixedResume = isMobileViewPort ? 'is-fixed-resume' : null;

  return (
    <div
      className={classNames('block', Styles.bill, Styles.customBill, fixedResume)}
      id="bill-info"
      style={isMobileViewPort && paddingBill ? { paddingBottom: `${paddingBill}px` } : undefined}
    >
      <div className="block__header">
        <h2 className="title m-b">{TEXT.DETAIL_INFO_PAYMENT}</h2>
      </div>
      <div className="block__body">
        <div className="list-group">
          <div className="list-group__item divide--dashed text-nowrap">
            <BillItem label={TEXT.ACCOUNT_VieON} value={nameLabel || '-'} />
          </div>
          <div className="list-group__item divide--dashed">
            <BillItem label={TEXT.CONTENT_NAME} value={productNameMsg || '-'} />
            <BillItem
              label={isLiveEvent && !isChangeToVod ? `${TEXT.TERM}` : `${TEXT.TERM}*`}
              value={isLiveEvent && !isChangeToVod ? TEXT.LIVE_EVENT_ENDED : durationMsg}
            />
          </div>
          <div className="list-group__item divide--dashed">
            <BillItem label={TEXT.TRANSACTION_DATE} value={startDateMsg || '-'} />
          </div>
          <div className="list-group__item divide--dashed">
            <BillItem label={TEXT.WORTH} value={priceMsg} />
            {discountAmount ? <BillItem label={TEXT.DISCOUNT} value={discountAmountMsg} /> : ''}
            {isEnableInputPromotionCode && (
              <Promotion isNotSelectBank={isNotSelectBank} onHandleLogin={onHandleLogin} />
            )}
          </div>
        </div>
      </div>
      <div className={classNames('block__footer', Styles.billFooter)} ref={footerRef}>
        <div className="price temporary">
          <BillItem
            billItemAlign=" align-middle"
            label={TEXT.TOTAL_AMOUNT}
            value={totalPriceMsg}
            valueClass="text text-bold large-x highlight"
          />
        </div>
        {extraError && (
          <div className="form-error flex-box align-middle justify-content-center m-b padding-small-up-top-5 padding-small-up-bottom-12 padding-large-up-bottom-20">
            <span className="icon icon--tiny">
              <i className="vie vie-exclamation-tri-rc-light" />
            </span>
            <span className="text padding-small-up-left-4 p-t p-b">Vui lòng chọn Ngân hàng</span>
          </div>
        )}
        {tvodNote && (
          <div
            className="text text-gray117 text-12"
            dangerouslySetInnerHTML={{ __html: tvodNote }}
          />
        )}
        <div className="text text-gray117 text-12">
          {`Bằng việc thanh toán, bạn xác nhận đã đọc và đồng ý với `}
          <a
            className="link"
            href={inApp ? PAGE.ZALOPAY_REGULATION : PAGE.REGULATION}
            target="_blank"
            title={TEXT.CONTRACT_POLICY}
            rel="noreferrer"
          >
            {TEXT.CONTRACT_POLICY}
          </a>
          {` của VieON`}
        </div>
        {checkoutButton?.isShow && (
          <div className="button-group child-auto">
            <Button
              className="button button--green button--large"
              title={TEXT.CHECK_OUT}
              textClass={classNames(Styles.textCheckOut, '!text-white')}
              disabled={checkoutButton?.disabled}
              onClick={onCheckOut}
            />
          </div>
        )}
        <LogoCertificate />
      </div>
    </div>
  );
};

export default React.memo(BillTVod);
