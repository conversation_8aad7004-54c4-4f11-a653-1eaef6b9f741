import React, { useEffect, useState } from 'react';
import InputGroup from '../components/basic/Input/InputGroup';
import { TEXT } from '@vieon/core/constants/text';
import { EL_ID } from '@vieon/core/constants/constants';
import { useDispatch, useSelector } from 'react-redux';
import BillingInfoAPI from '@vieon/core/api/billing/BillingInfo';
import { ACTION_TYPE, createAction } from '@vieon/core/store/actions/actionType';
import SingleSelect from '../components/basic/SingleSelect/SingleSelect';

function BillingInfo() {
  const [countryOptions, setCountryOptions] = useState([]);
  const [provinceOptions, setProvinceOptions] = useState([]);
  const [districtOptions, setDistrictOptions] = useState([]);
  const [wardOptions, setWardOptions] = useState([]);

  const [country, setCountry] = useState<any>();
  const [province, setProvince] = useState<any>();
  const [district, setDistrict] = useState<any>();
  const [ward, setWard] = useState<any>();
  const [address, setAddress] = useState<any>('');
  const [addressError, setAddressError] = useState<any>('');
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal) || false;
  const isLogged = useSelector((state: any) => state?.Profile?.profile?.id) || false;
  const billingInfo = useSelector((state: any) => state?.Payment?.billingInfo);
  const dispatch = useDispatch();

  useEffect(() => {
    getCountry().then();
  }, []);

  useEffect(() => {
    if (isLogged) {
      getBillingInfoAsiaPay().then();
    }
  }, [isLogged]);
  const setBillingInfo = (data: any) => {
    dispatch(createAction(ACTION_TYPE.SET_BILLING_INFO, data));
  };
  // GET DATA
  const getBillingInfoAsiaPay = async () => {
    const countryData = await getCountry();
    let provinceData = [];
    let districtData = [];
    let wardData = [];
    const { data: { result = {} } = {} } = await BillingInfoAPI.getBillingInfoAsiaPay();
    if (billingInfo?.address.length === 0 && result?.address) {
      setAddress(result.address);
      setBillingInfo({ address: result.address });
    } else {
      setAddress(billingInfo?.address);
    }
    if (billingInfo?.address.length === 0 && result?.country) {
      const foundCountry = await countryData.find(
        (option: any) => option.code === result.country_code
      );
      if (foundCountry) {
        setCountry(foundCountry);
        setBillingInfo({
          country: foundCountry.name,
          country_code: foundCountry.code
        });
        provinceData = await getProvince(foundCountry?.id);
      }
    } else {
      const foundCountry = await countryData.find(
        (option: any) => option.code === billingInfo?.country_code
      );
      if (foundCountry) {
        setCountry(foundCountry);
        provinceData = await getProvince(foundCountry?.id);
      }
    }
    if (billingInfo?.address.length === 0 && result?.province && provinceData.length > 0) {
      const foundProvince = provinceData.find((option: any) => option.name === result.province);
      if (foundProvince) {
        setProvince(foundProvince);
        setBillingInfo({
          province: foundProvince.name,
          postal_code: foundProvince.postal_code,
          iso_subdivision: foundProvince.iso_subdivision
        });
        districtData = await getDistrict(foundProvince?.id);
      }
    } else {
      const foundProvince = provinceData.find(
        (option: any) => option.name === billingInfo?.province
      );
      if (foundProvince) {
        setProvince(foundProvince);
        districtData = await getDistrict(foundProvince?.id);
      }
    }

    if (billingInfo?.address.length === 0 && result?.district && districtData.length > 0) {
      const foundDistrict = districtData.find((option: any) => option.name === result.district);
      if (foundDistrict) {
        setDistrict(foundDistrict);
        setBillingInfo({ district: foundDistrict.name });
        wardData = await getWard(foundDistrict?.id);
      }
    } else {
      const foundDistrict = districtData.find(
        (option: any) => option.name === billingInfo?.district
      );
      if (foundDistrict) {
        setDistrict(foundDistrict);
        wardData = await getWard(foundDistrict?.id);
      }
    }

    if (billingInfo?.address.length === 0 && result?.ward && districtData.length > 0) {
      const foundWard = wardData.find((option: any) => option.name === result.ward);
      setWard(foundWard);
      setBillingInfo({ ward: foundWard.name });
    } else {
      const foundWard = wardData.find((option: any) => option.name === billingInfo?.ward);
      setWard(foundWard);
    }
  };
  const getCountry = async () => {
    const { data: { result = {} } = {} } = await BillingInfoAPI.getCountry();
    if (result?.items?.length > 0) {
      const options = result.items.map((item: any) => ({
        value: item.id,
        label: item.name,
        ...item
      }));
      setCountryOptions(options);

      if (!isGlobal) {
        setBillingInfo({ country: options[0].name, country_code: options[0].code });
        await getProvince(options[0].id); // Default VN countryId=1
      }
      return options;
    }
    return [];
  };
  const getProvince = async (countryId: any) => {
    const { data: { result = {} } = {} } = await BillingInfoAPI.getProvince(countryId);
    if (result?.items?.length > 0) {
      const options = result.items.map((item: any) => ({
        value: item.id,
        label: item.name,
        ...item
      }));
      setProvinceOptions(options);
      return options;
    }
    return [];
  };

  const getDistrict = async (provinceId: any) => {
    const { data: { result = {} } = {} } = await BillingInfoAPI.getDistrict(provinceId);
    if (result?.items?.length > 0) {
      const options = result.items.map((item: any) => ({
        value: item.id,
        label: item.name,
        ...item
      }));
      setDistrictOptions(options);
      return options;
    }
    return [];
  };
  const getWard = async (districtId: any) => {
    const { data: { result = {} } = {} } = await BillingInfoAPI.getWard(districtId);
    if (result?.items?.length > 0) {
      const options = result.items.map((item: any) => ({
        value: item.id,
        label: item.name,
        ...item
      }));
      setWardOptions(options);
      return options;
    }
    return [];
  };

  // ON CHANGE
  const onChangeCountry = (value: any) => {
    setProvince(value);
    setBillingInfo({
      country: value.name,
      country_code: value.code,
      province: '',
      postal_code: '',
      iso_subdivision: '',
      district: '',
      ward: ''
    });
    getProvince(value.value);
  };

  const onChangeProvince = (value: any) => {
    setProvince(value);
    setBillingInfo({
      province: value.name,
      postal_code: value.postal_code,
      iso_subdivision: value.iso_subdivision,
      district: '',
      ward: ''
    });
    getDistrict(value.value);
    setDistrict('');
    setWard('');
  };
  const onChangeDistrict = (value: any) => {
    setDistrict(value);
    setBillingInfo({ district: value.name, ward: '' });
    getWard(value.value);
    setWard('');
  };
  const onChangeWard = (value: any) => {
    setBillingInfo({ ward: value.name });
    setWard(value);
  };

  const onChangeAddress = (event: any) => {
    const regexAddressLength = /^.{3,50}$/;
    const regexAddressInput = /^[\w\-/\\ \u00C0-\u1EF9]*$/;

    setAddress(event.target.value);
    setBillingInfo({ address: event.target.value });
    if (!regexAddressLength.test(event.target.value)) {
      setAddressError(TEXT.BILLING_ADDRESS_ERROR_LENGTH);
    } else if (!regexAddressInput.test(event.target.value)) {
      setAddressError(TEXT.BILLING_ADDRESS_ERROR_INPUT);
    } else setAddressError('');
  };

  return (
    <div>
      <div className="pb-4 text-[#222] font-[500]">Địa chỉ thanh toán</div>
      <InputGroup
        label={TEXT.BILLING_ADDRESS}
        id={EL_ID.BILLING_ADDRESS}
        className="input-for-light"
        value={address}
        onChange={onChangeAddress}
        error={addressError}
        autoComplete="cc-name"
        name={EL_ID.BILLING_ADDRESS}
      />
      {!isGlobal && (
        <>
          <SingleSelect
            classNames="pb-8"
            onChange={onChangeProvince}
            options={provinceOptions}
            label={TEXT.BILLING_CITY}
            value={province}
            id="asd"
          />
          <SingleSelect
            classNames="pb-8"
            onChange={onChangeDistrict}
            options={districtOptions}
            label={TEXT.BILLING_DISTRIC}
            value={district}
            isDisabled={!province?.value}
            id="as2d"
          />
          <SingleSelect
            onChange={onChangeWard}
            options={wardOptions}
            classNames="pb-4"
            label={TEXT.BILLING_WARD}
            value={ward}
            isDisabled={!district?.value}
            id="as1d"
          />
        </>
      )}

      {isGlobal && (
        <>
          <SingleSelect
            onChange={onChangeCountry}
            options={countryOptions}
            label={TEXT.BILLING_COUNTRY}
            value={country}
          />
          <SingleSelect
            onChange={onChangeProvince}
            options={provinceOptions}
            label={TEXT.BILLING_DISTRIC_G}
            value={!country?.value}
          />
          <SingleSelect
            onChange={onChangeDistrict}
            options={districtOptions}
            label={TEXT.BILLING_CITY_G}
            value={district}
            isDisabled={!province?.value}
          />
        </>
      )}
    </div>
  );
}

export default BillingInfo;
