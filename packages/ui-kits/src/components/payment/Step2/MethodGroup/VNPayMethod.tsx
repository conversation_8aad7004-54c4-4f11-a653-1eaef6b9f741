import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectBank, getVnPayList } from '@vieon/core/store/actions/payment';
import TrackingPayment from '@vieon/tracking/functions/payment';
import { TEXT } from '@vieon/core/constants/text';
import Image from '../components/basic/Image/Image';
import classNames from 'classnames';

const trackingPayment = new TrackingPayment();

const VNPayMethod = ({ isActive, isRentalContent }: any) => {
  const dispatch = useDispatch();
  const { vnPayList, bank } = useSelector((state: any) => state?.Payment) || {};
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);

  useEffect(() => {
    setWrapHeight(wrapRef?.current?.clientHeight || 0);
  });

  const onSelectBank = (item: any) => {
    trackingPayment.selectBank({ bank: item });
    dispatch(selectBank(item));
  };

  useEffect(() => {
    if (!vnPayList) {
      dispatch(getVnPayList());
    }
  }, [vnPayList]);

  return (
    <div
      className={classNames(
        'accordion__body collapse overflow',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0px' }}
    >
      <div className="accordion__wrap p-t2 padding-large-up-top-16" ref={wrapRef}>
        <div className="list list--bank">
          <p className="text">
            {`Chọn một trong các ngân hàng sau và bấm ${
              isRentalContent ? TEXT.CHECK_OUT : TEXT.SUBSCRIBE_PACKAGE
            }`}
          </p>
          <div className="grid-x grid-margin-x4 small-up-4 medium-up-6 large-up-8 grid-margin-y4">
            {(vnPayList?.slice(1) || []).map((item: any) => (
              <ItemBank key={item.id} data={item} bank={bank} onSelect={onSelectBank} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const ItemBank = ({ data, bank, onSelect }: any) => (
  <div className="cell" onClick={() => onSelect(data)}>
    <div className={`card card--bank${data.id === bank?.id ? ' active' : ''}`}>
      <div className="card-image ratio-16-9">
        <Image src={data?.logo} title={data?.code} notWebp />
      </div>
    </div>
  </div>
);

export default VNPayMethod;
