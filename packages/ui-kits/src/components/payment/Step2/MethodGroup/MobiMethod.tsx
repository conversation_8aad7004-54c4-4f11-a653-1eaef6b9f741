import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import ConfigImage from '@vieon/core/config/ConfigImage';
import { exportHrefSms, exportSmsQrCode } from '@vieon/core/services/paymentServices';
import { TEXT } from '@vieon/core/constants/text';
import styles from './Method.module.scss';
import classNames from 'classnames';

const MobiMethod = ({ method, isActive, selectedTerm }: any) => {
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const synText = useMemo(() => method?.syntax?.[selectedTerm?.id], [method, selectedTerm?.id]);
  const [syntaxData, setSyntax] = useState({
    syntax: synText,
    qrCode: ''
  });
  const hrefSms = useMemo(
    () => exportHrefSms(method?.smsTo, syntaxData?.syntax),
    [method, syntaxData?.syntax]
  );
  const wrapRef = useRef<any>(null);
  const [wrapHeight, setWrapHeight] = useState(0);
  useEffect(() => {
    setWrapHeight(wrapRef?.current?.clientHeight || 0);
  });

  useEffect(() => {
    setQrCode();
  }, [profile, selectedTerm?.id, synText]);

  const setQrCode = async () => {
    let syntax = `${synText} `;
    const { email, mobile, phoneVerified, emailVerified } = profile || {};
    if (mobile && phoneVerified) {
      syntax += mobile;
    } else if (email && emailVerified) {
      syntax += email;
    } else {
      syntax += TEXT.PHONE_EMAIL_VieON;
    }
    const qrCode: any = await exportSmsQrCode({
      phone: method?.smsTo,
      text: syntax
    });
    setSyntax({
      syntax,
      qrCode
    });
  };
  const renderInfo = (text: any) => (
    <div className="flex-box relative text-white">
      <span className="icon icon--tiny absolute left">
        <i className="vie vie-tick" />
      </span>
      <div
        className="text-12 p-l3"
        dangerouslySetInnerHTML={{
          __html: text || ''
        }}
      />
    </div>
  );

  return (
    <div
      className={classNames(
        'accordion__body collapse partner-mobi overflow',
        isActive && '!opacity-100 !visible'
      )}
      style={{ height: isActive ? `${wrapHeight}px` : '0' }}
    >
      <div className="accordion__wrap p-t2 padding-large-up-top-16" ref={wrapRef}>
        <div className="recharge recharge-mobi bg-blue108 round-2">
          <div className="recharge-box grid-x p-x3 p-y3 margin-cel-large-down-bottom-16">
            <div className="qr-code small-12 medium-shrink p-t">
              <div className="box-width-xlarge-up-292 padding-medium-up-right-20 flex flex-col items-center gap-4">
                <p className="text text-center text-white text-sm">{TEXT.QR_CODE_SMS}</p>
                {syntaxData?.qrCode && <img src={syntaxData?.qrCode} alt="Mã QRCode" />}
              </div>
            </div>
            <div className="small-12 medium-auto">
              <div className="padding-medium-up-left-20 margin-cel-bottom-12 margin-cel-medium-up-bottom-16">
                <p className={styles.MethodSms}>{TEXT.SEND_SMS_SYNTAX}</p>
                <div className="recharge-content bg-white round-4 padding-xlarge-up-left-24 padding-xlarge-up-right-24 padding-small-up-left-12 padding-small-up-right-12 padding-small-up-top-4 padding-small-up-bottom-4">
                  <div className="text text-center text-bold flex justify-center items-center gap-2">
                    {syntaxData?.syntax || ''}
                    <img src={ConfigImage.btnSendMessage} alt="" />
                    {method?.smsTo || ''}
                  </div>
                </div>
                <div className="button-group child-auto hide-for-large-only">
                  <a href={hrefSms} className="button button--light hollow">
                    <span className="icon icon--small">
                      <i className="vie vie-messages-light text text-white" />
                    </span>
                    <div className="text">{TEXT.CREATE_SMS}</div>
                  </a>
                </div>
                <div className="margin-cel-small-up-bottom-4">
                  {method?.info && (method?.info || []).map((item: any) => renderInfo(item))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobiMethod;
