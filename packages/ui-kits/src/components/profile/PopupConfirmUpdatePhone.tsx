import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setToast } from '@vieon/core/store/actions/app';
import { getProfile } from '@vieon/core/store/actions/profile';
import { TEXT } from '@vieon/core/constants/text';
import { ID, TIME_RESEND_OTP } from '@vieon/core/constants/constants';
import TrackingLoyalty from '@vieon/tracking/functions/TrackingLoyalty';
import InputCustomOtpCode from '../basic/Input/InputCustomOtpCode';
import CountdownButton from '../basic/Buttons/CountdownButton';
import InputCustom from '../basic/Input/InputCustom';
import Modal from '../basic/Modal';

// custom hook
function useFormInput(initialValue: any) {
  const [value, setValue] = useState(initialValue);
  const handleChange = (event: any) => {
    setValue(event.target.value);
  };
  return { value, onChange: handleChange };
}

let inTervalTemp: any = null;
const PopupConfirmUpdatePhone = ({
  dataProfile,
  phoneUpdated,
  confirmUpdatePhone,
  resendOtpUpdatePhone,
  actionName,
  onOpenPopup,
  closePopup
}: any) => {
  const dispatch = useDispatch();
  const handleClickOutside = () => {
    if (closePopup) closePopup();
  };

  // data form input
  const passwordTemp = useFormInput('');
  const confirmPasswordTemp = useFormInput('');
  const [otpCodeState, setOtpCode] = useState<any>('');

  // data error
  const [errorUpdatePhone, setErrorUpdatePhone] = useState<any>({
    otpCode: '',
    password: '',
    confirmPassword: '',
    error: ''
  });
  const { deviceModel, deviceName, deviceType } = useSelector((state: any) => state?.App || {});
  const { isOnLoyalty } = useSelector((state: any) => state?.App?.webConfig?.featureFlag || false);

  const onConfirmUpdatePhone = (e?: any, otpValue?: any) => {
    if (e) e.preventDefault();

    const otpCode = otpValue || otpCodeState;
    if (otpCode?.length !== 4) {
      setErrorUpdatePhone({
        otpCode: TEXT.OTP_WRONG,
        password: '',
        confirmPassword: ''
      });
      return;
    }

    const password = passwordTemp.value;
    const confirmPassword = confirmPasswordTemp.value;
    if (!dataProfile?.mobile) {
      if (password.length < 6) {
        setErrorUpdatePhone({
          otpCode: '',
          password: TEXT.PASSWORD_REQUIRED_VALUE,
          confirmPassword: '',
          error: ''
        });
        return;
      }

      if (password !== confirmPassword) {
        setErrorUpdatePhone({
          otpCode: '',
          password: '',
          confirmPassword: TEXT.PASSWORD_NOT_MATCHING
        });

        return;
      }
    }

    confirmUpdatePhone(otpCode, password, confirmPassword).then((resp: any) => {
      const error = resp?.data?.message;
      if (error) {
        setErrorUpdatePhone({
          otpCode: '',
          password: '',
          confirmPassword: '',
          error
        });
      } else {
        if (isOnLoyalty) {
          TrackingLoyalty.trackingLoyaltyUpdatePhoneNumber();
        }
        if (onOpenPopup) onOpenPopup();
        dispatch(setToast({ message: TEXT.UPDATE_PHONE_SUCCESS }));
        dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      }
    });
  };

  const countDown = 5;
  // set time resend otp code
  const [currentWait, setcurrentWait] = useState(countDown);
  useEffect(() => {
    inTervalTemp = setInterval(() => {
      if (currentWait > 0) setcurrentWait(currentWait - 1);
    }, 1000);
    return function cleanup() {
      clearInterval(inTervalTemp);
    };
  });

  const onReSendOtpCode = () => {
    resendOtpUpdatePhone().then((resp: any) => {
      const error = resp?.data?.message;
      if (error) {
        setErrorUpdatePhone({
          otpCode: '',
          password: '',
          confirmPassword: error
        });
      }
    });
  };

  const onChangeOtp = (valueOtpCode: any) => {
    setOtpCode(valueOtpCode);
    if ((valueOtpCode || '').length === 4 && dataProfile?.mobile) {
      onConfirmUpdatePhone(null, valueOtpCode);
    }
  };

  let subTitle = `Vui lòng nhập mã xác nhận được gửi đến số điện thoại của bạn. Và đồng thời đặt mật khẩu để có thể đăng nhập tài khoản VieON với số điện thoại ${
    phoneUpdated || ''
  }`;

  let title = TEXT.CONFIRM_PHONE_PASSWORD;

  if (dataProfile.mobile) {
    subTitle = `Vui lòng nhập mã xác thực được gửi đến số điện thoại ${phoneUpdated || ''} của bạn`;
    title = TEXT.CONFIRM_PHONE;
  }

  const renderBody = () => (
    <div className="block block--for-dark">
      <h2 className="title text-center text-white">{title || ''}</h2>
      <p className="text text-center text-muted">{subTitle || ''}</p>

      <form
        className="form form-default form-for-light form--member form--member-modal form--member-sign"
        autoComplete="off"
        id="PROFILE_FORM_ID"
        noValidate
      >
        <InputCustomOtpCode
          id="otpCode"
          onEnter={onConfirmUpdatePhone}
          onChangeOtp={onChangeOtp}
          autoComplete="new-password"
        />
        {!dataProfile.mobile && (
          <>
            <InputCustom
              className="input-for-dark"
              icon="vie vie-key-skeleton-o"
              type="password"
              label="Mật khẩu"
              id="password_new"
              error={errorUpdatePhone.password}
              valueOutput={passwordTemp}
              autoComplete="new-password"
            />

            <InputCustom
              className="input-for-dark"
              type="password"
              icon="vie vie-key-skeleton-o"
              label="Nhập lại mật khẩu"
              id="confirm_password"
              error={errorUpdatePhone.confirmPassword}
              valueOutput={confirmPasswordTemp}
              autoComplete="new-password"
            />
          </>
        )}
        <div className="text-center">
          <CountdownButton
            label="Không nhận được tin nhắn ?"
            buttonName={TEXT.RE_SEND_OTP}
            time={TIME_RESEND_OTP}
            onResend={onReSendOtpCode}
          />
          {errorUpdatePhone?.error && (
            <label className="form-error is-visible text-center m-t2">
              {errorUpdatePhone?.error}
            </label>
          )}
        </div>
      </form>
    </div>
  );

  const renderFooter = () => (
    <div className="button-group child-auto p-y4">
      <button
        onClick={onConfirmUpdatePhone}
        className="button button--light button--large"
        type="button"
        disabled={
          !!(
            otpCodeState === '' ||
            (!dataProfile.mobile && passwordTemp.value === '') ||
            (!dataProfile.mobile && confirmPasswordTemp.value) === ''
          )
        }
        title="Xác nhận"
      >
        {TEXT.CONFIRM}
      </button>
    </div>
  );

  if (actionName !== ID.CONFIRM_PHONE && actionName !== ID.CONFIRM_PHONE_PASSWORD) return null;

  return (
    <Modal
      className="green-line modal--sign modal--sign-confirm"
      renderBody={renderBody}
      renderFooter={renderFooter}
      onClosed={handleClickOutside}
    />
  );
};

export default PopupConfirmUpdatePhone;
