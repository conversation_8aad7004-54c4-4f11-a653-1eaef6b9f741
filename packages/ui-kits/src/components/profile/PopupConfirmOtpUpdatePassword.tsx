import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { getOtpUpdatePassword, confirmOtpUpdatePassword } from '@vieon/core/store/actions/user';
import { PROFILE_STATUS, HTTP_CODE } from '@vieon/core/constants/constants';
import ConfirmOtp from '../components/profile/ConfirmOtp';

const PopupConfirmOtpUpdatePassword = ({ setHideOtpUpdatePassword }: any) => {
  const [otpCode, setOtpCode] = useState<any>('');
  const [error, setError] = useState<any>('');
  const dispatch = useDispatch();
  const { otpUpdatePassword, confirmedOtpUpdatePassword } = useSelector(
    (state: any) => state?.User || {}
  );
  const sessionId = otpUpdatePassword?.data?.otp?.session_id;
  const isLimitSendOtp = otpUpdatePassword?.httpCode === HTTP_CODE.TOO_MANY_REQUEST;

  useEffect(() => {
    dispatch(getOtpUpdatePassword());
  }, []);

  useEffect(() => {
    if (otpUpdatePassword && isLimitSendOtp) {
      setError(otpUpdatePassword?.data?.msg);
    }
  }, [otpUpdatePassword]);

  useEffect(() => {
    if (isLimitSendOtp) return;
    if (!isEmpty(confirmedOtpUpdatePassword)) {
      if (confirmedOtpUpdatePassword?.status === PROFILE_STATUS.SUCCESS) {
        setHideOtpUpdatePassword(true);
      } else {
        setError(confirmedOtpUpdatePassword?.msg);
      }
    }
  }, [confirmedOtpUpdatePassword]);

  const onReSendOtpCode = () => {
    if (isLimitSendOtp) return;
    dispatch(getOtpUpdatePassword());
    if (isLimitSendOtp) {
      setError(otpUpdatePassword?.data?.msg);
    } else {
      setError('');
    }
  };

  const onChangeOtp = (valueOtpCode: any) => {
    if (isLimitSendOtp) return;
    setOtpCode(valueOtpCode);
    if (valueOtpCode.length === 4) {
      dispatch(confirmOtpUpdatePassword(sessionId, valueOtpCode));
    } else {
      setError('');
    }
  };

  return (
    <ConfirmOtp
      onChangeOtp={onChangeOtp}
      onReSendOtpCode={onReSendOtpCode}
      forceDisabled={isLimitSendOtp}
      error={error}
      otpCode={otpCode}
      isUpdatePassword
    />
  );
};

export default React.memo(PopupConfirmOtpUpdatePassword);
