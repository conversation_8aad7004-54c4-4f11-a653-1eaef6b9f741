import React from 'react';
import { openPopup } from '@vieon/core/store/actions/popup';
import { useDispatch } from 'react-redux';
import Modal from '../components/basic/Modal';
import classNames from 'classnames';
import Styles from './Styles.module.scss';
import Image from '../components/basic/Image/Image';
import { statusConfirmPolicyAnnounce } from '@vieon/core/store/actions/notification';

const PopupPolicyAnnounceSync = ({ greenLine, image, title, description, btnPrimary }: any) => {
  const dispatch = useDispatch();

  const handleClosePopup = () => {
    dispatch(statusConfirmPolicyAnnounce(true));
    dispatch(openPopup());
  };

  const renderHeader = () => {
    return (
      <div className="mask">
        <div className="mask-inner text-center">
          <Image src={image} alt={title} />
        </div>
      </div>
    );
  };

  const renderBody = () => {
    return (
      <article className={Styles.NotifyAnnounceSyncModalContent}>
        {title && <h2 className={Styles.NotifyAnnounceSyncModalTitle}>{title}</h2>}
        {description && <p>{description}</p>}
      </article>
    );
  };

  const renderFooter = () => {
    return (
      <div className={classNames(Styles.NotifyAnnounceSyncModalActionGroup)}>
        <button
          className={classNames(Styles.NotifyAnnounceSyncModalActionPrimary)}
          title={btnPrimary}
          onClick={handleClosePopup}
        >
          {btnPrimary}
        </button>
      </div>
    );
  };

  return (
    <Modal
      renderHeader={image && renderHeader}
      renderBody={renderBody}
      renderFooter={renderFooter}
      notClosedButton
      modalNewUi
      modalUiWrapper={Styles.NotifyAnnounceSyncModalWrapper}
      bodyClass={Styles.NotifyAnnounceSyncModalBody}
      size="small"
      greenLine={greenLine}
    />
  );
};

export default PopupPolicyAnnounceSync;
