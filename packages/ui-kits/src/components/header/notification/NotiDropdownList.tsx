import { TEXT } from '@vieon/core/constants/text';
import Image from '../components/basic/Image/Image';
import ConfigImage from '@vieon/core/config/ConfigImage';
import React from 'react';
import { ACTION_NOTIFY } from '@vieon/core/constants/constants';

const NotiDropdownList = ({
  readNoti,
  onClickCheckBox,
  activeCheckBox,
  refEL,
  setActiveDots
}: any) => {
  const onHandleActiveCheckBox = (e: any) => {
    const { checked } = e.target;
    onClickCheckBox(checked);
    setActiveDots(false);
  };
  const onHandleActionNotify = (e: any, action: any) => {
    readNoti(e, action);
    setActiveDots(false);
    if (action === ACTION_NOTIFY.DELETE_ALL) {
      onClickCheckBox(false);
    }
  };
  return (
    <div
      ref={refEL}
      className="absolute rounded-[4px] py-2 px-4 space-y-2 bg-[#222] top-[calc(100%+1px)] right-6 text-white z-[100]"
    >
      <div className="py-1">
        <label className="relative flex items-center group  text-xl">
          <input
            type="checkbox"
            className="cursor-pointer absolute left-1/2 top-0 -translate-x-1/2 w-full h-full peer appearance-none rounded-md opacity-0"
            onChange={(e) => onHandleActiveCheckBox(e)}
            defaultChecked={activeCheckBox}
          />
          <span className="peer-checked:bg-green-400 peer-checked:after:translate-x-[15px] cursor-pointer w-[32px] h-[17px] flex items-center flex-shrink-0  p-[1px] bg-[#999999] rounded-full duration-300 ease-in-out after:w-[15px] after:h-[15px] after:bg-white after:rounded-full after:shadow-md after:duration-300" />
          <span className="relative z-[10] cursor-pointer ml-3 text-[#DEDEDE] hover:text-white text-[18px] font-medium">
            {TEXT.ONLY_SHOW_NOTI_UNREAD}
          </span>
        </label>
      </div>
      <div
        className=" cursor-pointer py-1"
        onClick={(e) => onHandleActionNotify(e, ACTION_NOTIFY.READ_ALL)}
      >
        <Image src={ConfigImage.tickNoti} alt="dots" notWebp />
        <span className="ml-3 text-[#DEDEDE] hover:text-white text-[18px] font-medium">
          {TEXT.MARK_ALL_READ}
        </span>
      </div>
      <div
        className="cursor-pointer py-1"
        onClick={(e) => onHandleActionNotify(e, ACTION_NOTIFY.DELETE_ALL)}
      >
        <Image src={ConfigImage.remove} alt="dots" notWebp />
        <span className="ml-3 text-[#DEDEDE] hover:text-white text-[18px] font-medium">
          {TEXT.REMOVE_ALL_NOTI}
        </span>
      </div>
    </div>
  );
};
export default NotiDropdownList;
