import React, { useMemo, useRef, useState, useEffect } from 'react';
import { useOnClickOutside } from '@customHook';
import { TEXT } from '@vieon/core/constants/text';
import { KEY_TIME_NOTIFY, POSITION_TRIGGER, TYPE_TRIGGER_ALWAYS } from '@vieon/core/constants/constants';
import TriggerTouchPoint from '../components/home/<USER>';
import { useDispatch, useSelector } from 'react-redux';
import style from '../components/home/<USER>';
import Image from '../components/basic/Image/Image';
import ConfigImage from '@vieon/core/config/ConfigImage';
import NotiDropdownList from '../components/header/notification/NotiDropdownList';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { computeCompareTime, createTimeout } from '@vieon/core/utils/common';
import { getDataTriggerPoint } from '@vieon/core/store/actions/trigger';
import NotifyPolicyAnnounce from '../components/header/notification/NotifyPolicyAnnounce';
import Button from '../../basic/Buttons/Button';
import EmptyNotify from '../../empty/EmptyNotify';
import NotificationItem from './NotificationItem';

const NotificationList = React.forwardRef((props: any, ref: any) => {
  const {
    isNotify,
    readNoti,
    listDataAllPage,
    firstPage,
    isMobile,
    closeNotify,
    closeMenu,
    onCloseContentItem,
    wrapperRef,
    handleClickItemNoti
  } = props;
  const dispatch = useDispatch();
  const refEL = useRef<any>(null);
  const refElIcon = useRef<any>(null);
  const clickTimerRef = useRef<any>(null);
  const { dataTriggerNoti } = useSelector((state: any) => state?.Trigger);
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const [activeDots, setActiveDots] = useState(false);
  const [activeCheckBox, setActiveCheckBox] = useState(false);

  const dataNotiAnnounce = useMemo(() => {
    if (isEmpty(listDataAllPage)) return [];
    if (activeCheckBox) {
      return (
        listDataAllPage[1].filter((item: any) => item.isNotiTypeAnnounce && !item.isRead) || []
      );
    }
    return listDataAllPage[1].filter((item: any) => item.isNotiTypeAnnounce) || [];
  }, [listDataAllPage, activeCheckBox]);
  useOnClickOutside(refEL, () => setActiveDots(false), refElIcon);

  const onClickNotice = () => {
    if (closeNotify) closeNotify();
  };

  const onHandleClickDots = () => {
    setActiveDots(!activeDots);
  };
  const onClickCheckBox = (checked: any) => {
    setActiveCheckBox(checked);
  };
  const compareTimeArrays = computeCompareTime(listDataAllPage, activeCheckBox);
  if (!isMobile) useOnClickOutside(wrapperRef, onCloseContentItem);

  const paneClass = `pane pane--user pane--user-notify pane--dark animate-fade-right ${
    isMobile ? 'p-y' : 'absolute top-full'
  }`;

  useEffect(() => {
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.NOTIFICATION }));
    }, 400);
    return () => {
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    };
  }, []);

  return (
    <div className={paneClass}>
      <div className="pane-container">
        <div className="pane__header p-y relative !px-3 md:!px-5">
          <h4 className="pane__title">
            <Button
              className="button button--for-dark button--large !px-0"
              // iconClass="icon--small"
              iconName={isMobile ? 'vie-chevron-left-r-medium' : ''}
              // iconAlign="middle-left"
              textClass="!text-[20px]"
              title={TEXT.NOTIFICATION}
              onClick={onClickNotice}
            />
          </h4>
          <div ref={refElIcon} className="cursor-pointer text-[#DEDEDE]">
            <Image src={ConfigImage.dots} alt="dots" notWebp onClick={onHandleClickDots} />
          </div>
          {activeDots && (
            <NotiDropdownList
              readNoti={readNoti}
              onClickCheckBox={onClickCheckBox}
              activeCheckBox={activeCheckBox}
              refEL={refEL}
              setActiveDots={setActiveDots}
            />
          )}
        </div>

        <div
          className={classNames(
            'max-h-full md:max-h-[73vh] scrollable-y over-scroll-contain overflow-x',
            (!!firstPage && !isNotify) || isEmpty(compareTimeArrays) ? 'h-[73vh] relative' : ''
          )}
          ref={ref}
        >
          {!isGlobal && !isEmpty(dataTriggerNoti) && !currentProfile?.isKid && (
            <TriggerTouchPoint
              image={dataTriggerNoti?.image}
              imageMobile={dataTriggerNoti?.image}
              url={dataTriggerNoti?.navigateUrl}
              positionTrigger={POSITION_TRIGGER.NOTIFY}
              className={style.scroll}
              closeMenu={closeMenu}
            />
          )}

          {(!isEmpty(dataNotiAnnounce) || !isEmpty(compareTimeArrays)) && (
            <div className="group horizontal epr-view-1 py-4 pl-3 md:pl-5 pr-5 md:pr-7 space-y-3">
              {dataNotiAnnounce.map((item: any, index: any) => (
                <NotifyPolicyAnnounce
                  item={item}
                  key={`${item.id}_${index}`}
                  itemPosition={index}
                  readNoti={readNoti}
                  closeNotify={closeNotify}
                  closeMenu={closeMenu}
                  page={1}
                  handleClickItemNoti={handleClickItemNoti}
                />
              ))}
              {Object.keys(compareTimeArrays).map((key) => {
                let titleDay = TEXT.NOTIFY_TODAY;
                if (Number.parseInt(key) === KEY_TIME_NOTIFY.YESTERDAY)
                  titleDay = TEXT.NOTIFY_YESTERDAY;
                if (Number.parseInt(key) === KEY_TIME_NOTIFY.OLD_DAYS) titleDay = TEXT.NOTIFY_OLDER;
                if (isEmpty(compareTimeArrays[key])) return null;
                return (
                  <div key={key + 1} className="space-y-3">
                    {!isEmpty(compareTimeArrays[key]) && (
                      <div className="text-[#999] !text-sm md:!text-base font-medium">
                        {titleDay}
                      </div>
                    )}
                    {compareTimeArrays[key] &&
                      compareTimeArrays[key].map((item: any, i: any) => (
                        <NotificationItem
                          item={item}
                          key={i}
                          readNoti={readNoti}
                          page={key}
                          itemPosition={i}
                          closeNotify={closeNotify}
                          closeMenu={closeMenu}
                          handleClickItemNoti={handleClickItemNoti}
                        />
                      ))}
                  </div>
                );
              })}
            </div>
          )}
          {(!!firstPage && !isNotify) ||
          (isEmpty(compareTimeArrays) && isEmpty(dataNotiAnnounce)) ? (
            <div className="w-full px-[18%] md:px-[20%] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
              <EmptyNotify />
            </div>
          ) : (
            ''
          )}
        </div>
      </div>
    </div>
  );
});

export default NotificationList;
