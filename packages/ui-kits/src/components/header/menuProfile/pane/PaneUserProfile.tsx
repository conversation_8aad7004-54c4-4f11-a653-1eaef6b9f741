import React from 'react';
import VieLink from '../components/VieLink';
import Icon from '../components/basic/Icon/Icon';
import LobbyNavigationOnTop from '../components/LobbyProfile/LobbyNavigationOnTop';
import { useOnClickOutside } from '@customHook';
import { TEXT } from '@vieon/core/constants/text';
import { PAGE } from '@vieon/core/constants/constants';
import classNames from 'classnames';
import MemberTier from '../../memberTier';

const PaneUserProfile = ({
  isInApp,
  displayName,
  isKid,
  onOpenEditLobbyProfile,
  onClickSetting,
  onOpenPopupLogout,
  isMobile,
  wrapperRef,
  onCloseContentItem,
  currentTier,
  isFailInfoLoyalty
}: any) => {
  useOnClickOutside(wrapperRef, onCloseContentItem);

  return (
    <div
      className="pane pane--user pane--user-profile pane--dark absolute animate-fade-right top-full"
      data-hover-child
    >
      <div className="pane-container">
        <div className="pane__header">
          {isInApp && !isMobile && (
            <div className="pane__item">
              <a>
                <span className="text">{displayName}</span>
              </a>
            </div>
          )}
          {(currentTier || isFailInfoLoyalty) && (
            <div className="padding-x-small-up-16 padding-y-small-up-12 border-st2-bottom">
              <MemberTier />
            </div>
          )}
          {!isInApp && !isMobile && (
            <div className="pane__item">
              <LobbyNavigationOnTop />
            </div>
          )}
          {!isKid && !isInApp && !isMobile && (
            <div className="pane__item">
              <a title={TEXT.EDIT_INFORMATION} onClick={onOpenEditLobbyProfile}>
                <Icon spClass="icon--small absolute" iClass="vie-pencil-o-rc" />
                <span className="text">{TEXT.EDIT_INFORMATION}</span>
              </a>
            </div>
          )}
        </div>
        <div className={classNames('pane__body', isMobile && 'p-y')}>
          {!isInApp && !isMobile && (
            <div className="pane__item">
              <VieLink href={PAGE.PROFILE}>
                <a title={TEXT.ACCOUNT_SETTING} onClick={onClickSetting}>
                  <Icon spClass="icon icon--small absolute" iClass="vie-cog-o" />
                  <span className="text">{TEXT.ACCOUNT_SETTING}</span>
                </a>
              </VieLink>
            </div>
          )}
          {!isKid && (
            <div className="pane__item">
              <a title={TEXT.LOGOUT} onClick={onOpenPopupLogout}>
                <Icon spClass="icon--small absolute" iClass="vie-power-off-light" />
                <span className="text">{TEXT.LOGOUT}</span>
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(PaneUserProfile);
