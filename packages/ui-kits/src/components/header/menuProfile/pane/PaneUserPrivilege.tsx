import React from 'react';
import { useSelector } from 'react-redux';
import { useOnClickOutside } from '@customHook';
import { LOCATION } from '@vieon/core/constants/constants';

const PaneUserPrivilege = ({
  onOpenLogin,
  onOpenRegister,
  wrapperRef,
  onCloseContentItem
}: any) => {
  const { geoCheck } = useSelector((state: any) => state?.App);
  const isGeoCheckValid = geoCheck?.geo_country === LOCATION.VIETNAM && geoCheck?.geo_valid;
  useOnClickOutside(wrapperRef, onCloseContentItem);

  return (
    <div className="pane pane--user pane--user-profile pane--dark absolute animate-fade-right top-full size-min-w-175">
      <div className="pane-container">
        <div className="pane__footer p-2">
          <div className="button-group vertical">
            <button
              className="button button--dark hollow medium size-w-full m-r"
              type="button"
              onClick={onOpenLogin}
            >
              <span className="text">Đăng nhập</span>
            </button>
            {isGeoCheckValid && (
              <button
                className="button button--dark hollow medium size-w-full m-b m-l"
                type="button"
                onClick={onOpenRegister}
              >
                <span className="text">Đăng ký</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaneUserPrivilege;
