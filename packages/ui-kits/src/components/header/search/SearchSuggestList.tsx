import React, { useEffect, useRef } from 'react';
import SearchItemAds from '../components/OutstreamAds/SearchItemAds';
import { CONTENT_TYPE, POSITION_TRIGGER, TYPE_TRIGGER_ALWAYS } from '@vieon/core/constants/constants';
import { useDispatch, useSelector } from 'react-redux';
import TriggerTouchPoint from '../components/home/<USER>';
import style from '../components/home/<USER>';
import SearchItem from './SearchItem';
import { getDataTriggerPoint } from '@vieon/core/store/actions/trigger';
import isEmpty from 'lodash/isEmpty';
import { createTimeout } from '@vieon/core/utils/common';

const SearchSuggestList = ({
  className,
  isNotShowAds,
  inputEl,
  onScrollingSearchSuggestion,
  dataShowPopup,
  handleClickBtnDel,
  itemsRef,
  activeID,
  handleClickSearchItems,
  handleIconSearch
}: any) => {
  const dispatch = useDispatch();
  const clickTimerRef = useRef<any>(null);
  const { dataTriggerSearch } = useSelector((state: any) => state?.Trigger);
  const currentProfile = useSelector((state: any) => state?.MultiProfile.currentProfile || {});
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  useEffect(() => {
    if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      dispatch(getDataTriggerPoint({ type: TYPE_TRIGGER_ALWAYS.SEARCH }));
    }, 400);
    return () => {
      if (clickTimerRef.current) clearTimeout(clickTimerRef.current);
    };
  }, []);

  return (
    <div
      id="search-suggestion"
      ref={inputEl}
      className={className}
      onScroll={onScrollingSearchSuggestion}
    >
      {!isGlobal && !isEmpty(dataTriggerSearch) && !currentProfile?.isKid && (
        <TriggerTouchPoint
          image={dataTriggerSearch?.image}
          imageMobile={dataTriggerSearch?.image}
          url={dataTriggerSearch?.navigateUrl}
          positionTrigger={POSITION_TRIGGER.SEARCH}
          className={style.scroll}
        />
      )}
      <div className="search__header" id={dataShowPopup.id}>
        <div className="title title-white">
          {dataShowPopup.title}
          {dataShowPopup.showDeleteBtn && (
            <button
              className="button button-clear remove-search"
              type="button"
              onClick={handleClickBtnDel}
            >
              Xoá
            </button>
          )}
        </div>
      </div>
      <div className="search__body">
        <ul className="search-suggestion__list">
          {(dataShowPopup?.items || []).map((item: any, index: any) => {
            const uniqueKey = `${item.id || ''}-${index}`;

            if (item.type === CONTENT_TYPE.ADS) {
              return isNotShowAds || isGlobal ? null : (
                <SearchItemAds key={uniqueKey} data={item} />
              );
            }

            return (
              <SearchItem
                key={uniqueKey}
                data={item}
                itemsRef={itemsRef}
                activeID={activeID}
                handleClickSearchItems={handleClickSearchItems}
                handleIconSearch={handleIconSearch}
              />
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default React.memo(SearchSuggestList);
