import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useVieRouter } from '@customHook';
import { useDispatch, useSelector } from 'react-redux';
import sortBy from 'lodash/sortBy';
import isEmpty from 'lodash/isEmpty';
import { CONTENT_TYPE, EL_ID, PAGE } from '@vieon/core/constants/constants';
import { addParamToUrlVieON, createTimeout } from '@vieon/core/utils/common';
import { onFocusSearchBox, getTrendKeywords } from '@vieon/core/store/actions/search';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import { NAME, PROPERTY } from '@vieon/core/config/ConfigSegment';
import { TEXT } from '@vieon/core/constants/text';
import Button from '../components/basic/Buttons/Button';
import { getTriggerConfig } from '@vieon/core/store/actions/appConfig';
import NewIcon from '../components/basic/Icon/NewIcon';
import FieldInputSearch from './FieldInputSearch';
import PaneNav from '../pane/PaneNav';
import SearchSuggestList from './SearchSuggestList';

let timeOutRequestSuggest: any = null;
let clickTimer: any = null;
let scrollTimer: any = null;
let localRouter = '';
let teamPathname = '';

const SearchInput = ({
  dataSearchTrendKeyword,
  dataSearchHistory,
  userProfile,
  openConfirmDel,
  onFirstFocus,
  searchItemAds,
  isMobile
}: any) => {
  const isSearchShow = useSelector((state: any) => state?.Search?.searchShow);
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
  const triggerConfig = useSelector((state: any) => state?.AppConfig?.trigger) || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const router = useVieRouter();
  const inputEl = useRef<any>(null);
  const itemsRef = useRef<any>([]);
  const wrapperRef = useRef<any>(null);
  const dispatch = useDispatch();
  const [setCustomInput] = useState<any>(null);
  const [isHoverPane, setHoverPane] = useState(false);
  const [isOffPaneNav, setOffPaneNav] = useState(false);
  const [state, setState] = useState<any>({
    valInput: router.query.keyword,
    isShowPopup: false,
    dataSearchSuggest: null,
    isFirstFocus: false,
    cursor: 0,
    currentCursor: -1,
    activeID: null,
    keyCode: null
  });
  const { valInput, isShowPopup, dataSearchSuggest } = state;
  const dataShowPopup = useMemo(
    () =>
      prepareData({
        dataSearchTrendKeyword,
        dataSearchSuggest,
        dataSearchHistory,
        valInput,
        router,
        userProfile,
        isMobile,
        searchItemAds
      }),
    [
      dataSearchTrendKeyword,
      dataSearchSuggest,
      dataSearchHistory,
      valInput,
      router,
      userProfile,
      isMobile,
      searchItemAds
    ]
  );

  useEffect(
    () =>
      // returned function will be called on component unmount
      function cleanup() {
        clearTimeout(timeOutRequestSuggest);
        clearTimeout(clickTimer);
      },
    []
  );

  useEffect(() => {
    let keyword = keywordFromPath({ router });
    if (router.pathname !== PAGE.SEARCH) {
      setState((prevState: any) => ({
        ...prevState,
        isShowPopup: false,
        valInput: ''
      }));
    } else if (keyword) {
      setState((prevState: any) => ({
        ...prevState,
        isShowPopup: true,
        valInput: keyword
      }));
    } else {
      if (isShowPopup && valInput === '') {
        keyword = '';
      }
      setState((prevState: any) => ({
        ...prevState,
        isShowPopup: false,
        valInput: keyword
      }));
    }
    // Bind the event listener
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchend', handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchend', handleClickOutside);
    };
  }, [wrapperRef, router.asPath, isSearchShow]);

  const handleClickOutside = (event: any) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
      const topSearch: any = document.getElementById(EL_ID.SEARCH_INPUT);
      if (!topSearch?.value) {
        handleToggleSearchPopup(false);
        if (!isMobile) setOffPaneNav(false);
      }
    } else {
      handleFocusSearchBox(false);
    }
  };

  const keywordFromPath = ({ router }: any) => {
    const { q } = router?.query || {};
    return q;
  };

  const handleToggleSearchPopup = (isShow?: any) => {
    const keyword = keywordFromPath({ router });
    setState((prevState: any) => ({
      ...prevState,
      isShowPopup: isShow !== undefined ? isShow : !isShowPopup,
      valInput: isShow ? keyword || '' : ''
    }));
  };

  const handleOnFocusInput = () => {
    if (!state.isFirstFocus) {
      onFirstFocus();
    }
    handleFocusSearchBox(true);
    handleToggleSearchPopup(true);
  };

  const goSearchPage = (e: any) => {
    if (e) e.preventDefault();
    if (!valInput || valInput === '') return;
    const href = PAGE.SEARCH;
    const queryParams = addParamToUrlVieON(router?.query, { q: valInput });
    router.push({ pathname: href, query: queryParams }, { pathname: href, query: queryParams });
  };

  const handleFocusSearchBox = (status: any) =>
    createTimeout(async () => {
      await dispatch(onFocusSearchBox(status));
    }, 500);

  const handleClickSearchItems = ({ item }: any) => {
    if (item) {
      const href = item?.href;
      router.push(href, item?.seo?.url);
    }
  };

  const handleOnInputSearch = (e: any) => {
    if (e.key === 'Enter') {
      const item = state.activeItem;
      if (item) {
        handleClickSearchItems({ item });
      } else goSearchPage(e);
    }
  };

  const handleLoadmoreItemsSearch = async ({ metadata }: any) => {
    const { total, page } = metadata || {};
    if (total) {
      const totalPage = Math.ceil(total / 10);
      const nextPage = page + 1;
      if (nextPage < totalPage) {
        await dispatch(getTrendKeywords({ page: nextPage, limit: 10, isGlobal }));
      }
    }
  };

  const handleKeyDown = (e: any) => {
    e.stopPropagation();
    e.persist();
    const dataSearch = dataSearchTrendKeyword || [];
    if (dataSearch === null) return;
    if (e.keyCode === 38 || e.keyCode === 40) {
      const itemsDataSearch = dataSearch?.items;
      if (itemsDataSearch) {
        let { currentCursor } = state;
        if (e.keyCode === 40) {
          currentCursor += 1;
        }
        if (e.keyCode === 38) currentCursor -= 1;
        if (currentCursor < -1) return;
        const activeCursorID = itemsDataSearch[currentCursor]?.id;
        if (activeCursorID || currentCursor === -1) {
          if (currentCursor + 1 >= itemsDataSearch?.length) {
            handleLoadmoreItemsSearch({
              metadata: dataSearchTrendKeyword?.metadata
            });
          }
          if (itemsRef.current[activeCursorID]) itemsRef.current[activeCursorID].scrollIntoView();
          setState((prevState: any) => ({
            ...prevState,
            activeID: activeCursorID,
            activeItem: itemsDataSearch[currentCursor],
            keyCode: e.keyCode,
            currentCursor
          }));
        }
      }
    }
  };

  const handleClickBtnDel = (e: any) => {
    handleToggleSearchPopup(false);
    openConfirmDel();
    e.preventDefault();
  };

  const handleOnChangeSearchInput = (e: any) => {
    setValSearchInput(e.target.value);
    if (e.target.value !== '') {
      setState((prevState: any) => ({
        ...prevState,
        currentCursor: -1
      }));
    } else {
      setState((prevState: any) => ({
        ...prevState,
        valInput: ''
      }));
    }
  };

  const setValSearchInput = (value: any) => {
    // set state valInput and keep value of another state
    if (clickTimer) clearTimeout(clickTimer);
    clickTimer = createTimeout(() => {
      if (value === '') {
        if (localRouter.includes('/tim-kiem/?q=')) {
          localRouter = '/';
        }
        router.push({ pathname: teamPathname }, { pathname: localRouter });
      } else {
        if (!router.asPath.includes('/tim-kiem')) {
          localRouter = router.asPath;
          teamPathname = router?.pathname;
        }
        const queryParams = addParamToUrlVieON(router?.query, { q: value });
        router.push(
          { pathname: PAGE.SEARCH, query: queryParams },
          { pathname: PAGE.SEARCH, query: queryParams }
        );
      }
    }, 1000);
    return setState((prevState: any) => ({
      ...prevState,
      valInput: value || ''
    }));
  };

  const checkBottomPosition = (el: any) => {
    if (!el) return;
    return el.scrollTop + el.getBoundingClientRect().height + 10 >= el.scrollHeight;
  };

  const onScrollingSearchSuggestion = (e: any) => {
    e.persist();
    if (scrollTimer) clearTimeout(scrollTimer);
    scrollTimer = createTimeout(() => {
      const isBottomEl = checkBottomPosition(e.target);
      if (isBottomEl) {
        handleLoadmoreItemsSearch({
          metadata: dataSearchTrendKeyword?.metadata
        });
      }
    }, 500);
  };

  const onClickIconSearch = () => {
    if (isEmpty(triggerConfig)) {
      dispatch(getTriggerConfig());
    }
    if (!isMobile) setOffPaneNav(true);
    handleToggleSearchPopup();
    segmentEvent(NAME.SEARCH_ICON_SELECTED, {
      [PROPERTY.CURRENT_PAGE]: window.location.href
    });
  };

  const handleIconSearch = ({ type }: any) => {
    if (type === CONTENT_TYPE.LIVE_TV) {
      return (
        <NewIcon iconName="vie-television-retro-o-rc" iCustomizeClass="text-[1.125rem]" isFadeIn />
      );
    }
    if (type === CONTENT_TYPE.LIVESTREAM) {
      return <NewIcon iconName="vie-stream-rc-medium" iCustomizeClass="text-[1.125rem]" isFadeIn />;
    }
    return <NewIcon iconName="vie-play-solid-rc" iCustomizeClass="text-[1.125rem]" isFadeIn />;
  };
  const onClickButtonClosed = () => {
    if (!isMobile) setOffPaneNav(false);
    setState(() => ({
      isShowPopup: false,
      valInput: ''
    }));
    if (router.asPath.includes('/tim-kiem/?q=')) {
      localRouter = '/';
      teamPathname = '/';
    }
    router.push({ pathname: localRouter }, { pathname: teamPathname }, { shallow: true });
  };

  const searchSuggestionClass = isShowPopup
    ? 'search-suggestion absolute is-open'
    : 'search-suggestion absolute';
  const className = isShowPopup
    ? `search-container absolute top-1 right animate-fade-right layer-1${
        valInput === '' ? '' : ' height-small-up-56rem'
      }`
    : 'search-container absolute top-1 right animate-fade-out hide';

  const handleMouseEnter = () => {
    setHoverPane(true);
  };
  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  return (
    <>
      {isShowPopup &&
        (isMobile ? (
          <nav className={`nav nav--right nav--for-mobile size-w-full ${className}`}>
            <div className="nav__wrap p-x">
              <div className="pane pane--user pane--user-notify pane--dark animate-fade-right">
                <div className="pane-container">
                  <div className="search search--top p-x" onSubmit={goSearchPage}>
                    <div
                      className="display-flex-inline align-middle size-w-full p-y1"
                      id="searchTop"
                    >
                      <Button
                        className="button button--for-dark button--large"
                        iconClass="!text-[1.125rem]"
                        iconName="vie-chevron-left-r-medium"
                        onClick={onClickButtonClosed}
                      />
                      <div className="cell auto p-l p-r2" onSubmit={goSearchPage}>
                        <FieldInputSearch
                          valInput={valInput}
                          handleOnChangeSearchInput={handleOnChangeSearchInput}
                          handleOnInputSearch={handleOnInputSearch}
                          handleKeyDown={handleKeyDown}
                          setCustomInput={setCustomInput}
                          handleOnFocusInput={handleOnFocusInput}
                          onClickButtonClosed={onClickButtonClosed}
                        />
                      </div>
                    </div>
                    <div className="pane__body scrollable-y over-scroll-contain overflow-x">
                      {dataShowPopup && valInput === '' && router.pathname !== PAGE.SEARCH && (
                        <SearchSuggestList
                          isNotShowAds={userProfile?.isPremium || isMobile || isKid}
                          inputEl={inputEl}
                          onScrollingSearchSuggestion={onScrollingSearchSuggestion}
                          dataShowPopup={dataShowPopup}
                          handleClickBtnDel={handleClickBtnDel}
                          itemsRef={itemsRef}
                          activeID={state.activeID}
                          handleClickSearchItems={handleClickSearchItems}
                          handleIconSearch={handleIconSearch}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </nav>
        ) : (
          <div className={className} id="searchTop">
            <div className="search search--top" onSubmit={goSearchPage} ref={wrapperRef}>
              <FieldInputSearch
                valInput={valInput}
                handleOnChangeSearchInput={handleOnChangeSearchInput}
                handleOnInputSearch={handleOnInputSearch}
                handleKeyDown={handleKeyDown}
                setCustomInput={setCustomInput}
                handleOnFocusInput={handleOnFocusInput}
                onClickButtonClosed={onClickButtonClosed}
              />
              {dataShowPopup && valInput === '' && router.pathname !== PAGE.SEARCH && (
                <SearchSuggestList
                  isNotShowAds={userProfile?.isPremium || isMobile || isKid}
                  className={searchSuggestionClass}
                  inputEl={inputEl}
                  onScrollingSearchSuggestion={onScrollingSearchSuggestion}
                  dataShowPopup={dataShowPopup}
                  handleClickBtnDel={handleClickBtnDel}
                  itemsRef={itemsRef}
                  activeID={state.activeID}
                  handleClickSearchItems={handleClickSearchItems}
                  handleIconSearch={handleIconSearch}
                />
              )}
            </div>
          </div>
        ))}
      <button
        className="button !px-0 flexible !text-[1.25rem] text-vo-gray-200 hover:text-vo-green"
        data-toggle="searchTop"
        aria-expanded="true"
        aria-controls="searchTop"
        aria-label="Tìm kiếm nội dung"
        onMouseEnter={!isMobile ? handleMouseEnter : undefined}
        onMouseLeave={!isMobile ? handleMouseLeave : undefined}
        onClick={onClickIconSearch}
        type="button"
      >
        <NewIcon
          iCustomizeClass="!text-[1.125rem] md:!text-[1.125rem]"
          iconName="vie-find-o-rc-medium"
          isFadeIn
        />
      </button>
      {!isMobile && isHoverPane && !isOffPaneNav && (
        <PaneNav content={TEXT.CONTENT_NAVIGATION.SEARCH} />
      )}
    </>
  );
};

const prepareData = ({
  dataSearchTrendKeyword,
  dataSearchSuggest,
  valInput,
  router,
  userProfile,
  isMobile,
  searchItemAds
}: any) => {
  if (router.pathname === PAGE.VOD) return;
  if (valInput && valInput !== '' && dataSearchSuggest !== null) {
    // kiem tra co du lieu suggest hay khong
    if (dataSearchSuggest && dataSearchSuggest.items && dataSearchSuggest.items.length > 0) {
      const newData = [];
      // xử lý lại data thêm key keyword cho đồng bộ
      for (let i = 0; i < dataSearchSuggest.items.length; i += 1) {
        newData[i] = {
          keyword: dataSearchSuggest.items[i],
          type: 'SUGGESTED'
        };
      }
      return {
        title: 'Gợi ý tìm kiếm',
        items: newData
      };
    }
  }
  if (dataSearchTrendKeyword) {
    let listItems = dataSearchTrendKeyword.items || [];
    if (!userProfile?.isPremium && !isMobile && !isEmpty(searchItemAds)) {
      const listAdsItemSorted = sortBy(searchItemAds, ['position']);
      listAdsItemSorted.map((item: any) => {
        const positionInArr = item.position - 1;
        listItems = [
          ...listItems.slice(0, positionInArr),
          { ...item, type: CONTENT_TYPE.ADS },
          ...listItems.slice(positionInArr)
        ];
      });
    }
    return {
      id: 'SEARCH_MOST',
      title: TEXT.MOST_SEARCHED,
      items: listItems
    };
  }
  return null;
};
export default SearchInput;
