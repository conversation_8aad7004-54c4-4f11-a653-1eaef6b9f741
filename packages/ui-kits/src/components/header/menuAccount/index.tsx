import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { TEXT } from '@vieon/core/constants/text';
import { POSITION } from '@vieon/core/constants/constants';
import NewIcon from '../components/basic/Icon/NewIcon';
import PaneNav from '../pane/PaneNav';

const MenuAccount = ({ openPageAuthLogin }: any) => {
  const [isHoverPane, setHoverPane] = useState(false);
  const { isMobile } = useSelector((state: any) => state?.App || {});

  const handleMouseEnter = () => {
    setHoverPane(true);
  };

  const handleMouseLeave = () => {
    setHoverPane(false);
  };

  const handleKeyDown = (event: any) => {
    if (event.key === 'Enter' || event.key === ' ') {
      openPageAuthLogin();
    }
  };

  return (
    <li
      className="menu__item size-h-full relative"
      onMouseEnter={!isMobile ? handleMouseEnter : undefined}
      onMouseLeave={!isMobile ? handleMouseLeave : undefined}
      onClick={openPageAuthLogin}
      onKeyDown={handleKeyDown}
    >
      <button
        className="avatar relative flexible !text-[1.625rem] text-vo-gray-200 hover:text-vo-green"
        aria-label="Nhấn để đăng nhập"
      >
        <NewIcon iconName="vie-user-o-c-medium" isFadeIn />
      </button>
      {isHoverPane && (
        <PaneNav position={POSITION.BOTTOM_RIGHT} content={TEXT.CONTENT_NAVIGATION.NON_ACCOUNT} />
      )}
    </li>
  );
};

export default React.memo(MenuAccount);
