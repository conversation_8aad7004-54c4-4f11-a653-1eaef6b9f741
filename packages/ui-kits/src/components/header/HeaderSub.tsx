import React, { useEffect, useRef, useState } from 'react';
import VieLink from '../components/VieLink';
import { useVieRouter } from '@customHook';
import { removeURLQueryParams } from '@vieon/core/utils/common';
import { PAGE } from '@vieon/core/constants/constants';
import Button from '../components/basic/Buttons/Button';

const useOnClickOutside = (ref: any, handler: any) => {
  useEffect(() => {
    const listener = (event: any) => {
      if (!ref.current || ref.current.contains(event.target)) {
        return;
      }
      handler(event);
    };
    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);
    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
};

const HeaderSub = ({ activeSubMenu, isPayment, onClickMenu, subHeader, isMobile }: any) => {
  const router = useVieRouter();
  const { asPath } = router;
  const ref = useRef<any>(null);
  const [subMenuOpen, setSubMenuOpen] = useState(false);

  useOnClickOutside(ref, () => setSubMenuOpen(false));

  const onClickItem = () => {
    setSubMenuOpen(false);
    onClickMenu();
  };

  const subMenuRibbonData = activeSubMenu?.subMenuRibbonData || [];
  const getActiveSub = (subMenuRibbon: any) => {
    let path = (asPath || '').split('?')?.[0] || '/';
    if ((asPath || '').indexOf('#') > -1) path = removeURLQueryParams(asPath, '#');
    return (subMenuRibbon || []).find((item: any) => item?.seo?.url === path);
  };
  const onClick = () => {
    setSubMenuOpen(!subMenuOpen);
  };
  const activeSub = getActiveSub(activeSubMenu?.subMenuRibbon);
  if (!subMenuRibbonData || subMenuRibbonData?.length === 0 || isPayment) return null;
  let openClass =
    'filter__pane filter__pane-outline filter__pane-horizontal animate-fade-in-down filter__pane-absolute';
  const headerSubStyle =
    'header-sub__label text-white text-small-up-20 text-medium-up-22 text-xlarge-up-40 margin-small-up-right-16 margin-large-up-right-24';
  if (subMenuOpen) openClass += ' is-open';
  return (
    <div className="header-sub">
      <div className="canal-v">
        <div className="grid-x align-middle">
          <div className="cell shrink">
            <label className={headerSubStyle}>{activeSubMenu?.name || ''}</label>
          </div>
          <div className="cell shrink">
            <div className="filter filter--gallery filter--dark-glass">
              <Button
                customizeClass="flex items-center justify-center !border !border-solid !border-vo-gray-200 hover:!border-vo-green h-9 px-3 bg-vo-dark-gray-900/50 !text-white hover:!text-vo-green space-x-3 transition-colors"
                iconNameSlotRight="vie-chevron-down-red-medium"
                title={activeSub?.name || subHeader?.name || ''}
                onClick={onClick}
                iconAlign="middle-right"
              />

              {subMenuOpen && (
                <div
                  className={openClass}
                  ref={ref}
                  data-alignment={`${isMobile ? 'bottom-right' : 'bottom-left'}`}
                  data-auto-focus="true"
                >
                  <div className="filter__pane__wrap scrollable-y over-scroll-contain">
                    {(subMenuRibbonData || []).map((sub: any, index: any) => (
                      <ul key={index} className="vertical menu menu--sort">
                        {/* {(sub || []).map((item, idx) => {
                          if (!isEmpty(item.name)) {
                            <li key={item.id}>
                              <VieLink
                                as={item?.seo?.url}
                                href={index === 0 && idx === 0 ? PAGE.HOME : PAGE.COLLECTION}
                              >
                                <a title={item?.name} onClick={onClickItem}>
                                  {item?.name}
                                </a>
                              </VieLink>
                            </li>;
                          }
                        })} */}
                        {(sub || []).map((item: any, idx: any) => (
                          <li key={idx}>
                            <VieLink
                              as={item?.seo?.url}
                              href={index === 0 && idx === 0 ? PAGE.HOME : PAGE.COLLECTION}
                            >
                              <a title={item?.name} onClick={onClickItem}>
                                {item?.name}
                              </a>
                            </VieLink>
                          </li>
                        ))}
                      </ul>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderSub;
