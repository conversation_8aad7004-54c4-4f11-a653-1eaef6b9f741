import React from 'react';
import DownloadApp from '../components/DownloadApp';
import { useOnClickOutside } from '@customHook';

const DownloadContent = ({
  onHandleClickButton,
  isMobile,
  onCloseContentItem,
  wrapperRef
}: any) => {
  if (!isMobile) useOnClickOutside(wrapperRef, onCloseContentItem);

  return (
    <div className="pane pane--user pane--user-download pane--dark absolute animate-fade-right right gold top-full">
      <div className="pane-container shadow">
        <div className="pane__body">
          <div className="block block--download-app">
            <DownloadApp onHandleClickButton={onHandleClickButton} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadContent;
