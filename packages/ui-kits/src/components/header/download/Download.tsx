import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import DownloadContent from '../components/header/download/DownloadContent';
import PaneNav from '../components/header/pane/PaneNav';
import { LINK_VIEON_DOWNLOAD_APP, PAGE, POSITION } from '@vieon/core/constants/constants';
import { useVieRouter } from '@customHook';
import { setStatusDownloadApp } from '@vieon/core/store/actions/app';
import TrackingMWebToApp from '@vieon/tracking/functions/TrackingMWebToApp';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import { TEXT } from '@vieon/core/constants/text';
import NewIcon from '../components/basic/Icon/NewIcon';
import InAppBenefit from './InAppBenefit';

const Download = ({ setStatusBenefitInApp, isOpenBenefit }: any) => {
  const router = useVieRouter();
  const timeOutLeaveRef = useRef<any>(null);
  const wrapperRef = useRef<any>(null);
  const [isOffPaneNav, setOffPaneNav] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isHoverPane, setHoverPane] = useState(false);
  const dispatch = useDispatch();
  const { pathname } = router;

  const {
    enableDialogOnboarding,
    enableTVodReminderScreen,
    enablePaymentConversion,
    isMobile,
    offDownloadApp,
    webConfig
  } = useSelector((state: any) => state?.App);

  const { featureFlag } = webConfig || {};
  const { mwebToApp } = featureFlag || {};
  const { inAppBenefit } = webConfig?.mwebToApp || {};

  const enableBenefit = useMemo(() => {
    if (enablePaymentConversion || enableTVodReminderScreen || enableDialogOnboarding) return false;
    return true;
  }, [enableDialogOnboarding, enableTVodReminderScreen, enablePaymentConversion]);

  const { enableInApp } = inAppBenefit || {};

  useEffect(
    () => () => {
      clearTimeout(timeOutLeaveRef.current);
    },
    []
  );

  useEffect(() => {
    if (router.asPath) setIsOpen(false);
  }, [router.asPath]);

  const onMouseEnter = () => {
    dispatch(setStatusDownloadApp(false));
    setHoverPane(true);
  };

  const onMouseLeave = () => {
    setHoverPane(false);
  };

  const handleCloseContentItem = () => {
    if (isOpen) {
      setOffPaneNav(false);
      setIsOpen(false);
    }
  };

  const handleDownloadApp = () => {
    if (timeOutLeaveRef.current) clearTimeout(timeOutLeaveRef.current);
    if (isMobile) {
      TrackingMWebToApp.inAppDownload({ flowName: VALUE.TRIGGER_BY_BENEFIT });
      timeOutLeaveRef.current = setTimeout(() => {
        window.open(LINK_VIEON_DOWNLOAD_APP);
      }, 200);
    } else {
      setIsOpen(!isOpen);
      setOffPaneNav(!isOffPaneNav);
    }
  };

  const handleClickButton = () => {
    dispatch(setStatusDownloadApp(true));
  };

  return (
    <li
      className="menu__item size-h-full"
      onMouseEnter={!isMobile ? onMouseEnter : undefined}
      onMouseLeave={!isMobile ? onMouseLeave : undefined}
      ref={wrapperRef}
    >
      <button
        className="button !px-0 relative flexible !text-[1.125rem] !text-vo-gray-200 hover:!text-vo-green"
        aria-label="Bấm để tải ứng dụng VieON"
        onClick={handleDownloadApp}
        type="button"
      >
        <NewIcon
          iconName="vie-mobile-download"
          iCustomizeClass="!text-[1.125rem] md:!text-[1.125rem]"
          isFadeIn
        />
      </button>
      {isMobile && mwebToApp && enableInApp && pathname === PAGE.HOME && enableBenefit && (
        <InAppBenefit isOpenBenefit={isOpenBenefit} setStatusBenefitInApp={setStatusBenefitInApp} />
      )}
      {!isMobile && isOpen && !offDownloadApp && (
        <DownloadContent
          onHandleClickButton={handleClickButton}
          wrapperRef={wrapperRef}
          isMobile={isMobile}
          onCloseContentItem={handleCloseContentItem}
        />
      )}
      {!isMobile && isHoverPane && !isOffPaneNav && (
        <PaneNav position={POSITION.BOTTOM_RIGHT} content={TEXT.CONTENT_NAVIGATION.DOWNLOAD} />
      )}
    </li>
  );
};

export default Download;
