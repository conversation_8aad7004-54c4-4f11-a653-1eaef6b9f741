import React, { useRef, useState } from 'react';
import { includes } from 'lodash';
import { useVieRouter } from '@customHook';
import Button from '../components/basic/Buttons/Button';
import SvgIcon from '../components/basic/Icon/SvgIcon';
import { ICON_KEY, LAYOUT_MENU, PAGE } from '@vieon/core/constants/constants';
import { createTimeout } from '@vieon/core/utils/common';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import cn from 'classnames';
import MenuMore from '../dropdown/MenuMore';
import SubMenu from './SubMenu';
import Styles from './Menu.module.scss';

const MenuItem = ({
  activeMenu,
  activeSubMenu,
  onClickMenu,
  isMobile,
  isMore,
  onToggle,
  data
}: any) => {
  const router = useVieRouter();
  const clickTimerRef = useRef<any>(null);
  const [isOpen, setOpen] = useState(!!isMobile);
  const [isHovering, setIsHovering] = useState(false);
  const {
    seo,
    name,
    href,
    iconText,
    subMenu,
    id,
    dotCode,
    layoutType,
    highlights,
    primary,
    urlRedirect
  } = data || {};

  let itemClass = `${Styles.MenuItem} flexible`;
  if (activeMenu?.id === id) itemClass += ` ${Styles.MenuItemActive}`;
  if (dotCode) itemClass += ' hot';

  if (isMobile) itemClass += ' height-small-up-36rem';
  const layoutMore = layoutType === LAYOUT_MENU.MORE;

  const menuPath = primary ? PAGE.HOME : seo?.url;

  const onClickMenuItem = (e: any) => {
    e.preventDefault();
    clearTimeout(clickTimerRef.current);
    clickTimerRef.current = createTimeout(() => {
      if (!layoutMore && !urlRedirect) {
        router.push({ pathname: href }, { pathname: menuPath });
      }
    }, 500);
    onClickMenu(data);
    TrackingApp.menuSelected(data);
  };

  const handleMouseEnter = () => {
    if (layoutMore && !isMobile) {
      setOpen(true);
    }
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    if (layoutMore && !isMobile) {
      setOpen(false);
    }
    setIsHovering(false);
  };

  const iconChevron = `vie-chevron-${isMore ? 'up-r' : 'down-red'}-medium`;

  return (
    <li
      role="presentation"
      className={`menu__item size-h-full ${layoutMore ? 'relative' : ''} ${
        isMobile ? 'relative hover-light-vertical' : 'hover-light'
      }`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        type="button"
        className={itemClass}
        title={name || ''}
        onClick={onClickMenuItem}
        id={id}
      >
        {includes(ICON_KEY, iconText) && (
          <span className={cn(Styles.MenuItemIcon)}>
            <SvgIcon type={iconText} isActive={activeMenu?.id === id} isHovering={isHovering} />
          </span>
        )}
        <span className={Styles.MenuItemText}>{name || ''}</span>
        {dotCode && <i className="dot" style={{ backgroundColor: dotCode }} />}
      </button>
      {subMenu && subMenu?.length > 1 && isMobile && (
        <Button
          className="button absolute right top-2 text-white size-h-auto"
          iconClass="icon--tiny"
          iconName={iconChevron}
          subTitle={isMore ? 'Less' : 'More'}
          onClick={onToggle}
        />
      )}
      {isOpen && layoutMore && (
        <MenuMore
          subMenu={subMenu}
          activeSubMenu={activeSubMenu}
          onClickMenu={onClickMenu}
          isMobile={isMobile}
          isMore={isMore}
        />
      )}
      {subMenu && subMenu?.length > 1 && (activeMenu?.id === id || isMobile) && (
        <SubMenu
          subMenu={subMenu}
          activeMenu={activeMenu}
          isMobile={isMobile}
          activeSubMenu={activeSubMenu}
          parentId={id}
          onClickMenu={onClickMenu}
          isMore={isMore}
          layoutType={layoutType}
          highlights={highlights}
        />
      )}
    </li>
  );
};

export default React.memo(MenuItem);
