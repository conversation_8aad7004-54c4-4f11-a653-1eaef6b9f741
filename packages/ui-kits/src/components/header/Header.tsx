import React, { memo, useEffect, useMemo, useState } from 'react';
import { useVieRouter } from '@customHook';
import SearchInputContainer from '@containers/Header/SearchInputContainer';
import NotificationContainer from '@containers/Header/NotificationContainer';
import { LAYOUT_MENU, PAGE, POPUP } from '@vieon/core/constants/constants';
import { encodeParamDestination, onOpenPayment } from '@vieon/core/utils/common';
import { useSelector } from 'react-redux';
import TrackingPayment from '@vieon/tracking/functions/payment';
import { VALUE } from '@vieon/core/config/ConfigSegment';
import isEmpty from 'lodash/isEmpty';
import { TEXT } from '@vieon/core/constants/text';
import Menu from './menu/Menu';
import Logo from '../basic/Logo/Logo';
import LogoMobile from '../basic/Logo/LogoMobile';
import MenuProfile from './menuProfile/MenuProfile';
import HeaderSub from './HeaderSub';
import Download from './download/Download';
import MenuCode from './menuCode';
import MenuVipPackage from './menuVipPackage';
import MenuAccount from './menuAccount';
import ConfigLocalStorage from '@/config/ConfigLocalStorage';
import LocalStorage from '@/config/LocalStorage';

const trackingPayment = new TrackingPayment();

const Header = React.forwardRef(
  (
    {
      menuList,
      profile,
      activeMenu,
      activeSubMenu,
      USER_TYPE,
      isPayment,
      isMobile,
      onClickMenu,
      subHeader,
      openPopup,
      introPackages,
      isCanNotWatch
    }: any,
    ref: any
  ) => {
    const router = useVieRouter();
    const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);
    const { isGlobal } = useSelector((state: any) => state?.App?.geoCheck);

    const [showMobileMenu, setShowMobileMenu] = useState(false);

    const isHideBuyPackage = useMemo(() => USER_TYPE?.hideButtonBuyPackage, [USER_TYPE]);
    const isDataSub = useMemo(() => !!activeSubMenu?.subMenuRibbon, [activeSubMenu]);
    const isSubMenu = useMemo(
      () =>
        (activeMenu?.layoutType !== LAYOUT_MENU.MORE ? activeMenu?.subMenu || [] : false).length >
        1,
      [activeMenu]
    );
    const isMasterBanner = useSelector((state: any) => state?.Page?.isMasterBanner);

    useEffect(() => {
      setShowMobileMenu(false);
    }, [router?.asPath]);

    const openPageAuthLogin = () => {
      localStorage.setItem('currentAuthFlow', 'profile_icon');
      const remakeDestination = encodeParamDestination(router?.asPath);
      router.push(`${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}`);

      // Flow trigger auth x mins
      const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
      const { url }: any = JSON.parse(reLoginParams || '{}');
      const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
      const queryAuth = Object.fromEntries(urlResultParams.entries());
      if (queryAuth?.isTriggerAuth) {
        ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
      }
    };

    const openPopupLogout = () => {
      openPopup({ name: POPUP.NAME.LOGOUT });
    };

    const onClickOpenPayment = () => {
      onOpenPayment(router, { curPage: VALUE.HOME_PAGE });
      trackingPayment.globalPaymentButtonSelected({ currentPage: VALUE.HOME_PAGE });
    };

    const handleOnClickLogoMobile = () => {
      setShowMobileMenu(!showMobileMenu);
    };

    return (
      <header
        className={`header fixed${
          isDataSub || isSubMenu || isMasterBanner ? ' header-gradient' : ''
        }`}
        data-sub={isDataSub}
        data-sub-menu={isSubMenu}
        style={isCanNotWatch ? { zIndex: 9998 } : {}}
        ref={ref}
        id="primary-header"
      >
        <div className="top-bar height-small-up-48rem height-large-up-56rem" id="mainNavigation">
          <div className="top-bar-left">
            {isMobile && <LogoMobile onClick={handleOnClickLogoMobile} />}
            <Logo className="logo logo--top" />
            <Menu
              menuLayout={isMobile ? 'vertical' : 'horizontal'}
              menuProperties={!isMobile ? 'relative' : ''}
              menuCustom="menu--main"
              menuList={menuList}
              showMobileMenu={showMobileMenu}
              activeMenu={activeMenu}
              isPayment={isPayment}
              onClickMenu={onClickMenu}
              activeSubMenu={activeSubMenu}
            />
          </div>
          <div className="top-bar-right align-middle" id="topBarRight">
            {!isKid && (
              <>
                {!isHideBuyPackage && (
                  <MenuVipPackage
                    onClickOpenPayment={onClickOpenPayment}
                    introPackages={introPackages}
                    buttonTitle={
                      isEmpty(profile)
                        ? `${TEXT.SUBSCRIBE_PACKAGE_GUESS}`
                        : profile?.isPremium
                        ? `${TEXT.SUBSCRIBE_PACKAGE}`
                        : `${TEXT.SUBSCRIBE_PACKAGE_MEMBER}`
                    }
                  />
                )}
                {!isGlobal && <MenuCode />}
              </>
            )}
            <ul className="menu menu--secondary horizontal logged relative size-h-full">
              <li className="menu__item relative size-h-full">
                <SearchInputContainer />
              </li>
              {!isKid && (
                <>
                  <Download />
                  {profile?.id && <NotificationContainer profile={profile} isPayment={isPayment} />}
                </>
              )}

              {!profile?.id ? (
                <MenuAccount openPageAuthLogin={openPageAuthLogin} />
              ) : (
                <MenuProfile openPopupLogout={openPopupLogout} />
              )}
            </ul>
          </div>
        </div>
        <HeaderSub
          activeSubMenu={activeSubMenu}
          subHeader={subHeader}
          isPayment={isPayment}
          onClickMenu={onClickMenu}
        />
      </header>
    );
  }
);

export default memo(Header);
