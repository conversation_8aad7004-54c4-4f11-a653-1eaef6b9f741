import { NAME, PROPERTY } from '@vieon/core/config/ConfigSegment';
import { USER_TYPE_ENUM } from '@vieon/core/constants/constants';
import { segmentEvent } from '../TrackingSegment';

const nearlyExpireBannerLoaded = ({ subscriptions }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.NEARLY_EXPIRE_BANNER_LOADED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};

const nearlyExpireBannerSelected = ({ subscriptions }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.NEARLY_EXPIRE_BANNER_SELECTED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};

const nearlyExpireFullscreenLoaded = ({ subscriptions }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.NEARLY_EXPIRE_FULLSCREEN_LOADED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const nearlyExpireFullscreenButtonSelected = ({ subscriptions, buttonStatus }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.NEARLY_EXPIRE_FULLSCREEN_BUTTON_SELECTED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || '',
    [PROPERTY.FULLSCREEN_PAGE_BUTTON_STATUS]: buttonStatus || ''
  });
};
const nearlyExpireFullscreenContentSelected = ({ subscriptions, cardData, profile }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.NEARLY_EXPIRE_FULLSCREEN_CONTENT_SELECTED, {
    [PROPERTY.CONTENT_ID]: cardData?.id,
    [PROPERTY.CONTENT_TITLE]: cardData?.title || '',
    [PROPERTY.CONTENT_TYPE]: cardData?.type,
    [PROPERTY.USER_ID]: profile?.id || '',
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const expiredBannerLoaded = ({ subscriptions }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.EXPIRE_BANNER_LOADED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const expiredBannerSelected = ({ subscriptions, buttonStatus }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.EXPIRE_BANNER_SELECTED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || '',
    [PROPERTY.BANNER_BUTTON_STATUS]: buttonStatus
  });
};
const expiredFullscreenLoaded = ({ subscriptions }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.EXPIRE_FULLSCREEN_LOADED, {
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const expiredButtonSelected = ({ subscriptions, buttonStatus }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.EXPIRE_BUTTON_SELECTED, {
    [PROPERTY.FULLSCREEN_PAGE_BUTTON_STATUS]: buttonStatus,
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const expiredFullscreenContentSelected = ({ subscriptions, cardData, profile }: any) => {
  if (!subscriptions) return;
  segmentEvent(NAME.PAYMENT_CONVERSION.EXPIRE_FULLSCREEN_CONTENT_SELECTED, {
    [PROPERTY.CONTENT_ID]: cardData?.id,
    [PROPERTY.CONTENT_TITLE]: cardData?.title || '',
    [PROPERTY.CONTENT_TYPE]: cardData?.type,
    [PROPERTY.USER_ID]: profile?.id || '',
    [PROPERTY.USER_TYPE]: subscriptions?.packageName || ''
  });
};
const svodTrialSubscribePackageButtonSelected = ({
  triggerFrom,
  userType,
  contentName,
  contentId
}: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.SVOD_TRIAL_SUBSCRIBE_PACKAGE_BUTTON_SELECTED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};
const svodTrialCancelButtonSelected = ({ triggerFrom, userType, contentName, contentId }: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.SVOD_TRIAL_CANCEL_BUTTON_SELECTED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};
const svodTrialSubscribePackageButtonLoaded = ({
  triggerFrom,
  userType,
  contentName,
  contentId
}: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.SVOD_TRIAL_SUBSCRIBE_PACKAGE_BUTTON_LOADED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};

const removeAdsSubscribePackageButtonSelected = ({ userType, contentName, contentId }: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.REMOVE_ADS_SUBSCRIBE_PACKAGE_BUTTON_SELECTED, {
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.TRIGGER_FROM]: 'remove_ads_inforbox'
  });
};
const qualitySubSubscribePackageButtonSelected = ({
  userType,
  triggerFrom,
  contentId,
  contentName
}: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.QUALITY_SUB_SUBSCRIBE_PACKAGE_BUTTON_SELECTED, {
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};
const exclusiveHotContentSubscribePackageButtonSelected = ({
  userType,
  triggerFrom,
  contentId,
  contentName
}: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.EXCLUSIVE_HOT_CONTENT_SUBSCRIBE_PACKAGE_BUTTON_SELECTED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};
const exclusiveHotContentSubscribePackageLoaded = ({
  userType,
  triggerFrom,
  contentId,
  contentName
}: any) => {
  segmentEvent(NAME.PAYMENT_CONVERSION.EXCLUSIVE_HOT_CONTENT_SUBSCRIBE_PACKAGE_LOADED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};
const firstMonthTrialOffer = () => {
  segmentEvent(NAME.PAYMENT_CONVERSION.FIRST_MONTH_TRIAL_OFFER, {
    [PROPERTY.USER_TYPE]: '',
    [PROPERTY.BANNER_POSITION]: ''
  });
};

const vipFeatureSubscribePackageLoaded = ({
  triggerFrom,
  userType,
  contentName,
  contentId
}: any) => {
  segmentEvent(NAME.SVOD.VIP_FEATURE_SUBSCRIBE_PACKAGE_LOADED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};

const vipFeatureSubscribePackageCancelButtonSelected = ({
  triggerFrom,
  userType,
  contentName,
  contentId
}: any) => {
  segmentEvent(NAME.SVOD.VIP_FEATURE_SUBSCRIBE_PACKAGE_CANCEL_BUTTON_SELECTED, {
    [PROPERTY.TRIGGER_FROM]: triggerFrom,
    [PROPERTY.USER_TYPE]: userType || USER_TYPE_ENUM.GUEST,
    [PROPERTY.CONTENT_ID]: contentId || '',
    [PROPERTY.CONTENT_NAME]: contentName || '',
    [PROPERTY.CAMPAIGN_ID]: null,
    [PROPERTY.CAMPAIGN_NAME]: null
  });
};

export {
  nearlyExpireBannerLoaded,
  nearlyExpireBannerSelected,
  nearlyExpireFullscreenLoaded,
  nearlyExpireFullscreenButtonSelected,
  nearlyExpireFullscreenContentSelected,
  expiredBannerLoaded,
  expiredBannerSelected,
  expiredFullscreenLoaded,
  expiredButtonSelected,
  expiredFullscreenContentSelected,
  svodTrialSubscribePackageButtonSelected,
  svodTrialCancelButtonSelected,
  svodTrialSubscribePackageButtonLoaded,
  removeAdsSubscribePackageButtonSelected,
  qualitySubSubscribePackageButtonSelected,
  exclusiveHotContentSubscribePackageButtonSelected,
  exclusiveHotContentSubscribePackageLoaded,
  firstMonthTrialOffer,
  vipFeatureSubscribePackageLoaded,
  vipFeatureSubscribePackageCancelButtonSelected
};
