import { splitArrayWithCount } from '../utils/common';
import { DOMAIN_WEB } from '../config/ConfigEnv';
import { CONTENT_TYPE, EL_ID, PERMISSION } from '../constants/constants';
import { convertToDateTime } from '../utils/utils';
import { calculatePlayingTime } from './playerServices';

export function parseEpisodesList(items: any, activeId: any, count: any) {
  if (!items || items?.length === 0) return [];
  return splitArrayWithCount({ data: items, count: count || 0 });
}

export function parseComments(comments: any) {
  let list = [];
  if (comments && comments.length > 0) {
    list = comments.map((comment: any) => ({
      id: comment?.id,
      contentId: comment?.content_id || comment?.product_id,
      message: comment?.message,
      pin: comment?.pin,
      createdAt: convertToDateTime(comment?.created_at * 1000, 'HH:mm DD/MM/YYYY'),
      userAvatar: comment?.user.avatar,
      userName: comment?.user.name,
      reply: comment?.reply
    }));
  }
  return list;
}

export function parseEpisodeRange(data: any) {
  if (!data || data.length === 0) return [];
  return data.map((item: any, index: any) => ({
    name: item,
    page: index,
    title: item
  }));
}

export function getSeoData(content: any, contentEpisode: any, isSeaSon?: any, seo?: any) {
  if (!content && !contentEpisode) return null;

  const data = contentEpisode || content;
  const type = isSeaSon
    ? content?.type === CONTENT_TYPE.SEASON
      ? 'TVSeries'
      : 'Movie'
    : 'Episode';
  const shareUrlSeo = data?.share_url_seo || data?.seo?.url;
  const name = seo?.title || data?.title;
  const description = seo?.meta_description || data?.shortDescription || '';
  const image = data?.images?.vod_thumb_big || data?.images?.thumbnail;
  const thumbnail = data?.images?.thumbnail;
  const episodeNumber = contentEpisode?.episode;
  const numberOfSeasons = content?.related_season?.length || 1;
  const runtime = contentEpisode?.runTime || content?.runTime || data?.runtime || '';

  const tags = content?.tags || [];

  const genre = (tags.filter((tag: any) => tag.type === 'genre') || []).map(
    (tag: any) => tag?.name
  );

  const avgRate = content?.avgRate || content?.avg_rate || 4;
  const totalRate = content?.totalRate || content?.total_rate || 40;
  const releaseYear = content?.releaseYear || contentEpisode?.releaseYear;

  const actors = (content?.people?.actor || []).map((act: any) => ({
    '@type': 'Person',
    name: act.name || ''
  }));
  const director = (content?.people?.director || []).map((act: any) => ({
    '@type': 'Person',
    name: act.name || ''
  }));
  const creator = (content?.people?.creator || []).map((act: any) => ({
    '@type': 'Person',
    name: act.name || ''
  }));
  const seoData: any = {
    '@context': 'http://schema.org',
    '@type': type,
    url: DOMAIN_WEB + shareUrlSeo,
    name,
    description,
    image,
    thumbnailUrl: thumbnail,
    genre,
    actor: actors,
    director,
    creator: creator || []
  };
  if (!isSeaSon) {
    seoData.timeRequired = `PT${runtime}M`;
  } else {
    seoData.contentRating = 'PG13';
    seoData.dateCreated = releaseYear || new Date().getFullYear();
    if (type !== 'Movie') {
      seoData.startDate = releaseYear || new Date().getFullYear();
    }
  }
  const aggregateRating = {
    '@type': 'AggregateRating',
    ratingValue: avgRate,
    reviewCount: totalRate
  };

  if (avgRate > 0 && totalRate > 0) {
    seoData.aggregateRating = aggregateRating;
  }

  const partOfSeries = {
    '@type': 'CreativeWorkSeries',
    name: content?.seo?.title,
    url: content?.share_url_seo
  };

  if (contentEpisode) {
    seoData.episodeNumber = episodeNumber;
    seoData.partOfSeries = partOfSeries;
  } else if (type !== 'Movie') {
    seoData.numberOfSeasons = numberOfSeasons;
  }

  return seoData;
}

function parseTimeFromVtt(time: any) {
  if (!time) return 0;

  let res = 0;
  const temp = (time.split(':') || []).reverse();
  temp.forEach((t: any, i: any) => {
    if (i === 0) res += parseFloat(t);
    if (i === 1) res += parseFloat(t) * 60;
    if (i === 2) res += parseFloat(t) * 3600;
  });
  return res;
}

function parseImagePosition(data: any, domain: any) {
  // let x;
  // let y;
  // let w;
  // let h;
  // let thumbSrc;
  const temp1 = data.split('#');
  const temp2 = temp1 && temp1[1] ? temp1[1].split('=') : [];
  const temp3 = temp2 && temp2[1] ? temp2[1].split(',') : [];
  const x = temp3 ? parseInt(temp3[0], 10) : 0;
  const y = temp3 ? parseInt(temp3[1], 10) : 0;
  const w = temp3 ? parseInt(temp3[2], 10) : 0;
  const h = temp3 ? parseInt(temp3[3], 10) : 0;
  const thumbSrc = domain + temp1?.[0];
  return { x, y, w, h, thumbSrc };
}

export function parseVttItem(data: any, i: any, domain: any) {
  const item1 = data[i];
  const item2 = data[i + 1];
  const item3 = data[i + 2];
  const index = parseInt(item1, 10);
  const time = item2.split(' --> ') || [];
  const start = parseTimeFromVtt(time[0]);
  const end = parseTimeFromVtt(time[1]);
  const { x, y, w, h, thumbSrc } = parseImagePosition(item3, domain);
  return { index, start, end, x, y, w, h, thumbSrc };
}

export function handleGetSlugEpsFromPath({ path }: any) {
  const fullPath = (path || '').split('.').shift().replace('/', '');
  const data: any = {};
  if (fullPath.indexOf('--eps') > -1) {
    // const strSplit = fullPath.split('--eps-');
    // data.slugFromPath = strSplit[0];
    // data.episodeFromPath = strSplit[1];
    const [slugFromPath, episodeFromPath] = fullPath.split('--eps-');
    data.slugFromPath = slugFromPath;
    data.episodeFromPath = episodeFromPath;
  } else if (fullPath.indexOf('--rel-') > -1) {
    // const strSplit = fullPath.split('--rel-');
    // data.slugFromPath = strSplit[0];
    // data.relatedFromPath = strSplit[1];
    const [slugFromPath, relatedFromPath] = fullPath.split('--rel-');
    data.slugFromPath = slugFromPath;
    data.relatedFromPath = relatedFromPath;
  } else {
    data.slugFromPath = fullPath;
  }
  return { ...data };
}

export function handleGetInAppFromPath({ pathname }: any) {
  const fullPath = (pathname || '').replace('/in-app/', '').split('/');
  return fullPath[0];
}

export function getParamsForSegment(props: any) {
  const {
    content,
    currentEpisode,
    contentDetail,
    adTagUrl,
    progress,
    bitrate,
    quality,
    sessionId,
    totalPlayedData
  } = props || {};

  const video: any = document.getElementById(EL_ID.VIE_PLAYER);
  const midAds = (contentDetail?.ads || []).find((ad: any) => ad.type === 'mid');
  const repeat = midAds?.repeat || 0;
  const currentTime = video?.currentTime || 0;
  let partIndexTemp = 1;
  if (currentTime && repeat) {
    partIndexTemp = Math.round(currentTime / (repeat * 60));
  }
  const categoryText = content?.categoryText || '';
  const genreText = content?.genreText || '';
  const duration = video?.duration || 0;
  const contentId = contentDetail?.id;
  const permission = contentDetail?.permission;
  const trialDuration = contentDetail?.trialDuration;
  const triggerLoginDuration = contentDetail?.triggerLoginDuration;
  const playTrial =
    permission !== PERMISSION.CAN_WATCH && (trialDuration > 0 || triggerLoginDuration > 0);
  const isEnd = currentEpisode ? currentEpisode?.is_end : true;
  const contentTitle = currentEpisode?.title || content?.title;
  const seasonName = content?.title;
  const contentType = currentEpisode?.type || content?.type;
  const episodeName = currentEpisode?.title || '';
  const contentName = seasonName + (episodeName ? ` - ${episodeName}` : '');
  const partLength = repeat * 60;
  const partIndex = partIndexTemp;
  const hasAds = contentDetail?.ads?.length > 0;
  const videoPlayType = contentDetail?.videoPlayType;
  const playedDuration = calculatePlayingTime({ totalPlayedData, video });
  const shortDescription = currentEpisode?.shortDescription || '';
  const thumbnail = content?.images?.thumbnail || '';
  return {
    sessionId,
    playedDuration,
    genreText,
    categoryText,
    contentId,
    playTrial,
    contentTitle,
    seasonName,
    contentType,
    partLength,
    duration,
    partIndex,
    hasAds,
    videoPlayType,
    adTagUrl,
    progress,
    bitrate,
    quality,
    isEnd,
    currentTime,
    episodeName,
    contentName,
    shortDescription,
    thumbnail
  };
}
