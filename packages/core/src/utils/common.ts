import Moment from 'moment';
import Bowser from 'bowser';
import get from 'lodash/get';
import keys from 'lodash/keys';
import CryptoJS from 'crypto-js';
import isEmpty from 'lodash/isEmpty';
import {
  getSelectorsByUserAgent,
  isChrome,
  isEdge,
  isFirefox,
  isIE,
  isIOS,
  isOpera,
  isSafari
} from 'react-device-detect';
import { setToast, setToken } from '../store/actions/app';
import { openPopup } from '../store/actions/popup';
import { getProfile } from '../store/actions/profile';
import {
  ACTION_TYPE_NOTIFY,
  BANNER_IMAGE_RATE,
  BROWSER,
  CONTENT_TYPE,
  DOMAIN_API_SSR,
  EL_ID,
  KEY_TIME_NOTIFY,
  LOGIN_TYPE,
  MATCH_STATUS,
  MKT_PARAMS,
  PAGE,
  PAYMENT,
  REGEX,
  RESOLUTION,
  RIBBON_TYPE,
  SEASON_CATEGORY,
  USER_TYPE
} from '../constants/constants';
import { PLAYER_NAME, PLAYER_TYPE } from '../constants/player';
import { TEXT } from '../constants/text';
import {
  DOMAIN_LOCAL_BACKEND_USER,
  DOMAIN_LOCAL_SERVICE_USER_REPORT,
  DOMAIN_LOCAL_VIEON_CM_ACTIVITY,
  DOMAIN_LOCAL_VIEON_CM_V5,
  LINK_DOWNLOAD_APP_URL,
  SECRET_KEY
} from '../config/ConfigEnv';
import LocalStorage from '../config/LocalStorage';
import { NAME, PROPERTY, VALUE } from '../config/ConfigSegment';
import ConfigSocket from '../config/ConfigSocket';
import ConfigImage from '../config/ConfigImage';
import ConfigPayment from '../config/ConfigPayment';
import ConfigCookie from '../config/ConfigCookie';
import { segmentEvent } from '@vieon/tracking/TrackingSegment';
import RibbonItem from '@vieon/models/ribbonItem';
import { UtmParams } from '@vieon/models/subModels';
import ConfigLocalStorage from '../config/ConfigLocalStorage';
import Fingerprint2 from 'fingerprintjs2';
import { TYPE_TRIGGER_AUTH } from '../constants/types';
import { getMultiProfile } from '@/actions/multiProfile';

declare const window: any;
declare const document: any;

export const formatPhoneNumberVN = (phone: any) => {
  if (phone) {
    if (phone[0] === '0') {
      return phone.slice(1, phone.length);
    }
    if (phone[0] === '+') {
      return phone.slice(3, phone.length);
    }
    return phone;
  }
  return '';
};

export const handleToastWhenAccessVieON = async ({
  isLogin,
  isRegister,
  dispatch,
  destination,
  isFirstLogin,
  bindAccount,
  accessToken
}: any) => {
  let message = '';
  const desDecode = decodeParamDestination(destination || '');
  const parseQuery: any = parseQueryString(destination);
  const reLoginParams: any = ConfigLocalStorage.get(LocalStorage.RE_LOGIN_PARAMS);
  const { url } = JSON.parse(reLoginParams || '{}');
  const urlResultParams = new URLSearchParams(url?.slice(url.indexOf('?')));
  const queryAuth = Object.fromEntries(urlResultParams.entries());

  const resMultiProfiles = await dispatch(getMultiProfile({ accessToken }));
  const profiles = resMultiProfiles?.data?.items || [];
  if (parseQuery?.isTSvod === 'true') {
    message = TEXT.RELOGIN_WELCOME;
  } else {
    if (desDecode.includes(PAGE.VOUCHER)) {
      message = TEXT.LOGIN_VOUCHER;
    } else if (queryAuth?.isTriggerAuth && !bindAccount && profiles?.length !== 1) {
      message = TEXT.TRIGGER_AUTH_LOGIN_SUCCESS;
    } else if (
      desDecode.includes(PAGE.PAYMENT_METHOD) ||
      desDecode.includes(PAGE.RENTAL_CONTENT) ||
      desDecode.includes(PAGE.ZALOPAY_METHOD)
    ) {
      message = TEXT.PLEASE_CONTINUE_CHECKOUT;
      if (isLogin) {
        message = TEXT.LOGIN_CHECKOUT;
      } else if (isRegister) {
        message = TEXT.REGISTER_CHECKOUT;
      }
    }
  }

  if (message) {
    dispatch(setToast({ message }));
  } else {
    if (profiles?.length !== 1 || !queryAuth?.isTriggerAuth) {
      const loginType = isFirstLogin ? LOGIN_TYPE.FIRST_LOGIN : LOGIN_TYPE.RE_LOGIN;
      ConfigLocalStorage.set(LocalStorage.LOGIN_TYPE, loginType);
      if (isFirstLogin) {
        ConfigLocalStorage.set(LocalStorage.TRIGGER_AFTER_LOGIN, LOGIN_TYPE.FIRST_LOGIN);
      }
    }
  }
};

export const parseTimeSecondToMinutes = (time: any) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  let strMinutes: any = minutes;
  let strSeconds: any = seconds;
  if (seconds < 10) strSeconds = `0${seconds}`;
  if (minutes > 0 && minutes < 10) {
    strMinutes = `0${minutes}`;
  }

  if (minutes > 0) return `${strMinutes}:${strSeconds}`;

  return `00:${strSeconds}`;
};

export const loadScript = (src: any) =>
  new Promise<void>((resolve, reject) => {
    if (document.querySelector(`script[src="${src}"]`)) return resolve();
    const script = document.createElement('script');
    script.src = src;
    script.onload = () => resolve();
    script.onerror = (err: any) => reject(err);
    document.body.appendChild(script);
  });

export const encodeParamDestination = (str: any) => (str || '/').replace(/&/g, '-@-');

export const decodeParamDestination = (str: any) => (str || '').replace(/-@-/g, '&');

export const handleAccessToVieON = async ({
  isLogin,
  isRegister,
  dispatch,
  pathname,
  accessToken,
  isInputPromotion,
  deviceModel,
  deviceName,
  deviceType,
  destination
}: any) => {
  let notReload = false;
  let message = '';
  switch (pathname) {
    case PAGE.VOUCHER: {
      message = TEXT.LOGIN_VOUCHER;
      notReload = true;
      break;
    }
    case PAGE.PAYMENT_METHOD:
    case PAGE.RENTAL_CONTENT:
    case PAGE.ZALOPAY_METHOD:
      message = TEXT.PLEASE_CONTINUE_CHECKOUT;
      if (isLogin) {
        const parseQuery: any = parseQueryString(destination);
        if (parseQuery?.isTSvod === 'true') {
          message = TEXT.RELOGIN_WELCOME;
        } else {
          message = TEXT.LOGIN_CHECKOUT;
          if (isInputPromotion) {
            message = TEXT.LOGIN_INPUT_PROMOTION_CODE;
          }
        }
      } else if (isRegister) {
        message = TEXT.REGISTER_CHECKOUT;
        if (isInputPromotion) {
          message = TEXT.REGISTER_INPUT_PROMOTION_CODE;
        }
      }
      notReload = true;
      break;
    default:
      break;
  }
  saveAccessToken(accessToken);
  if (message) dispatch(setToast({ message }));
  dispatch(openPopup());
  await dispatch(
    getProfile({
      accessToken,
      deviceModel,
      deviceName,
      deviceType
    })
  );
  dispatch(setToken(accessToken));

  return {
    notReload,
    isShowedMessage: !!message,
    showLobbyImmediately:
      !pathname.includes(PAGE.ZALOPAY) &&
      !pathname.includes(PAGE.PAYMENT) &&
      !pathname.includes(PAGE.VOUCHER)
  };
};

export const formatQueryParams = (urlOrAs: any, query: any) => {
  if (isEmpty(urlOrAs)) return;
  if (isEmpty(query)) return urlOrAs;
  let utmQuery = {};
  const listKeys = keys(query);
  listKeys.forEach((key) => {
    if (key.includes('utm') || MKT_PARAMS.includes(key)) {
      utmQuery = {
        ...utmQuery,
        [key]: get(query, key, '')
      };
    }
  });
  if (isEmpty(utmQuery)) return urlOrAs;

  if (typeof urlOrAs === 'object') {
    return {
      ...urlOrAs,
      query: { ...urlOrAs?.query, ...utmQuery }
    };
  }
  if (typeof urlOrAs === 'string') {
    const splitUrl = urlOrAs.split('?');
    if (!splitUrl?.[1]) {
      return {
        pathname: urlOrAs,
        query: utmQuery
      };
    }
    const urlResultParams = new URLSearchParams(splitUrl[1]);
    const queryParams = Object.fromEntries(urlResultParams.entries());
    return {
      pathname: splitUrl[0],
      query: { ...queryParams, ...utmQuery }
    };
  }
  return urlOrAs;
};

export const isToday = (someDate: any) => {
  const today = new Date();
  return (
    someDate.getDate() === today.getDate() &&
    someDate.getMonth() === today.getMonth() &&
    someDate.getFullYear() === today.getFullYear()
  );
};

export const parseJwt = (token: any) => {
  const base64Url = (token || '').split('.')[1];
  if (!base64Url) return null;
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
};

export const getCookie = (cname: any, fullCookies: any) => {
  const name = `${cname}=`;
  const decodedCookie = decodeURIComponent(fullCookies);
  const ca = decodedCookie.split(';');
  for (let i = 0; i < ca.length; i += 1) {
    let c: any = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
};
export const isAPartElmInView = (elm: any) => {
  if (typeof window !== 'undefined' && elm) {
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
    const elmSize = elm.getBoundingClientRect();
    return elmSize.top <= viewportHeight;
  }
  return;
};

export const mobileCheck = () => {
  if (typeof window === 'undefined') return false;
  let check = false;
  (function mobileCheckFunction(a) {
    if (
      /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(
        a
      ) ||
      /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55|capi|ccwa|cdm|cell|chtm|cldc|cmd|comp|craw|dait|dll|ng|dbte|dc|devi|dica|dmob|doc|po|ds(12|d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i.test(
        a.substr(0, 4)
      )
    ) {
      check = true;
    }
  })(navigator.userAgent || navigator.vendor || window.opera);
  return check;
};

export const trans = (word: any) => {
  switch (word.toLowerCase()) {
    case 'fail':
      return 'Không thành công';
    case 'success':
      return 'Thành công';
    case 'processing':
      return 'Đang xử lý';
    default:
      return word;
  }
};

export const checkIsEndPage = (metadata: any) => {
  metadata = metadata || {};
  const { page, limit, total } = metadata;
  if (Number.isInteger(total) && Number.isInteger(page) && Number.isInteger(limit)) {
    if ((page + 1) * limit >= total) {
      return true;
    }
  }
  return false;
};

export const getPathFromUrl: any = (url: any) => (url || '').split(/[?#]/)[0].trim();

export const checkPosition = (position: any) => {
  let isShow = false;
  let classPosition = 'tags-group horizontal shrink';
  switch (position) {
    case ConfigSocket.POSITION_LIVE_STREAM.TOP_LEFT:
      break;
    case ConfigSocket.POSITION_LIVE_STREAM.TOP_RIGHT:
      classPosition += ' m-l-auto';
      break;
    case ConfigSocket.POSITION_LIVE_STREAM.BOTTOM_LEFT:
      isShow = true;
      break;
    case ConfigSocket.POSITION_LIVE_STREAM.BOTTOM_RIGHT:
      isShow = true;
      classPosition += ' m-l-auto';
      break;
    default:
      break;
  }
  return {
    classPosition,
    isShow
  };
};
export const computeCompareTime = (data: any, active: any) => {
  let compareTimeArrays: any = {};
  if (isEmpty(data)) return;
  const addToCompareTimeArrays = (item: any, key: any) => {
    compareTimeArrays = {
      ...compareTimeArrays,

      [key]: [...(compareTimeArrays[key] || []), item]
    };
  };
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      if (data[key]?.length !== 0) {
        data[key].forEach((noti: any) => {
          if ((active && noti.isRead) || noti.isNotiTypeAnnounce) return;
          const notificationDate = new Date(noti.createdAt * 1000);
          const currentDate = new Date();

          const notificationDay = notificationDate.getDate();
          const notificationMonth = notificationDate.getMonth();
          const notificationYear = notificationDate.getFullYear();

          const currentDay = currentDate.getDate();
          const currentMonth = currentDate.getMonth();
          const currentYear = currentDate.getFullYear();
          if (
            notificationDay === currentDay &&
            notificationMonth === currentMonth &&
            notificationYear === currentYear
          ) {
            addToCompareTimeArrays(noti, KEY_TIME_NOTIFY.TO_DAY);
          } else if (
            notificationDay === currentDay - 1 &&
            notificationMonth === currentMonth &&
            notificationYear === currentYear
          ) {
            addToCompareTimeArrays(noti, KEY_TIME_NOTIFY.YESTERDAY);
          } else {
            addToCompareTimeArrays(noti, KEY_TIME_NOTIFY.OLD_DAYS);
          }
        });
      }
    }
  }
  return compareTimeArrays;
};

export const getLiveTime = (timestamp: any, isNotify?: any) => {
  if (!timestamp) return '';
  if (timestamp.toString().length > 10) {
    timestamp = Math.floor(timestamp / 1000);
  }
  const liveTime = new Date(timestamp * 1000);
  const day = `0${liveTime.getDate()}`.slice(-2);
  const month = `0${liveTime.getMonth() + 1}`.slice(-2);
  const year = liveTime.getFullYear();

  const compareTime = new Date().getTime() / 1000 - timestamp;
  const compareMonths = Math.floor(compareTime / 86400 / 30);
  const compareDates = Math.floor(compareTime / 86400);
  const compareHours = Math.floor(compareTime / 3600);
  const compareMinutes = Math.floor(compareTime / 60);
  const compareSeconds = Math.floor(compareTime);

  let strTimeLive = 'Vừa xong';
  if (compareMonths > 0) {
    strTimeLive = `${compareMonths} tháng trước`;
  } else if (compareDates > 0) {
    if (isNotify) {
      if (compareTime / 3600 > 48) strTimeLive = `${day}/${month}/${year}`;
      else strTimeLive = `${compareHours} giờ trước`;
    } else strTimeLive = `${compareDates} ngày trước`;
  } else if (compareHours > 0) {
    strTimeLive = `${compareHours} giờ trước`;
  } else if (compareMinutes > 0) {
    strTimeLive = `${compareMinutes} phút trước`;
  } else if (compareSeconds > 0) {
    // strTimeLive = `${compareSeconds} giây trước`
    if (isNotify && compareSeconds < 60) strTimeLive = `Ngay bây giờ`;
    else strTimeLive = `1 phút trước`;
  }

  return strTimeLive;
};

export const buildSlugSeoContentNew = ({ slug, episode, slugVideo }: any) => {
  if (!slug) {
    return null;
  }

  let path = slug;
  episode = (episode || '').trim().replace(/ /g, '-');
  slugVideo = (slugVideo || '').trim().replace(/ /g, '-');
  if (episode && episode !== '') {
    path += `--eps-${episode}`;
  } else if (slugVideo) {
    path += `--rel-${slugVideo}`;
  }

  return path.replace(/\/+/g, '/');
};

export const json_to_query_string = (json: any) => {
  if (!json) {
    return '';
  }
  return Object.keys(json)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`)
    .join('&');
};

export const handleGetDeviceId = () =>
  new Promise((resolve) => {
    Fingerprint2.get(async (components: any) => {
      const values = components.map((component: any) => component.value);
      const murmur = Fingerprint2.x64hash128(values.join(''), 31);
      resolve(murmur);
    });
  });
export const getDeviceId = () => {
  if (typeof window === 'undefined') return '';
  const store = window.__NEXT_REDUX_STORE__;
  const state = store.getState();
  return state?.App?.deviceId;
};

export const getValueFromStore = (path: any) => {
  if (typeof window === 'undefined') return '';
  const store = window.__NEXT_REDUX_STORE__;
  const state = store.getState();
  return get(state, path);
};

export const getModelDevice = (userAgent?: any) => {
  let userAgentName = userAgent || '';
  if (!userAgentName && typeof window !== 'undefined') {
    userAgentName = window.navigator.userAgent || '';
  }

  if (!userAgentName) return 'Unknown';
  const { osName, osVersion } = getSelectorsByUserAgent(userAgentName);
  if (!osName && !osVersion) return 'Unknown';
  return `${osName} ${osVersion}`;
};

export const getRandomInt = (max: any) => Math.floor(Math.random() * Math.floor(max));

export const parsedTimeLive = (startTime: any, endTime: any) => {
  if (!startTime || !endTime) return '';
  let start = new Date(startTime * 1000);
  let end = new Date(endTime * 1000);
  return `${Moment(start).format('H:mm')} - ${Moment(end).format('H:mm')}`;
};

export const parseRemainText = (progress: any, runtimeSecond: any) => {
  let progressPercent = progress;
  let remainTime = runtimeSecond;
  let remainText = '';
  if (runtimeSecond > 0 && runtimeSecond > progress && progress > -1) {
    progressPercent = ((parseInt(progress) / parseInt(runtimeSecond)) * 100).toFixed(2);
    remainTime = runtimeSecond - progress;

    let hours = remainTime / 3600;
    let min = remainTime / 60;

    if (hours >= 1) {
      min = Math.ceil((remainTime % 3600) / 60);
      if (min >= 1) remainText += `${Math.floor(hours)}g ${min}ph`;
      if (min === 0) remainText += `${Math.floor(hours)}g`;
    } else if (min >= 1) {
      remainText = `${Math.ceil(min)}ph`;
    } else if (runtimeSecond >= 1) {
      remainText = `${Math.ceil(runtimeSecond)}giây`;
    }
  }
  if (progress === 0) progressPercent = 0;
  if (progressPercent > 99) progressPercent = 99;

  return {
    progressPercent,
    remainText
  };
};

export const checkTimeLive = (data: any) => {
  const nowTime = Math.floor(new Date().getTime() / 1000);
  const checkTimeLive = data - nowTime <= 0;

  return checkTimeLive;
};
export const getSLugTVodFromRouter = (url: any) => {
  const parseSplitted = url.split('--live-');
  const query = { slug: parseSplitted[0] };
  return query;
};

export const parseUrlString = (str: any, key?: any, value?: any, isCapitalizeFirstLetter?: any) => {
  let newStr = str;
  const myKey = `{${key}}`;
  if (str && key) {
    newStr = newStr.replace(
      isCapitalizeFirstLetter ? capitalizeFirstLetter(myKey || '') : myKey,
      value || ''
    );
  }
  return newStr;
};

export const onShareSocial = ({ link, name, callback, callbackSuccess }: any) => {
  // shootGa('vod_detail', 'click_share', 'share_' + name, false)
  if (name === 'facebook' && typeof window.FB !== 'undefined') {
    window.FB.ui(
      {
        method: 'share',
        href: link
      },
      // callback
      (response: any) => {
        if (typeof callback === 'function') callback(response);
        if (response && !response.error_message) {
          if (typeof callbackSuccess === 'function') callbackSuccess(response);
        } else {
          console.log('Error while posting.');
        }
      }
    );
  }
};

export const getLabelMethodPayment = (key: any) => {
  let label = '';
  switch (key) {
    case PAYMENT.METHOD_KEY.ATM:
      label = 'Thẻ ATM nội địa';
      break;
    case PAYMENT.METHOD_KEY.EWALLET:
      label = 'E_WALLET';
      break;
    case PAYMENT.METHOD_KEY.INTERNATIONAL_CARD:
      label = 'Thẻ quốc tế Visa, Master, JCB, UnionPay';
      break;
    case PAYMENT.METHOD_KEY.INTERNET_BANKING:
      label = 'Internet Banking';
      break;
    case PAYMENT.METHOD_KEY.VNPAYQR:
      label = 'VNPay QR';
      break;
    case PAYMENT.METHOD_KEY.VNPAY:
      label = 'VNPAY';
      break;
    case PAYMENT.METHOD_KEY.MOMO:
      label = 'Ví MoMo';
      break;
    default:
      label = 'Khác';
      break;
  }
  return label;
};

export const encodeSignature = ({ accessToken, profileToken }: any) => {
  const secret = SECRET_KEY;
  let encodeValue = '';
  if (accessToken && profileToken) {
    encodeValue = accessToken + ConfigCookie.KEY.ACCESS_TOKEN_PROFILE + profileToken;
  } else if (accessToken) {
    encodeValue = accessToken;
  } else if (profileToken) {
    const { accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
    if (accessToken) {
      encodeValue = accessToken + ConfigCookie.KEY.ACCESS_TOKEN_PROFILE + profileToken;
    }
  }
  return CryptoJS.AES.encrypt(encodeValue, secret).toString();
};

export const decodeSignature = ({ value }: any) => {
  if (!value) return;
  const secret = SECRET_KEY;
  const keyProfileToken = ConfigCookie.KEY.ACCESS_TOKEN_PROFILE;
  const valueDecode = CryptoJS.AES.decrypt(value.toString(), secret).toString(CryptoJS.enc.Utf8);
  const indexKey = valueDecode.indexOf(keyProfileToken);
  return {
    accessToken: indexKey > 0 ? valueDecode.slice(0, indexKey) : indexKey === 0 ? '' : valueDecode,
    profileToken: indexKey >= 0 && valueDecode.slice(indexKey + keyProfileToken.length)
  };
};

export const saveAccessTokenProfile = (profileToken: any) => {
  // time expire token
  const timeNow = new Date();
  timeNow.setFullYear(timeNow.getFullYear() + 1);
  // save access token
  const valueEncode = encodeSignature({ profileToken });
  ConfigCookie.save(ConfigCookie.KEY.SIGNATURE, valueEncode);
};

export const removeAccessTokenProfile = () => {
  const { profileToken, accessToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};
  if (profileToken) {
    const valueEncode = encodeSignature({ accessToken });
    ConfigCookie.save(ConfigCookie.KEY.SIGNATURE, valueEncode); // remove token profile
  }
};

export const saveAnonymousToken = (accessToken: any) => {
  ConfigCookie.save(ConfigCookie.KEY.ANONYMOUS_TOKEN, accessToken);
  const dataToken = parseJwt(accessToken);
  ConfigLocalStorage.set(LocalStorage.ANONYMOUS_ID, dataToken?.sub || '');
};

export const saveAccessToken = (accessToken: any, profileToken?: any, refreshToken?: any) => {
  // time expire token
  const timeNow = new Date();
  timeNow.setFullYear(timeNow.getFullYear() + 1);
  // save access token
  const valueEncode = encodeSignature({
    accessToken,
    profileToken
  });
  ConfigCookie.save(ConfigCookie.KEY.SIGNATURE, valueEncode);
  ConfigCookie.remove(ConfigCookie.KEY.ANONYMOUS_TOKEN);
  ConfigLocalStorage.set(LocalStorage.RE_LOGIN, true);
  ConfigLocalStorage.set(LocalStorage.RE_LOGIN_BANNER_VIP, true);
  if (refreshToken) {
    ConfigCookie.save(ConfigCookie.KEY.REFRESH_TOKEN, refreshToken);
  }
};

export const removeAccessToken = () => {
  // time expire token
  ConfigCookie.remove(ConfigCookie.KEY.SIGNATURE);
  ConfigCookie.remove(ConfigCookie.KEY.ANONYMOUS_TOKEN);
  ConfigCookie.remove(ConfigCookie.KEY.REFRESH_TOKEN);
};

export const setUrlParams = (url: any, params: any) => {
  if (!url || !params) return url;
  // let newUrl = new URL(url);

  const newUrl = new URL(url);
  const keys = Object.keys(params);

  // params &&
  //   Object.keys(params) &&
  //   Object.keys(params).forEach((key) => {
  //     if (params[key]) {
  //       newUrl.searchParams.set(key, params[key]);
  //     }
  //   });

  keys.forEach((key) => {
    if (params[key] !== undefined && params[key] !== null) {
      newUrl.searchParams.set(key, params[key]);
    }
  });

  return newUrl;
};

export const parseTimeToText = (seconds: any) => {
  if (!seconds) return '';
  let dates: any = Math.floor(seconds / 86400);
  let hours: any = Math.floor(seconds / 3600);
  let minutes: any = Math.floor(seconds / 60);
  let text = '';
  if (dates >= 1) {
    text = ` ${dates} ngày`;
    const remainSec: any = seconds - Math.floor(dates) * 86400;
    const parseText1: any = parseTimeToText(remainSec);
    const remainText = parseText1?.text || '';
    text += ` ${remainText}`;
  } else if (hours >= 1) {
    text += ` ${hours} giờ`;
    const remainSec: any = seconds - Math.floor(hours) * 3600;
    const parseText2: any = parseTimeToText(remainSec);
    text += ` ${parseText2?.text || ''}`;
  } else if (minutes >= 1) {
    text += ` ${minutes} phút`;
    const remainSec: any = seconds - Math.floor(minutes) * 60;
    const parseText3: any = parseTimeToText(remainSec);
    const remainText = parseText3?.text || '';
    text += ` ${remainText}`;
  }
  return {
    text,
    dates,
    hours,
    minutes,
    seconds
  };
};

export const addClassActive = (className: any, isAdd: any) => {
  const elements = document.getElementsByClassName(className);
  // for (const element of elements) {
  //   if (element && element.classList) {
  //     if (!element.classList.contains('ribbonFavorite')) {
  //       if (isAdd) {
  //         element.classList.add('is-active');
  //         element.title = TEXT.REMOVE_FAVORITE;
  //       } else {
  //         element.classList.remove('is-active');
  //         element.title = TEXT.ADD_FAVORITE;
  //       }
  //     }
  //   }
  // }

  Array.from(elements).forEach((element: any) => {
    if (element && element.classList) {
      if (element.classList.contains('ribbonFavorite')) {
        if (isAdd) {
          element.classList.add('is-active');
          element.title = TEXT.REMOVE_FAVORITE;
        } else {
          element.classList.remove('is-active');
          element.title = TEXT.ADD_FAVORITE;
        }
      }
    }
  });
};

export const parsePayDuration = (numberDuration: any) => {
  if (typeof numberDuration !== 'number') return '';
  let strDuration = '';
  let timer;
  if (numberDuration >= 720) {
    // Theo thang
    timer = (numberDuration / 720).toFixed(0);
    strDuration = `${timer} tháng`;
  } else if (numberDuration > 24) {
    // Theo ngay
    timer = (numberDuration / 24).toFixed(0);
    strDuration = `${timer} ngày`;
  } else {
    strDuration = `${numberDuration.toFixed(0)} giờ`;
  }
  return strDuration;
};

export const parseSecond = (value: any) => {
  if (Number.isNaN(value)) {
    return {
      time: '',
      hours: 0,
      minutes: 0,
      second: 0
    };
  }
  const hours = Math.floor(value / 3600);
  let minutes = Math.floor(value / 60);
  let hoursText = hours >= 1 ? hours : '';
  let minutesText: any = '';
  let secondText: any = '';
  if (hours >= 1) {
    minutes = Math.floor((value % 3600) / 60);
  }

  let second = Math.round(value - hours * 3600 - minutes * 60);
  if (second === 60) second = 59;
  minutesText = minutes >= 0 && minutes <= 9 ? `0${minutes}` : minutes;
  secondText = second >= 0 && second <= 9 ? `0${second}` : second;
  let time = '';

  if (hoursText) {
    time = `${hoursText}:${minutesText}:${secondText}`;
  } else {
    time = `${minutesText}:${secondText}`;
  }

  return {
    time,
    hours,
    minutes,
    second
  };
};

export const checkIsFullscreen = () => {
  if (typeof document === 'undefined') return false;
  const videoTag = document.getElementById(EL_ID.VIE_PLAYER);
  return (
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.mozFullScreenElement ||
    document.msFullscreenElement ||
    videoTag?.webkitDisplayingFullscreen
  );
};

export const getUrlFromPath = (path: any) => {
  let url = ((path || '').split('?') || [])?.[0];
  return url || path;
};

export const splitArrayWithCount = ({ data, start, count }: any) => {
  let result: any = [];
  const from = start || 0;
  if (data && data.length > 0) {
    data.forEach((item: any, index: any) => {
      const newItem = new RibbonItem(item);
      if (!count || count === 0) {
        result.push(newItem);
      } else {
        const temp = index - from;
        let resultIndex = Math.floor(temp / count);
        if (from > 0) {
          resultIndex += 1;
        }
        if (temp < 0) {
          resultIndex = 0;
        }
        if (!result[resultIndex]) result[resultIndex] = [];
        result[resultIndex].push(newItem);
      }
    });
  } else {
    result = data;
  }
  return result;
};

export const onOpenPayment = (router: any, params?: any) => {
  ConfigLocalStorage.set('PREVIOUS_URL', JSON.stringify(window.location.href) || '/');
  segmentEvent(NAME.CHECK_OUT_STARTED, {
    [PROPERTY.REFERRAL]: window.location.href
  });
  const {
    returnUrl,
    returnUrlFastTrack,
    pkg,
    referralCode,
    promotionCode,
    termId,
    curPage,
    isPvod,
    id,
    type,
    isBannerTriggerPvod,
    newTriggerPaymentBuyPackage = {}
  } = params || {};

  const { query } = router || {};
  const newParams: any = UtmParams(query);
  if (isPvod) {
    ConfigLocalStorage.set(LocalStorage.FROM_URL_FAST_TRACK, returnUrlFastTrack || '');
    if (isBannerTriggerPvod) {
      ConfigLocalStorage.set(LocalStorage.IS_BANNER_TRIGGER_PVOD, isBannerTriggerPvod || '');
    }
    if (type) newParams.type = type;
    if (id) newParams.id = id;
  }
  ConfigLocalStorage.set(LocalStorage.FROM_URL, returnUrl || '');

  ConfigLocalStorage.remove(LocalStorage.RE_LOGIN_PARAMS);
  if (pkg && !isPvod) newParams.pkg = pkg;
  if (typeof referralCode !== 'undefined') newParams.referralCode = referralCode;
  if (typeof promotionCode !== 'undefined') newParams.promotionCode = promotionCode;
  if (typeof termId !== 'undefined') newParams.termId = termId;
  if (curPage) newParams.curPage = curPage;
  if (!isEmpty(newTriggerPaymentBuyPackage) && !newTriggerPaymentBuyPackage?.isGlobal) {
    if (!newTriggerPaymentBuyPackage?.profileId) {
      router.push(
        pkg
          ? `${PAGE.AUTH}/?destination=${`${PAGE.PAYMENT_METHOD}/?pkg=${pkg}`}&trigger=${
              TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE
            }`
          : PAGE.PAYMENT
      );
    } else {
      router.push({
        pathname: isPvod ? PAGE.PVOD_CONTENT : pkg ? PAGE.PAYMENT_METHOD : PAGE.PAYMENT,
        query: newParams
      });
    }
    return;
  }
  if (router) {
    router.push({
      pathname: isPvod
        ? PAGE.PVOD_CONTENT
        : !newTriggerPaymentBuyPackage?.isGlobal && (pkg || promotionCode)
        ? PAGE.PAYMENT_METHOD
        : PAGE.PAYMENT,
      query: newParams
    });
  }
};

export const numberWithCommas = (x: any) => {
  if (x === undefined) return x;
  const parts = x.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  return parts.join('.');
};

export const roundNumber = (number: number, countDec: number) =>
  +Number(Math.round(Number(`${number}e+${countDec || 0}`)) + `e-${countDec || 0}`);

export const onNumberViews = (numViews: any, countDec?: any) => {
  let output = '';
  if (numViews > 0 && numViews < 9999) {
    output = numViews;
  } else if (numViews >= 10000 && numViews < 999999) {
    output = `${roundNumber(numViews / 1000, countDec)} N`;
  } else if (numViews >= 1000000 && numViews < 999999999) {
    output = `${roundNumber(numViews / 1000000, countDec)} Tr`;
  } else if (numViews >= 1000000000) {
    output = `${roundNumber(numViews / 1000000000, countDec)} Tỷ`;
  }
  return output;
};

export const setTableHeader = (headerData: any, firstItem: any) => {
  const keyList = Object.keys(firstItem || {}) || [];
  let header = (headerData || []).filter(
    (item: any) => (keyList || []).findIndex((k) => k === item.key) !== -1
  );
  return header;
};

export const getRandomText = (length: any) => {
  let result = '';
  let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let charactersLength = characters.length;
  for (let i = 0; i < length; i += 1) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
export const parseHoursToDate = (hours: any) => Math.floor(hours / 720);

export const parseExpiredDate = (date: any) => {
  const time = Moment(date).format('LTS');
  const day = Moment(date).format('DD/MM/YYYY');
  if (time === 'Invalid date' || day === 'Invalid date') return '';
  return `${time} - ${day}`;
};

export const parseExpiredDateTime = (date: any) => {
  if (!date) return '';
  const day = Moment(date).format('DD/MM/YYYY');
  return day;
};

export const handleScrollTop = () => {
  const html = document.getElementsByTagName('html');
  const htmlElement = html?.[0];
  if (typeof htmlElement?.scrollTo === 'function') {
    htmlElement.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  } else if (htmlElement) {
    htmlElement.scrollTop = 0;
  }
};

// export const queryStringEncoding = (obj) => {
//   let str = [];
//   for (let p in obj) {
//     if (obj.hasOwnProperty(p)) {
//       if (obj[p]) {
//         str.push(`${encodeURIComponent(p)}=${encodeURIComponent(obj[p])}`);
//       }
//     }
//   }
//   return str.join('&');
// };

export const queryStringEncoding = (obj: any) =>
  Object.keys(obj)
    .filter((key) => obj[key])
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join('&');

export const getALTSEOImage = (data: any) => {
  let dataMirage = data;
  if (dataMirage.items) {
    // get(dataMirage, 'items', []).map((item, idx) => {
    //   if (item?.people?.director || item?.people?.actor) {
    //     item.altSEOImg = `${item?.seo?.title} ${idx < 10 ? ` - 0${idx + 1}` : idx + 1} - ${(
    //       item?.people?.director || []
    //     )
    //       .map((item: any) => item.name)
    //       .join(' - ')}${(item?.people?.actor || []).map((item: any) => item.name).join(' - ')}`;
    //   } else {
    //     item.altSEOImg = item?.seo?.title;
    //   }
    // });
    get(dataMirage, 'items', []).forEach((item: any, idx: any) => {
      // Use forEach instead of map
      if (item?.people?.director || item?.people?.actor) {
        item.altSEOImg = `${item?.seo?.title} ${idx < 10 ? ` - 0${idx + 1}` : idx + 1} - ${(
          item?.people?.director || []
        )
          .map((item: any) => item.name)
          .join(' - ')}${(item?.people?.actor || []).map((item: any) => item.name).join(' - ')}`;
      } else {
        item.altSEOImg = item?.seo?.title;
      }
    });
  }

  return dataMirage;
};

export const setClassWithType = ({ ribbonType }: any) => {
  let thumbRatio = 'ratio-16-9';
  let cardClass = '';

  switch (ribbonType) {
    case RIBBON_TYPE.TOP_VIEWS: {
      cardClass = 'card--vod-rank';
      thumbRatio = 'ratio-3-43';
      break;
    }
    case RIBBON_TYPE.ORIGINAL:
      thumbRatio = 'ratio-1-2';
      break;
    default:
      break;
  }
  return {
    thumbRatio,
    cardClass
  };
};

export const replaceKey = (str: any, key: any, value: any) => {
  let newStr = str;
  const myKey = `{${key}}`;
  if (str && key) newStr = newStr.replace(myKey, value || '');
  return newStr;
};

export const getCardType = (cardNumber: any) => {
  let res = '';
  ConfigPayment.CARD_TYPE.forEach((item) => {
    const regex = item.pattern;
    if (regex.test(cardNumber)) res = item.name;
  });
  return res;
};

export const validateCardExpired = (cardExpired: any) => {
  const temp = (cardExpired || '').split('/');
  let validatedError = '';
  if (temp?.[1]?.length === 2 || temp?.[1]?.length === 4) {
    const temp1Number = parseInt(temp?.[1]?.length === 2 ? `20${temp?.[1]}` : temp?.[1]);
    const temp0Number = parseInt(temp?.[0]);
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;
    if (temp1Number < year) {
      validatedError = TEXT.VALIDATED_CARD_EXPIRED;
    } else if (temp1Number === year && temp0Number < month) {
      validatedError = TEXT.VALIDATED_CARD_EXPIRED;
    } else if (temp0Number === 0) validatedError = TEXT.VALIDATED_CARD_EXPIRED;
  } else {
    validatedError = TEXT.VALIDATED_CARD_EXPIRED;
  }
  return validatedError;
};

export const validateCardNumber = (cardNumber: any) => {
  let validatedError = '';
  const cardType = getCardType(cardNumber);
  const isVisa = cardType === ConfigPayment?.CARD_TYPE?.[2]?.name;
  const isMaster = cardType === ConfigPayment?.CARD_TYPE?.[3]?.name;

  if (cardNumber?.length < 16 || !(isVisa || isMaster)) {
    validatedError = TEXT.VALIDATED_CARD_NUMBER;
  }
  return {
    validatedError,
    isVisa,
    isMaster
  };
};

export const validateEmail = (email: any) => {
  const REG_EMAIL = REGEX.EMAIL;
  return REG_EMAIL.test(String(email).toLowerCase());
};

export const getAttrLink = ({ as, href, type }: any) => {
  if (href && href !== '') {
    // nếu có truyền href(tên page) thì return thẳng
    return {
      as: as || href,
      href
    };
  }

  // tìm page theo type VOD truyền vào

  switch (type) {
    case CONTENT_TYPE.EPISODE:
      href = PAGE.VOD;
      break;
    case CONTENT_TYPE.SEASON:
    case CONTENT_TYPE.MOVIE:
      href = PAGE.VOD;
      break;
    case CONTENT_TYPE.LIVE_TV:
      // as = '/truyen-hinh-truc-tuyen'
      href = PAGE.LIVE_TV_SLUG;
      break;
    case CONTENT_TYPE.EPG:
      // as = '/truyen-hinh-truc-tuyen'
      href = PAGE.LIVE_TV_EPG;
      break;
    case CONTENT_TYPE.LIVESTREAM:
      as = '/truc-tiep';
      href = PAGE.LIVE_STREAM;
      break;
    case CONTENT_TYPE.TRAILER:
      href = PAGE.VOD;
      break;
    case CONTENT_TYPE.RIBBON:
      href = PAGE.COLLECTION;
      break;
    default:
      if (!Number.isNaN(type) && as && as !== '') {
        href = PAGE.VOD;
      } else {
        href = '/_error';
        as = href;
      }
      break;
  }
  return {
    as: as || href,
    href
  };
};

export const parseTagsData = (item: any, isComingSoon?: any) => {
  let data = [];

  const {
    timeLiveTv,
    category,
    relatedSeason,
    releaseYear,
    type,
    title,
    tags,
    runTime,
    labelSubtitleAudio,
    subtitle,
    labelPublicDay,
    ageRange,
    resolution,
    isUpComingSoon,
    isBroadcasting
  } = item || {};
  const countries = (tags || []).filter((tag: any) => tag.type === 'country');
  // const genres = (item?.tags || []).filter(tag => (tag.type === 'genre'))
  if ((type === CONTENT_TYPE.LIVE_TV || type === CONTENT_TYPE.EPG) && !isComingSoon) {
    if (subtitle || title) data.push({ name: subtitle || title });
    if (timeLiveTv) data.push({ name: timeLiveTv });
  } else if (type === CONTENT_TYPE.LIVESTREAM && labelPublicDay) {
    data.push({ name: labelPublicDay });
  } else {
    if (releaseYear) {
      data.push({ name: releaseYear });
    }
    if (ageRange) {
      if (
        !(
          (isUpComingSoon || isBroadcasting) &&
          (type === CONTENT_TYPE.LIVE_TV || type === CONTENT_TYPE.EPG)
        )
      ) {
        data.push({ name: ageRange });
      }
    }
    if (countries && countries.length > 0) {
      data = data.concat(countries);
    }
    if (type === CONTENT_TYPE.MOVIE && runTime > 0) {
      const { remainText } = parseRemainText(0, runTime * 60);
      data.push({ name: remainText });
    } else if (type === CONTENT_TYPE.SEASON && relatedSeason?.length > 0) {
      let seasonText = `${relatedSeason.length} Phần`;
      if (category === SEASON_CATEGORY.SHOW) seasonText = `${relatedSeason?.length} Mùa`;
      data.push({ name: seasonText });
    }
    if (labelSubtitleAudio) {
      data.push({ name: labelSubtitleAudio });
    }
    if (resolution) {
      switch (resolution) {
        case RESOLUTION.SD:
          data.push({ name: TEXT.RESOLUTION.SD });
          break;
        case RESOLUTION.HD:
          data.push({ name: TEXT.RESOLUTION.HD });
          break;
        case RESOLUTION.FULL_HD:
          data.push({ name: TEXT.RESOLUTION.FULL_HD });
          break;
        case RESOLUTION.UHD:
          data.push({ name: TEXT.RESOLUTION.UHD });
          break;
        default:
          break;
      }
    }
  }

  return data;
};

export const parseInfoDebug = ({ player, linkPlay, playerName, video }: any) => {
  const elmDevice = getOSVersion();
  const elmBrowser = getBrowser();
  let elmBufferTime: any = '';
  let elmMbpsBandwidth: any = '';
  let elmViewPort: any = '';
  let elmVideoCodec: any = '';
  let elmAudioCodec: any = '';
  const setPow = 10 ** 6;
  if (playerName === PLAYER_NAME.SHAKA_PLAYER) {
    const stats = player?.getStats() || {};
    elmBufferTime = `${(stats?.bufferingTime || 0).toFixed(2)}s`;
    elmMbpsBandwidth = `${(stats?.streamBandwidth / 10 ** 6).toFixed(2)} mbps`;

    const index = (player?.getVariantTracks() || []).findIndex(
      (item: any) => item?.active === true
    );
    const getInfoTrack = player?.getVariantTracks().slice(index);
    const getVariantTracks = getInfoTrack[0];
    elmViewPort = `${getVariantTracks?.width || video?.videoWidth} x ${
      getVariantTracks?.height || video?.videoHeight
    }`;
    elmVideoCodec = getVariantTracks?.videoCodec;
    elmAudioCodec = `${getVariantTracks?.audioCodec} | ${getVariantTracks?.language}`;
  } else if (playerName === PLAYER_NAME.HLS_PLAYER) {
    const currentLevel = (player?.levels || []).findIndex(
      (item: any) => item?.level === player?.currentLevel
    );
    const getInfoLevel = player?.levels?.slice(currentLevel);
    const getCurrentLevel = getInfoLevel ? getInfoLevel[0] : [];
    let timeBuffer = '';
    if (isIOS) {
      timeBuffer = (video?.buffered?.end(0) - video?.buffered?.start(0)).toFixed(2);
    } else {
      timeBuffer = (player?.streamController?.stats?.tbuffered / setPow).toFixed(2);
    }

    elmBufferTime = `${timeBuffer}s`;
    elmMbpsBandwidth = `${(player?.bandwidthEstimate / setPow).toFixed(2)} mbps` || null;
    elmViewPort =
      getCurrentLevel?.attrs?.RESOLUTION || `${video?.videoWidth} x ${video?.videoHeight}`;
    elmVideoCodec = getCurrentLevel?.videoCodec || null;
    elmAudioCodec = getCurrentLevel?.audioCodec || null;
  }

  const infoDebug = [
    { text: `- Device: ${elmDevice}` },
    { text: `- Browser: ${elmBrowser}` },
    { text: `- Buffer time: ${elmBufferTime}` },
    { text: `- Bandwidth: ${elmMbpsBandwidth}` },
    { text: `- View port: ${elmViewPort}` },
    { text: `- Video format: ${elmVideoCodec}` },
    { text: `- Audio format: ${elmAudioCodec}` },
    { text: `- Link Play: ${linkPlay}` }
  ];
  return infoDebug;
};

export const copyText = (textValue: any) => {
  let textField = document.createElement('textarea');
  textField.innerText = textValue;
  document.body.appendChild(textField);
  textField.select();
  document.execCommand('copy');
  textField.remove();
  return textValue;
};

export const getBrowser = () => {
  let result: any = Bowser.getParser(window.navigator.userAgent);
  return `${result.parsedResult.browser.name} | ${result.parsedResult.browser.version}`;
};

export const getOSVersion = () => {
  let osVersion = null;
  if (typeof window === 'undefined') return '';
  let { userAgent } = window.navigator;
  let { platform } = window.navigator;
  let macosPlatforms = ['Macintosh', 'MacIntel', 'MacPPC', 'Mac68K'];
  let windowsPlatforms = ['Win32', 'Win64', 'Windows', 'WinCE'];
  let iosPlatforms = ['iPhone', 'iPad', 'iPod'];
  if (macosPlatforms.indexOf(platform) !== -1) {
    osVersion = 'Mac OS';
  } else if (iosPlatforms.indexOf(platform) !== -1) {
    osVersion = 'iOS';
  } else if (windowsPlatforms.indexOf(platform) !== -1) {
    osVersion = 'Windows';
  } else if (/Android/.test(userAgent)) {
    osVersion = 'Android';
  } else if (!osVersion && /Linux/.test(platform)) {
    osVersion = 'Linux';
  }
  return osVersion;
};

export const parseTimeText = (number: any, unit?: any) => {
  let text = '';
  if (!number || number <= 0) return '';

  let seconds = 0;
  let minutes = 0;
  let hours = 0;

  switch (unit) {
    case 'second': {
      seconds = number;
      minutes = Math.floor(number / 60);
      hours = Math.floor(number / 3600);
      break;
    }
    case 'minute': {
      minutes = number;
      hours = Math.floor(number / 60);
      break;
    }

    default:
      minutes = number;
      hours = Math.floor(number / 60);
      break;
  }

  const hoursText = hours > 0 ? `${hours}h` : '';
  const minutesText = minutes > 0 ? `${minutes}p` : '';
  const secondsText = seconds > 0 ? `${seconds}g` : '';

  text =
    (hoursText || '') +
    (minutesText ? (hoursText ? ' ' : '') + minutesText : '') +
    (secondsText ? (minutesText ? ' ' : '') + secondsText : '');
  return text;
};

export const setLinkAttributeNotify = ({ action, packageId }: any) => {
  let href = '';
  switch (action) {
    case ACTION_TYPE_NOTIFY.PAYMENT:
      href = `${PAGE.PAYMENT}/?pkg=${packageId}`;
      break;
    case ACTION_TYPE_NOTIFY.FAVORITE:
      href = PAGE.PROFILE_FAVORITE;
      break;
    default:
      break;
  }
  return href;
};
export const setLinkAttribute = (contentType: any, url?: any) => {
  let href = PAGE.VOD;
  switch (contentType) {
    case CONTENT_TYPE.SEASON:
    case CONTENT_TYPE.EPISODE:
    case CONTENT_TYPE.MOVIE:
    case CONTENT_TYPE.TRAILER:
      href = PAGE.VOD;
      break;
    case CONTENT_TYPE.LIVE_TV:
      href = PAGE.LIVE_TV_SLUG;
      break;
    case CONTENT_TYPE.EPG: {
      const isSlug = (url || '').split('/');
      href = isSlug[3] && isSlug[3] !== '' ? PAGE.LIVE_TV_EPG : PAGE.LIVE_TV_SLUG;
      break;
    }
    case CONTENT_TYPE.LIVESTREAM:
      href = PAGE.LIVE_STREAM_SLUG;
      break;
    case CONTENT_TYPE.RIBBON:
      href = PAGE.COLLECTION_RIB;
      break;
    default:
      break;
  }
  return { href };
};

export const setCardImage = ({
  images,
  ribbonType,
  isMain,
  isMasterBanner,
  isCardDetail,
  expand,
  isCollectionBanner,
  isMobile,
  isSchedule
}: any) => {
  let src = images?.thumbnail;
  let defaultSrc = ConfigImage.defaultBanner16x9;
  if (!!isMasterBanner || (isCollectionBanner && !isMobile) || isMain) {
    src = isMobile ? images?.thumbnailNTC || images?.carouselNTC : images?.carouselNTC;
  } else if (isMobile && isCollectionBanner) {
    src = images?.posterNTC || images?.poster;
  } else if (expand) {
    src = images?.thumbnailBigNTC || images?.thumbnailHot;
  } else if (isSchedule) {
    src = images?.carousel || images?.thumbnailBig;
  } else {
    switch (ribbonType) {
      case RIBBON_TYPE.ORIGINAL: {
        src = images?.posterOriginal || images?.poster;
        defaultSrc = ConfigImage.defaultOriginal;
        break;
      }
      case RIBBON_TYPE.TOP_VIEWS: {
        src = !isCardDetail ? images?.poster : src;
        defaultSrc = ConfigImage.defaultPoster;
        break;
      }
      default: {
        src = images?.thumbnail || images?.imageLink; // imageLink Livetv Favorite items
        defaultSrc = ConfigImage.defaultBanner16x9;
        break;
      }
    }
  }
  return {
    src: src || images?.thumbnail,
    defaultSrc
  };
};

export const addParamToUrlVieON = (query: any, params?: any, notSearchParam?: any) => {
  const newParams: any = { ...UtmParams(query) };
  if (query?.q && !notSearchParam) newParams.q = query?.q;
  if (query?.id && !notSearchParam) newParams.id = query?.id;
  const finalParams = { ...newParams, ...params };
  return { ...finalParams };
};

export const controlScroll = ({ isScroll }: any) => {
  const htmlEl: any = document.getElementsByTagName('html')[0];
  if (typeof document === 'undefined' || !htmlEl) {
    return;
  }
  if (!isScroll) {
    htmlEl.classList.add('overflow');
    htmlEl.style.paddingRight = '0.75rem';
  } else {
    htmlEl.classList.remove('overflow');
    htmlEl.style.paddingRight = 0;
  }
};

export const parseParamsFromContent = (contentItem: any) => {
  if (!contentItem?.id) return {};
  const { href, seo } = contentItem || {};
  const link = seo?.url;
  const params: any = {};
  const type = contentItem?.type;
  switch (type) {
    case CONTENT_TYPE.LIVESTREAM: {
      let slug = contentItem?.slug || (link || '').split('/')?.[3];
      if (slug) params.slug = slug;
      break;
    }
    default: {
      break;
    }
  }
  return {
    page: href,
    params,
    link
  };
};

export const controlPlayVideo = ({
  name,
  noControl,
  expand,
  isEndFreeTrial,
  backToPlay,
  isEndScreenVod
}: any) => {
  if (typeof window === 'undefined' || noControl || isEndScreenVod) return;
  if (name) {
    setVideoPlay({ playerId: null });
  } else if (expand) {
    setVideoPlay({ playerId: PLAYER_TYPE.CARD_DETAIL });
  } else {
    if (!isEndFreeTrial) {
      setVideoPlay({ isAll: backToPlay });
    }
    handleMasterPlayer();
  }
};
export const openAppMobile = (data: any) => {
  const linkDownloadApp = get(data, 'link', LINK_DOWNLOAD_APP_URL);
  const urlRemake = linkDownloadApp + (window.location?.search || '');
  window.location = urlRemake;
};

export const handleMasterPlayer = (data?: any) => {
  if (data?.isFullscreenPaymentTrigger) return;
  const isDetail = !!data?.data?.id;
  const player = document.getElementById(PLAYER_TYPE.MASTER_BANNER);
  const html = document.getElementsByTagName('html')?.[0];

  let allowPlay = false;
  if (html) {
    const scrollTop = html?.scrollTop;
    const bannerHeight = (html.clientWidth || 0) * BANNER_IMAGE_RATE;
    allowPlay = scrollTop < bannerHeight * 0.6;
  }

  if (!player) return;
  if (isDetail) {
    player.pause();
  } else if (!player?.ended && player?.paused && allowPlay) {
    const playPromise = player.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          // Automatic playback started!
          // Show playing UI.
          // this.setState({ isLoading: false })
        })
        .catch(() => {
          // Auto-play was prevented
          // Show paused UI.
        });
    }
  } else {
    player.pause();
  }
};

export const setGlobalPlayer = ({ id, player }: any) => {
  if (typeof window === 'undefined') return;
  if (id && player) {
    removeGlobalPlayer({ id });
    if (!window.playerList) window.playerList = [];
    window.playerList.push({
      id,
      player
    });
    window[id] = player;
  }
};

export const removeGlobalPlayer = ({ id }: any) => {
  if (typeof window === 'undefined') return;
  if (id) {
    const playerIndex = (window.playerList || []).findIndex((item: any) => item.id === id);
    if (playerIndex > -1) {
      window.playerList.splice(playerIndex, 1);
      window[id] = null;
    }
  }
};

export const setVideoPlay = ({ playerId, noPauseOther, isAll }: any) => {
  if (typeof window === 'undefined') return;
  if (
    window?.[playerId] &&
    window[playerId]?.paused &&
    typeof window[playerId]?.play === 'function'
  ) {
    const playPromise = window?.[playerId]?.play();
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          // Automatic playback started!
          // Show playing UI.
        })
        .catch(() => {
          // Auto-play was prevented
          // Show paused UI.
        });
    }
  }
  get(window, 'playerList', []).forEach((vi: any) => {
    if (isAll) {
      if (!(vi?.id || '').includes(PLAYER_TYPE.CARD_AIRING)) {
        if (vi?.player.paused) vi?.player.play();
      }
    } else if (vi?.id && vi?.player && vi?.id !== playerId && !noPauseOther) {
      if (vi?.player.played) vi?.player.pause();
    }
  });
};

export const setMutePlayer = ({ playerId, isMuted }: any) => {
  if (!playerId) return;
  const player = document.getElementById(playerId);
  if (player) player.muted = isMuted;
};

export const getUpcommingTime = (timeStart: any, isPremiere: any) => {
  const day = Moment(timeStart).utc().format('DD/MM/YYYY'); // Moment.unix(timeStart).format("DD/MM/YYYY");
  const time = Moment(timeStart).utc().format('HH:mm'); // Moment(timeStart).format('hh:mm')
  if (time === 'Invalid date' || day === 'Invalid date') return '';
  return `${isPremiere ? 'Công chiếu' : 'Trực tiếp'} lúc ${time}, ngày ${day}`;
};
export const parseSegmentPopupNameFromContent = (content: any) => {
  switch (content?.type) {
    case CONTENT_TYPE.SEASON:
    case CONTENT_TYPE.MOVIE:
      return VALUE.POPUP_NAME.GUEST_VOD_LIVESTREAM_VIP;
    case CONTENT_TYPE.EPISODE:
      return VALUE.POPUP_NAME.GUEST_EPISODE_VIP;
    default:
      return '';
  }
};

export const parseMatchSecond = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));

  let matchDay = matchDate.getDate();
  const matchMonth = matchDate.getMonth() + 1;
  const matchYear = matchDate.getFullYear();
  let scheduleDate = `${matchYear}-${matchMonth}-${matchDay}`;
  let dateScheduleSame = new Date(scheduleDate.replace(/-/g, '/'));
  return dateScheduleSame.getTime();
};

export const parseMatchDate = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));
  let matchDay = matchDate.getDate();

  return matchDay;
};

export const parseMatchMonth = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));
  const matchMonth = matchDate.getMonth() + 1;

  return matchMonth;
};

export const parseMatchHours = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));
  const matchHours = matchDate.getHours();
  const matchMin = matchDate.getMinutes();
  let matchTime = `${matchHours > 9 ? matchHours : `0${matchHours}`}:${
    matchMin > 9 ? matchMin : `0${matchMin}`
  }`;

  return matchTime;
};

export const parseMatchYear = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));
  const matchYear = matchDate.getFullYear();

  return matchYear;
};
export const parseMatchFirstDay = (date: any) => {
  let matchDate = new Date(date.replace(/-/g, '/'));
  let newFirstDay = matchDate.getDay();
  switch (newFirstDay) {
    case 0:
      return 'Chủ nhật';
    case 1:
      return 'Thứ 2';
    case 2:
      return 'Thứ 3';
    case 3:
      return 'Thứ 4';
    case 4:
      return 'Thứ 5';
    case 5:
      return 'Thứ 6';
    case 6:
      return 'Thứ 7';
    default:
      return '';
  }
};

export const parseWebpSrc = (src: any) => {
  let webpSrc = src;
  const lastDotIndex = (src || '').lastIndexOf('.');
  if (lastDotIndex > -1) {
    webpSrc = `${src.substring(0, lastDotIndex)}.webp`;
  }
  return webpSrc;
};

export const parseTimeStartNotify = (timeStart: any) => {
  const timeEndOfDay = +Moment.utc().endOf('day').format('X');
  const day = Moment.unix(timeStart).format('DD-MM'); // Moment.unix(timeStart).format("DD-MM");
  const time = Moment.unix(timeStart).local().format('kk:mm'); // Moment(timeStart).format('hh:mm')
  if (timeEndOfDay - timeStart > 0) {
    return `Nội dung bạn chọn xem sẽ diễn ra vào ${time} hôm nay`;
  }
  return `Nội dung bạn chọn xem sẽ diễn ra vào ${time} ngày ${day}`;
};

export const parteExpiredDate = (str: any) => {
  if (!str) return '';
  const bits = (str || '').split(/\D/);
  // let date = new Date(bits[0], --bits[1], bits[2]);
  let date = new Date(bits[0], bits[1] - 1, bits[2]);
  const timeStamp = date.getTime() / 1000;
  return Moment.unix(timeStamp).format('DD/MM');
};

export const scrollToTop = () => {
  document.body.scrollTop = 0; // For Safari
  document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
};

export const detectIphoneX = () => {
  const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
  // Get the device pixel ratio
  const ratio = window.devicePixelRatio || 1;
  // Define the users device screen dimensions
  const screen = {
    width: window.screen.width * ratio,
    height: window.screen.height * ratio
  };
  const aspect = screen.width / screen.height;
  if (iOS && aspect.toFixed(3) === '0.462') {
    return true;
  }
  return false;
};

export const isEmptyObject = (value: any) =>
  Object.keys(value).length === 0 && value.constructor === Object;

export const buildDeeplink = (routerProps: any, deeplink: any) => {
  if (!routerProps) return;
  const { utm_campaign, utm_content, utm_medium, utm_source } = routerProps || {};
  let link = `https://click.vieon.vn/hjsB?pid=DownloadFromMobileWeb&af_dp=${
    deeplink || 'vieonapp://vieon.vn'
  }&af_force_deeplink=true`;
  // `https://click.vieon.vn/hjsB/footer?af_dp=${deeplink || 'vieonapp://home'}`
  if ((utm_medium && utm_medium !== '') || (utm_source && utm_source !== '')) {
    // link = `https://click.vieon.vn/hjsB/footer?utm_medium=${utm_medium}${utm_medium !== 'null'  ? `&pid=${utm_medium === 'social' || utm_medium === 'organic'? 'owned' : 'paid'}-${utm_source}` : ''}&af_dp=${deeplink || 'vieonapp://home'}&utm_source=${utm_source}&utm_campaign=${utm_campaign}&utm_content=${utm_content}&af_force_deeplink=true`
    link = `https://click.vieon.vn/hjsB?utm_medium=${utm_medium}${
      utm_campaign ? `&c=${utm_campaign}` : '&c=owned-vieon'
    }${utm_source !== 'null' ? `&pid=DownloadFromMobileWeb&af_channel=${utm_source}` : ''}&af_dp=${
      deeplink || 'vieonapp://vieon.vn'
    }&utm_source=${utm_source}&utm_campaign=${
      utm_campaign || 'owned-vieon'
    }&utm_content=${utm_content}&af_force_deeplink=true`;
  }
  return link;
};

export const parseQueryString = (query: any) => {
  const queryParsed = (query || '').split('?').join('');
  const vars = queryParsed.split('&');
  let query_string: any = {};
  for (let i = 0; i < vars.length; i += 1) {
    let pair = vars[i].split('=');
    let key = decodeURIComponent(pair[0]);
    let value = decodeURIComponent(pair[1]);
    // If first entry with this name

    if (typeof query_string[key] === 'undefined') {
      query_string[key] = decodeURIComponent(value);
      // If second entry with this name
    } else if (typeof query_string[key] === 'string') {
      let arr = [query_string[key], decodeURIComponent(value)];

      query_string[key] = arr;
      // If third or later entry with this name
    } else {
      query_string[key].push(decodeURIComponent(value));
    }
  }
  return query_string;
};

export const formatTimeCodeAds = (data: any) =>
  (data || []).map((item: any) => ({
    secondsStartAds: item.seconds_start_ads,
    typeAds: item.type_ads
  }));

export const parseAdsLink = (ads: any) => {
  const adsData = (ad: any) => {
    let url = '';
    if (ad?.url) {
      // TODO: Những url có "&correlator=" ở cuối thì add thêm timestamp vào
      // những url khác thì không thêm (ví dụ: https://adnetwork-core-lb7.aiactiv.io/direct-ads/51ed65de-5251-44be-9ed0-bdacdf4890c5)
      if (/&correlator=$/i.exec(ad.url)) {
        url = ad.url + new Date().getTime();
      } else {
        url = ad.url;
      }
    }
    return {
      url,
      timeSkip: ad?.time_skip || 0,
      skip: ad?.skip === 1
    };
  };
  return (ads || []).map((ad: any) => {
    const url1 = adsData(ad?.list_url?.[0]);
    const url2 = adsData(ad?.list_url?.[1]);
    const url3 = adsData(ad?.list_url?.[2]);
    return {
      type: ad?.type,
      repeat: ad?.repeat,
      count: ad.count || 1,
      url1,
      url2,
      url3
    };
  });
};

export const handleAddSpacing = (value: any, char: any, count: any) => {
  const newValue = (value || '').split(char).join('');
  let i = 0;
  let temp = [];
  while (i * count < (newValue || '').length) {
    const index = i * count;
    temp.push(newValue.slice(index, index + count));
    i += 1;
  }
  const output = temp.join(char);

  return output;
};

export const isValidDate = (d: any) => d instanceof Date && !Number.isNaN(d);

export const handleMultiDomainLocal = (url: any) => {
  const replaceDomain = (str: any, replaceString: any, value: any) => {
    let result = str;
    let idx = result.indexOf(replaceString);
    let length = (replaceString || '').length || 0;
    const domainString = (result || '').substring(0, idx + length);
    result = result.replace(domainString, value);
    return result;
  };

  let ssrUrl = `${url}`;
  let domainSSR = '';
  let domainSSRLocal = '';
  let isUseDomainLocal = false;
  if ((ssrUrl || '').includes(DOMAIN_API_SSR.CM_V5)) {
    domainSSR = DOMAIN_API_SSR.CM_V5;
    domainSSRLocal = DOMAIN_LOCAL_VIEON_CM_V5;
  } else if ((ssrUrl || '').includes(DOMAIN_API_SSR.BACKEND_USER)) {
    domainSSR = DOMAIN_API_SSR.BACKEND_USER;
    domainSSRLocal = DOMAIN_LOCAL_BACKEND_USER;
  } else if ((ssrUrl || '').includes(DOMAIN_API_SSR.USER_REPORT)) {
    domainSSR = DOMAIN_API_SSR.USER_REPORT;
    domainSSRLocal = DOMAIN_LOCAL_SERVICE_USER_REPORT;
  } else if ((ssrUrl || '').includes(DOMAIN_API_SSR.CM_ACTIVITY)) {
    domainSSR = DOMAIN_API_SSR.CM_ACTIVITY;
    domainSSRLocal = DOMAIN_LOCAL_VIEON_CM_ACTIVITY;
  }

  if (domainSSR && domainSSRLocal) {
    ssrUrl = replaceDomain(ssrUrl, domainSSR, domainSSRLocal);
    isUseDomainLocal = true;
  }
  return {
    ssrUrl,
    isUseDomainLocal
  };
};

export const customScheduleSport = (listData: any) => {
  let checkScheduleSport = new Map();
  if ((listData || []).length === 0) return null;
  // (listData || []).map((item: any) => {
  //   const { href } = setLinkAttribute(item.contentType, item.contentUrl);
  //   let playIconClass = '';
  //   let isPointer = false;
  //   let isLive = false;
  //   if (item.matchStatus === MATCH_STATUS.FINISHED) {
  //     playIconClass = 'vie-replay-o-medium';
  //   } else if (item.matchStatus === MATCH_STATUS.LIVE) {
  //     playIconClass = 'vie-play-solid';
  //     isLive = true;
  //   }

  //   if (item.contentType && item.contentUrl) {
  //     isPointer = true;
  //   } else {
  //     playIconClass = '';
  //   }

  //   const newItem = {
  //     awayTeam: item.awayTeam,
  //     contentId: item.contentId,
  //     contentType: item.contentType,
  //     contentUrl: item.contentUrl,
  //     href,
  //     homeTeam: item.homeTeam,
  //     id: item.id,
  //     liveTVImage: item.livetvImage,
  //     matchStatus: item.matchStatus,
  //     matchDate: parseMatchSecond(item?.matchDate),
  //     matchDay: parseMatchDate(item?.matchDate),
  //     matchMonth: parseMatchMonth(item?.matchDate),
  //     matchYear: parseMatchYear(item?.matchDate),
  //     matchFirstDay: parseMatchFirstDay(item?.matchDate),
  //     matchHours: parseMatchHours(item?.matchDate),
  //     playIconClass,
  //     isLive,
  //     isPointer
  //   };

  //   const key = newItem?.matchDate;
  //   let collection = checkScheduleSport.get(key);
  //   if (!collection) {
  //     checkScheduleSport.set(key, [newItem]);
  //   } else {
  //     collection.push(newItem);
  //   }
  // });
  (listData || []).forEach((item: any) => {
    const { href } = setLinkAttribute(item.contentType, item.contentUrl);
    let playIconClass = '';
    let isPointer = false;
    let isLive = false;

    if (item.matchStatus === MATCH_STATUS.FINISHED) {
      playIconClass = 'vie-replay-o-medium';
    } else if (item.matchStatus === MATCH_STATUS.LIVE) {
      playIconClass = 'vie-play-solid';
      isLive = true;
    }

    if (item.contentType && item.contentUrl) {
      isPointer = true;
    } else {
      playIconClass = '';
    }

    const newItem = {
      awayTeam: item.awayTeam,
      contentId: item.contentId,
      contentType: item.contentType,
      contentUrl: item.contentUrl,
      href,
      homeTeam: item.homeTeam,
      id: item.id,
      liveTVImage: item.livetvImage,
      matchStatus: item.matchStatus,
      matchDate: parseMatchSecond(item?.matchDate),
      matchDay: parseMatchDate(item?.matchDate),
      matchMonth: parseMatchMonth(item?.matchDate),
      matchYear: parseMatchYear(item?.matchDate),
      matchFirstDay: parseMatchFirstDay(item?.matchDate),
      matchHours: parseMatchHours(item?.matchDate),
      playIconClass,
      isLive,
      isPointer
    };

    const key = newItem?.matchDate;
    let collection = checkScheduleSport.get(key);
    if (!collection) {
      checkScheduleSport.set(key, [newItem]);
    } else {
      collection.push(newItem);
    }
  });
  return Array.from(checkScheduleSport || []);
};

export const parseConfigParams = ({ ipAddress, userAgent, origin }: any) => {
  let config: any = {};
  if (ipAddress) {
    config = {
      ipForwarded: {
        'x-forwarded-for': ipAddress,
        // 'x-forwarded-for': '**************',
        'true-client-ip': ipAddress
        // 'true-client-ip': '**************'
      }
    };
  }
  if (userAgent) {
    config.userAgent = {
      'user-agent': userAgent
    };
  }
  if (origin) {
    config.userAgent = {
      origin: `https://${origin}`
      // origin: `https://testing.vieon.global`
    };
  }
  return config;
};
export const handleSaveBindAccountInfo = () => {
  const store = window.__NEXT_REDUX_STORE__;
  const state = store.getState();
  const profileID = state?.Profile?.profile?.id;
  const dataJson: any = ConfigLocalStorage.get(LocalStorage.BIND_ACCOUNT);
  let laterClicked = 0;
  const timeStamp = new Date().getTime() / 1000;

  if (dataJson) {
    const dataParsed = JSON.parse(dataJson);
    if (dataParsed?.laterClicked) {
      laterClicked = dataParsed?.laterClicked;
    }
  }
  const data = {
    laterClicked: laterClicked + 1,
    timestamp: timeStamp,
    id: profileID
  };
  const jsonString = JSON.stringify(data);
  ConfigLocalStorage.set(LocalStorage.BIND_ACCOUNT, jsonString);
  ConfigLocalStorage.remove(LocalStorage.USER_JUST_LOGGED);
};

export const removeURLQueryParams = (url: any, symbol: any) => {
  let [str] = url;
  if (symbol) {
    [str] = (url || '').split(symbol);
  }
  return str;
};

export const createTimeout = (func: any, time: any) => {
  if (typeof func !== 'function') {
    func = () => {};
  }
  if (typeof time !== 'number') time = 0;

  const maxSec = **********; // maximum 24.855 days
  return setTimeout(func, time > maxSec ? maxSec : time);
};

export const parseBrowserName = () => {
  let browserName = '';
  const brands = window?.navigator?.userAgentData?.brands;
  if (brands?.length > 0) {
    brands.forEach((item: any) => {
      if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.EDGE)) {
        browserName = BROWSER.KEY.EDGE;
      } else if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.CHROME)) {
        browserName = BROWSER.KEY.CHROME;
      } else if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.SAFARI)) {
        browserName = BROWSER.KEY.SAFARI;
      } else if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.OPERA)) {
        browserName = BROWSER.KEY.OPERA;
      } else if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.FIREFOX)) {
        browserName = BROWSER.KEY.FIREFOX;
      } else if ((item.brand || '').toUpperCase().includes(BROWSER.KEY.IE)) {
        browserName = BROWSER.KEY.IE;
      }
    });
  } else {
    if (isEdge) browserName = BROWSER.KEY.EDGE;
    if (isChrome) browserName = BROWSER.KEY.CHROME;
    if (isSafari) browserName = BROWSER.KEY.SAFARI;
    if (isOpera) browserName = BROWSER.KEY.OPERA;
    if (isFirefox) browserName = BROWSER.KEY.FIREFOX;
    if (isIE) browserName = BROWSER.KEY.IE;
  }
  return browserName;
};

export const checkOffPlaybackSpeed = () => {
  const {
    browserName,
    isMobile,
    isSafari,
    isChrome,
    isOpera,
    fullBrowserVersion,
    isAndroid,
    osVersion
  } = getSelectorsByUserAgent(window.navigator.userAgent);
  if (
    !isMobile &&
    ((browserName.toUpperCase().includes(BROWSER.KEY.CHROME) &&
      parseFloat(fullBrowserVersion) <= 74) ||
      (browserName.toUpperCase().includes(BROWSER.KEY.EDGE) &&
        parseFloat(fullBrowserVersion) <= 18) ||
      (browserName.toUpperCase().includes(BROWSER.KEY.SAFARI) &&
        parseFloat(fullBrowserVersion) <= 13) ||
      (browserName.toUpperCase().includes(BROWSER.KEY.FIREFOX) &&
        parseFloat(fullBrowserVersion) <= 47) ||
      (browserName.toUpperCase().includes(BROWSER.KEY.OPERA) &&
        parseFloat(fullBrowserVersion) <= 60) ||
      (browserName.toUpperCase().includes(BROWSER.KEY.IE) && parseFloat(osVersion) <= 11))
  ) {
    return true;
  }
  if (
    isMobile &&
    ((isSafari && parseFloat(fullBrowserVersion) <= 13.3) ||
      (isAndroid && parseFloat(osVersion) <= 4.4) ||
      (isAndroid && isChrome && parseFloat(fullBrowserVersion) < 114) ||
      (isOpera && parseFloat(osVersion) <= 12.1))
  ) {
    return true;
  }
  return false;
};

export const checkNextEpisodeToWatch = (props: any) => {
  const { isMobile, isEpisodeTrialInApp, episode, numberTrialEpisode, type } = props;
  let result = false;
  if (
    isMobile &&
    isEpisodeTrialInApp &&
    episode > numberTrialEpisode &&
    (type === CONTENT_TYPE?.SEASON || type === CONTENT_TYPE?.EPISODE)
  ) {
    result = true;
  }
  return result;
};

export function loadJS({ fileUrl, onLoaded, async = true }: any) {
  let scriptEle = document.createElement('script');

  scriptEle.setAttribute('src', fileUrl);
  scriptEle.setAttribute('type', 'text/javascript');
  scriptEle.setAttribute('async', async);

  document.body.appendChild(scriptEle);

  // success event
  scriptEle.addEventListener('load', () => {
    onLoaded(true);
    console.log('File loaded');
  });
  // error event
  scriptEle.addEventListener('error', (ev: any) => {
    onLoaded(false);
    console.log('Error on loading file', ev);
  });
}

export const capitalizeFirstLetter = (str: any) => {
  if (str) {
    return str?.charAt(0).toUpperCase() + str?.slice(1);
  }
  return str;
};

export const toPascalCase = (str: any) =>
  str
    ? (str.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g) || '')
        .map((x: any) => x.charAt(0).toUpperCase() + x.slice(1).toLowerCase())
        .join('')
    : '';

export const detectWebViewUA = (userAgent: any) => {
  const normalizedUserAgent = userAgent.toLowerCase();
  const { standalone }: any = navigator;

  const isIos =
    /ip(ad|hone|od)/.test(normalizedUserAgent) ||
    (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
  const isAndroid = /android/.test(normalizedUserAgent);
  const isSafari = /safari/.test(normalizedUserAgent);
  return (
    (isAndroid && /; wv\)/.test(normalizedUserAgent)) ||
    (isIos && !standalone && !isSafari) ||
    (userAgent || '').includes('Googlebot') ||
    (userAgent || '').includes('AdsBot-Google') ||
    (userAgent || '').includes('UCBrowser') ||
    (userAgent || '').includes('Line') ||
    (userAgent || '').includes('MQQBrowser') ||
    (userAgent || '').includes('MiuiBrowser') ||
    (userAgent || '').includes('Mediapartners-Google')
  );
};

const toSnakeCase = (str: any) =>
  str.replace(/[A-Z]/g, (letter: any) => `_${letter.toLowerCase()}`);

export const convertKeysToSnakeCaseSelective = (obj: any) => {
  const keysToConvert = ['companyName', 'taxCode'];
  const newObj: any = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (keysToConvert.includes(key)) {
        const newKey = toSnakeCase(key);

        newObj[newKey] = obj[key];
      } else {
        newObj[key] = obj[key];
      }
    }
  }

  return newObj;
};
const baseScale = (scale: any, opacity: any) => ({
  scale,
  opacity
});

const positionStyle = (type: any, position: any) => {
  const offsetAnimate = type === 'initial' ? '-50%' : 0;
  const positionStyles: any = {
    middleLeft: { top: '50%', left: offsetAnimate, translateY: '-50%' },
    middleRight: { top: '50%', right: offsetAnimate, translateY: '-50%' },
    topRight: { top: type ? '-50%' : 0, right: offsetAnimate },
    topCenter: { top: offsetAnimate, left: '50%', translateX: '-50%' },
    topLeft: { top: offsetAnimate, left: offsetAnimate },
    bottomRight: { right: offsetAnimate, bottom: offsetAnimate },
    bottomCenter: { bottom: offsetAnimate, left: '50%', translateX: '-50%' },
    bottomLeft: { left: offsetAnimate, bottom: offsetAnimate },
    default: { top: '50%', left: '50%', translateX: '-50%', translateY: '-50%' }
  };

  return positionStyles[position] || positionStyles.default;
};

export const initAnimateScale = (displayPosition: any) => {
  const position = positionStyle('initial', displayPosition);
  return { ...baseScale(0, 0), ...position };
};

export const animateScale = (displayPosition: any) => {
  const position = positionStyle('', displayPosition);
  return { ...baseScale(1, 1), ...position };
};

export const decodeHtmlEntities = (input: any) => {
  const entityMap: any = {
    '&quot;': '"',
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&#39;': "'",
    '&apos;': "'",
    '&nbsp;': ' '
  };

  let decoded = input;
  for (const entity in entityMap) {
    const regex = new RegExp(entity, 'g');

    decoded = decoded.replace(regex, entityMap[entity]);
  }

  return decoded;
};

// function checkTvodRented
// props: destination dùng router để tránh conflict logic decode và encode làm destination trên router bị sai với yêu cầu
// export const checkTvodRented = (dataLogin, destination) => {
//   const { data: { profile } = {} } = dataLogin || {};
//   const query = destination?.query;
//   const isTSvodContent = query?.isTSvod === 'true';
//   const isPremium = profile?.isPremium || profile?.type === USER_TYPE.VIP;
//   console.log(isPremium, isTSvodContent, 'thuc');

//   if (isPremium && isTSvodContent) {
//     return {
//       status: true,
//       redirect: query?.slugTvod
//     };
//   } else if (profile?.hadTvod || !isTSvodContent) {
//     return {
//       status: false,
//       redirect: ''
//     };
//   }

//   return {
//     status: false,
//     redirect: ''
//   };
// };

export const checkTvodRented = (dataLogin: any, router: any) => {
  const profile = dataLogin?.data?.profile;
  const query = router?.query;
  const destination = decodeParamDestination(query?.destination);
  const parsedQuery = parseQueryString(destination);
  const isTSvodContent = parsedQuery?.isTSvod === 'true';

  if (
    parsedQuery?.page?.includes(PAGE.PAYMENT_METHOD) ||
    !isTSvodContent ||
    (!profile?.hadTvod && isTSvodContent)
  ) {
    return { status: false, redirect: '' };
  }

  if (profile?.isPremium || profile?.type === USER_TYPE.VIP) {
    return {
      status: true,
      redirect: (parsedQuery?.slugTvod as string) || (query?.slugTvod as string) || ''
    };
  }

  return { status: false, redirect: '' };
};

export const parseRentType = (benefitInfo: any) => {
  const { benefitType } = benefitInfo;
  return {
    isRented: benefitType > 0 || benefitType === 1,
    isViewed: benefitType === 2,
    isExpired: benefitType === -1
  };
};
