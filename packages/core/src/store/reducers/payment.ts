import { ACTION_TYPE } from '../../store/actions/actionType';
import initialState from './initialState';

const payment = (state: any = initialState.payment, { type, data }: any) => {
  const result: any = { ...state };
  switch (type) {
    case ACTION_TYPE.GET_TEMPORARY_DATA:
      result.dataTemporary = data;
      break;
    case ACTION_TYPE.GET_PAYMENT_CONFIG_SUCCESS:
      result.sliderBenefitVip = data?.sliderBenefitVip || {};
      result.paymentDeepLink = data?.deeplink || [];
      result.listPackagesConfig = data?.packages || [];
      result.listMethodsConfig = data?.methods || [];
      result.discountInfo = data?.discountInfo || [];
      result.benefits = data?.benefits || [];
      break;
    case ACTION_TYPE.GET_INFO_TRANSACTION:
      result.infoTransaction = data;
      break;
    case ACTION_TYPE.CONFIRM_RESULT_TRANSACTION:
      result.confirmResultTransaction = data;
      break;
    case ACTION_TYPE.GET_LIST_TOKENS_SAVED:
      result.tokensSaved = data;
      break;
    case ACTION_TYPE.CHECK_FIRST_PAY:
      result.checkFirstPay = data;
      break;
    case ACTION_TYPE.SELECT_TOKEN_SAVED:
      result.tokenSelected = data;
      break;
    case ACTION_TYPE.GET_PACKAGES:
      result.packages = data;
      break;
    case ACTION_TYPE.SELECT_PACKAGE:
      result.selectedPackage = data;
      break;
    case ACTION_TYPE.SELECT_TERM:
      result.selectedTerm = data;
      break;
    case ACTION_TYPE.SELECT_METHOD:
      result.selectedMethod = data;
      break;
    case ACTION_TYPE.SET_BILLING_INFO:
      result.billingInfo = { ...result.billingInfo, ...data };
      break;
    case ACTION_TYPE.SET_CARD_INFO:
      result.cardInfo = { ...result.cardInfo, ...data };
      break;
    case ACTION_TYPE.SET_CARD_INFO_CAKE:
      result.cardInfoCake = { ...result.cardInfoCake, ...data };
      break;
    case ACTION_TYPE.GET_VN_PAY_LIST:
      result.vnPayList = data;
      break;
    case ACTION_TYPE.SET_BANK:
      result.bank = data;
      break;
    case ACTION_TYPE.SET_PROMOTION_CODE:
      result.promotionData = { promotionCode: data };
      break;
    case ACTION_TYPE.SET_PROMOTION_DATA:
      result.promotionData = data;
      break;
    case ACTION_TYPE.RESET_VALUE_REFERRAL_CODE:
      result.valueReferralCode = null;
      break;
    case ACTION_TYPE.RESET_PROMOTION_DATA:
      result.promotionData = null;
      break;
    case ACTION_TYPE.CREATE_TRANSACTION:
      result.transactionCreated = data;
      break;
    case ACTION_TYPE.SET_TRANSACTION_RESULT:
      result.transactionResult = data;
      break;
    case ACTION_TYPE.SET_LINKED_ZALO_PAY:
      result.zaloPayLinked = data;
      break;
    case ACTION_TYPE.GET_LINKED_SHOPEE_PAY:
      result.shopeePayLinked = data;
      break;
    case ACTION_TYPE.GET_TVOD_INFO:
      result.tvodInfo = data;
      break;
    case ACTION_TYPE.CLEAR_TVOD_INFO:
      result.tvodInfo = null;
      break;
    case ACTION_TYPE.CLEAR_TVOD_OFFER:
      result.tvodOffer = null;
      break;
    case ACTION_TYPE.GET_TVOD_OFFER:
      result.tvodOffer = data;
      break;
    case ACTION_TYPE.GET_PVOD_INFO:
      result.pvodInfo = data;
      break;
    case ACTION_TYPE.CLEAR_PVOD_INFO:
      result.pvodInfo = null;
      break;
    case ACTION_TYPE.CLEAR_PVOD_OFFER:
      result.pvodOffer = null;
      break;
    case ACTION_TYPE.GET_PVOD_OFFER:
      result.pvodOffer = data;
      break;
    case ACTION_TYPE.GET_PACKAGE_DISCOUNT:
      result.packageDiscount = data;
      break;
    case ACTION_TYPE.CLEAR_METHOD_CONFIG:
      result.listMethodsConfig = null;
      break;
    case ACTION_TYPE.SET_VALUE_REFERRAL_CODE:
      result.valueReferralCode = data;
      break;
    case ACTION_TYPE.GET_CAMPAIGN:
      result.dataCampaign = data;
      break;
    case ACTION_TYPE.STATUS_LOGIN_PAYMENT:
      result.isLoginPaymentSuccess = data;
      break;
    case ACTION_TYPE.CHECK_STATUS_LOGIN_PROMOTION:
      result.isLoginPromotionSuccess = data;
      break;
    case ACTION_TYPE.RECURRING_STATUS_PAYMENT:
      result.isRecurring = data;
      break;
    case ACTION_TYPE.COLLAPSE_FOOTER_RECOMMEND:
      result.isCollapseFooterRecommend = data;
      break;
    default:
      break;
  }

  return result;
};

export default payment;
