import { ACTION_TYPE } from '../../store/actions/actionType';
import initialState from './initialState';
const profileReducer = (state: any = initialState.profile, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.GET_PROFILE:
      return { ...state, profile: data };
    case ACTION_TYPE.RESTORE_ACCOUNT:
      return { ...state, restoreAccount: data };
    default:
      return state;
  }
};

export default profileReducer;
