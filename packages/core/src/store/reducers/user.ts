import {
  USER_REPORT,
  OFF_BANNER_VIP_PRIVILEGE,
  USER_FEEDBACK,
  USER_NOTIFY_COMINGSOON,
  USER_UNSUBCRIBE_NOTIFY_COMINGSOON,
  USER_SUBCRIBE_NOTIFY_COMINGSOON,
  <PERSON><PERSON><PERSON>_CONFIG,
  DEVICE_ID,
  MSG_CONFIG,
  USER_TYPE,
  STATIC_LINK,
  CONFIG_PHIM_HAY,
  CONFIG_PHIM_LE,
  PURCHASED_PAYMENT,
  USER_PACKAGE_INFO
} from '../../store/actions/user';
import { ACTION_TYPE } from '../../store/actions/actionType';
import initialState from './initialState';

const userReducer = (state: any = initialState.user, action: any) => {
  const data = action?.data;
  const type = action?.type;
  const result: any = { ...state };
  const dataPayload = action?.payload?.data;
  switch (type) {
    case USER_REPORT:
      result[action.type] = dataPayload;
      break;
    case USER_NOTIFY_COMINGSOON:
    case USER_UNSUBCRIBE_NOTIFY_COMINGSOON:
    case USER_SUBCRIBE_NOTIFY_COMINGSOON:
    case USER_FEEDBACK:
      result[action.type] = dataPayload;
      break;
    case OFF_BANNER_VIP_PRIVILEGE:
    case DEVICE_ID:
    case MSG_CONFIG:
    case USER_TYPE:
    case POPUP_CONFIG:
    case CONFIG_PHIM_HAY:
    case CONFIG_PHIM_LE:
    case PURCHASED_PAYMENT:
    case STATIC_LINK: {
      result[action.type] = action?.payload;
      break;
    }
    case ACTION_TYPE.TRANSACTION: {
      const metadata = dataPayload?.metadata || {};
      const items = [...(result.transactions?.data?.items || []), ...(dataPayload?.items || [])];
      const header =
        dataPayload?.tableData?.header || result.transactions?.data?.tableData?.header || [];
      const body = [
        ...(result.transactions?.data?.tableData?.body || []),
        ...(dataPayload?.tableData?.body || [])
      ];
      const tableData = { header, body };
      result.transactions = { data: { items, metadata, tableData } };
      break;
    }
    case ACTION_TYPE.CLEAR_TRANSACTION: {
      result.transactions = {};
      break;
    }
    case ACTION_TYPE.USER_TVOD_INFO: {
      const { isReminder, typeSingleTVod, isPreOrder, typeSingleTVodPreOder } = data || {};
      if (isReminder) {
        result.dataTvod = {
          ...result.dataTvod,
          dataReminder: data?.dataReminder,
          dataPreOrder: data?.dataPreOrder,
          typeSingleTVod
        };
      } else if (isPreOrder) {
        result.dataTvod = {
          ...result.dataTvod,
          dataPreOrder: data?.dataPreOrder,
          dataReminder: data?.dataReminder,
          typeSingleTVodPreOder
        };
      } else {
        result.dataTvod = {
          ...result.dataTvod,
          items: data?.items
        };
      }
      result.dataTvod = { ...result.dataTvod, loadedData: true };
      break;
    }
    case ACTION_TYPE.TVOD_REMINDER_PRE_ODER_INFO: {
      const { typeSingleTVodReminderPreOder } = data || {};
      result.dataTvodPreOder = {
        ...result?.dataTvodPreOder,
        dataReminderPreOder: data?.dataReminderPreOder,
        typeSingleTVodReminderPreOder
      };
      break;
    }
    case ACTION_TYPE.SHOW_TVOD:
      result.enableTVod = data;
      break;
    case USER_PACKAGE_INFO:
      result[action.type] = action.payload;
      break;
    case ACTION_TYPE.GET_PREORDER_REMINDER:
      result.preorderReminder = data;
      break;
    case ACTION_TYPE.GET_REFERRAL_CODE:
      result.referralProgCode = data;
      break;
    case ACTION_TYPE.GET_OTP_UPDATE_PASSWORD:
      result.otpUpdatePassword = data;
      break;
    case ACTION_TYPE.CHECK_OTP_UPDATE_PASSWORD:
      result.confirmedOtpUpdatePassword = data;
      break;
    case ACTION_TYPE.GET_DEVICES_MANAGEMENT:
      result.devicesManagement = data;
      break;
    case ACTION_TYPE.SET_HEIGHT_HEADER:
      result.heightHeader = data;
      break;
    case ACTION_TYPE.GET_SEGMENT_USER:
      result.dataSegment = data;
      break;
    case ACTION_TYPE.SEE_ALL_VOUCHER:
      result.loyalty.seeAllStatus = data;
      break;
    case ACTION_TYPE.INFO_REDEEM_DATA:
      result.loyalty.infoRedeemData = data;
      break;
    case ACTION_TYPE.GET_INFO_LOYALTY:
      result.loyalty.info = data;
      break;
    case ACTION_TYPE.GET_TIER_BENEFITS:
      result.loyalty.tierBenefits = data;
      break;
    case ACTION_TYPE.GET_EARNING_ACTIVITIES:
      result.loyalty.earningActivityHistory = data;
      break;
    case ACTION_TYPE.GET_USED_POINTS_HISTORY:
      result.loyalty.usedPointHistory = data;
      break;
    case ACTION_TYPE.GET_ACTIVITIES:
      result.loyalty.earningActivities = data;
      break;
    case ACTION_TYPE.GET_VOUCHERS:
      result.loyalty.vouchers = data;
      break;
    case ACTION_TYPE.GET_VOUCHERS_BY_CATEGORY:
      result.loyalty.vouchersByCategory = data;
      break;
    case ACTION_TYPE.REDEEM_VOUCHER:
      result.loyalty.redeemVoucher = data;
      break;
    case ACTION_TYPE.CLEAR_REDEEM_CODE:
      result.loyalty.redeemVoucher = {};
      break;
    case ACTION_TYPE.GET_FAIL_INFO_LOYALTY:
      result.loyalty.isGetFailInfoLoyalty = true;
      break;
    case ACTION_TYPE.SUB_ITEM_CATEGORY:
      result.loyalty.subItemCategory = data;
      break;
    case ACTION_TYPE.GET_DEVICE_TOKEN:
      result.loyalty.deviceTokenData = data;
      break;
    case ACTION_TYPE.SET_STATUS_REPAY_CONVERSION:
      result.isStatusRepay = data;
      break;
    case ACTION_TYPE.IS_TRIGGER_FIRST_PAY:
      result.isTriggerFirstPay = action?.payload;
      break;
    case ACTION_TYPE.CANCEL_FIRST_PAY:
      result.isCancelFirstPay = action?.payload?.isCancelFirstPay;
      break;
    default:
      break;
  }
  return result;
};

export default userReducer;
