import get from 'lodash/get';
import uniqWith from 'lodash/uniqWith';
import isEqual from 'lodash/isEqual';
import { ACTION_TYPE } from '../../store/actions/actionType';
import { LIMIT_ITEMS_RIBBON_FOR_SHOW } from '../../constants/constants';
import initialState from './initialState';

const pageReducer = (state: any = initialState.page, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.SET_PAGE_BANNER: {
      const pageSlug = data?.pageSlug || '';
      let pageBanner = {};
      if (pageSlug) {
        pageBanner = { ...state?.pageBanner, [pageSlug]: data };
      }
      return { ...state, pageBanner };
    }
    case ACTION_TYPE.SET_PAGE_RIBBON: {
      const pageSlug = data?.pageSlug || data?.id;
      const pageRibbon = pageSlug ? { ...state?.pageRibbon, [pageSlug]: data?.data } : {};
      return { ...state, pageRibbon };
    }
    case ACTION_TYPE.SET_RIBBON_DATA: {
      const ribbonSlug = data?.ribbonSlug || data?.id;
      const ribbonData = { ...state?.ribbonData, [ribbonSlug]: data };
      if (state?.ribbonData?.[ribbonSlug]) {
        // add more items infinity loop
        if (data?.isLoadmore) {
          const dataItemsAdded = [
            ...get(state, `ribbonData.${ribbonSlug}.items`, []),
            ...(data?.items || [])
          ];
          let listUniq = uniqWith(dataItemsAdded, isEqual);
          if (listUniq.length > LIMIT_ITEMS_RIBBON_FOR_SHOW) {
            listUniq = listUniq.filter((_, i) => i < LIMIT_ITEMS_RIBBON_FOR_SHOW);
          }
          ribbonData[ribbonSlug].items = listUniq;
        }
      }
      return { ...state, ribbonData };
    }
    case ACTION_TYPE.GET_LIVESTREAM_EVENTS: {
      const eventSlug = data?.slug || data?.seo?.url;
      const eventsData = data;
      return { ...state, eventsData, [eventSlug]: data };
    }
    case ACTION_TYPE.GET_TIP_DATA_SUCCESS: {
      const id = data?.id;
      return { ...state, tipData: { ...(state?.tipData || {}), [id]: data } };
    }
    case ACTION_TYPE.ADD_MY_LIST: {
      const id = data?.contentId;

      if (!state?.tipData?.[id]) return { ...state };
      const newTip = {
        ...state.tipData,

        [id]: { ...state?.tipData?.[id], isWatchLater: !state?.tipData?.[id]?.isWatchLater }
      };
      return { ...state, tipData: newTip };
    }
    case ACTION_TYPE.SET_SUBSCRIBE_COMING_SOON: {
      const id = data?.contentId;
      const isSubscribe = data?.isSubscribe;

      if (!state?.tipData?.[id]) return { ...state };

      const newTip = { ...state.tipData, [id]: { ...state?.tipData?.[id], isSubscribe } };
      return { ...state, tipData: newTip };
    }
    case ACTION_TYPE.SEO_TEMPLATE_CONFIG: {
      return { ...state, seoTemplateConfig: data };
    }
    case ACTION_TYPE.GET_SEO_ALL_PAGE: {
      return { ...state, dataSEOAllPage: data };
    }
    case ACTION_TYPE.SET_IS_MASTER_BANNER: {
      return { ...state, isMasterBanner: data };
    }

    case ACTION_TYPE.GET_CONTENT_BROADCASTING_PENDING: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          isComingSoonLoading: data
        }
      };
    }

    case ACTION_TYPE.GET_CONTENT_BROADCASTING: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          dataContentBroadcasting: data?.data,
          totalBroadcasting: data?.data?.metadata?.total
        }
      };
    }

    case ACTION_TYPE.GET_CONTENT_BROADCASTING_MORE: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          dataContentBroadcasting: {
            ...state.comingSoon.dataContentBroadcasting,
            items: [...state.comingSoon.dataContentBroadcasting.items, ...data?.data?.items]
          },
          totalBroadcasting: data?.data?.metadata?.total
        }
      };
    }

    case ACTION_TYPE.GET_CONTENT_UP_COMING_SOON: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          dataContentUpComingSoon: data?.data,
          totalUpComingSoon: data?.data?.metadata?.total
        }
      };
    }

    case ACTION_TYPE.GET_CONTENT_UP_COMING_SOON_PENDING: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          isComingSoonLoading: data
        }
      };
    }
    case ACTION_TYPE.GET_CONTENT_UP_COMING_SOON_MORE: {
      return {
        ...state,
        comingSoon: {
          ...state.comingSoon,
          dataContentUpComingSoon: {
            ...state.comingSoon.dataContentUpComingSoon,
            items: [...state.comingSoon.dataContentUpComingSoon.items, ...data?.data?.items]
          },
          totalUpComingSoon: data?.data?.metadata?.total
        }
      };
    }
    case ACTION_TYPE.CLEAR_RIBBON_DATA: {
      return { ...state, ribbonData: {} };
    }
    default:
      return state;
  }
};
export default pageReducer;
