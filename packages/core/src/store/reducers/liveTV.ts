import { produce } from 'immer';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import keys from 'lodash/keys';
import filter from 'lodash/filter';
import isEqual from 'lodash/isEqual';
import omit from 'lodash/omit';
import { setDataIsNotifyForEpg, setListEpgsInListDateFilter } from '../../services/liveTVServices';
import { ACTION_TYPE } from '../../store/actions/actionType';
import { RIBBON_TYPE } from '../../constants/constants';

const INITIAL_STATE = {
  activeCategory: {},
  activeCategoryFullscreen: {},
  activeFilterChannelPage: {},
  activeFilterChannelPageFullscreen: {},
  activeSubCategoryDSK: {},
  activeSubCategoryDSKFullscreen: {},
  activeSubCategoryDPS: {},
  activeSubCategoryDPSFullscreen: {},
  activeEpg: {},
  listSubCategoriesDSK: {},
  listSubCategoriesDPS: [],
  listFilterChannelPage: [],
  listChannels: {},
  listFavoriteChannels: [],
  listWatchedChannels: [],
  listAllChannels: {},
  listRibbonNotFound: {},
  detailChannel: {},
  listEpgsOfChannel: {},
  validateKPlus: {}
};

const liveTVReducer = produce((draft: any, action: any) => {
  const data = action?.data;
  switch (action.type) {
    case ACTION_TYPE.VALIDATE_K_PLUS: {
      draft.validateKPlus = data;
      break;
    }
    case ACTION_TYPE.RESET_VALIDATE_K_PLUS: {
      draft.validateKPlus = {};
      break;
    }
    case ACTION_TYPE.SET_ACTIVE_CATEGORY: {
      if (data?.isFullscreen) {
        draft.activeCategoryFullscreen = data?.category;
      } else {
        draft.activeCategory = data?.category;
      }
      break;
    }
    case ACTION_TYPE.SET_ACTIVE_EPG:
      draft.activeEpg = data;
      break;
    case ACTION_TYPE.SET_ACTIVE_FILTER_CHANNEL_PAGE: {
      if (data?.isFullscreen) {
        draft.activeFilterChannelPageFullscreen = data?.filter;
      } else {
        draft.activeFilterChannelPage = data?.filter;
      }
      break;
    }
    case ACTION_TYPE.SET_ACTIVE_SUB_CATEGORY: {
      const dataResult = data?.data;
      const dataCategory = data?.category;
      if (data?.isFullscreen) {
        if (dataCategory === 'DSK') {
          draft.activeSubCategoryDSKFullscreen = dataResult;
        } else if (dataCategory === 'DPS') {
          draft.activeSubCategoryDPSFullscreen = dataResult;
        }
      } else if (dataCategory === 'DSK') {
        draft.activeSubCategoryDSK = dataResult;
      } else if (dataCategory === 'DPS') {
        draft.activeSubCategoryDPS = dataResult;
      }
      break;
    }
    case ACTION_TYPE.GET_LIST_SUB_CATEGORIES_OF_CHANNEL_LIST:
      if (isEmpty(draft?.listSubCategoriesDSK?.[data?.id]) && isEmpty(data?.data)) break;
      else {
        draft.listSubCategoriesDSK = {
          ...draft.listSubCategoriesDSK,
          [data?.id]: data?.data
        };
        break;
      }
    case ACTION_TYPE.GET_LIST_SUB_CATEGORIES_OF_BROADCASTING:
      draft.listSubCategoriesDPS = data;
      break;
    case ACTION_TYPE.GET_LIST_FILTER_CHANNEL_PAGE:
      if (isEmpty(draft?.listFilterChannelPage) && isEmpty(data)) break;
      else {
        draft.listFilterChannelPage = data;
        break;
      }
    case ACTION_TYPE.GET_LIST_ALL_CHANNELS:
      draft.listAllChannels = data;
      break;
    case ACTION_TYPE.GET_LIST_CHANNELS: {
      const currentList = get(draft.listChannels, data?.id, {});
      if (isEmpty(currentList)) {
        draft.listChannels = {
          ...draft.listChannels,
          [data?.id]: data?.data
        };
      } else {
        const nextData = data?.data;
        const currentMetadata = get(currentList, 'metadata', {});
        const nextMetadata = get(nextData, 'metadata', {});
        if (isEqual(currentMetadata, nextMetadata)) {
          draft.listChannels = {
            ...draft.listChannels,
            [data?.id]: nextData
          };
        } else {
          const currentPage = get(currentMetadata, 'page', 0);
          const nextPage = get(nextMetadata, 'page', 0);
          if (nextPage === currentPage + 1) {
            draft.listChannels = {
              ...draft.listChannels,
              [data?.id]: {
                ...currentList,
                ...nextData,
                items: [...(currentList?.items || []), ...get(nextData, 'items', [])]
              }
            };
          } else if (nextPage === 0) {
            draft.listChannels = {
              ...draft.listChannels,
              [data?.id]: nextData
            };
          }
        }
      }
      break;
    }
    case ACTION_TYPE.GET_LIST_FAVORITE_CHANNEL: {
      const idListSub = get(draft.listFilterChannelPage, '[0].id', '');
      const listSubFirstFilter = get(draft.listSubCategoriesDSK, idListSub, []);
      const subCategoryFavoriteChannel =
        find(listSubFirstFilter, ['type', RIBBON_TYPE.FAVORITE_LIVE_TV]) || {};
      draft.listFavoriteChannels = data.map((item: any) => ({
        ...item,
        ribbonName: subCategoryFavoriteChannel.name,
        ribbonOrder: subCategoryFavoriteChannel.index,
        ribbonId: subCategoryFavoriteChannel.id
      }));
      break;
    }
    case ACTION_TYPE.GET_LIST_WATCHED_CHANNEL: {
      const idListSub = get(draft.listFilterChannelPage, '[0].id', '');
      const listSubFirstFilter = get(draft.listSubCategoriesDSK, idListSub, []);
      const subCategoryWatchedChannel =
        find(listSubFirstFilter, ['type', RIBBON_TYPE.WATCHED_LIST]) || {};
      draft.listWatchedChannels = data.map((item: any) => ({
        ...item,
        ribbonName: subCategoryWatchedChannel.name,
        ribbonOrder: subCategoryWatchedChannel.index,
        ribbonId: subCategoryWatchedChannel.id
      }));
      break;
    }
    case ACTION_TYPE.CLEAR_DETAIL_CHANNEL: {
      draft.detailChannel = {};
      break;
    }
    case ACTION_TYPE.GET_DETAIL_CHANNEL_BY_ID:
    case ACTION_TYPE.GET_DETAIL_CHANNEL_BY_SLUG:
      draft.detailChannel = data;
      break;
    case ACTION_TYPE.GET_LIST_EPGS: {
      const {
        idChannel,
        listFilterDate,
        listEpgs,
        strDate,
        liveEpg,
        nextEpg,
        comingSoonListIdEpgs
      }: any = data;
      if (!isEmpty(listFilterDate) && idChannel !== get(draft.listEpgsOfChannel, 'idChannel', '')) {
        const listFilterUpdate = setListEpgsInListDateFilter(listFilterDate, listEpgs, strDate);
        draft.listEpgsOfChannel = {
          ...omit(data, ['listEpgs', 'strDate']),
          listFilterDate: listFilterUpdate
        };
      } else {
        const listFilterUpdate = setListEpgsInListDateFilter(
          get(draft.listEpgsOfChannel, 'listFilterDate', []),
          listEpgs,
          strDate
        );
        let dataUpdate: any = {
          listFilterDate: listFilterUpdate,
          comingSoonListIdEpgs: !isEmpty(comingSoonListIdEpgs) ? comingSoonListIdEpgs : []
        };
        if (!isEmpty(liveEpg)) {
          dataUpdate = { ...dataUpdate, liveEpg };
        }
        if (!isEmpty(nextEpg)) {
          dataUpdate = { ...dataUpdate, nextEpg };
        }
        draft.listEpgsOfChannel = {
          ...draft.listEpgsOfChannel,
          ...dataUpdate
        };
      }
      break;
    }
    case ACTION_TYPE.GET_QNET_INFO_OF_CHANNEL:
      draft.detailChannel = { ...draft.detailChannel, qnetInfo: data };
      break;
    case ACTION_TYPE.SET_FAVORITE_CHANNEL: {
      if (draft.detailChannel?.id === data?.id) {
        draft.detailChannel = {
          ...draft.detailChannel,
          isFavorite: !data?.isFavorite
        };
      }
      if (
        data?.isFavorite &&
        (!isEmpty(draft.listFavoriteChannels) || draft.listFavoriteChannels.length > 0)
      ) {
        draft.listFavoriteChannels = filter(
          draft.listFavoriteChannels,
          (item) => item.id !== data?.id
        );
      }
      if (!isEmpty(draft.listWatchedChannels) || draft.listWatchedChannels?.length > 0) {
        draft.listWatchedChannels = draft.listWatchedChannels.map((item: any) => {
          if (item.id === data?.id) {
            return {
              ...item,
              isFavorite: !data?.isFavorite
            };
          }
          return item;
        });
      }
      const listKeyInListChannels = keys(draft.listChannels);
      if (!isEmpty(listKeyInListChannels)) {
        let listChannelsRemake = draft.listChannels;
        listKeyInListChannels.map((key) => {
          listChannelsRemake = {
            ...listChannelsRemake,
            [key]: {
              ...listChannelsRemake[key],

              items: draft.listChannels?.[key]?.items.map((item: any) => {
                if (item.id === data?.id) {
                  return {
                    ...item,
                    isFavorite: !data?.isFavorite
                  };
                }
                return item;
              })
            }
          };
        });
        draft.listChannels = listChannelsRemake;
      }
      break;
    }
    case ACTION_TYPE.GET_CHANNEL_RIBBON_NOTFOUND:
      draft.listRibbonNotFound = data;
      break;
    case ACTION_TYPE.GET_INFO_NOTIFY_LIST_EPGS: {
      if (!isEmpty(data)) {
        draft.listEpgsOfChannel = {
          ...draft.listEpgsOfChannel,
          comingSoonListIdEpgs: [],
          listFilterDate: setDataIsNotifyForEpg(
            data,
            get(draft.listEpgsOfChannel, 'listFilterDate', [])
          )
        };
      }
      break;
    }
    case ACTION_TYPE.SET_NOTIFY_COMING_SOON_EPG: {
      if (data?.success && !isEmpty(data?.data)) {
        draft.listEpgsOfChannel = {
          ...draft.listEpgsOfChannel,
          listFilterDate: setDataIsNotifyForEpg(
            data?.data,
            get(draft.listEpgsOfChannel, 'listFilterDate', [])
          )
        };
      }
      break;
    }
    default:
      break;
  }
}, INITIAL_STATE);

export default liveTVReducer;
