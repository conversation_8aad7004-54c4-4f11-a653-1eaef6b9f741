import {
  ACTION_TPBANK_CHECK_PHONE_NUMBER,
  ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE,
  ACTION_TPBANK_LOGIN_BY_PHONE,
  ACTION_TPBANK_GET_BILLING_PACKAGE,
  ACTION_TPBANK_UPDATECONFIG
} from '../../store/actions/tpbank';

const tpbankReducer = (state: any = {}, { type, data }: any) => {
  switch (type) {
    case ACTION_TPBANK_CREATE_ACCOUNT_BY_PHONE:
    case ACTION_TPBANK_LOGIN_BY_PHONE:
    case ACTION_TPBANK_CHECK_PHONE_NUMBER: {
      return { ...state, userInfo: { ...state.userInfo, ...data } };
    }
    case ACTION_TPBANK_GET_BILLING_PACKAGE: {
      return { ...state, userPackage: data };
    }
    case ACTION_TPBANK_UPDATECONFIG: {
      return { ...state, config: { ...state.config, ...data } };
    }
    default:
      return state;
  }
};
export default tpbankReducer;
