import { ACTION_TYPE } from '../../store/actions/actionType';
import { POSITION } from '../../constants/constants';
import initialState from './initialState';

const appReducer = (state: any = initialState.app, action: any) => {
  const data = action?.data;
  const type = action?.type;

  switch (type) {
    case ACTION_TYPE.SET_VIEW_PORT:
      return { ...state, isMobileViewPort: data };
    case ACTION_TYPE.SET_DATA_INTRO_PACKAGES:
      return { ...state, introPackages: data };
    case ACTION_TYPE.SET_PERSONALIZATION_FLOW:
      return { ...state, configPersonalizationFlow: data };
    case ACTION_TYPE.SET_FEATURES_FLAG:
      return { ...state, featureFlag: data };
    case ACTION_TYPE.SET_DEVICE_INFO: {
      const {
        isMobile,
        isTablet,
        isSafari,
        isAndroid,
        isIOS,
        isMacOs,
        isMobileOnly,
        deviceModel,
        deviceName,
        deviceType,
        isSmartTvPlatform,
        isWindows,
        userAgent,
        isWebPSupported
      } = data || {};
      return {
        ...state,
        isMobile,
        isTablet,
        isSafari,
        isIOS,
        isMacOs,
        isAndroid,
        isMobileOnly,
        deviceModel,
        deviceName,
        deviceType,
        isWindows,
        isSmartTvPlatform,
        userAgent,
        isWebPSupported
      };
    }
    case ACTION_TYPE.SET_TOKEN: {
      return { ...state, token: data };
    }
    case ACTION_TYPE.SET_TOKEN_PROFILE:
      return { ...state, tokenProfile: data };
    case ACTION_TYPE.GET_SEO_CONFIG: {
      return { ...state, seoText: { ...state.seoText, ...data } };
    }
    case ACTION_TYPE.SET_DEVICE_ID: {
      return { ...state, deviceId: data };
    }
    case ACTION_TYPE.SET_LOADING: {
      return { ...state, loading: data };
    }
    case ACTION_TYPE.SET_LOADED_DATA: {
      return { ...state, isLoadedData: data };
    }
    case ACTION_TYPE.GET_FAQS: {
      return { ...state, faqs: data };
    }
    case ACTION_TYPE.GET_ABOUT_US: {
      const { aboutUs }: any = state;
      return { ...state, aboutUs: { ...aboutUs, [data.key]: data.data } };
    }
    case ACTION_TYPE.GET_USAGE: {
      return { ...state, usage: data };
    }
    case ACTION_TYPE.GET_USAGE_V1: {
      return { ...state, usageV1: data };
    }
    case ACTION_TYPE.GET_USAGE_V2: {
      return { ...state, usageV2: data };
    }
    case ACTION_TYPE.GET_USAGE_V3: {
      return { ...state, usageV3: data };
    }
    case ACTION_TYPE.GET_USAGE_V4: {
      return { ...state, usageV4: data };
    }
    case ACTION_TYPE.WEB_ANNOUNCE: {
      return { ...state, announce: data };
    }
    case ACTION_TYPE.GET_AGREEMENT: {
      return { ...state, agreement: data };
    }
    case ACTION_TYPE.GET_AGREEMENT_120424: {
      return { ...state, agreement120424: data };
    }
    case ACTION_TYPE.GET_PRIVACY: {
      return { ...state, privacy: data };
    }
    case ACTION_TYPE.GET_PRIVACY_120424: {
      return { ...state, privacy120424: data };
    }
    case ACTION_TYPE.GET_LICENSE: {
      return { ...state, license: data };
    }
    case ACTION_TYPE.WEB_REGULATION: {
      return { ...state, regulation: data };
    }
    case ACTION_TYPE.WEB_POLICY_CANCELLATION: {
      return { ...state, policyCancellation: data };
    }
    case ACTION_TYPE.GEO_CHECK: {
      return { ...state, geoCheck: data };
    }
    case ACTION_TYPE.SET_TOAST: {
      const { toastData }: any = state;
      const position = data?.position || POSITION.TOP_RIGHT;

      const focusToast = [...(toastData?.[position] || toastData?.topRight || [])];
      return { ...state, toastData: { ...toastData, [position]: [...focusToast, data] } };
    }
    case ACTION_TYPE.CLEAR_TOAST: {
      const { toastData }: any = state;
      const position = data?.position || POSITION.TOP_RIGHT;

      const focusToast = [...(toastData?.[position] || toastData?.topRight || [])];
      focusToast.shift();
      return { ...state, toastData: { ...toastData, [position]: [...focusToast] } };
    }
    case ACTION_TYPE.SET_SOCIAL_CONFIG: {
      return { ...state, social: data };
    }
    case ACTION_TYPE.SET_BIND_ACCOUNT_CONFIG: {
      return { ...state, bindAccount: data };
    }
    case ACTION_TYPE.SET_REGISTRATION_TRIGGER_CONFIG: {
      return { ...state, registrationTrigger: data };
    }
    case ACTION_TYPE.SET_PAYMENT_CONVERSION_CONFIG: {
      return { ...state, paymentConversion: data };
    }
    case ACTION_TYPE.WEB_CONFIG: {
      return { ...state, webConfig: data };
    }
    case ACTION_TYPE.SET_OUT_STREAM_ADS: {
      return { ...state, outStreamAds: { ...state?.outStreamAds, ...data } };
    }
    case ACTION_TYPE.SET_FLOAT_BUTTON: {
      return { ...state, floatButton: data };
    }
    case ACTION_TYPE.SET_HIDE_CLICK_LATER_BIND_ACCOUNT: {
      return { ...state, hideClickLater: data };
    }
    case ACTION_TYPE.SET_TOKEN_EXPIRED: {
      return { ...state, isTokenExpired: data };
    }
    case ACTION_TYPE.OFF_BIND_ACCOUNT: {
      return { ...state, offBindAccount: data };
    }
    case ACTION_TYPE.TVOD: {
      return { ...state, tVod: data };
    }
    case ACTION_TYPE.SET_STATUS_LOAD_OUT_STREAM_ADS: {
      return { ...state, statusLoadOutStreamAds: data };
    }
    case ACTION_TYPE.SET_STATUS_ONBOARDING: {
      return { ...state, closeOnboarding: data };
    }
    case ACTION_TYPE.SET_STATUS_DOWNLOAD_APP: {
      return { ...state, offDownloadApp: data };
    }
    case ACTION_TYPE.SET_STATUS_DIALOG_ONBOARDING: {
      return { ...state, enableDialogOnboarding: data };
    }
    case ACTION_TYPE.SET_STATUS_PAYMENT_CONVERSION: {
      return { ...state, enablePaymentConversion: data };
    }
    case ACTION_TYPE.SET_STATUS_TVOD_REMINDER_SCREEN: {
      return { ...state, enableTVodReminderScreen: data };
    }
    case ACTION_TYPE.SET_STATUS_LOAD_MASTHEAD_ADS: {
      return { ...state, statusMasthead: data };
    }
    case ACTION_TYPE.SET_STATUS_COMPANION_BANNER: {
      return { ...state, isHasCompanionBanner: data };
    }
    default:
      return state;
  }
};

export default appReducer;
