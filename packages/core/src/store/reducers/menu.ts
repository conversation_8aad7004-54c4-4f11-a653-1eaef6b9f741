import { getActiveMenuBySlug } from '../../services/menuServices';
import { ACTION_TYPE } from '../../store/actions/actionType';
import initialState from './initialState';

const menuReducer = (state: any = initialState.menu, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.SET_ACTIVE_MENU_FROM_PATH: {
      const menuList = state?.menuList;
      const { activeMenu, activeSubMenu, subHeader } = getActiveMenuBySlug({
        menuList,
        slug: data
      });
      return { ...state, activeMenu, activeSubMenu, subHeader };
    }
    case ACTION_TYPE.SET_MENU: {
      return { ...state, menuList: data };
    }
    case ACTION_TYPE.SET_ACTIVE_MENU: {
      return { ...state, activeMenu: data };
    }
    case ACTION_TYPE.SET_ACTIVE_SUB_MENU: {
      return { ...state, activeSubMenu: data };
    }
    case ACTION_TYPE.SET_SUB_HEADER: {
      return { ...state, subHeader: data };
    }
    case ACTION_TYPE.SET_OPEN_TAG_MENU: {
      return { ...state, enableMenu: data };
    }
    default:
      return state;
  }
};

export default menuReducer;
