import {
  ACTION_GET_INFO_ARTIST,
  ACTION_GET_CONTENT_ARTIST,
  ACTION_GET_ARTIST_RELATED
} from '../../store/actions/artist';

const artistReducer = (state: any = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action?.payload?.data;

  switch (action.type) {
    case ACTION_GET_INFO_ARTIST: {
      // khởi tạo giá trị nếu chưa tồn tại

      if (!result[action.type]) {
        result[action.type] = {};
      }

      result[action.type][action.slug] = dataPayload;
      break;
    }
    case ACTION_GET_CONTENT_ARTIST: {
      const key1 = action.slug;
      const key2 = action.sort;
      const key3 = action.page;

      if (!result[action.type]) {
        result[action.type] = {};
      }
      // nếu chưa tồn tại key1 thì khởi tạo giá trị {}

      if (!result[action.type][key1]) {
        result[action.type][key1] = {};
      }
      // nếu chưa tồn tại key2 thì khởi tạo giá trị {}

      if (!result[action.type][key1][key2]) {
        result[action.type][key1][key2] = {};
      }

      result[action.type][key1][key2][key3] = dataPayload;
      break;
    }
    case ACTION_GET_ARTIST_RELATED: {
      const key1 = action.slug;
      // khởi tạo giá trị nếu chưa tồn tại

      if (!result[action.type]) {
        result[action.type] = {};
      }

      result[action.type][key1] = dataPayload;
      break;
    }
    default:
      break;
  }
  return result;
};

export default artistReducer;
