import { ACTION_BILLING, ACTION_BILLING_GET_SERVICES, SET_VIP_INFO } from '../../store/actions/billing';

const billingReducer = (state: any = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action.payload && action.payload.data ? action.payload.data : [];
  switch (action.type) {
    case ACTION_BILLING:
    case ACTION_BILLING_GET_SERVICES:
      result[action.type] = dataPayload;
      break;
    case SET_VIP_INFO:
      result[action.type] = action.payload;
      break;
    default:
      break;
  }

  return result;
};

export default billingReducer;
