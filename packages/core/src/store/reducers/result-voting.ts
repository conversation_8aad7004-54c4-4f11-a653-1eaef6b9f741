import { ACTION_TYPE } from '../../store/actions/actionType';

const resultVotingReducer = (state = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action?.payload;

  switch (action.type) {
    case ACTION_TYPE.GET_LIST_CAMPAIGNS:
      if (!result[action.type]) {
        result[action.type] = {};
      }

      result[action.type] = dataPayload ? dataPayload.result : [];
      break;
    case ACTION_TYPE.GET_LIST_WINNERS_OF_CAMPAIGNS: {
      const { contentId } = action;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][contentId]) {
        result[action.type][contentId] = {};
      }

      result[action.type][contentId] = dataPayload ? dataPayload.result : [];
      break;
    }
    case ACTION_TYPE.GET_LIST_ROUNDS_VOTING:
      if (!result[action.type]) {
        result[action.type] = {};
      }

      result[action.type] = dataPayload ? dataPayload.results : [];
      break;
    case ACTION_TYPE.GET_LIST_EPISODES_OF_ROUND: {
      const { campaignId } = action;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][campaignId]) {
        result[action.type][campaignId] = {};
      }

      result[action.type][campaignId] = dataPayload ? dataPayload.results : [];
      break;
    }
    case ACTION_TYPE.GET_DETAIL_EPISODE_IN_ROUND: {
      const { questionId } = action;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][questionId]) {
        result[action.type][questionId] = {};
      }

      result[action.type][questionId] = dataPayload ? dataPayload.results : [];
      break;
    }
    case ACTION_TYPE.GET_LIST_RATING_OF_EPISODE: {
      const { questionId } = action;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][questionId]) {
        result[action.type][questionId] = {};
      }

      result[action.type][questionId] = dataPayload ? dataPayload.results : [];
      break;
    }
    case ACTION_TYPE.GET_FINAL_RESULT: {
      const { questionId } = action;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][questionId]) {
        result[action.type][questionId] = {};
      }

      result[action.type][questionId] = dataPayload ? dataPayload.results : [];
      break;
    }
    default:
      break;
  }
  return result;
};

export default resultVotingReducer;
