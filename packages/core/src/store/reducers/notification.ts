import { ACTION_TYPE } from '../../store/actions/actionType';
import { produce } from 'immer';

const INITIAL_STATE = {
  dataCountNotify: {},
  dataNotify: {},
  statusPolicyConfirm: {},
  dataContentPolicy: {},
  dataConfirmDetailPolicy: {},
  trackingDataNotify: {},
  isConfirmPolicyAnnounce: false
};
const notificationReducer = produce((draft: any, action: any) => {
  const data = action?.data;
  switch (action.type) {
    case ACTION_TYPE.ACTION_GET_LIST_NOTIFICATION:
      draft.dataNotify = data;
      break;
    case ACTION_TYPE.ACTION_TRACKING_NOTIFICATION:
      draft.trackingDataNotify = data;
      break;
    case ACTION_TYPE.ACTION_COUNT_NOTIFICATION:
      draft.dataCountNotify = data;
      break;
    case ACTION_TYPE.CHECK_POLICY_CONFIRM:
      draft.statusPolicyConfirm = data;
      break;
    case ACTION_TYPE.GET_DETAIL_POLICY_ANNOUNCE:
      draft.dataContentPolicy = data;
      break;
    case ACTION_TYPE.CONFIRM_DETAIL_POLICY_ANNOUNCE:
      draft.dataConfirmDetailPolicy = data;
      break;
    case ACTION_TYPE.STATUS_POLICY_CONFIRM:
      draft.isConfirmPolicyAnnounce = data;
      break;
    default:
      break;
  }
}, INITIAL_STATE);

export default notificationReducer;
