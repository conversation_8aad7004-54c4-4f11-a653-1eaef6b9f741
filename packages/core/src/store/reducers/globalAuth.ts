import { produce } from 'immer';
import get from 'lodash/get';
import {
  ACTION_GLOBAL_CONFIRM_OTP,
  ACTION_GLOBAL_FORGOT_PASSWORD,
  ACTION_GLOBAL_LOGIN,
  ACTION_GLOBAL_REGISTER,
  ACTION_GLOBAL_RESTORE_ACCOUNT,
  ACTION_GLOBAL_UPDATE_PASSWORD,
  ACTION_GLOBAL_VALIDATE_OTP,
  ACTION_LINK_PHONE_NUMBER,
  ACTION_RESET_GLOBAL_AUTH
} from '../../store/actions/actionType';

const INITIAL_STATE = {
  dataRegister: {},
  dataLogin: {},
  dataValidateOTP: {},
  dataConfirmOTP: {},
  dataForgotPassword: {},
  dataRestoreAccount: {},
  dataAccessToken: {},
  confirmationNo: '',
  dataUpdatePassword: {},
  dataLinkPhoneNumber: {}
};

const globalAuthReducer = produce((draft: any, action: any) => {
  const data = action?.data;
  switch (action.type) {
    case ACTION_RESET_GLOBAL_AUTH: {
      if (!data) {
        draft.dataRegister = {};
        draft.dataLogin = {};
        draft.dataValidateOTP = {};
        draft.dataConfirmOTP = {};
        draft.dataRestoreAccount = {};
        draft.dataForgotPassword = {};
        draft.dataUpdatePassword = {};
        draft.dataLinkPhoneNumber = {};
      } else {
        draft[data] = {};
      }
      break;
    }
    case ACTION_GLOBAL_UPDATE_PASSWORD:
      draft.dataUpdatePassword = data;
      draft.confirmationNo = get(data, 'data.result.confirmationNo', '');
      break;
    case ACTION_LINK_PHONE_NUMBER:
      draft.dataLinkPhoneNumber = data;
      draft.confirmationNo = get(data, 'data.result.confirmationNo', '');
      break;
    case ACTION_GLOBAL_RESTORE_ACCOUNT:
      draft.dataRestoreAccount = data;
      draft.confirmationNo = get(data, 'data.result.confirmationNo', '');
      draft.dataRegister = {};
      draft.dataValidateOTP = {};
      draft.dataConfirmOTP = {};
      draft.dataForgotPassword = {};
      break;
    case ACTION_GLOBAL_VALIDATE_OTP:
      draft.dataValidateOTP = data;
      break;
    case ACTION_GLOBAL_CONFIRM_OTP:
      draft.dataConfirmOTP = data;
      draft.dataAccessToken = get(data, 'data.accessToken', '');
      break;
    case ACTION_GLOBAL_LOGIN:
      draft.dataLogin = data;
      draft.dataAccessToken = get(data, 'data.accessToken', '');
      break;
    case ACTION_GLOBAL_REGISTER:
      draft.dataRegister = data;
      draft.confirmationNo = get(data, 'data.result.confirmationNo', '');
      break;
    case ACTION_GLOBAL_FORGOT_PASSWORD: {
      draft.dataForgotPassword = data;
      draft.confirmationNo = get(data, 'data.result.confirmationNo', '');
      break;
    }
    default:
      break;
  }
}, INITIAL_STATE);

export default globalAuthReducer;
