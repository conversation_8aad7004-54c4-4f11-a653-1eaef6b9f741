import initialState from '../../store/reducers/initialState';
import { ACTION_TYPE } from '../../store/actions/actionType';

const livestreamReducer = (state: any = initialState.livestream, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.CHECK_STATUS_LIVESTREAM_SUCCESS:
      return { ...state, statusEvent: data };
    case ACTION_TYPE.GET_LIVESTREAM_TVOD_INFO:
      return { ...state, tvodInfo: data };
    case ACTION_TYPE.GET_LIVESTREAM_TVOD_INFO_RESET:
      return {};
    case ACTION_TYPE.GET_EVENT_RELATED:
      return { ...state, eventRelated: data };
    default:
      return state;
  }
};

export default livestreamReducer;
