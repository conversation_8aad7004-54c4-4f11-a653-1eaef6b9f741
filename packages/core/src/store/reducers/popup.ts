import { ACTION_TYPE } from '../../store/actions/actionType';
import { controlPlayVideo, handleMasterPlayer } from '../../utils/common';
import { POPUP } from '../../constants/constants';
import initialState from './initialState';

const popupReducer = (state: any = initialState.popup, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.OPEN_CARD_HOVER:
      return { ...state, cardHover: data };
    case ACTION_TYPE.OPEN_CARD_RELATED_HOVER:
      return { ...state, cardRelatedHover: data };
    case ACTION_TYPE.OPEN_POPUP: {
      const { name, noControl, isEndFreeTrial, playerId, backToPlay, isEndScreenVod } = data || {};
      const expand = state?.previewCard?.expand;
      controlPlayVideo({
        name,
        noControl,
        playerId,
        expand,
        isEndFreeTrial,
        backToPlay,
        isEndScreenVod
      });
      const previewCard = { ...(state.previewCard || {}) };
      const configs = state?.configs;
      const popupData = name
        ? { ...state, popupName: name, ...data }
        : { popupName: data?.name, ...data };

      const currentPopupName = state?.popupName;

      return {
        ...popupData,
        popupName:
          currentPopupName === POPUP.NAME.LOCAL_TO_GLOBAL ||
          currentPopupName === POPUP.NAME.GLOBAL_TO_LOCAL
            ? currentPopupName
            : popupData?.popupName,
        configs,
        previewCard: { ...previewCard }
      };
    }
    case ACTION_TYPE.PREVIEW_CARD: {
      handleMasterPlayer(data);
      const previewCard = { ...state?.previewCard, ...data };
      return { ...state, previewCard };
    }
    case ACTION_TYPE.EXPAND_PREVIEW: {
      const previewCard = { ...state?.previewCard, ...data };
      return { ...state, previewCard };
    }
    case ACTION_TYPE.CLOSE_ALL_TAB_LIVETV: {
      return { ...state, dataCloseTabLivetv: data };
    }
    case ACTION_TYPE.POPUP_CONFIG: {
      return { ...state, configs: data };
    }
    case ACTION_TYPE.POPUP_TRIGGER_PAYMENT: {
      return { ...state, popupTriggerPayment: data };
    }
    case ACTION_TYPE.IS_PLAYER_TRIGGER: {
      return { ...state, isPlayerTrigger: data };
    }
    default:
      return state;
  }
};

export default popupReducer;
