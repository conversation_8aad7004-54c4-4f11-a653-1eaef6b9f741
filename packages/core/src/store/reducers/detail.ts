import { parseComments } from '../../services/detailServices';
import { ACTION_TYPE } from '../../store/actions/actionType';
import { ERROR_CODE } from '../../constants/constants';
import {
  GET_CONTENT_SERVICE,
  GET_CONTENT,
  RESET_CONTENT,
  GET_CONTENT_EPISODE,
  ACTION_GET_CONTENT_DETAIL,
  ACTION_GET_CONTENT_RELATED,
  ACTION_GET_CONTENT_POPVER,
  ACTION_GET_CONTENT_WATCHLATER,
  ACTION_GET_CONTENT_WATCHMORE,
  EPISODE_LIST_DATA,
  ACTION_GET_CONTENT_RELATED_VOD,
  ADD_WATCH_LATER,
  REMOVE_WATCH_LATER,
  REMOVE_WATCH_MORE,
  ACTION_DETAIL_RIBBON_NOTFOUND,
  ACTION_GET_RELATED_VOD,
  ACTION_GET_RECOMMEND_VOD,
  ACTION_GET_EPISODE_LIST_VOD,
  ACTION_GET_CONTENT_DETAIL_ID,
  GET_LIST_COMMENTS_SUCCESS,
  GET_DATA_PRODUCT_BRAND,
  SET_TYPE_ACTION_VIDEO_INDEXING,
  ACTION_GET_CONTENT_BY_SLUG_SEO,
  ACTION_GET_LIST_EPISODES_CONTENT
} from '../../store/actions/detail';

const detailReducer = (state: any = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action.payload && action.payload.data ? action.payload.data : [];

  switch (action.type) {
    case GET_LIST_COMMENTS_SUCCESS: {
      const newData = action.payload;
      const contentId = newData?.contentId;

      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][contentId]) {
        result[action.type][contentId] = {};
      }

      if (newData?.error === ERROR_CODE.CODE_1001 || newData?.error === ERROR_CODE.CODE_400) {
        result[action.type][contentId] = { blocked: true };
      } else {
        const newListItems = parseComments(newData?.items);
        const page = newData?.metadata?.page;
        if (page === 0) {
          result[action.type][contentId] = { ...newData, items: newListItems };
        } else {
          const currentData = result[action.type][contentId];
          const currentPage = currentData?.metadata?.page;
          if (page === currentPage + 1) {
            const currentListItems = currentData?.items || [];

            result[action.type][contentId] = {
              ...newData,
              items: currentListItems.concat(newListItems)
            };
          }
        }
      }

      break;
    }
    case GET_CONTENT:
    case GET_CONTENT_EPISODE: {
      const data = action?.payload?.data;

      result[action.type] = { ...data };
      break;
    }
    case RESET_CONTENT: {
      result[GET_CONTENT] = null;
      break;
    }
    //TODO: check action non define in @actions/detail.js
    case ACTION_GET_CONTENT_BY_SLUG_SEO: {
      if (!result[action.type]) {
        result[action.type] = {};
      }
      const { slug } = action;

      result[action.type][slug] = dataPayload;
      break;
    }
    case ACTION_GET_LIST_EPISODES_CONTENT: {
      if (!result[action.type]) {
        result[action.type] = {};
      }
      const { contentId } = action;

      result[action.type][contentId] = dataPayload;
      break;
    }
    case ACTION_DETAIL_RIBBON_NOTFOUND:
      result.ribbonNotFound = dataPayload;
      break;
    case ACTION_GET_CONTENT_DETAIL_ID:
    case ACTION_GET_CONTENT_DETAIL: {
      result[action.type] = dataPayload;
      break;
    }
    case ACTION_GET_CONTENT_POPVER: {
      if (!result[action.type]) {
        result[action.type] = {};
      }

      if (!result[action.type][action.contentId]) {
        result[action.type][action.contentId] = {};
      }
      // nếu là episode thì lưu bằng key epsId
      if (action.epsId) {
        result[action.type][action.contentId][action.epsId] = dataPayload;
      } else {
        // nếu là content thường thì lưu với key = -1

        result[action.type][action.contentId][-1] = dataPayload;
      }
      break;
    }
    case EPISODE_LIST_DATA: {
      result[action.type] = dataPayload;
      break;
    }
    case ACTION_GET_CONTENT_WATCHLATER: {
      const watchLaterData = { ...result.listContentsWatchLater };
      const items = watchLaterData?.items || [];
      const newItems =
        !items || items?.length === 0 || dataPayload?.metadata?.page === 0
          ? dataPayload?.items
          : (watchLaterData?.items || []).concat(dataPayload?.items || []);
      watchLaterData.items = newItems;
      watchLaterData.metadata = dataPayload?.metadata;
      watchLaterData.loadedData = true;
      result.listContentsWatchLater = watchLaterData;
      break;
    }
    case ACTION_GET_CONTENT_WATCHMORE: {
      const watchMoreData = { ...result.listContentsWatchMore };
      const items = watchMoreData?.items || [];
      const newItems =
        !items || items?.length === 0 || dataPayload?.metadata?.page === 0
          ? dataPayload?.items
          : (watchMoreData?.items || []).concat(dataPayload?.items || []);
      watchMoreData.items = newItems;
      watchMoreData.metadata = dataPayload?.metadata;
      watchMoreData.type = dataPayload?.type;
      watchMoreData.loadedData = true;
      result.listContentsWatchMore = watchMoreData;
      break;
    }
    case ADD_WATCH_LATER: {
      const watchLater = { ...result.listContentsWatchLater };
      const watchLaterItems = [...(result.listContentsWatchLater?.items || [])];
      const item = action?.data;
      if (item && item.id) {
        watchLaterItems.unshift(item);
      }
      watchLater.items = watchLaterItems;
      result.listContentsWatchLater = watchLater;
      break;
    }

    case REMOVE_WATCH_LATER: {
      const watchLater = { ...result.listContentsWatchLater };
      const watchLaterItems = [...(result.listContentsWatchLater?.items || [])];
      const item = action?.data;
      if (item && item.id) {
        const itemIndex = watchLaterItems.findIndex((x) => x.id === item.id);
        if (itemIndex !== -1) {
          watchLaterItems.splice(itemIndex, 1);
        }
      }
      watchLater.items = watchLaterItems;
      result.listContentsWatchLater = watchLater;
      break;
    }
    case REMOVE_WATCH_MORE: {
      result.listContentsWatchMore = {};
      break;
    }
    case ACTION_GET_EPISODE_LIST_VOD: {
      result[action.type] = action.payload;
      break;
    }
    case ACTION_GET_RECOMMEND_VOD:
    case ACTION_GET_RELATED_VOD: {
      result[action.type] = action.payload;
      break;
    }
    case ACTION_GET_CONTENT_RELATED:
    case ACTION_GET_CONTENT_RELATED_VOD: {
      result[action.type] = dataPayload;
      break;
    }
    case GET_CONTENT_SERVICE: {
      result.dataIndexing = dataPayload;
      break;
    }
    case ACTION_TYPE.REGISTER_CONSULTATION: {
      const listItems = [...(result.listItemsRegister || [])];
      const itemIndex = listItems.findIndex(
        (item) => item.objectId === dataPayload?.items?.[0].objectId
      );
      if (itemIndex === -1) {
        listItems.push(dataPayload?.items?.[0]);
      }
      if (!dataPayload?.items?.[0]?.isValidate && itemIndex !== -1) {
        listItems.splice(itemIndex, 1);
      }
      result.registerConsultation = dataPayload?.items;
      result.listItemsRegister = listItems;

      break;
    }
    case ACTION_TYPE.REGISTER_CONSULTATION_FAIL: {
      result.isRegisterFail = true;
      break;
    }
    case ACTION_TYPE.CLEAR_REGISTER_CONSULTATION: {
      result.listItemsRegister = [];
      break;
    }
    case ACTION_TYPE.SET_SESSION_ID_VI_INDEXING: {
      if (action.payload.data) result.sessionId = dataPayload;
      else result.sessionId = '';
      break;
    }
    case GET_DATA_PRODUCT_BRAND: {
      result.listProductBrand = dataPayload;
      break;
    }
    case SET_TYPE_ACTION_VIDEO_INDEXING: {
      result.typeActionVideoIndexing = dataPayload;
      break;
    }
    case ACTION_TYPE.GET_ITEM_INDICATOR: {
      const listItem = [...(result.itemIndicator || [])];
      const itemIndex = listItem.findIndex(
        (item) => item?.objectId === dataPayload?.data?.objectId
      );
      if (itemIndex === -1) {
        listItem.push(dataPayload?.data);
      }
      result.itemIndicator = listItem;
      break;
    }
    case ACTION_TYPE.SET_SESSION_PLAY: {
      result.concurrentScreen = action.data;
      break;
    }
    case ACTION_TYPE.REFRESH_SESSION_PLAY: {
      result.dataRefreshSession = action.data;
      break;
    }
    case ACTION_TYPE.SET_STATUS_END_SCREEN_VOD: {
      result.statusEndScreenVod = action.data;
      break;
    }
    default:
      break;
  }

  return result;
};

export default detailReducer;
