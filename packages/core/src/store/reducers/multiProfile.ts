import { produce } from 'immer';
import { ACTION_TYPE } from '../../store/actions/actionType';
import isEmpty from 'lodash/isEmpty';

const INITIAL_STATE = {
  step: '',
  multiProfile: {},
  multiProfileKid: [],
  restrictionContent: {},
  resultDeleteProfile: {},
  ageRanges: [],
  ageRangesKid: [],
  genders: [],
  avatars: [],
  resultForm: {},
  resultProfile: {},
  statusLobbyProfile: '',
  currentProfile: {},
  defaultForm: {},
  kidsActivity: {
    isLoading: false,
    watchedItems: [],
    watchedTotalItems: 0,
    isExportEmail: null
  },
  titleRestriction: {
    restrictedListOfKids: {},
    isInRemoveContentFlow: false
  }
};
const multiProfileReducer = produce((state: any, action: any) => {
  const data = action?.data;
  switch (action.type) {
    case ACTION_TYPE.SET_LOBBY_STEP:
      state.step = data || '';
      break;
    case ACTION_TYPE.DELETE_PROFILE:
      state.resultDeleteProfile = data;
      break;
    case ACTION_TYPE.GET_PROFILE_ID:
      state.resultProfile = data;
      break;
    case ACTION_TYPE.CACHE_FORM:
      state.resultForm = { ...state.resultForm, ...data };
      break;
    case ACTION_TYPE.RESET_FORM:
      state.resultForm = data;
      break;
    case ACTION_TYPE.DEFAULT_FORM:
      state.defaultForm = data;
      break;
    case ACTION_TYPE.GET_AGE_RANGES:
      state.ageRanges = data;
      break;
    case ACTION_TYPE.GET_AGE_RANGES_KID:
      state.ageRangesKid = data;
      break;
    case ACTION_TYPE.GET_GENDERS:
      state.genders = data;
      break;
    case ACTION_TYPE.GET_AVATARS:
      state.avatars = data;
      break;
    case ACTION_TYPE.SET_STATUS_LOBBY_PROFILE:
      state.statusLobbyProfile = data;
      break;
    case ACTION_TYPE.GET_MULTI_PROFILE:
      state.multiProfile = {
        ...data,
        empty: isEmpty(data?.items)
      };
      break;
    case ACTION_TYPE.GET_MULTI_PROFILE_KID:
      state.multiProfileKid = data?.items;
      break;
    case ACTION_TYPE.GET_RESTRICTION_CONTENT:
      state.restrictionContent = data;
      break;
    case ACTION_TYPE.CLEAR_PROFILE:
      state.multiProfile.empty = false;
      break;
    case ACTION_TYPE.SELECTED_PROFILE:
      state.currentProfile = data;
      break;
    case ACTION_TYPE.IS_EXPORT_EMAIL:
      state.kidsActivity.isExportEmail = data;
      break;
    case ACTION_TYPE.POST_EXPORT_EMAIL:
      state.kidsActivity.isExportEmail = data;
      break;
    case ACTION_TYPE.GET_DETAIL_WATCH:
      state.kidsActivity.watchedItems = data?.data;
      state.kidsActivity.watchedTotalItems = data.total;
      break;
    case ACTION_TYPE.GET_MORE_DETAIL_WATCH:
      state.kidsActivity.watchedItems = [...state.kidsActivity.watchedItems, ...data?.data];
      state.kidsActivity.watchedTotalItems = data.total;
      break;
    case ACTION_TYPE.GET_DETAIL_WATCH_PENDING:
      state.kidsActivity.isLoading = data;
      break;
    case ACTION_TYPE.GET_RESTRICTED_LIST_OF_KIDS:
      state.titleRestriction.restrictedListOfKids = data;
      break;
    case ACTION_TYPE.IS_REMOVE_CONTENT_FLOW:
      state.titleRestriction.isInRemoveContentFlow = data;
      break;
    case ACTION_TYPE.RESET_ALL_STATE_MULTI_PROFILE:
      state = INITIAL_STATE;
      break;
    default:
      break;
  }
}, INITIAL_STATE);

export default multiProfileReducer;
