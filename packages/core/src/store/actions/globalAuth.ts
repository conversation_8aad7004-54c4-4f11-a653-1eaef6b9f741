import UserApi from '../api/userApi';
import { handleToastWhenAccessVieON, saveAccessToken } from '../../utils/common';
import { setToken } from '../../store/actions/app';
import { getProfileSuccess } from '../../store/actions/profile';
import { getMultiProfile } from '../../store/actions/multiProfile';
import { ERROR_CODE, PROFILE_STATUS, PROVIDER } from '../../constants/constants';
import isEmpty from 'lodash/isEmpty';
import { statusLoginPayment, statusLoginPromotionCode } from '../../store/actions/payment';
import {
  ACTION_GLOBAL_CONFIRM_OTP,
  ACTION_GLOBAL_FORGOT_PASSWORD,
  ACTION_GLOBAL_LOGIN,
  ACTION_GLOBAL_REGISTER,
  ACTION_GLOBAL_RESTORE_ACCOUNT,
  ACTION_GLOBAL_UPDATE_PASSWORD,
  ACTION_GLOBAL_VALIDATE_OTP,
  ACTION_LINK_PHONE_NUMBER,
  ACTION_RESET_GLOBAL_AUTH,
  createAction
} from './actionType';
import LocalStorage from '../../config/LocalStorage';
import ConfigLocalStorage from '../../config/ConfigLocalStorage';
import { TEXT } from '../../constants/text';
import { destinationLogin } from '../../services/multiProfileServices';

const globalLinkPhoneNumber = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalLinkPhoneNumber(dataRequest).then((resp) =>
    dispatch(createAction(ACTION_LINK_PHONE_NUMBER, resp))
  );

const globalUpdatePassword = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalUpdatePassword(dataRequest).then((resp) =>
    dispatch(createAction(ACTION_GLOBAL_UPDATE_PASSWORD, resp))
  );

const globalRestoreAccount = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalRestoreAccount(dataRequest).then((resp) => {
    const accessToken = resp?.data?.accessToken;
    const profile = resp?.data?.profile;

    if (accessToken && !isEmpty(profile)) {
      saveAccessToken(accessToken, '', resp?.data?.refreshToken);
      dispatch(setToken(accessToken));
      dispatch(getProfileSuccess(profile));
      handleToastWhenAccessVieON({
        isLogin: true,
        dispatch,
        destination: dataRequest?.destination,
        isFirstLogin: resp?.data?.isFirstLogin,
        accessToken
      });
      destinationLogin({
        dataLogin: resp?.data,
        destination: dataRequest?.destination,
        router: dataRequest?.router,
        dispatch
      });
    }
    dispatch(createAction(ACTION_GLOBAL_RESTORE_ACCOUNT, resp));
  });

const loginAnonymous = (dataRequest?: any) => (dispatch: any) =>
  UserApi.loginAnonymous(dataRequest)?.then((res: any) => {
    if (res?.data?.accessToken) {
      dispatch(setToken(res?.data?.accessToken));
      dispatch(getProfileSuccess({}));
    }
    return res;
  });

const globalLoginSocial = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalLoginSocial(dataRequest).then((resp) => {
    const accessToken = resp?.data?.accessToken;
    const profile = resp?.data?.profile;
    const isBindAccount = resp?.data?.profile?.phoneRequired || resp?.data?.profile?.forceBindPhone;

    if (accessToken && !isEmpty(profile) && profile.status !== PROFILE_STATUS.WAIT_FOR_DELETE) {
      saveAccessToken(accessToken, '', resp?.data?.refreshToken);
      dispatch(setToken(accessToken));
      dispatch(getProfileSuccess(profile));
      handleToastWhenAccessVieON({
        isLogin: true,
        dispatch,
        destination: dataRequest?.destination,
        isFirstLogin: resp?.data?.isFirstLogin,
        bindAccount: dataRequest?.bindAccount && isBindAccount,
        accessToken
      });
      destinationLogin({
        dataLogin: resp?.data,
        destination: dataRequest?.destination,
        router: dataRequest?.router,
        dispatch,
        isLoginBySocial: true,
        isBindAccount: dataRequest?.bindAccount && isBindAccount
      });
    }
    ConfigLocalStorage.set(LocalStorage.LOGGED_SOCIAL_BY_PROVIDER, dataRequest.provider);
    dispatch(createAction(ACTION_GLOBAL_LOGIN, { ...resp, isLoginBySocial: true }));
  });

const globalValidateOTP = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalValidateOTP(dataRequest).then((resp) => {
    dispatch(createAction(ACTION_GLOBAL_VALIDATE_OTP, resp));
  });

const globalConfirmOTP = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalConfirmOTP(dataRequest).then((resp) => {
    const accessToken = resp?.data?.accessToken;
    const profile = resp?.data?.profile;

    if (accessToken && !isEmpty(profile) && profile.status !== PROFILE_STATUS.WAIT_FOR_DELETE) {
      saveAccessToken(accessToken, '', resp?.data?.refreshToken);
      dispatch(setToken(accessToken));
      dispatch(getProfileSuccess(profile));
      if (dataRequest?.hasToSelectProfileDefault) {
        dispatch(
          getMultiProfile({
            accessToken,
            hasToSelectProfileDefault: true
          })
        );
      }
      handleToastWhenAccessVieON({
        isLogin: !dataRequest?.isRegister,
        isRegister: dataRequest?.isRegister,
        dispatch,
        destination: dataRequest?.destination,
        isFirstLogin: resp?.data?.isFirstLogin,
        accessToken
      });
      destinationLogin({
        dataLogin: resp?.data,
        destination: dataRequest?.destination,
        router: dataRequest?.router,
        dispatch
      });
    }
    dispatch(
      createAction(ACTION_GLOBAL_CONFIRM_OTP, { ...resp, isRegister: dataRequest?.isRegister })
    );
  });

const globalLogin = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalLogin(dataRequest).then((resp) => {
    ConfigLocalStorage.set(LocalStorage.HIDE_MASTHEAD, 1);
    const accessToken = resp?.data?.accessToken;
    const profile = resp?.data?.profile;

    if (accessToken && !isEmpty(profile) && profile.status !== PROFILE_STATUS.WAIT_FOR_DELETE) {
      saveAccessToken(accessToken, '', resp?.data?.refreshToken);
      dispatch(setToken(accessToken));
      dispatch(getProfileSuccess(profile));
      handleToastWhenAccessVieON({
        isLogin: true,
        dispatch,
        destination: dataRequest?.destination,
        isFirstLogin: resp?.data?.isFirstLogin,
        accessToken
      });
      if (dataRequest?.isLoginPayment) {
        dispatch(statusLoginPayment(true));
      } else {
        destinationLogin({
          dataLogin: resp?.data,
          destination: dataRequest?.destination,
          router: dataRequest?.router,
          dispatch
        });
      }
      if (dataRequest?.isPromotionCode) {
        dispatch(statusLoginPromotionCode(true));
      }
    }
    if (
      resp.data.code === ERROR_CODE.CODE_400 &&
      (dataRequest?.isLoginPayment || dataRequest?.isPromotionCode)
    ) {
      dataRequest.setError(TEXT.ERROR_LOGIN_PAYMENT);
    }
    dispatch(createAction(ACTION_GLOBAL_LOGIN, resp));
    if (dataRequest.isLoggedByEmail) {
      ConfigLocalStorage.set(LocalStorage.LOGGED_SOCIAL_BY_PROVIDER, PROVIDER.GOOGLE);
    }
  });

const globalRegister = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalRegister(dataRequest).then((resp) =>
    dispatch(createAction(ACTION_GLOBAL_REGISTER, resp))
  );
const resetGlobalAuth = (key?: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_RESET_GLOBAL_AUTH, key));

const globalForgotPassword = (dataRequest: any) => (dispatch: any) =>
  UserApi.globalForgotPassword(dataRequest)?.then((res: any) =>
    dispatch(createAction(ACTION_GLOBAL_FORGOT_PASSWORD, res))
  );

export {
  globalLinkPhoneNumber,
  globalUpdatePassword,
  globalRestoreAccount,
  loginAnonymous,
  globalLoginSocial,
  globalValidateOTP,
  globalConfirmOTP,
  globalLogin,
  globalRegister,
  resetGlobalAuth,
  globalForgotPassword
};
