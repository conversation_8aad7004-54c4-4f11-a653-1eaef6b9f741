import PaymentApi from '../api/Payment';
import PaymentV2Api from '../api/PaymentV2';
import AccessTradeApi from '../api/AccessTrade';
import UserApi from '../api/userApi';
import { CONFIG_KEY } from '../../constants/constants';
import { TEXT } from '../../constants/text';
import ConfigCookie from '../../config/ConfigCookie';
import { getListPackageDiscount } from '../../services/paymentServices';
import { ACTION_TYPE, createAction } from './actionType';
import { groupPackageTerms } from '@customHook';

const createTransactionSuccess =
  ({ data, notTrackAccessTrade, valueReferralCode }: any) =>
  (dispatch: any) => {
    const orderId =
      data?.order_id || data?.orderId || data?.orderID || data?.txnRef || data?.tnxID || '';
    if (orderId && !notTrackAccessTrade) {
      const affNetwork = ConfigCookie.load(ConfigCookie.KEY.AFF_NETWORK) || '';
      const affSId = ConfigCookie.load(ConfigCookie.KEY.AFF_SID) || '';
      if (affNetwork && affSId) {
        AccessTradeApi.updateUtmSource({
          utmSource: affNetwork,
          transactionId: orderId
        })?.then((res: any) => {
          if (res?.success) {
            AccessTradeApi.createConversion({
              trackingId: affSId,
              transactionId: orderId,
              type: 'pay'
            });
          }
        });
      }
    }
    if (valueReferralCode) {
      PaymentApi.getReferralApply({
        transactionId: orderId,
        referralCode: valueReferralCode
      });
    }
    dispatch(createAction(ACTION_TYPE.CREATE_TRANSACTION, data));
  };
const setTemporaryData = (dataRequest: any) => PaymentApi.setTemporaryData(dataRequest);
const getTemporaryData = (id: any) => (dispatch: any) =>
  PaymentApi.getTemporaryData({ id })?.then((res: any) => {
    const { query } = res?.value || {};
    dispatch(createAction(ACTION_TYPE.GET_TEMPORARY_DATA, query));
  });

const getListTokensSaved =
  ({ paymentMethod, paymentService }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getListTokensSaved({
      paymentMethod,
      paymentService
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_LIST_TOKENS_SAVED, res));
    });
const checkFirstPay = (userId: any) => (dispatch: any) =>
  PaymentApi.checkFirstPay({ userId })?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.CHECK_FIRST_PAY, res));
  });
const checkReferralValid = (code: any) => (dispatch: any) =>
  PaymentApi.checkReferralValid(code)?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.CHECK_REFERRAL_CODE, res));
  });
const setReferralValue = (referralCode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_VALUE_REFERRAL_CODE, referralCode));
};

const selectTokenSaved = (token: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SELECT_TOKEN_SAVED, token));
};

const getPackages =
  ({ isInAppZalo }: any) =>
  (dispatch: any) =>
    PaymentApi.getPackages({ isInAppZalo })?.then((res: any) => {
      const newPackages = (res || []).map((pkg: any) => groupPackageTerms(pkg));
      dispatch(createAction(ACTION_TYPE.GET_PACKAGES, newPackages));
      return newPackages;
    });

const selectPackage = (selectedPackage: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SELECT_PACKAGE, selectedPackage));
};

const selectTerm: any = (selectedTerm: any, promotionCode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SELECT_TERM, selectedTerm));
  if (!promotionCode) {
    dispatch(resetPromotionData());
  }
};

const setSelectedMethod: any = (selectedMethod: any, promotionCode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SELECT_METHOD, selectedMethod));
  if (!promotionCode) {
    dispatch(resetPromotionData());
  }
};

const getPaymentConfig =
  ({ isGlobal }: any) =>
  (dispatch: any) =>
    UserApi.getConfig({
      key: isGlobal ? CONFIG_KEY.PAYMENT_CONFIG_GLOBAL : CONFIG_KEY.PAYMENT_CONFIG
    })?.then((res: any) => dispatch(getPaymentConfigSuccess(res)));
const getPaymentConfigSuccess = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.GET_PAYMENT_CONFIG_SUCCESS, data));

const getVnPayList = () => (dispatch: any) =>
  PaymentApi.getVnPayList()?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.GET_VN_PAY_LIST, res));
  });
const selectBank = (bank: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_BANK, bank));
};

const setPromotionCode = (promotionCode: any) => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.SET_PROMOTION_CODE, promotionCode || ''));
};

const getPromotionDetail =
  ({ packageId, promotionCode, selectedMethod, userId }: any) =>
  (dispatch: any) =>
    PaymentApi.promotionCode({
      packageId,
      promotionCode,
      selectedMethod,
      userId
    })?.then((res: any) => {
      const { errorCode, valid, used } = res || {};
      if (!errorCode && valid === 1 && used === 0) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            { message: TEXT.APPLY_PROMOTION_CODE_SUCCESS },
            dispatch
          )
        );
      }
      dispatch(
        createAction(ACTION_TYPE.SET_PROMOTION_DATA, {
          ...res,
          promotionCode
        })
      );
    });

const resetPromotionData = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.RESET_PROMOTION_DATA));
};
const resetValueReferralCode = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.RESET_VALUE_REFERRAL_CODE));
};

const checkAsiaPayTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentApi.checkAsiaPayTransaction({ orderId })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
      return res;
    });

const checkVnPayTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentApi.checkVnPayTransaction({ orderId })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
    });
const checkMocaTransaction =
  ({ mcOrderId, tnxId }: any) =>
  (dispatch: any) =>
    PaymentApi.checkMocaTransaction({
      mcOrderId,
      tnxId
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
    });

const tvodCheckTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentApi.tvodCheckTransaction({ orderId })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
      return res;
    });

const pvodCheckTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentApi.pvodCheckTransaction({ orderId })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
      return res;
    });

const checkZaloPayTransaction =
  ({ zlOrderId }: any) =>
  (dispatch: any) =>
    PaymentApi.checkZaloPayTransaction({ zlOrderId })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res));
    });
const checkLinkZaloPayTransaction = () => (dispatch: any) =>
  PaymentApi.checkLinkZaloPayTransaction()?.then((res: any) => {
    dispatch(createAction(ACTION_TYPE.SET_LINKED_ZALO_PAY, res?.data?.status === 1));
    return res?.data?.status === 1;
  });
const getLinkShopeePayTransaction =
  ({ paymentMethod, paymentService, returnUrl }: any) =>
  (dispatch: any) =>
    PaymentApi.getLinkShopeePayTransaction({
      paymentMethod,
      paymentService,
      returnUrl
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_LINKED_SHOPEE_PAY, res));
      return res;
    });

const clearTVodInfo = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_TVOD_INFO));
};
const clearTVodOffer = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_TVOD_OFFER));
};
const getTVodInfo =
  ({ contentId, contentType, isSimulcast, isLiveEvent, eventData }: any) =>
  (dispatch: any) =>
    PaymentApi.getTVodInfo({
      contentId,
      contentType,
      isSimulcast,
      isLiveEvent,
      eventData
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_TVOD_INFO, res));
    });

const getTVodOffer =
  ({ tvodProductId, promotionCode }: any) =>
  (dispatch: any) =>
    PaymentApi.getTVodOffer({
      tvodProductId,
      promotionCode
    })?.then((res: any) => dispatch(createAction(ACTION_TYPE.GET_TVOD_OFFER, res)));

// PVOD
const getPVodInfo =
  ({ contentId, contentType }: any) =>
  (dispatch: any) =>
    PaymentApi.getPVodInfo({
      contentId,
      contentType
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_PVOD_INFO, res));
    });

const getPVodOffer =
  ({ pvodProductId, promotionCode }: any) =>
  (dispatch: any) =>
    PaymentApi.getPVodOffer({
      pvodProductId,
      promotionCode
    })?.then((res: any) => dispatch(createAction(ACTION_TYPE.GET_PVOD_OFFER, res)));

const clearPVodInfo = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_PVOD_INFO));
};
const clearPVodOffer = () => (dispatch: any) => {
  dispatch(createAction(ACTION_TYPE.CLEAR_PVOD_OFFER));
};
const getPackageDiscount = (isMobile: any) => (dispatch: any) =>
  PaymentApi.getPackageDiscount()?.then((res: any) => {
    const packageDiscount = getListPackageDiscount(res?.data, isMobile);
    dispatch(createAction(ACTION_TYPE.GET_PACKAGE_DISCOUNT, packageDiscount));
  });
const getPackageDiscountOEM: any = () => () =>
  PaymentApi.getPackageDiscountOEM({
    params: { check_frequency: true }
  })?.then((res: any) => res);

const getCampaign =
  ({ isMobile }: any) =>
  (dispatch: any) =>
    PaymentApi.getCampaign({ isMobile })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_CAMPAIGN, res))
    );

const statusLoginPayment =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.STATUS_LOGIN_PAYMENT, status));
  };
const statusLoginPromotionCode =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.CHECK_STATUS_LOGIN_PROMOTION, status));
  };

const setRecurringStatusPayment =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.RECURRING_STATUS_PAYMENT, status));
  };
const setCollapseFooterRecommend =
  (status = false) =>
  (dispatch: any) => {
    dispatch(createAction(ACTION_TYPE.COLLAPSE_FOOTER_RECOMMEND, status));
  };

export {
  getPackageDiscount,
  createTransactionSuccess,
  clearTVodInfo,
  clearTVodOffer,
  tvodCheckTransaction,
  pvodCheckTransaction,
  setTemporaryData,
  getTemporaryData,
  getPaymentConfig,
  getListTokensSaved,
  selectTokenSaved,
  checkLinkZaloPayTransaction,
  checkZaloPayTransaction,
  checkMocaTransaction,
  checkVnPayTransaction,
  checkAsiaPayTransaction,
  resetPromotionData,
  getPromotionDetail,
  selectBank,
  setSelectedMethod,
  getPackages,
  selectPackage,
  selectTerm,
  getVnPayList,
  setPromotionCode,
  getTVodInfo,
  getTVodOffer,
  getPVodInfo,
  getPVodOffer,
  clearPVodInfo,
  clearPVodOffer,
  checkFirstPay,
  checkReferralValid,
  setReferralValue,
  resetValueReferralCode,
  getLinkShopeePayTransaction,
  getCampaign,
  statusLoginPayment,
  statusLoginPromotionCode,
  getPackageDiscountOEM,
  setRecurringStatusPayment,
  setCollapseFooterRecommend
};
