import isEmpty from 'lodash/isEmpty';
import { setToast } from '../../store/actions/app';
import LiveTV<PERSON>pi from '../api/liveTVApi';
import PageApi from '../api/cm/PageApi';
import UserApi from '../api/userApi';
import { HTTP_CODE, POPUP, RIBBON_TYPE } from '../../constants/constants';
import { TEXT } from '../../constants/text';
import { openPopup } from '../../store/actions/popup';
import { ERROR_PLAYER } from '../../constants/player';
import { ACTION_TYPE, createAction } from './actionType';
const setActiveCategory = (data: any, isFullscreen?: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_ACTIVE_CATEGORY, { category: data, isFullscreen }));

const setActiveFilterChannelPage = (data: any, isFullscreen?: any) => (dispatch: any) =>
  dispatch(
    createAction(ACTION_TYPE.SET_ACTIVE_FILTER_CHANNEL_PAGE, { filter: data, isFullscreen })
  );
const setActiveSubCategory = (data: any, category: any, isFullscreen?: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_ACTIVE_SUB_CATEGORY, { data, category, isFullscreen }));
const setActiveEpg = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_ACTIVE_EPG, data));

const setFavoriteChannel =
  (channel: any, refreshFromApi: any, isGlobal: any) => (dispatch: any) => {
    if (!isEmpty(channel)) {
      LiveTVApi.addFavorite({ id: channel?.id })?.then((res: any) => {
        if (res && res.success) {
          let message = '';
          if (channel.isFavorite) {
            message = `Đã gỡ kênh ${channel.title} khỏi danh sách kênh yêu thích`;
          } else {
            message = `Đã thêm kênh ${channel.title} vào danh sách kênh yêu thích`;
            if (refreshFromApi) {
              dispatch(getFavoriteList({ isGlobal }));
            }
          }
          dispatch(setToast({ message }));
          dispatch(createAction(ACTION_TYPE.SET_FAVORITE_CHANNEL, channel));
        } else {
          dispatch(setToast({ message: TEXT.MSG_ERROR }));
        }
      });
    }
  };

const setNotifyComingSoonEpg =
  (id: any, isUnsubscribe: any, startTime: any, contentType: any) => (dispatch: any) => {
    const dataUpdate = { [id]: !isUnsubscribe };
    if (isUnsubscribe) {
      return UserApi.unsubscribeComingSoon({ contentId: id })?.then((res: any) =>
        dispatch(
          createAction(ACTION_TYPE.SET_NOTIFY_COMING_SOON_EPG, { ...res?.data, data: dataUpdate })
        )
      );
    }
    return UserApi.subscribeComingSoon({ contentId: id, startTime, contentType })?.then(
      (res: any) => {
        if (res?.data?.success) {
          dispatch(setToast({ message: TEXT.LIVE_STREAM_NOTIFY }));
        }
        dispatch(
          createAction(ACTION_TYPE.SET_NOTIFY_COMING_SOON_EPG, { ...res?.data, data: dataUpdate })
        );
      }
    );
  };

const getListSubCategoriesOfChannelList =
  ({ id, accessToken, profileToken, ssr, ipAddress, userAgent, origin }: any) =>
  (dispatch: any) =>
    PageApi.getPageRibbonsID({
      id,
      accessToken,
      profileToken,
      ssr,
      ipAddress,
      userAgent,
      origin
    })?.then((res: any) => {
      dispatch(
        createAction(ACTION_TYPE.GET_LIST_SUB_CATEGORIES_OF_CHANNEL_LIST, { data: res?.data, id })
      );
      return res?.data;
    });

const getListSubCategoriesOfBroadcasting =
  ({ accessToken, ssr }: any) =>
  (dispatch: any) =>
    LiveTVApi.getBroadcastingList({ accessToken, ssr })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_LIST_SUB_CATEGORIES_OF_BROADCASTING, res))
    );

const getListFilterChannelPage =
  ({ ipAddress, userAgent, origin }: any) =>
  (dispatch: any) =>
    LiveTVApi.getChannelPage({ ipAddress, userAgent, origin })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_LIST_FILTER_CHANNEL_PAGE, res?.items || []));
      return res?.items || [];
    });

const getFavoriteList =
  ({ accessToken, profileToken, ipAddress, ssr, isGlobal, origin }: any) =>
  (dispatch: any) =>
    LiveTVApi.getFavoriteList({
      accessToken,
      profileToken,
      ipAddress,
      ssr,
      isGlobal,
      origin
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_LIST_FAVORITE_CHANNEL, res));
      return res;
    });

const getWatchedList =
  ({ accessToken, profileToken, ipAddress, ssr, isGlobal, origin }: any) =>
  (dispatch: any) =>
    LiveTVApi.getWatchedList({ accessToken, profileToken, ipAddress, ssr, isGlobal, origin })?.then(
      (res) => {
        dispatch(createAction(ACTION_TYPE.GET_LIST_WATCHED_CHANNEL, res));
        return res;
      }
    );

const getListChannels =
  ({
    accessToken,
    ipAddress,
    profileToken,
    ssr,
    type,
    id,
    page,
    limit,
    ribbonOrder,
    userAgent,
    isGlobal,
    origin
  }: any) =>
  (dispatch: any) => {
    if (type === RIBBON_TYPE.FAVORITE_LIVE_TV) {
      return dispatch(
        getFavoriteList({ accessToken, profileToken, ipAddress, ssr, isGlobal, origin })
      );
    }
    if (type === RIBBON_TYPE.WATCHED_LIST) {
      return dispatch(
        getWatchedList({ accessToken, profileToken, ipAddress, ssr, isGlobal, origin })
      );
    }
    return PageApi.getDataRibbonsId({
      id,
      page,
      limit,
      isTV: true,
      accessToken,
      profileToken,
      ribbonOrder,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => dispatch(createAction(ACTION_TYPE.GET_LIST_CHANNELS, { ...res, id })));
  };

const getListAllChannels =
  ({ page, limit }: any) =>
  (dispatch: any) =>
    LiveTVApi.getChannelListAll({ page, limit })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_LIST_ALL_CHANNELS, res))
    );
const clearDetailChannel = () => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.CLEAR_DETAIL_CHANNEL));

const getDetailChannelById = (id: any, iOS: any) => (dispatch: any) =>
  LiveTVApi.getChannelDetail({ id, iOS, dispatch })?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_DETAIL_CHANNEL_BY_ID, res))
  );

const getDetailChannelBySlug =
  ({ slug, accessToken, profileToken, epg, ssr, ipAddress, iOS, userAgent, origin }: any) =>
  (dispatch: any) =>
    LiveTVApi.getChannelDetailBySlug({
      slug,
      accessToken,
      profileToken,
      epg,
      ssr,
      ipAddress,
      iOS,
      userAgent,
      origin,
      dispatch
    })?.then((res: any) => {
      dispatch(createAction(ACTION_TYPE.GET_DETAIL_CHANNEL_BY_SLUG, res));
      return res;
    });

const getListEpgs =
  ({ id, strDate }: any) =>
  (dispatch: any) =>
    LiveTVApi.getEpgList({ id, strDate })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_LIST_EPGS, res))
    );

const getQNetInfo =
  ({ contentId, type }: any) =>
  (dispatch: any) =>
    LiveTVApi.getQNetInfo({ contentId, type })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.GET_QNET_INFO_OF_CHANNEL, res))
    );
const getInfoNotifyListEpgs = (listId: any) => (dispatch: any) =>
  UserApi.getUserNotifyComingSoon(listId)?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_INFO_NOTIFY_LIST_EPGS, res?.data))
  );
const validateKPlus =
  ({ accessToken, deviceId, isMobile, liveTVId, userId, browserName }: any) =>
  (dispatch: any) =>
    LiveTVApi.validateKPlus({
      accessToken,
      deviceId,
      isMobile,
      liveTVId,
      userId,
      browserName
    })?.then((res: any) => {
      if (res.httpCode === HTTP_CODE.EXPIRE) {
        UserApi.globalRefreshToken()?.then((resRefresh) => {
          if (resRefresh.httpCode === HTTP_CODE.OK_200) {
            LiveTVApi.validateKPlus({
              accessToken: resRefresh?.data?.accessToken,
              deviceId,
              isMobile,
              liveTVId,
              userId,
              browserName
            })?.then((reValidateRes) =>
              dispatch(createAction(ACTION_TYPE.VALIDATE_K_PLUS, reValidateRes))
            );
          } else {
            openPopup({
              name: POPUP.NAME.PLAYER_ERROR_LIVETV,
              retryAction: () => window.location.reload(),
              errorData: {
                errorType: ERROR_PLAYER.TYPE.VALIDATE_KPLUS,
                validateKPlusData: res
              }
            });
          }
        });
      }
      return dispatch(createAction(ACTION_TYPE.VALIDATE_K_PLUS, res));
    });
const resetValidateKPlus = () => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.RESET_VALIDATE_K_PLUS));

const getRibbonLiveTvNotFound =
  ({ accessToken, ssr, ipAddress, userAgent }: any) =>
  (dispatch: any) =>
    LiveTVApi.getRibbonLiveTvNotFound({ accessToken, ssr, ipAddress, userAgent })?.then(
      (res: any) => dispatch(createAction(ACTION_TYPE.GET_CHANNEL_RIBBON_NOTFOUND, res))
    );

export {
  validateKPlus,
  resetValidateKPlus,
  setActiveCategory,
  setActiveFilterChannelPage,
  setActiveSubCategory,
  setActiveEpg,
  setFavoriteChannel,
  setNotifyComingSoonEpg,
  getListSubCategoriesOfChannelList,
  getListSubCategoriesOfBroadcasting,
  getListFilterChannelPage,
  getListChannels,
  getListAllChannels,
  getDetailChannelById,
  getDetailChannelBySlug,
  getListEpgs,
  getQNetInfo,
  getInfoNotifyListEpgs,
  getRibbonLiveTvNotFound,
  clearDetailChannel
};
