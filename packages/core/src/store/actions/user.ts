import User<PERSON>pi from '../api/userApi';
import TVod<PERSON><PERSON> from '../api/tvodApi';
import { TEXT } from '../../constants/text';
import { setToast, setLoading, setDeviceId } from '../../store/actions/app';
import { HTTP_CODE, POSITION, TOAST } from '../../constants/constants';
import ConfigCookie from '../../config/ConfigCookie';
import { ACTION_TYPE, createAction } from './actionType';

const USER_REPORT = 'USER_REPORT';
const USER_FEEDBACK = 'USER_FEEDBACK';
const OFF_BANNER_VIP_PRIVILEGE = 'OFF_BANNER_VIP_PRIVILEGE';
const USER_NOTIFY_COMINGSOON = 'USER_NOTIFY_COMINGSOON';
const USER_SUBCRIBE_NOTIFY_COMINGSOON = 'USER_SUBCRIBE_NOTIFY_COMINGSOON';
const USER_UNSUBCRIBE_NOTIFY_COMINGSOON = 'USER_UNSUBCRIBE_NOTIFY_COMINGSOON';
const POPUP_CONFIG = 'POPUP_CONFIG';
const MSG_CONFIG = 'MSG_CONFIG';
const STATIC_LINK = 'STATIC_LINK';
const USER_TYPE = 'USER_TYPE';
const DEVICE_ID = 'DEVICE_ID';
const CONFIG_PHIM_HAY = 'CONFIG_PHIM_HAY';
const CONFIG_PHIM_LE = 'CONFIG_PHIM_LE';
const PURCHASED_PAYMENT = 'PURCHASED_PAYMENT';
const USER_PACKAGE_INFO = 'USER_PACKAGE_INFO';
const IS_TRIGGER_FIRST_PAY = 'IS_TRIGGER_FIRST_PAY';
const CANCEL_FIRST_PAY = 'CANCEL_FIRST_PAY';
const getUserReport = () => (dispatch: any) =>
  UserApi.getUserReport()?.then((res: any) => {
    dispatch({
      type: USER_REPORT,
      payload: res
    });
    return res;
  });

const getUserPackageInfo = () => (dispatch: any) =>
  UserApi.getUserPackageInfo()?.then((res: any) => {
    dispatch({
      type: USER_PACKAGE_INFO,
      payload: res?.data
    });
    return res;
  });
const getUserNotifyComingSoon: any = (listId: any) => (dispatch: any) =>
  UserApi.getUserNotifyComingSoon(listId)?.then((res: any) => {
    dispatch({
      type: USER_NOTIFY_COMINGSOON,
      payload: res
    });
    return res;
  });
const getUserSubcribeNotifyComingSoon: any =
  (contentId: any, startTime: any, contentType: any) => (dispatch: any) =>
    UserApi.subscribeComingSoon({ contentId, startTime, contentType })?.then((res: any) => {
      dispatch({
        type: USER_SUBCRIBE_NOTIFY_COMINGSOON,
        payload: res
      });
      return res;
    });
const getUserUnSubcribeNotifyComingSoon: any = (contentId: any) => (dispatch: any) =>
  UserApi.unsubscribeComingSoon({ contentId })?.then((res: any) => {
    dispatch({
      type: USER_UNSUBCRIBE_NOTIFY_COMINGSOON,
      payload: res
    });
    return res;
  });
const getUserFeedback = () => (dispatch: any) =>
  UserApi.userFeedBack()?.then((res: any) => {
    const isData = res?.data?.Like || res?.data?.DisLike;
    const data = isData ? res?.data : null;
    dispatch({
      type: USER_FEEDBACK,
      payload: { data }
    });
    return data;
  });

const offBannerVipPrivilege = () => (dispatch: any) =>
  dispatch({
    type: OFF_BANNER_VIP_PRIVILEGE,
    payload: true
  });

const getTransactions =
  ({ page, pageSize }: any) =>
  (dispatch: any) =>
    UserApi.getTransactions({ page, pageSize })?.then((res: any) =>
      dispatch({
        type: ACTION_TYPE.TRANSACTION,
        payload: { data: res?.data }
      })
    );

const getPurchased = () => (dispatch: any) =>
  UserApi.getPurchased()?.then((res: any) =>
    dispatch({
      type: PURCHASED_PAYMENT,
      payload: { data: res?.data }
    })
  );

const getConfigPopup = (key: any) => (dispatch: any) =>
  UserApi.getConfig({ key })?.then((res: any) =>
    dispatch({
      type: POPUP_CONFIG,
      payload: res
    })
  );

const getUserType = (accessToken: any) => (dispatch: any) =>
  UserApi.getUserType(accessToken)?.then((res: any) =>
    dispatch({
      type: USER_TYPE,
      payload: res?.data
    })
  );

const getConfigMessage = (key: any) => (dispatch: any) =>
  UserApi.getConfig({ key })?.then((res: any) =>
    dispatch({
      type: MSG_CONFIG,
      payload: res
    })
  );

const getIsFirstPay = (data: any) => (dispatch: any) =>
  dispatch({
    type: IS_TRIGGER_FIRST_PAY,
    payload: data
  });

const cancelFirstPay = () => (dispatch: any) => {
  dispatch({
    type: CANCEL_FIRST_PAY,
    payload: {
      isCancelFirstPay: true
    }
  });
};

export const getUserTvod =
  ({
    isPreOrder,
    isReminder,
    idTvodData,
    idTvodPreOrderData,
    isMobile,
    ribbonId,
    ribbonOrder,
    ribbonName,
    isGlobal
  }: any) =>
  (dispatch: any) =>
    UserApi.getUserTvod({
      isPreOrder,
      isReminder,
      idTvodData,
      isMobile,
      ribbonOrder,
      ribbonId,
      ribbonName,
      idTvodPreOrderData,
      isGlobal
    })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.USER_TVOD_INFO, { ...res?.data, isReminder, isPreOrder }))
    );

const setShowTvod =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SHOW_TVOD, status));

const setDeviceIdUser = (deviceId: any) => (dispatch: any) =>
  dispatch(setDeviceId({ type: DEVICE_ID, payload: deviceId }));
const getPreorderReminder = () => (dispatch: any) =>
  TVodApi.getReminderPreorder()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_PREORDER_REMINDER, res?.items || []))
  );

const getReferralProg = () => (dispatch: any) =>
  UserApi.getReferralProg()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_REFERRAL_CODE, res || []))
  );
const getOtpUpdatePassword = () => (dispatch: any) =>
  UserApi.getOtpUpdatePassword()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_OTP_UPDATE_PASSWORD, res))
  );
const confirmOtpUpdatePasswordSuccess = (data: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.CHECK_OTP_UPDATE_PASSWORD, data));

const confirmOtpUpdatePassword = (sessionId: any, otpValue: any) => (dispatch: any) =>
  UserApi.confirmOtpUpdatePassword(sessionId, otpValue)?.then((res: any) =>
    dispatch(confirmOtpUpdatePasswordSuccess(res?.data))
  );

const getDevicesManagement = () => (dispatch: any) =>
  UserApi.getDevicesManagement()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_DEVICES_MANAGEMENT, res))
  );
const setCurrentHeightHeader = (value: any) => (dispatch: any) =>
  dispatch(createAction(ACTION_TYPE.SET_HEIGHT_HEADER, value));

const disabledDevicesManagement = (result: any) => (dispatch: any) => {
  UserApi.disabledDevicesManagement(result)?.then((res: any) => {
    if (res?.data?.success) {
      dispatch(setToast({ message: TEXT.DISABLED_DEVICE_SUCCESS }));
      return dispatch(getDevicesManagement());
    }
    dispatch(setToast({ message: TEXT.DISABLED_DEVICE_FAIL }));
  });
};

const getSegmentedUser = () => (dispatch: any) =>
  UserApi.getSegmentedUser()?.then((res: any) =>
    dispatch(createAction(ACTION_TYPE.GET_SEGMENT_USER, res))
  );

// Loyalty
const seeAllVoucher = (data: any) => createAction(ACTION_TYPE.SEE_ALL_VOUCHER, data);
const saveInfoRedeemData = (data: any) => createAction(ACTION_TYPE.INFO_REDEEM_DATA, data);

const getInfoLoyalty =
  ({ userId }: any) =>
  (dispatch: any) => {
    UserApi.getInfoLoyalty({ userId })?.then((res: any) => {
      if (res?.isSuccess) return dispatch(createAction(ACTION_TYPE.GET_INFO_LOYALTY, res?.data));
      return dispatch(createAction(ACTION_TYPE.GET_FAIL_INFO_LOYALTY, res?.isSuccess));
    });
  };

const getTierBenefits = () => (dispatch: any) => {
  UserApi.getTierBenefit()?.then((res: any) => {
    if (res?.isSuccess) return dispatch(createAction(ACTION_TYPE.GET_TIER_BENEFITS, res?.data));
  });
};

const getEarningActivity =
  ({ userId }: any) =>
  (dispatch: any) => {
    UserApi.getEarningActivity({ userId })?.then((res: any) => {
      if (res?.isSuccess) {
        return dispatch(createAction(ACTION_TYPE.GET_EARNING_ACTIVITIES, res?.data));
      }
    });
  };

const getUsedPointHistory =
  ({ userId }: any) =>
  (dispatch: any) => {
    UserApi.getUsedPointHistory({ userId })?.then((res: any) => {
      if (res?.isSuccess) {
        return dispatch(createAction(ACTION_TYPE.GET_USED_POINTS_HISTORY, res?.data));
      }
    });
  };

const getActivities =
  ({ pageSize, isClickSeeAll }: any) =>
  (dispatch: any) => {
    UserApi.getActivities({ pageSize })?.then((res: any) => {
      if (res?.isSuccess) {
        dispatch(createAction(ACTION_TYPE.GET_ACTIVITIES, res?.data));
        if (isClickSeeAll) {
          dispatch(seeAllVoucher(TEXT.EARNING_POINT));
          window.scrollTo({
            top: 0
          });
        }
      }
    });
  };

const getVouchers =
  ({
    userId,
    categoryIds,
    subCategoryIds,
    voucherPageSize,
    categoryPageSize,
    isClickSeeAll
  }: any) =>
  (dispatch: any) => {
    UserApi.getVouchers({
      userId,
      categoryIds,
      subCategoryIds,
      voucherPageSize,
      categoryPageSize
    })?.then((res: any) => {
      if (res?.isSuccess) {
        dispatch(createAction(ACTION_TYPE.GET_VOUCHERS, res?.data));
        if (isClickSeeAll) {
          dispatch(seeAllVoucher(TEXT.VOUCHER));
          window.scrollTo({
            top: 0
          });
        }
      }
    });
  };
const getVouchersByCategory = (data: any) =>
  createAction(ACTION_TYPE.GET_VOUCHERS_BY_CATEGORY, data);
const getItemSubCate: any = (data: any) => createAction(ACTION_TYPE.SUB_ITEM_CATEGORY, data);
const getDeviceToken: any = (data: any) => createAction(ACTION_TYPE.GET_DEVICE_TOKEN, data);

const redeemVoucher =
  ({ userId, voucherId, data, onCloseLoyalty, codeDeliveryChannel, phoneNumber, email }: any) =>
  async (dispatch: any) => {
    const COUNTDOWN = 10000;
    const { profileToken } = ConfigCookie.load(ConfigCookie.KEY.SIGNATURE) || {};

    await UserApi.profile
      ?.getProfile({
        profileToken
      })
      ?.then(async (res) => {
        if (res?.httpCode === HTTP_CODE.BLOCKED_ACCOUNT) {
          dispatch(setLoading(false));
          return;
        }
        dispatch(setToast({ message: TEXT.REDEEM_VOUCHER_PROCESSING }));

        await UserApi.redeemVoucher({
          userId,
          voucherId,
          codeDeliveryChannel,
          phoneNumber,
          email
        })?.then((res: any) => {
          setTimeout(() => {
            if (res?.isSuccess) {
              dispatch(getInfoLoyalty({ userId }));
              dispatch(createAction(ACTION_TYPE.REDEEM_VOUCHER, res?.data));
              dispatch(
                setToast({
                  title: TEXT.EXCHANGE_SUCCESSFULLY,
                  content: TEXT.CONTENT_EXCHANGE_SUCCESSFULLY?.replace(
                    /{(?:user_type|user_value)}/gi,
                    (matched: any) => data[matched]
                  ),
                  duration: COUNTDOWN,
                  noIcon: true,
                  position: POSITION.BOTTOM_RIGHT,
                  kind: TOAST.KIND.TIMER,
                  type: 'success',
                  btnClick: onCloseLoyalty
                })
              );
              return;
            }
            dispatch(
              setToast({
                title: TEXT.EXCHANGE_FAILED,
                content: TEXT.CONTENT_EXCHANGE_FAILED,
                duration: COUNTDOWN,
                noIcon: true,
                position: POSITION.BOTTOM_RIGHT,
                kind: TOAST.KIND.TIMER,
                type: 'fail',
                btnClick: onCloseLoyalty
              })
            );
          }, 1000);
        });
      });
  };
const clearRedeemVoucherData = (data?: any) => createAction(ACTION_TYPE.CLEAR_REDEEM_CODE, data);

const updateStatusRedeemCode =
  ({ userId, voucherCode }: any) =>
  (dispatch: any) => {
    UserApi.updateStatusRedeemCode({ userId, voucherCode })?.then((res: any) => {
      if (res?.isSuccess) {
        return dispatch(createAction(ACTION_TYPE.UPDATE_STATUS_REDEEM_CODE, res?.data));
      }
    });
  };
export const setStatusRepayConversion =
  (status = false) =>
  (dispatch: any) =>
    dispatch(createAction(ACTION_TYPE.SET_STATUS_REPAY_CONVERSION, status));

const getConfigPackage = () => {};

export {
  USER_REPORT,
  OFF_BANNER_VIP_PRIVILEGE,
  USER_FEEDBACK,
  USER_NOTIFY_COMINGSOON,
  USER_UNSUBCRIBE_NOTIFY_COMINGSOON,
  USER_SUBCRIBE_NOTIFY_COMINGSOON,
  POPUP_CONFIG,
  DEVICE_ID,
  MSG_CONFIG,
  USER_TYPE,
  STATIC_LINK,
  CONFIG_PHIM_HAY,
  CONFIG_PHIM_LE,
  PURCHASED_PAYMENT,
  USER_PACKAGE_INFO,
  IS_TRIGGER_FIRST_PAY,
  CANCEL_FIRST_PAY,
  getOtpUpdatePassword,
  confirmOtpUpdatePassword,
  confirmOtpUpdatePasswordSuccess,
  getDevicesManagement,
  disabledDevicesManagement,
  getPreorderReminder,
  setDeviceIdUser,
  getConfigPopup,
  getConfigMessage,
  getTransactions,
  getUserSubcribeNotifyComingSoon,
  getUserUnSubcribeNotifyComingSoon,
  getUserNotifyComingSoon,
  offBannerVipPrivilege,
  getUserReport,
  getUserFeedback,
  getUserType,
  getPurchased,
  getUserPackageInfo,
  setShowTvod,
  getReferralProg,
  setCurrentHeightHeader,
  getSegmentedUser,
  seeAllVoucher,
  getInfoLoyalty,
  getEarningActivity,
  getUsedPointHistory,
  getActivities,
  getVouchers,
  saveInfoRedeemData,
  getVouchersByCategory,
  getTierBenefits,
  redeemVoucher,
  updateStatusRedeemCode,
  clearRedeemVoucherData,
  getItemSubCate,
  getDeviceToken,
  getIsFirstPay,
  cancelFirstPay,
  getConfigPackage
};
