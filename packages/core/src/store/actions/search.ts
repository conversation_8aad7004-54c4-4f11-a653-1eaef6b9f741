import SearchApi from '../api/cm/SearchApi';
import { ACTION_TYPE } from '../../store/actions/actionType';

const getTrendKeywords: any =
  ({ page, limit, accessToken, isGlobal }: any) =>
  (dispatch: any) =>
    SearchApi.getTrendKeywords({ page, limit, accessToken, isGlobal })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.ACTION_GET_TREND_KEYWORD,
        payload: res
      };
      return dispatch(result);
    });
const getSearchNewForyou: any =
  ({ page, limit, accessToken, isGlobal }: any) =>
  (dispatch: any) =>
    SearchApi.getSearchNewForyou({ page, limit, accessToken, isGlobal })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.ACTION_GET_SEARCH_FORYOU,
        payload: res
      };
      return dispatch(result);
    });
const getSearchSuggest =
  ({ keyword }: any) =>
  (dispatch: any) =>
    SearchApi.getSearchSuggest({ keyword })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.ACTION_GET_SEARCH_SUGGEST,
        payload: res,
        keyword
      };
      return dispatch(result);
    });
const getSearchHistory = () => (dispatch: any) =>
  SearchApi.getSearchHistory()?.then((res: any) => {
    const result: any = {
      type: ACTION_TYPE.ACTION_GET_SEARCH_HISTORY,
      payload: res
    };
    return dispatch(result);
  });

const delSearchHistory = () => (dispatch: any) =>
  SearchApi.delSearchHistory()
    ?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.ACTION_DELETE_SEARCH_HISTORY,
        payload: res
      };
      dispatch(result);
      return res;
    })
    .catch((err) => err);

const getSearchContent =
  ({ keyword, page, limit, tags, accessToken, ssr, ipAddress, userAgent, isGlobal, origin }: any) =>
  (dispatch: any) =>
    SearchApi.getSearchContent({
      keyword,
      page,
      limit,
      tags,
      accessToken,
      ssr,
      ipAddress,
      userAgent,
      isGlobal,
      origin
    })?.then((res: any) => {
      const result: any = {
        type: ACTION_TYPE.ACTION_GET_SEARCH_CONTENT,
        payload: res,
        keyword,
        // sort,
        tags,
        page
      };
      return dispatch(result);
    });
const postSearchHistory = ({ keyword, id, type, position, request_id }: any) => ({
  type: ACTION_TYPE.ACTION_POST_SEARCH_HISTORY,
  payload: SearchApi.postSearchHistory({ keyword, id, type, position, request_id })
});
const onFocusSearchBox = (status: any) => (dispatch: any) => {
  dispatch({
    type: ACTION_TYPE.ACTION_FOCUS_SEARCHBOX,
    payload: {
      data: { focused: status }
    }
  });
};
const setSearchShow =
  (status = false) =>
  (dispatch: any) => {
    dispatch({
      type: ACTION_TYPE.SET_SEARCH_SHOW,
      payload: {
        data: status
      }
    });
  };

export {
  onFocusSearchBox,
  getSearchNewForyou,
  getTrendKeywords,
  getSearchContent,
  getSearchSuggest,
  getSearchHistory,
  delSearchHistory,
  postSearchHistory,
  setSearchShow
};
