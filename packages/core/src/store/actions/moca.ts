import PaymentV2Api from '../api/PaymentV2';
import TrackingPayment from '@vieon/tracking/functions/payment';
import { createTimeout } from '../../utils/common';
import { PAGE } from '../../constants/constants';
import { DOMAIN_WEB } from '../../config/ConfigEnv';
import { TEXT } from '../../constants/text';
import { createTransactionSuccess } from './payment';
import { setLoading } from '../../store/actions/app';
import { ACTION_TYPE, createAction } from './actionType';

const trackingPayment = new TrackingPayment();
let timerConfirmTransaction: any = null;
const TIME_CONFIRM_TRANSACTION = 600000;

const createTransaction =
  ({
    tokenId,
    selectedTerm,
    selectedMethod,
    promotionData,
    valueReferralCode,
    isSegmentedUser
  }: any) =>
  (dispatch: any) => {
    dispatch(setLoading(true));
    PaymentV2Api.createTransaction({
      paymentMethod: 'WALLET',
      paymentService: 'MOCA',
      redirectUri: `${DOMAIN_WEB}${PAGE.PAYMENT_RESULT}`,
      packageId: selectedTerm?.id,
      promotionCode: promotionData?.promotionCode || selectedTerm?.giftCode || '',
      tokenId
    })?.then((res: any) => {
      dispatch(setLoading(false));
      const result = res?.data?.result || {};
      trackingPayment.payButtonSelected({
        selectedTerm,
        promotionData,
        selectedMethod,
        transaction: result,
        isSegmentedUser
      });
      if (!res?.success) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            { message: res?.data?.error_message || TEXT.MSG_ERROR },
            dispatch
          )
        );
      } else if (res?.data?.error_code === 1) {
        dispatch(
          createAction(
            ACTION_TYPE.SET_TOAST,
            {
              message: res?.data?.error_message || TEXT.MSG_ERROR
            },
            dispatch
          )
        );
      } else {
        dispatch(createTransactionSuccess({ data: result, valueReferralCode }));
        const payUrl = result?.pay_url;
        if (payUrl) {
          window.location = payUrl;
        } else {
          dispatch(
            createAction(
              ACTION_TYPE.SET_TOAST,
              { message: res?.data?.error_message || TEXT.MSG_ERROR },
              dispatch
            )
          );
        }
      }
      return result;
    });
  };

const getResultTransaction =
  ({ state, code, redirectUri, error, errorDescription, router }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getResultTransaction({
      state,
      code,
      error,
      errorDescription,
      redirectUri,
      paymentMethod: 'Wallet',
      paymentService: 'Moca'
    })?.then((res: any) => {
      if (timerConfirmTransaction) clearTimeout(timerConfirmTransaction);
      if (!res?.success) {
        if (res?.httpCode === 404) {
          dispatch(
            createAction(ACTION_TYPE.SET_TOAST, { message: TEXT.TRANSACTION_NOT_FOUND }, dispatch)
          );
        } else {
          timerConfirmTransaction = createTimeout(() => {
            dispatch(
              getResultTransaction({
                state,
                code,
                error,
                errorDescription,
                redirectUri,
                paymentMethod: 'Wallet',
                paymentService: 'Moca',
                router
              })
            );
          }, TIME_CONFIRM_TRANSACTION);
        }
      } else {
        router?.push(`${router?.asPath}&confirmed=true`);
      }
      dispatch(createAction(ACTION_TYPE.CONFIRM_RESULT_TRANSACTION, res));
    });

const getStatusTransaction =
  ({ orderId }: any) =>
  (dispatch: any) =>
    PaymentV2Api.getStatusTransaction({ orderId })?.then((res: any) =>
      dispatch(createAction(ACTION_TYPE.SET_TRANSACTION_RESULT, res))
    );

export { createTransaction, getResultTransaction, getStatusTransaction };
