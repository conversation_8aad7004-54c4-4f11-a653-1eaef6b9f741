import UserApi from '../api/userApi';
import ConfigCookie from '../../config/ConfigCookie';
import { HTTP_CODE } from '../../constants/constants';
import { getUserType } from './user';
import { ACTION_GLOBAL_LOGIN, ACTION_TYPE, createAction } from './actionType';
import { destinationLogin } from '../../services/multiProfileServices';
const getProfileSuccess = (data: any) => createAction(ACTION_TYPE.GET_PROFILE, data);

const getProfile =
  ({
    isMobile,
    isTablet,
    accessToken,
    ssr,
    ipAddress,
    deviceModel,
    deviceName,
    deviceType,
    profileToken,
    isSmartTvPlatform,
    origin,
    isLoginByQRCode,
    destination,
    router
  }: any) =>
  (dispatch: any) =>
    UserApi.profile
      ?.getProfile({
        isMobile,
        isTablet,
        accessToken,
        ssr,
        ipAddress,
        deviceModel,
        deviceName,
        deviceType,
        profileToken,
        isSmartTvPlatform,
        origin
      })
      ?.then(async (res: any) => {
        dispatch(createAction(ACTION_TYPE.SET_TOKEN_EXPIRED, res?.httpCode === HTTP_CODE.EXPIRE));
        const { success, httpCode, data } = res || {};
        if (!success && httpCode !== HTTP_CODE.EXPIRE) {
          ConfigCookie.remove(ConfigCookie.KEY.SIGNATURE);
        } else {
          dispatch(createAction(ACTION_TYPE.OFF_BIND_ACCOUNT, false));
        }

        if (isLoginByQRCode) {
          dispatch(
            createAction(ACTION_GLOBAL_LOGIN, {
              data: {
                profile: data,
                accessToken
              }
            })
          );
          destinationLogin({
            dataLogin: {
              profile: res?.data,
              accessToken: res?.data?.token
            },
            destination,
            router,
            dispatch
          });
        }

        const userTypeAction = await dispatch(getUserType(accessToken));
        return dispatch(getProfileSuccess({ ...res?.data, type: userTypeAction?.payload?.type }));
      });

const updateProfile =
  ({
    givenName = '',
    dob = '',
    gender = '',
    allowPush = '',
    deviceModel,
    deviceName,
    deviceType
  }: any) =>
  (dispatch: any) =>
    UserApi.profile
      ?.updateProfile({ givenName, dob, gender, allowPush, deviceModel, deviceName, deviceType })
      ?.then((res: any) => {
        dispatch(getProfile({ deviceModel, deviceName, deviceType }));
        return { ...res?.data, success: res?.success };
      });

const updateEmail =
  ({ email = '', deviceModel, deviceName, deviceType }: any) =>
  (dispatch: any) =>
    UserApi.profile
      ?.updateEmail({ email, deviceModel, deviceName, deviceType })
      ?.then((res: any) => {
        dispatch(getProfile({ deviceModel, deviceName, deviceType }));
        return res?.data;
      });
const updatePhoneNumberSuccess = (data: any) => createAction(ACTION_TYPE.UPDATE_PHONE_NUMBER, data);
const updateMobile = (phoneNumber: any) => (dispatch: any) =>
  UserApi.profile
    ?.updateMobile(phoneNumber)
    ?.then((res: any) => dispatch(updatePhoneNumberSuccess(res?.data)));
const updateConfirmMobileSuccess = (data: any) =>
  createAction(ACTION_TYPE.UPDATE_CONFIRM_MOBILE, data);
const confirmUpdateMobile =
  (registerSessionId: any, otpCode: any, password: any, confirmPassword: any) => (dispatch: any) =>
    UserApi.profile
      ?.confirmUpdateMobile(registerSessionId, otpCode, password, confirmPassword)
      ?.then((res: any) => dispatch(updateConfirmMobileSuccess(res?.data)));
const updatePasswordSuccess = (data: any) => createAction(ACTION_TYPE.UPDATE_PASSWORD, data);
const updatePassword =
  (sessionId: any, oldPassword: any, password: any, confimPassword: any) => (dispatch: any) =>
    UserApi.profile
      ?.updatePassword(sessionId, oldPassword, password, confimPassword)
      ?.then((res: any) => dispatch(updatePasswordSuccess(res?.data)));

const updateDob =
  ({ dob, deviceModel, deviceName, deviceType }: any) =>
  (dispatch: any) =>
    UserApi.profile.updateDob({ dob })?.then((res: any) => {
      dispatch(getProfile({ deviceModel, deviceName, deviceType }));
      return { ...res?.data, success: res?.success };
    });
const restoreAccountSuccess = (data: any) => createAction(ACTION_TYPE.RESTORE_ACCOUNT, data);

const restoreAccount =
  ({ accessToken }: any) =>
  (dispatch: any) =>
    UserApi.profile
      ?.restoreAccount({ accessToken })
      ?.then((res: any) => dispatch(restoreAccountSuccess(res?.data || {})));
const confirmOtpRestoreAccountSuccess = (data: any) =>
  createAction(ACTION_TYPE.CONFIRM_RESTORE_ACCOUNT, data);

const confirmOtpRestoreAccount =
  ({ accessToken, sessionId, optCode }: any) =>
  (dispatch: any) =>
    UserApi.profile
      ?.confirmRestoreOtpAccount({ accessToken, sessionId, optCode })
      ?.then((res: any) => dispatch(confirmOtpRestoreAccountSuccess(res?.data)));

const updateInvoiceInfo = (invoiceInfoUpdate: any) => (dispatch: any) =>
  UserApi.profile?.updateInvoiceInfo(invoiceInfoUpdate)?.then((res: any) => {
    const { deviceModel, deviceName, deviceType } = invoiceInfoUpdate;
    dispatch(getProfile({ deviceModel, deviceName, deviceType }));
    return { ...res?.data, success: res?.success };
  });

export {
  getProfile,
  updateProfile,
  updateEmail,
  updateMobile,
  updatePassword,
  confirmUpdateMobile,
  getProfileSuccess,
  updateDob,
  restoreAccount,
  confirmOtpRestoreAccount,
  updateInvoiceInfo
};
