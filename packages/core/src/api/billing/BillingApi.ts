import axios from 'axios';
import ConfigApi from '../../config/ConfigApi';
import { API_METHOD, PAYMENT } from '../../constants/constants';
import { json_to_query_string } from '../../utils/common';
import { setValueTitle } from '../../services/paymentServices';
import AxiosClient from '../axiosClient';

export default class BillingApi {
  static getBillingPackage() {
    const url = ConfigApi.billing.packages;
    return axios.get(url)?.then((res: any) => {
      const data = res?.data || [];
      const newData = data.map((d: any) => {
        const temp = {
          ...d,
          items: (d?.items || []).map((item: any) => ({
            ...item,
            valueDuration: setValueTitle(item)
          }))
        };
        return temp;
      });
      return { ...res, data: newData };
    });
  }

  static getServicesBilling() {
    const url = ConfigApi.billing.getServices;
    return axios.get(url);
  }

  static createBillingTransaction({ packageId, serviceCode, orderInfo, returnUrl }: any) {
    const url = ConfigApi.billing.createTransaction;
    const dataSend = {
      package_id: packageId,
      service_code: serviceCode,
      order_info: orderInfo || 'thanh toan goi cuoc',
      return_url: returnUrl || PAYMENT.RETURN_URL
    };
    return axios.post(url, json_to_query_string(dataSend));
  }

  static checkBillingTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkBillingTransaction;
    return axios.get(url, {
      params: {
        txn_ref: orderId
      }
    });
  }

  static createMomoTransaction({ packageId }: any) {
    const url = ConfigApi.billing.createMomoTransaction;
    const dataSend = {
      package_id: packageId
    };
    return axios.post(url, json_to_query_string(dataSend));
  }

  static checkMomoTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkMomoTransaction;
    return axios.get(url, {
      params: {
        txn_ref: orderId
      }
    });
  }

  static createVnPayTransaction({ packageId, url_return, order_info }: any) {
    const url = ConfigApi.billing.createVnPayTransaction;
    const dataSend = {
      package_id: packageId,
      order_info,
      url_return
    };
    return axios.post(url, json_to_query_string(dataSend));
  }

  static checkVnPayTransaction({ orderId }: any) {
    const url = ConfigApi.billing.checkVnPayTransaction;
    return axios.get(url, {
      params: {
        txn_ref: orderId
      }
    });
  }

  static getUserTransaction({ page, limit }: any) {
    const url = ConfigApi.billing.getUserTransactions;
    return axios.get(url, {
      params: {
        page,
        limit
      }
    });
  }

  static getUserTransactionSuccess({ page, limit }: any) {
    const url = ConfigApi.billing.getUserTransactionSuccess;
    return axios.get(url, {
      params: {
        page,
        limit
      }
    });
  }
  static getPackageInfo({ contentId }: any) {
    let url = ConfigApi.global.packageInfo + contentId;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method });
  }
}
