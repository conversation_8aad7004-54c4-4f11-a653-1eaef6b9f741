import ConfigApi from '../../config/ConfigApi';
import { API_METHOD } from '../../constants/constants';
import AxiosClient from '../axiosClient';

export default class BillingInfoAPI {
  /**
   * Retrieves the country data from the billing information API.
   *
   * @return {Promise} Returns a promise that resolves to the country data.
   */
  static getCountry() {
    const url = ConfigApi.billingInfo.getCountry;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method });
  }

  /**
   * Retrieves the province information for a given country ID.
   *
   * @param {number} countryId - The ID of the country to retrieve the province from.
   * @return {Promise} A promise that resolves with the province information.
   */
  static getProvince(countryId: any) {
    const url = ConfigApi.billingInfo.getProvince;
    const method = API_METHOD.GET;
    const params: any = {
      country_id: countryId
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }

  /**
   * Retrieves district information for a given province ID.
   *
   * @param {number} provinceId - The ID of the province.
   * @return {Promise} - A promise that resolves with the district information.
   */
  static getDistrict(provinceId: any) {
    const url = ConfigApi.billingInfo.getDistrict;
    const method = API_METHOD.GET;
    const params: any = {
      province_id: provinceId
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }

  /**
   * Retrieves the ward information based on the given district ID.
   *
   * @param {number} districtId - The district ID to retrieve the ward from.
   * @return {Promise} A promise that resolves with the ward information.
   */
  static getWard(districtId: any) {
    const url = ConfigApi.billingInfo.getWard;
    const method = API_METHOD.GET;
    const params: any = {
      district_id: districtId
    };
    return AxiosClient.executeWithCache({ url, method, params });
  }

  /**
   * Retrieves the billing information from the AsiaPay API.
   *
   * @returns {Promise} A promise that resolves to the billing information.
   */
  static getBillingInfoAsiaPay() {
    const url = ConfigApi.billingInfo.infoAsiaPay;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method });
  }

  /**
   * Posts billing information to AsiaPay API.
   *
   * @param {Object} params - The parameters for the request.
   * @returns {Promise} A promise that resolves with the API response.
   */
  static postBillingInfoAsiaPay(params: any) {
    const url = ConfigApi.billingInfo.infoAsiaPay;
    const method = API_METHOD.POST;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }
}
