import ConfigApi from '../../config/ConfigApi';
import { API_METHOD } from '../../constants/constants';
import AxiosClient from '../axiosClient';

class QnetApi {
  static ping(params: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.ccu.qnet.ping;
    return AxiosClient.executeWithCache({ url, method, params, isQnet: true });
  }
  static refresh(params: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.ccu.qnet.refresh;
    return AxiosClient.executeWithCache({ url, method, params, isQnet: true });
  }
  static end({ token }: any) {
    const method = API_METHOD.GET;
    const url = ConfigApi.ccu.qnet.end;
    const params: any = {
      token
    };
    return AxiosClient.executeWithCache({ url, method, params, isQnet: true });
  }
}

export default QnetApi;
