import get from 'lodash/get';
import ConfigApi from '../../config/ConfigApi';
import { parseConfigParams, parseUrlString, removeURLQueryParams } from '../../utils/common';
import CardItem from '@vieon/models/CardItem';
import StreamItem, { StreamComment } from '@vieon/models/StreamItem';
import { detectURL, handleRibbonData } from '../../services/pageServices';
import {
  CONTENT_TYPE,
  ERROR_CODE,
  LIMIT_DATA,
  PAGE,
  PERMISSION,
  RIBBON_TYPE,
  SEO_CONFIG,
  SEO_PAGES
} from '../../constants/constants';
import PaymentApi from '../Payment';
import { setSessionPlay } from '../../store/actions/detail';
import { LinkPlaysToRetry } from '@vieon/models/subModels';
import isEmpty from 'lodash/isEmpty';
import ComingSoonItem from '@vieon/models/ComingSoonItem';
import AxiosClient from '../axiosClient';
import ConfigLocalStorage from '../../config/ConfigLocalStorage';

export default class PageApi {
  static getPageBanners({
    pageSlug,
    accessToken,
    profileToken,
    isMobile,
    ipAddress,
    userAgent,
    origin,
    isGlobal
  }: any) {
    const page = 0;
    const limit = 9;
    const url = `${ConfigApi.cm.pageBanners}?page=${page}&limit=${limit}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { page_slug: pageSlug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      isMobile,
      config
    })?.then((res: any) => {
      const listItems = res?.data?.items || [];
      const bannerData = listItems.map(
        (item: any, index: any) => new CardItem({ ...item, isMasterBanner: true, isGlobal }, index)
      );
      return { data: bannerData };
    });
  }

  static getPageRibbons({
    pageSlug,
    page,
    limit,
    accessToken,
    profileToken,
    isMobile,
    ipAddress,
    ssr,
    userAgent,
    origin
  }: any) {
    const indexOfPage = typeof Number(page) === 'number' ? Number(page) : 0;
    const indexOfLimit = typeof Number(limit) === 'number' ? Number(limit) : 30;
    const url = `${ConfigApi.cm.pageRibbons}?page=${indexOfPage}&limit=${indexOfLimit}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { page_slug: pageSlug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      isMobile,
      config,
      ssr
    })?.then((res: any) => {
      let isRankingBoard = false;
      const items: any = [];
      get(res, 'data.items', []).map((item: any) => {
        const { properties, type } = item || {};
        let directURL = null;
        if (type === RIBBON_TYPE.LIVE_TV || type === RIBBON_TYPE.EPG) directURL = PAGE.LIVE_TV;
        if (type === RIBBON_TYPE.LIVESTREAM) directURL = PAGE.LIVE_STREAM;
        if (type === RIBBON_TYPE.RANKING_BOARD) {
          isRankingBoard = true;
        } else {
          items.push({
            ...item,
            isViewAll: properties?.is_view_all === 1,
            directURL,
            loadedData: false
          });
        }
      });
      return { data: items || [], isRankingBoard };
    });
  }

  static getPageRibbonsID({
    id,
    page,
    limit,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    origin
  }: any) {
    const indexOfPage = typeof Number(page) === 'number' ? Number(page) : 0;
    const indexOfLimit = typeof Number(limit) === 'number' ? Number(limit) : 30;
    const url = `${ConfigApi.cm.pageRibbonsID}/${id}?page=${indexOfPage}&limit=${indexOfLimit}`;
    const method = ConfigApi.METHOD.GET;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      const listRemake = (res?.data?.items || []).map((item: any, index: any) => ({
        ...item,
        index
      }));
      return { data: listRemake };
    });
  }

  static getLivestreamEvents({
    slug,
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    origin,
    dispatch
  }: any) {
    const url = `${ConfigApi.cm.liveStream.liveEvents}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { live_event_slug: slug };
    ConfigLocalStorage.set('selectedLivestreamSlug', slug);
    const config: any = parseConfigParams({ ipAddress, userAgent, origin });
    config.forceUpdate = true;
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config,
      accessToken,
      profileToken,
      ssr
    }).then(async (res) => {
      const { content_concurrent_group, title, permission } = res.data || {};
      const linkPlaysToRetry = LinkPlaysToRetry(res?.data);
      let concurrentScreen = '';
      if (
        permission === PERMISSION.CAN_WATCH &&
        content_concurrent_group &&
        !isEmpty(linkPlaysToRetry)
      ) {
        concurrentScreen = await dispatch(
          setSessionPlay({
            titleId: res.data?.id,
            titleName: title,
            contentConcurrentGroup: content_concurrent_group,
            accessToken
          })
        );
      }
      const data: any = new StreamItem({ ...res?.data, concurrentScreen });
      data.success = res.success;
      data.httpCode = res.httpCode;
      data.message = res.message;
      const { isPremiumTVod, id, isEndStream, contentId } = data || {};
      let tvod = null;
      if (isPremiumTVod) {
        tvod = await PaymentApi.getTVodInfo({
          contentId: isEndStream ? contentId : id,
          contentType: isEndStream ? CONTENT_TYPE.MOVIE : CONTENT_TYPE.LIVESTREAM,
          isLiveEvent: !isEndStream,
          accessToken,
          profileToken,
          eventData: data
        });
        data.tvod = tvod;
      }
      return data || {};
    });
  }

  static getLivestreamEventsById({ id, accessToken, dispatch, isCard }: any) {
    const url = parseUrlString(`${ConfigApi.cm.liveStream.liveEventsById}`, 'id', id);
    const method = ConfigApi.METHOD.GET;
    const config = {
      forceUpdate: true
    };
    return AxiosClient.executeWithCache({ url, method, config, accessToken }).then(async (res) => {
      const { content_concurrent_group, title, permission } = res.data || {};
      const linkPlaysToRetry = LinkPlaysToRetry(res?.data);
      let concurrentScreen = '';
      if (
        permission === PERMISSION.CAN_WATCH &&
        content_concurrent_group &&
        !isCard &&
        !isEmpty(linkPlaysToRetry)
      ) {
        concurrentScreen = await dispatch(
          setSessionPlay({
            titleId: res.data?.id,
            titleName: title,
            contentConcurrentGroup: content_concurrent_group
          })
        );
      }
      const data: any = new StreamItem({ ...res?.data, concurrentScreen });
      const { isPremiumTVod, id, isEndStream, contentId } = data || {};
      data.success = res.success;
      data.httpCode = res.httpCode;
      data.message = res.message;
      let tvod = null;
      if (isPremiumTVod) {
        tvod = await PaymentApi.getTVodInfo({
          contentId: isEndStream ? contentId : id,
          contentType: isEndStream ? CONTENT_TYPE.MOVIE : CONTENT_TYPE.LIVESTREAM,
          isLiveEvent: !isEndStream,
          eventData: data
        });
        data.tvod = tvod;
      }
      return data || {};
    });
  }

  static getSEOAllPage({
    slug,
    keyword,
    keyConfig,
    keySchema,
    keyBreadcrumbs,
    ssr,
    uncheckURL,
    ipAddress,
    userAgent,
    origin
  }: any) {
    const url = `${ConfigApi.cm.seoAllPage}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = {};
    if ((slug || '').indexOf('#') > -1) slug = removeURLQueryParams(slug, '#');
    if (slug) {
      params.slug_url = uncheckURL ? slug : detectURL(slug);
    }
    if (keyword) {
      params.search_keyword = keyword;
    }
    if (!keyConfig) {
      params.key_config = SEO_CONFIG.SNIPPET_GENERAL;
    } else {
      params.key_config = keyConfig;
    }
    if (keySchema) {
      params.key_schema = keySchema;
    }
    if (keyBreadcrumbs) {
      params.breadcrumb = keyBreadcrumbs;
    }
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({ url, method, params, ssr, config })?.then((res: any) => {
      if (keyBreadcrumbs === SEO_PAGES.LIVETV_EPG) {
        if (res?.data && res?.data?.seo?.meta_robots) {
          res.data.seo.meta_robots = 'noindex,follow';
        }
      }
      return res?.data || {};
    });
  }

  static getDataRibbons({
    ribbonSlug,
    page,
    limit,
    accessToken,
    isTV,
    isMobile,
    ssr,
    ipAddress,
    isMWebToApp,
    imageMWebToApp,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const indexOfPage = typeof Number(page) === 'number' ? Number(page) : 0;
    const indexOfLimit = typeof Number(limit) === 'number' ? Number(limit) : 41;
    const url = `${ConfigApi.cm.ribbonV3}?page=${indexOfPage}&limit=${indexOfLimit}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { ribbon_slug: ribbonSlug };
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      isMobile,
      ssr,
      config
    })?.then((res: any) =>
      handleRibbonData({ res, isTV, isMWebToApp, imageMWebToApp, ribbonSlug, isMobile, isGlobal })
    );
  }

  static getLivestreamCommentById({ id, accessToken, limit }: any) {
    const indexOfLimit = typeof Number(limit) === 'number' ? Number(limit) : 30;
    const url = `${parseUrlString(
      `${ConfigApi.cm.liveStream.liveComment}`,
      'id',
      id
    )}?limit=${indexOfLimit}`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ url, method, accessToken })?.then((res: any) => {
      if (res?.data?.data) {
        res.data.data = (res?.data?.data || []).map((item: any) => new StreamComment({ ...item }));
        res.data.pined = new StreamComment(res?.data?.pin_data);
      }
      const blocked =
        res?.data?.error === ERROR_CODE.CODE_1001 || res?.data?.error === ERROR_CODE.CODE_400;
      return { ...res.data, blocked };
    });
  }

  static getContentBroadcasting({ indexOfPage, indexOfLimit }: any) {
    const url = `${ConfigApi.cm.getContentBroadcasting}?page=${indexOfPage}&limit=${
      indexOfLimit || LIMIT_DATA
    }`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = new ComingSoonItem({ ...res?.data, isBroadcasting: true });
      return { ...res, data };
    });
  }

  static getContentUpcoming({ indexOfPage, indexOfLimit }: any) {
    const url = `${ConfigApi.cm.getContentUpcoming}?page=${indexOfPage}&limit=${
      indexOfLimit || LIMIT_DATA
    }`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({
      url,
      method
    })?.then((res: any) => {
      const data = new ComingSoonItem({ ...res?.data, isUpComingSoon: true });
      return { ...res, data };
    });
  }
  static postLiveStreamCommentId({ id, comment, name, email, phone, avatar, accessToken }: any) {
    const url = parseUrlString(`${ConfigApi.cm.liveStream.liveComment}`, 'id', id);
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      comment,
      name,
      email,
      phone_number: phone,
      avatar
    };
    return AxiosClient.executeWithCache({ url, method, params, accessToken })?.then(
      (res: any) => res
    );
  }

  static getDataRibbonsId({
    id,
    page,
    limit,
    isTV,
    accessToken,
    profileToken,
    ribbonOrder,
    isLoadmore,
    ribbonName,
    ipAddress,
    ssr,
    isMWebToApp,
    imageMWebToApp,
    isMobile,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const url = `${ConfigApi.cm.ribbon}/${id}?page=${page || 0}&limit=${limit || 41}`;
    const method = ConfigApi.METHOD.GET;
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      accessToken,
      profileToken,
      config,
      ssr,
      isMobile
    })?.then((res: any) =>
      handleRibbonData({
        res,
        isTV,
        ribbonOrder,
        isLoadmore,
        ribbonName,
        isMWebToApp,
        imageMWebToApp,
        isMobile,
        isGlobal
      })
    );
  }
}
