import axios from 'axios';
import { API_METHOD, ERROR_CODE, POPUP } from '../../constants/constants';
import ConfigApi from '../../config/ConfigApi';
import { handleGetDeviceId, parseConfigParams, parseUrlString } from '../../utils/common';
import CardItem from '@vieon/models/CardItem';
import TipItem from '@vieon/models/TipItem';
import { openPopup } from '../../store/actions/popup';
import AxiosClient from '../axiosClient';

export default class ContentApi {
  static getRibbonDetailNotFound({
    accessToken,
    profileToken,
    ssr,
    ipAddress,
    userAgent,
    isGlobal,
    origin
  }: any) {
    const url = ConfigApi.cm.ribbonVODNotFound;
    const method = API_METHOD.GET;
    const params: any = {};
    const config = parseConfigParams({ ipAddress, userAgent, origin });
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      accessToken,
      profileToken,
      ssr,
      config
    })?.then((res: any) => {
      const items = (res?.data?.items || []).map(
        (item: any, index: any) => new CardItem({ ...item, isGlobal }, index)
      );
      return { ...res, data: { ...res?.data, items } };
    });
  }
  static getContentRelated({ contentSlug, page, limit }: any) {
    page = typeof Number(page) === 'number' ? Number(page) : 0;
    limit = typeof Number(limit) === 'number' ? Number(limit) : 10;
    const url = `${ConfigApi.cm.contentRelated}?page=${page}&limit=${limit}`;
    const method = API_METHOD.POST;
    const params: any = { entity_slug: contentSlug };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static contentRecommendVod({ contentSlug, page, limit }: any) {
    page = typeof Number(page) === 'number' ? Number(page) : 0;
    limit = typeof Number(limit) === 'number' ? Number(limit) : 10;
    const url = `${ConfigApi.cm.contentRecommendVod}?page=${page}&limit=${limit}`;
    const method = API_METHOD.POST;
    const params: any = { entity_slug: contentSlug };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static getContentPopver({ contentId, epsId }: any) {
    let url = '';
    if (epsId && epsId !== '') {
      url = `${ConfigApi.cm.contentPopver + contentId}/tips?eps_id=${epsId}`;
    } else {
      url = `${ConfigApi.cm.contentPopver + contentId}/tips`;
    }
    const method = API_METHOD.GET;
    const params: any = {};
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static getContentWatchlater({ limit, page, accessToken, ribbonOrder, ribbonId, isGlobal }: any) {
    const url = `${ConfigApi.cm.watchLater}?page=${page}&limit=${limit}`;
    const method = API_METHOD.GET;
    page = page || 0;
    limit = limit || 30;
    const params: any = { page, limit };
    const config = {
      forceUpdate: true
    };
    return AxiosClient.executeWithCache({ url, method, params, config, accessToken }).then(
      (res) => {
        if (res?.data) {
          res.data.items = (res.data.items || []).map(
            (item: any, index: any) =>
              new CardItem(
                { ...item, ribbonType: res.data?.type, ribbonOrder, ribbonId, isGlobal },
                index
              )
          );
        }
        return res;
      }
    );
  }

  static getContentWatchmore({
    limit,
    page,
    accessToken,
    ribbonOrder,
    ribbonId,
    ribbonName,
    isGlobal
  }: any) {
    const url = `${ConfigApi.cm.watchMore}?page=${page}&limit=${limit}`;
    const method = API_METHOD.GET;
    page = page || 0;
    limit = limit || 30;
    const params: any = { page, limit };
    const config = {
      forceUpdate: true
    };
    return AxiosClient.executeWithCache({ url, method, params, config, accessToken }).then(
      (res) => {
        let newItems = res?.data?.items || [];
        if (res?.data) {
          newItems = (res.data.items || []).map(
            (item: any, index: any) =>
              new CardItem(
                {
                  ...item,
                  ribbonType: res.data?.type,
                  ribbonOrder,
                  ribbonId,
                  ribbonName,
                  isGlobal
                },
                index
              )
          );
        }
        return { ...res, data: { ...res.data, items: newItems } };
      }
    );
  }

  static getContentToolTips({ id }: any) {
    const url = parseUrlString(ConfigApi.cm.getContentToolTips, 'contentID', id);
    const method = API_METHOD.GET;
    const config = {
      forceUpdate: true
    };
    return AxiosClient.executeWithCache({ url, method, config })?.then((res: any) => ({
      ...res,
      data: new TipItem(res?.data)
    }));
  }

  // Mai mot ai tach code thi nho gom ra chung giup. tks all
  static delContentWatchmore() {
    const url = `${ConfigApi.cm.watchMore}?is_all=true`;
    return axios.delete(url);
  }
  static addContentWatchlater({ contentId }: any) {
    const url = `${ConfigApi.cm.watchLater}`;
    const method = API_METHOD.POST;
    const params: any = { content_id: contentId };
    return AxiosClient.executeWithCache({ url, method, params });
  }
  static async getAllIndicator({ data, contentId, page, size }: any): Promise<any> {
    let result = data || {};
    const newPage = page || 1;
    const newSize = size || 100;

    const pageData = await this.getContentService({
      contentId,
      page: newPage,
      size: newSize
    });

    if (pageData.success) {
      const items = pageData?.items || [];
      const pagination = pageData?.pagination || [];
      const total = pagination?.total || 0;
      const meta = pageData?.meta || [];
      result = {
        items: (result?.items || []).concat(items),
        pagination,
        meta: {
          objects: (result?.meta?.objects || []).concat(meta?.objects),
          video: meta?.video
        }
      };
      if (Math.ceil(total / newSize) >= newPage + 1) {
        return this.getAllIndicator({ contentId, data: result, page: newPage + 1, size: newSize });
      }
    }
    return result;
  }

  static getContentService({ contentId, page, size }: any) {
    const method = API_METHOD.GET;
    const url = `${parseUrlString(
      ConfigApi.contentService.getDataVideoIndexing,
      'contentId',
      contentId
    )}?page=${page}&size=${size}`;
    return AxiosClient.executeWithCache({ url, method })?.then((res: any) => {
      const data = res?.data?.result;
      return { ...data, httpCode: res?.httpCode, success: res?.success };
    });
  }

  static registerConsultationViaPhone({
    contentId,
    objectId,
    phoneNumber,
    isDeleteNumberPhone
  }: any) {
    const url = ConfigApi.contentService.registerConsultation;
    const params: any = {
      contentId,
      objectId,
      phoneNumber: `+84${phoneNumber}`
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const method = isDeleteNumberPhone ? API_METHOD.DELETE : API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, params, method, config }).then((res: any) => {
      if (res?.success) {
        return {
          items: [
            {
              isValidate: !isDeleteNumberPhone,
              objectId,
              phoneNumber
            }
          ]
        };
      }
      return {
        items: [],
        success: false,
        message: res?.message || 'Registration failed'
      };
    });
  }

  static async setSessionPlay({
    titleId,
    seasonId,
    episodeId,
    titleName,
    contentConcurrentGroup,
    dispatch,
    accessToken
  }: any) {
    const method = API_METHOD.POST;
    const url = ConfigApi.cm.setSessionPlay;
    const deviceID = await handleGetDeviceId();
    const params: any = {
      device_id: deviceID || '',
      title_id: titleId || '',
      season_id: seasonId || '',
      episode_id: episodeId || '',
      title_name: titleName || '',
      content_group: contentConcurrentGroup || ''
    };
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    return AxiosClient.executeWithCache({
      url,
      method,
      params,
      config,
      accessToken
    })?.then((res: any) => {
      const data = {
        code: res?.data?.code,
        countdownBySec: res?.data?.result?.countdown_by_sec,
        expiryAt: res?.data?.result?.expiry_at,
        sessionList: res?.data?.result?.session_list,
        sessionToken: res?.data?.result?.session_token,
        httpCode: res?.httpCode,
        success: res?.success
      };
      if (res?.data?.code === ERROR_CODE.CODE_2 && res?.success) {
        dispatch(
          openPopup({
            name: POPUP.NAME.CONCURRENT_SCREEN,
            sessionList: res?.data?.result?.session_list,
            contentConcurrentGroup
          })
        );
      }
      return data;
    });
  }

  static refreshSessionPlay(sessionToken: any) {
    const method = API_METHOD.POST;
    const url = ConfigApi.cm.refreshSessionPlay;
    return AxiosClient.executeWithCache({ url, method, sessionToken })?.then((res: any) => {
      const data = {
        code: res?.data?.code,
        countdownBySec: res?.data?.result?.countdown_by_sec,
        expiryAt: res?.data?.result?.expiry_at,
        sessionToken: res?.data?.result?.session_token,
        httpCode: res?.httpCode,
        success: res?.success
      };
      return data;
    });
  }

  static endSessionPlay(sessionToken: any) {
    const method = API_METHOD.DELETE;
    const url = ConfigApi.cm.endSessionPlay;
    return AxiosClient.executeWithCache({ url, method, sessionToken, isEnd: true })?.then(
      (res: any) => {
        const data = res?.data;
        return { ...data, httpCode: res?.httpCode, success: res?.success };
      }
    );
  }

  static async likeToggle(params: any) {
    const method = API_METHOD.POST;
    const url = ConfigApi.cm.likeVideo;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const res = await AxiosClient.executeWithCache({ url, method, params, config });
    const data = res?.data;
    return { ...data, httpCode: res?.httpCode, success: res?.success };
  }

  static async getTotalLike(content_id: any) {
    const method = API_METHOD.GET;
    const url = parseUrlString(ConfigApi.cm.getTotalLike, 'contentId', content_id);
    const res = await AxiosClient.executeWithCache({ url, method });
    const data = res?.data;
    return { ...data, httpCode: res?.httpCode, success: res?.success };
  }
}
