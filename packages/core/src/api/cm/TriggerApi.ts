import ConfigApi from '../../config/ConfigApi';
import { API_METHOD } from '../../constants/constants';
import AxiosClient from '../axiosClient';
import isEmpty from 'lodash/isEmpty';

export default class TriggerApi {
  static getDataTriggerPoint({ type, contentId }: any) {
    const url = ConfigApi.cm.getDataTriggerPoint;
    const method = API_METHOD.GET;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = { type, content_id: contentId || '' };
    return AxiosClient.executeWithCache({ url, method, params, config })?.then((res: any) => {
      const data = res.data?.result;
      if (res.success && !isEmpty(data)) {
        return { image: data?.image, navigateUrl: data?.navigate_url };
      }
      return {};
    });
  }
  static getDialogPaymentTrigger({ type, contentId, contentType }: any) {
    const url = ConfigApi.cm.getDialogPaymentTrigger;
    const method = API_METHOD.GET;
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };
    const params: any = {
      trigger_type: type,
      content_id: contentId || '',
      content_type: contentType
    };
    return AxiosClient.executeWithCache({ url, method, params, config });
  }
}
