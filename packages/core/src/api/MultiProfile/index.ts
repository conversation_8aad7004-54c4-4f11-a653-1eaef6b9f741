import { API_METHOD, LIMIT_DATA } from '../../constants/constants';
import ConfigApi from '../../config/ConfigApi';
import { parseUrlString } from '../../utils/common';
import { DetailWatch, ExportEmail, SummaryWatch } from '@vieon/models/kidActivity';
import ConfigLocalStorage from '../../config/ConfigLocalStorage';
import LocalStorage from '../../config/LocalStorage';
import AxiosClient from '../axiosClient';

const config = {
  headers: {
    'Content-Type': 'application/json'
  }
};
export default class MultiProfileApi {
  static createProfile = async (dataRequest: any) => {
    const url = ConfigApi.multiProfile.profiles;
    const method = ConfigApi.METHOD.POST;
    const params: any = dataRequest;
    return AxiosClient.executeWithCache({ method, url, params, config });
  };
  static editProfile = async (dataRequest: any) => {
    const url = `${ConfigApi.multiProfile.profiles}/${dataRequest?.profileId || ''}`;
    const method = ConfigApi.METHOD.PATCH;
    const params: any = dataRequest;
    return AxiosClient.executeWithCache({ method, url, params, config });
  };
  static getProfileId = async (profileId: any) => {
    const url = `${ConfigApi.multiProfile.profiles}/${profileId || ''}`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ method, url })?.then((res: any) => res);
  };
  static getAvatars = async () => {
    const url = ConfigApi.multiProfile.avatars;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ method, url })?.then((res: any) => res);
  };
  static deleteProfile = ({ id }: any) => {
    if (!id) return;
    const url = `${ConfigApi.multiProfile.profiles}/${id}`;
    return AxiosClient.executeWithCache({
      url,
      method: API_METHOD.DELETE
    });
  };

  // get multi profile (all profile)
  static getMultiProfile = async ({ accessToken, kid }: any) => {
    const url = ConfigApi.multiProfile.profiles;
    const method = API_METHOD.GET;
    let params = {};
    if (kid) {
      params = { kid };
    }
    return AxiosClient.executeWithCache({ accessToken, method, url, params })?.then((res: any) => {
      if (kid) return res?.data?.result;
      if (!res?.success || (res?.data?.result?.items || []).length === 0) {
        const profileList = ConfigLocalStorage.get(LocalStorage.PROFILE_LIST);
        const profileListStr = typeof profileList === 'string' ? profileList : '[]';
        return { items: JSON.parse(profileListStr) };
      }
      ConfigLocalStorage.set(LocalStorage.PROFILE_LIST, JSON.stringify(res?.data?.result?.items));
      return res?.data?.result;
    });
  };
  static validatePassword = async ({ password }: any) => {
    const url = ConfigApi.multiProfile.validatePassword;
    const method = API_METHOD.POST;
    const params: any = { password };
    return AxiosClient.executeWithCache({ method, url, params, config });
  };
  static validatePinCode = async ({ profileId, pinCode }: any) => {
    const url = parseUrlString(`${ConfigApi.multiProfile.validatePinCode}`, 'profileId', profileId);
    const method = API_METHOD.POST;
    const params: any = { pinCode };
    return AxiosClient.executeWithCache({ method, url, params });
  };
  static updatePinCode = async ({ profileId, pinCode, password, newPinCode }: any) => {
    const url = parseUrlString(`${ConfigApi.multiProfile.updatePinCode}`, 'profileId', profileId);
    const method = API_METHOD.POST;
    const params: any = {
      pinCode: pinCode || '',
      newPinCode
    };
    if (password) params.password = password;
    return AxiosClient.executeWithCache({ method, url, params, config });
  };
  static forgotPinCode = async ({ profileId }: any) => {
    const url = parseUrlString(`${ConfigApi.multiProfile.forgotPinCode}`, 'profileId', profileId);
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ method, url });
  };
  static resetPinCode = async ({ profileId, pinCode, confirmNo, otpCode }: any) => {
    const url = parseUrlString(`${ConfigApi.multiProfile.resetPinCode}`, 'profileId', profileId);
    const method = API_METHOD.POST;
    const params: any = {
      pinCode: pinCode || '',
      confirmNo: confirmNo || '',
      otpCode: otpCode || ''
    };
    return AxiosClient.executeWithCache({ method, url, params, config });
  };
  static getAgreement = async () => {
    const url = ConfigApi.multiProfile.agreement;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ method, url });
  };
  static updateAgreement = async ({ accepted }: any) => {
    const url = ConfigApi.multiProfile.agreement;
    const method = API_METHOD.PUT;
    const params: any = { accepted };
    return AxiosClient.executeWithCache({ method, url, params });
  };

  // get Access token profile
  static getAccessTokenOfProfile = async ({ id, pinCode, accessToken }: any) => {
    if (!id) return;
    const url = `${ConfigApi.multiProfile.profiles}/${id}/access`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { pinCode: pinCode || '' };
    return AxiosClient.executeWithCache({ accessToken, method, url, params, config }).then(
      (res) => res
    );
  };
  static getProfileSelected = async (dataRequest: any) => {
    const { accessToken, profileToken } = dataRequest || {};
    return AxiosClient.executeWithCache({
      method: ConfigApi.METHOD.GET,
      url: ConfigApi.multiProfile.myProfile,
      accessToken,
      profileToken
    })?.then((res: any) => res);
  };

  static async getExportActivityEmail() {
    const url = ConfigApi.kidActivity.exportEmail;
    const method = API_METHOD.GET;
    const res = await AxiosClient.executeWithCache({ url, method, config });
    return ExportEmail(res?.data);
  }

  static async getSummaryWatch({ profileId, fromDate, toDate }: any) {
    const url = parseUrlString(`${ConfigApi.kidActivity.getSummaryWatch}`, 'profileId', profileId);
    const method = API_METHOD.GET;
    const params: any = { from_date: fromDate, to_date: toDate };
    const res = await AxiosClient.executeWithCache({ url, params, method, config });
    return SummaryWatch(res?.data);
  }

  static async getDetailWatch({ profileId, offset }: any) {
    const url = parseUrlString(`${ConfigApi.kidActivity.getDetailWatch}`, 'profileId', profileId);
    const method = API_METHOD.GET;
    const params: any = { offset, limit: LIMIT_DATA };
    const res = await AxiosClient.executeWithCache({ url, params, method, config });
    return DetailWatch(res?.data);
  }
  static exportActivityEmail({ isExportEmail, email }: any) {
    const url = ConfigApi.kidActivity.exportEmail;
    const params: any = {
      is_export_email: isExportEmail,
      email
    };
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, params, method, config });
  }
  static insertLobbyTutorial() {
    const url = ConfigApi.user.insertLobbyTutorial;
    const method = API_METHOD.POST;
    return AxiosClient.executeWithCache({ url, method, config });
  }
  static getLobbyTutorial() {
    const url = ConfigApi.user.getLobbyTutorial;
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method, config });
  }
  static async getRestrictedListOfKids({ contentId }: any) {
    const url = parseUrlString(
      ConfigApi.cm.titleRestriction.restrictedListOfKids,
      'contentId',
      contentId
    );
    const method = API_METHOD.GET;
    const res = await AxiosClient.executeWithCache({ url, method, config });
    return res;
  }
  static async updateRestrictedContent(data: any) {
    const url = ConfigApi.cm.titleRestriction.updateRestrictedContent;
    const method = API_METHOD.PATCH;
    const arrayParam = data;
    const res = await AxiosClient.executeWithCache({
      url,
      method,
      arrayParam,
      config
    });
    return res;
  }
  static getRestrictionContent(profileId: any) {
    const url = parseUrlString(`${ConfigApi.user.getRestrictionContent}`, 'profileId', profileId);
    const method = API_METHOD.GET;
    return AxiosClient.executeWithCache({ url, method, config });
  }
  static removeRestriction(dataRemove: any) {
    const url = ConfigApi.user.removeRestriction;
    const method = API_METHOD.PATCH;
    const arrayParam = dataRemove;
    return AxiosClient.executeWithCache({ url, method, config, arrayParam });
  }
}
