import axios from 'axios';
import isArray from 'lodash/isArray';
import {
  TIME_OUT_REQUEST_API,
  LOCAL_DOMAIN_ENABLE,
  NODE_ENV as NODE_ENV_CONFIG
} from '../config/ConfigEnv';
import {
  API_METHOD,
  ERROR_CODE,
  HTTP_CODE,
  NODE_ENV,
  PAGE,
  PLATFORM,
  POPUP
} from '../constants/constants';
import { TEXT } from '../constants/text';
import {
  handleMultiDomainLocal,
  json_to_query_string,
  removeAccessTokenProfile,
  setUrlParams
} from '../utils/common';
import TrackingApp from '@vieon/tracking/services/functions/TrackingApp';
import { openPopup } from '../store/actions/popup';
import { setBlockPlayer } from '../store/actions/player';
import { setToast } from '../store/actions/app';
import { endSessionPlay } from '../store/actions/detail';

declare global {
  interface Window {
    __NEXT_REDUX_STORE__?: any;
  }
}

export default class AxiosClient {
  static executeWithCache({
    url,
    method,
    params,
    config,
    accessToken,
    profileToken,
    isMobile,
    isTablet,
    ssr,
    isSmartTvPlatform,
    arrayParam,
    sessionToken
  }: any) {
    let platform = PLATFORM.WEB;
    const configAxios = { ...(config || {}) };

    let authorization = accessToken || '';

    let mobile = false;
    let tablet = false;
    let smartTv = false;
    if (typeof window !== 'undefined') {
      const store = window.__NEXT_REDUX_STORE__;
      const state = store.getState();
      const appState = state?.App;
      if (!authorization) authorization = appState?.token;
      if (!mobile) mobile = appState?.isMobile;
      tablet = !tablet && appState?.isTablet;
      smartTv = !smartTv && appState?.isSmartTvPlatform;
    }

    if (typeof isMobile === 'boolean') mobile = isMobile;
    if (typeof isSmartTvPlatform === 'boolean') smartTv = isSmartTvPlatform;
    if (typeof isTablet === 'boolean') tablet = isTablet;

    configAxios.headers = configAxios.headers || {};

    if (authorization) {
      configAxios.headers.Authorization = authorization || '';
    }

    if (profileToken) {
      configAxios.headers['Profile-Token'] = profileToken || '';
    }
    if (sessionToken) {
      configAxios.headers['internal-session-play'] = sessionToken || '';
    }

    if (
      (config?.ipForwarded || config?.userAgent || config?.origin) &&
      typeof window === 'undefined'
    ) {
      if (config?.ipForwarded) {
        configAxios.headers = {
          ...(configAxios.headers || {}),
          ...(configAxios.ipForwarded || {})
        };
      }
      if (config?.userAgent) {
        configAxios.headers = {
          ...(configAxios.headers || {}),
          ...(configAxios.userAgent || {})
        };
      }
      if (config?.origin) {
        configAxios.headers = {
          ...(configAxios.headers || {}),
          ...(configAxios.origin || {})
        };
      }
    }

    if (mobile) {
      platform = PLATFORM.MOBILE_WEB;
    }

    if (params?.isMorePlatform) {
      if (smartTv) {
        platform = PLATFORM.SMART_TV_WEB;
      }
      if (tablet) {
        platform = PLATFORM.TABLET_WEB;
      }
    }

    const newParams = arrayParam || {
      ...params,
      platform,
      ui: '012021'
    };

    let urlSSR = url;
    let isUseUrlSSR = false;
    // ON Change domain local
    if (!!ssr && NODE_ENV_CONFIG === NODE_ENV.PROD && LOCAL_DOMAIN_ENABLE === true) {
      // TODO off local domain
      const { ssrUrl, isUseDomainLocal } = handleMultiDomainLocal(urlSSR);

      urlSSR = ssrUrl;
      isUseUrlSSR = isUseDomainLocal;
    }
    if (configAxios && isUseUrlSSR && !configAxios.timeout) {
      configAxios.timeout = TIME_OUT_REQUEST_API;
    }
    switch (method) {
      case API_METHOD.GET: {
        const newUrl = setUrlParams(urlSSR, { ...newParams });
        const axiosUrl = newUrl?.href;
        return axios
          .get(axiosUrl, configAxios || {})
          ?.then((res: any) => responseData.createResponseData(res || {}))
          .catch((e) => {
            const response = e?.response || {};
            const resData = responseData.createResponseData(response || {});
            this.handleError(resData);
            return resData;
          });
      }
      case API_METHOD.POST: {
        let data = json_to_query_string(newParams || {});
        const newUrl = setUrlParams(urlSSR, {
          platform,
          api_version: params?.api_version,
          ui: '012021'
        });
        const axiosUrl = newUrl?.href;
        if (config?.headers) data = newParams || {};
        return axios
          .post(axiosUrl, data, configAxios)
          ?.then((res: any) => responseData.createResponseData(res || {}))
          .catch((e) => {
            const response = e?.response || {};
            const resData = responseData.createResponseData(response || {});
            this.handleError(resData);
            return resData;
          });
      }
      case API_METHOD.PATCH: {
        let data = json_to_query_string(newParams || {});
        const newUrl = setUrlParams(urlSSR, {
          platform,
          api_version: params?.api_version,
          ui: '012021'
        });
        const axiosUrl = newUrl?.href;
        if (config?.headers) data = newParams || {};
        return axios
          .patch(axiosUrl, data, configAxios)
          ?.then((res: any) => responseData.createResponseData(res || {}))
          .catch((e) => {
            const response = e?.response || {};
            const resData = responseData.createResponseData(response || {});
            this.handleError(resData);
            return resData;
          });
      }
      case API_METHOD.PUT: {
        const newUrl = setUrlParams(urlSSR, { platform });
        const axiosUrl = newUrl?.href;
        return axios
          .put(axiosUrl, params)
          ?.then((res: any) => responseData.createResponseData(res || {}))
          .catch((e) => {
            const response = e?.response || {};
            const resData = responseData.createResponseData(response || {});
            this.handleError(resData);
            return resData;
          });
      }
      case API_METHOD.DELETE: {
        const newUrl = setUrlParams(urlSSR, { platform });
        const axiosUrl = newUrl?.href;
        if (params) {
          return axios
            .request({ url: axiosUrl, method: 'delete', data: params })
            ?.then((res: any) => responseData.createResponseData(res || {}))
            .catch((e) => {
              const response = e?.response || {};
              const resData = responseData.createResponseData(response || {});
              this.handleError(resData);
              return resData;
            });
        }
        return axios
          .delete(axiosUrl, params || configAxios)
          ?.then((res: any) => responseData.createResponseData(res || {}))
          .catch((e) => {
            const response = e?.response || {};
            const resData = responseData.createResponseData(response || {});
            this.handleError(resData);
            return resData;
          });
      }
      default:
        return Promise.reject(new Error(`Unsupported API method: ${method}`));
    }
  }

  static handleError = (response: any) => {
    const isClient = typeof window !== 'undefined';
    if (!isClient) return;
    this.handleBlockAccount(response);
    this.handleBlockAccountDelete(response);
    this.handleProfileDeleted(response);
    this.checkUnderConstruction(response);
    this.handleKickedDevice(response);
    TrackingApp.apiError(response);
  };

  static handleProfileDeleted(response: any) {
    const isClient = typeof window !== 'undefined';
    try {
      if (isClient) {
        const httpCode = response?.httpCode;
        const store = window.__NEXT_REDUX_STORE__;
        const state = store.getState();
        const popupName = state?.Popup?.popupName;
        if (
          httpCode === HTTP_CODE.PROFILE_DELETED ||
          httpCode === HTTP_CODE.TOKEN_PROFILE_INVALID ||
          (httpCode === HTTP_CODE.NOT_FOUND && response?.data?.code === ERROR_CODE.CODE_109) ||
          (httpCode === HTTP_CODE.FAIL && response?.data?.code === ERROR_CODE.CODE_110)
        ) {
          if (store && store.dispatch) {
            if (window?.location?.pathname?.includes(PAGE.LOBBY_PROFILES)) {
              removeAccessTokenProfile();
              store.dispatch(setToast({ message: TEXT.PROFILE_DELETED }));
              window.location = PAGE.LOBBY_PROFILES;
            } else if (popupName !== POPUP.NAME.PROFILE_HAS_DELETED) {
              store.dispatch(
                openPopup({
                  name: POPUP.NAME.PROFILE_HAS_DELETED
                })
              );
              store.dispatch(setBlockPlayer(true));
            }
          }
        }
      }
    } catch (e) {
      // console.log(e)
    }
  }

  static handleBlockAccount(response: any) {
    const isClient = typeof window !== 'undefined';
    try {
      if (isClient) {
        const httpCode = response?.httpCode;
        const store = window.__NEXT_REDUX_STORE__;
        const state = store.getState();
        const { concurrentScreen, dataRefreshSession } = state.Detail;
        const sessionToken = dataRefreshSession?.sessionToken || concurrentScreen?.sessionToken;
        const popupName = state?.Popup?.popupName;
        if (httpCode === HTTP_CODE.BLOCKED_ACCOUNT) {
          if (store && store.dispatch && popupName !== POPUP.NAME.BLOCK_ACCOUNT) {
            if (sessionToken) {
              store.dispatch(endSessionPlay(sessionToken));
            }
            store.dispatch(
              openPopup({
                name: POPUP.NAME.BLOCK_ACCOUNT
              })
            );
          }
        }
      }
    } catch (e) {
      // console.log(e)
    }
  }

  static handleBlockAccountDelete(response: any) {
    const httpCode = response?.httpCode;
    const store = window.__NEXT_REDUX_STORE__;
    const state = store.getState();
    const popupName = state?.Popup?.popupName;
    if (httpCode === HTTP_CODE.BLOCKED_ACCOUNT_DELETE) {
      if (store && store.dispatch && popupName !== POPUP.NAME.BLOCK_ACCOUNT_DELETE) {
        store.dispatch(
          openPopup({
            name: POPUP.NAME.BLOCK_ACCOUNT_DELETE
          })
        );
        store.dispatch(setBlockPlayer(true));
      }
    }
  }

  static checkUnderConstruction(response: any) {
    const httpCode = response?.httpCode;
    if (httpCode === HTTP_CODE.UNDER_CONSTRUCTION) {
      if (window?.location?.pathname && window?.location?.pathname !== PAGE.MAINTENANCE) {
        window.location.href = PAGE.MAINTENANCE;
      }
    }
  }

  static handleKickedDevice(response: any) {
    const httpCode = response?.httpCode;
    const store = window.__NEXT_REDUX_STORE__;
    const state = store.getState();
    const popupName = state?.Popup?.popupName;

    if (httpCode === HTTP_CODE.KICKED_DEVICE) {
      if (store && store.dispatch && popupName !== POPUP.NAME.KICKED_DEVICE) {
        store.dispatch(
          openPopup({
            name: POPUP.NAME.KICKED_DEVICE
          })
        );
        store.dispatch(setBlockPlayer(true));
      }
    }
  }
}

class responseData {
  data: any;
  httpCode: any;
  message: any;
  refreshFail: any;
  statusText: any;
  success: any;
  constructor() {
    this.success = false;
    this.statusText = '';
    this.message = '';
    this.refreshFail = false;
    this.data = null;
    this.httpCode = 0;
  }

  static createResponseData(_data: any) {
    const data: any = new responseData();
    if (_data) {
      data.httpCode = _data.status;
      data.success = _data.status === 200;
      data.refreshFail = _data.refreshFail || false;
      if (_data.status !== 200) {
        data.config = {
          headers: _data?.config?.headers || {},
          params: _data?.config?.params || {},
          url: _data?.config?.url || ''
        };
      }
      data.statusText = _data.statusText || '';
      if (_data.problem && _data.problem === 'TIMEOUT_ERROR') {
        data.message = 'Request timeout';
      }
      data.message = _data.message || '';
      if (isArray(_data.data)) {
        data.data = { items: _data.data };
      } else {
        data.data = _data.data || null;
      }
    }
    return data;
  }
}
