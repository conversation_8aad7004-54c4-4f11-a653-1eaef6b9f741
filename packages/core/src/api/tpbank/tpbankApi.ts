import ConfigApi from '../../config/ConfigApi';
import { getDeviceId, getModelDevice, saveAccessToken } from '../../utils/common';
import { UTM_SOURCE } from '../../constants/constants';
import AxiosClient from '../axiosClient';

class TpbankApi {
  static checkAccountExist({ phoneNumber }: any) {
    const url = `${ConfigApi.tpbank.checkAccountExist}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { phone: phoneNumber };
    return AxiosClient.executeWithCache({ method, url, params, isMobile: true })?.then(
      (res: any) => res
    );
  }
  static createAccountByPhone({ phoneNumber }: any) {
    const url = `${ConfigApi.tpbank.createAccountByPhone}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = { phone_number: phoneNumber };
    return AxiosClient.executeWithCache({ method, url, params, isMobile: true })?.then(
      (res: any) => {
        const accessToken = res?.data?.access_token || '';
        if (accessToken) {
          saveAccessToken(accessToken);
        }
        return res;
      }
    );
  }
  static loginByPhone({ phoneNumber, password }: any) {
    const url = `${ConfigApi.tpbank.loginMobile}`;
    const method = ConfigApi.METHOD.POST;
    const deviceId = getDeviceId();
    const params: any = {
      phone_number: phoneNumber,
      password,
      device_id: deviceId,
      model: getModelDevice(),
      push_token: ''
    };
    return AxiosClient.executeWithCache({ method, url, params, isMobile: true })?.then(
      (res: any) => {
        const accessToken = res?.data?.access_token || '';
        if (accessToken) {
          saveAccessToken(accessToken);
        }
        return res;
      }
    );
  }
  static getBillingPackage() {
    const url = `${ConfigApi.tpbank.billingPackage}`;
    const method = ConfigApi.METHOD.GET;
    return AxiosClient.executeWithCache({ method, url, isMobile: true })?.then((res: any) => res);
  }
  static createTransaction({
    userId,
    packageId,
    userTokenTpbank,
    diviceCodeTpBank,
    platform,
    promotionCode = ''
  }: any) {
    const url = `${ConfigApi.tpbank.createTransaction}`;
    const method = ConfigApi.METHOD.POST;
    const params: any = {
      user_id: userId,
      package_id: packageId,
      user_token_tpbank: userTokenTpbank,
      device_code_tpbank: diviceCodeTpBank,
      platform,
      promotion_code: promotionCode || '',
      utm_source: UTM_SOURCE.TP_BANK
    };
    return AxiosClient.executeWithCache({ method, url, params, isMobile: true })?.then(
      (res: any) => res
    );
  }
}

// Export component
export default TpbankApi;
