{"name": "@vieon/provider", "version": "1.0.0", "description": "VieON provider components", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"react-google-recaptcha-v3": "^1.11.0"}, "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3", "rimraf": "^3.0.2"}, "peerDependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "files": ["dist", "src"]}