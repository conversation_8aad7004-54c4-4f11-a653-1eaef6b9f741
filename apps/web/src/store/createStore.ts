import thunkMiddleware from 'redux-thunk';
import { applyMiddleware, createStore } from 'redux';
import { NODE_ENV as NODE_ENV_CONFIG } from '@config/ConfigEnv';
import { NODE_ENV } from '@constants/constants';
import rootReducer from '@reducers/index';
import { composeWithDevTools } from 'redux-devtools-extension';

function createMiddlewares({ isServer }: any) {
  const middlewares = [thunkMiddleware];
  return middlewares;
}

const CreateStore = (initialState?: any, context?: any) => {
  const { isServer } = context;
  const middlewares = createMiddlewares({ isServer });

  const composeStore =
    NODE_ENV_CONFIG === NODE_ENV.DEV
      ? composeWithDevTools(applyMiddleware(...middlewares))
      : applyMiddleware(...middlewares);
  return createStore(rootReducer, initialState, composeStore);
};

export default CreateStore;
