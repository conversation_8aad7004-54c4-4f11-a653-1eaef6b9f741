import React, { useEffect } from 'react';
import { ENABLE_SDK_DMCA } from '@config/ConfigEnv';

const DMCA = () => {
  useEffect(() => {
    const loadDMCAScript = async () => {
      if (!ENABLE_SDK_DMCA || ENABLE_SDK_DMCA === 'false') {
        return;
      }

      try {
        const script = document.createElement('script');
        script.src = 'https://images.dmca.com/Badges/DMCABadgeHelper.min.js';
        script.id = 'DMCA';
        script.async = true;
        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
          document.body.appendChild(script);
        });

        console.log('DMCA script loaded successfully');
      } catch (error) {
        console.error('Failed to load DMCA script:', error);
      }
    };

    loadDMCAScript();

    return () => {
      const script = document.getElementById('DMCA');
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, []);

  return null;
};

export default React.memo(DMCA);
