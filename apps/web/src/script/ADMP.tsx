import React from 'react';
import { DMP_ID, ENABLE_SDK_AIACTIV } from '@config/ConfigEnv';

const ADMP = () => {
  if (!ENABLE_SDK_AIACTIV) return null;
  return (
    <script
      id="DMP"
      async
      defer
      dangerouslySetInnerHTML={{
        __html: `
        window.AiactivSDK||(window.AiactivSDK={}),AiactivSDK.load=function(t){var e=document.createElement("script");e.async=!0,e.type="text/javascript",e.src="https://sdk-cdn.aiactiv.io/aiactiv-sdk.min.js?t="+Date.now(),e.addEventListener?e.addEventListener("load",function(e){"function"==typeof t&&t(e)},!1):e.onreadystatechange=function(){("complete"==this.readyState||"loaded"==this.readyState)&&t(window.event)};let a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(e,a)},AiactivSDK.load(function(){AiactivSDK.initialize({containerId:"${DMP_ID}", type: ["adnetwork", "dmp"]}),AiactivSDK.callMethodsFromContainer()});
      `
      }}
    />
  );
};

export default React.memo(ADMP);
