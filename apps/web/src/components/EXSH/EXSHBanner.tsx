'use client';

import React, { useState, useLayoutEffect, useMemo } from 'react';
import Swiper<PERSON><PERSON>, { Pagination, Navigation, Lazy } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@/customHook';
import { RIBBON_TYPE, PROMOTION_BANNER_FUNC_TYPE, PAGE } from '@constants/constants';
import { encodeParamDestination, getAttrLink, onOpenPayment } from '@helpers/common';
import { useSelector } from 'react-redux';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import CustomImage from '../basic/Image/Image';
import { motion, Variants } from 'framer-motion';
import classNames from 'classnames';
import LocalStorage from '@config/LocalStorage';
import ConfigLocalStorage from '@config/ConfigLocalStorage';

SwiperCore.use([Pagination, Navigation, Lazy]);

const shineVariants: Variants = {
  initial: { left: '-100%', opacity: 0 },
  animate: {
    left: '150%',
    opacity: 1,
    transition: { duration: 0.8, ease: [0.4, 0, 0.2, 1] }
  }
};

const PortraiBannerEXSH = ({ data }: any) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const { isGlobal } = useSelector((state: any) => state?.App?.geoCheck || {});
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const router = useVieRouter();

  const isMultiSlide = useMemo(() => data.length > 1, [data.length]);
  const handleClickBanner = (item: any) => {
    if (!item) return;

    const { allowClick, externalUrl, type, seo, optionDirect, optionDirectPackageId = 0 } = item;

    const params: any = {
      pathname: router?.pathname,
      asPath: router?.asPath,
      queryParams: router.query
    };
    ConfigLocalStorage.set(LocalStorage.BACK_FROM_PLAYER, JSON.stringify(params));
    if (type === RIBBON_TYPE.PROMOTION_BANNER_FUNC) {
      if (
        [
          PROMOTION_BANNER_FUNC_TYPE.buyPackage,
          PROMOTION_BANNER_FUNC_TYPE.buyVip,
          PROMOTION_BANNER_FUNC_TYPE.buyVipKPLUS,
          PROMOTION_BANNER_FUNC_TYPE.buyVipHBO,
          PROMOTION_BANNER_FUNC_TYPE.buyVipSPORT,
          PROMOTION_BANNER_FUNC_TYPE.buyVipFAMILY,
          PROMOTION_BANNER_FUNC_TYPE.buyAllAccess
        ].includes(optionDirect)
      ) {
        onOpenPayment(router, {
          returnUrl: window?.location?.href,
          pkg: optionDirectPackageId,
          newTriggerPaymentBuyPackage: { isGlobal, profileId: profile?.id }
        });
      } else if (
        [PROMOTION_BANNER_FUNC_TYPE.Login, PROMOTION_BANNER_FUNC_TYPE.Register].includes(
          optionDirect
        )
      ) {
        const remakeDestination = encodeParamDestination(router?.asPath);
        router.push(
          `${PAGE.AUTH}/?destination=${remakeDestination}&page=${router?.pathname}&trigger=${TYPE_TRIGGER_AUTH.PROMOTION}`
        );
      }
    } else if (allowClick === 1) {
      if (externalUrl) {
        const url = externalUrl.match(/^https?:/) ? externalUrl : `//${externalUrl}`;
        const finalUrl =
          url.includes('{accesstoken}') && profile?.token
            ? url.replace('{accesstoken}', profile.token)
            : url;
        window.open(finalUrl, '_blank');
      } else {
        const attrLink = getAttrLink({ as: '', href: '', type });
        router.push(attrLink.href, seo?.url);
      }
    }
  };

  useLayoutEffect(() => {
    if (!isMultiSlide) return;
    const bullets = document.querySelectorAll('.custom-swiper-pagination span');
    bullets.forEach((bullet, index) => {
      bullet.classList.remove(
        '!bg-[linear-gradient(to_right,_#C71F5C,_#FFD8FE,_#EBD0DA,_#FAF5F7,_#FFFFFF,_#B39AA2,_#DA86AD)]',
        'bg-[#ffffff66]'
      );
      bullet.classList.add(
        index === activeIndex
          ? '!bg-[linear-gradient(to_right,_#C71F5C,_#FFD8FE,_#EBD0DA,_#FAF5F7,_#FFFFFF,_#B39AA2,_#DA86AD)]'
          : 'bg-[#ffffff66]'
      );
    });
  }, [activeIndex, isMultiSlide]);

  if (!data?.length) return null;

  return (
    <div className="relative w-full overflow-hidden h-max">
      <Swiper
        slidesPerView="auto"
        loop={isMultiSlide}
        allowTouchMove={isMultiSlide}
        centeredSlides={false}
        initialSlide={0}
        spaceBetween={4}
        lazy={{
          loadPrevNext: isMultiSlide,
          loadPrevNextAmount: 1,
          loadOnTransitionStart: true
        }}
        preloadImages={false}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        navigation={
          isMultiSlide
            ? {
                nextEl: '.custom-swiper-next',
                prevEl: '.custom-swiper-prev'
              }
            : false
        }
        pagination={
          isMultiSlide
            ? {
                el: '.custom-swiper-pagination',
                clickable: true,
                renderBullet: (_, className) =>
                  `<span class="${className} transition-all duration-500 hover:opacity-90 inline-block h-2 w-8 mx-1 rounded-full cursor-pointer"></span>`
              }
            : false
        }
        className={classNames('relative', isMultiSlide && 'mb-12')}
      >
        {data.map((item: any, index: any) => {
          const imageSrc = item?.images?.posterOriginal || item?.images?.thumbnail;
          const isVisible =
            index === activeIndex ||
            index === (activeIndex - 1 + data.length) % data.length ||
            index === (activeIndex + 1) % data.length;
          return (
            <SwiperSlide key={item.id}>
              <motion.div
                className="w-full aspect-[440/635] rounded-2xl z-10 relative cursor-pointer overflow-hidden"
                onClick={() => handleClickBanner(item)}
                initial="initial"
                whileHover="animate"
              >
                <div className="relative w-full h-full rounded-2xl overflow-hidden">
                  <CustomImage
                    src={isVisible ? imageSrc : undefined}
                    dataSrc={imageSrc}
                    alt={item?.title}
                    notWebp
                    className={`rounded-2xl w-full h-full object-cover ${
                      isMultiSlide ? 'swiper-lazy' : ''
                    }`}
                    loading="lazy"
                    imageLazyLoad={isMultiSlide}
                  />
                  {isMultiSlide && (
                    <div className="swiper-lazy-preloader swiper-lazy-preloader-white" />
                  )}
                </div>
                <motion.div
                  className="absolute top-0 left-0 w-1/2 h-full pointer-events-none z-20"
                  variants={shineVariants}
                  style={{
                    background:
                      'linear-gradient(135deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.3) 75%, rgba(255,255,255,0) 100%)',
                    transform: 'skewX(-25deg)'
                  }}
                />
              </motion.div>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {isMultiSlide && (
        <>
          {/* Navigation */}
          <div className="absolute bottom-0.5 inset-x-0 flex justify-between px-2">
            <Button className="custom-swiper-prev w-8 h-8 text-lg after:content-[''] after:font-[vie,sans-serif] after:text-[#EB98C8] hover:after:opacity-80 after:transition-all z-20" />
            <Button className="custom-swiper-next w-8 h-8 text-lg after:content-[''] after:font-[vie,sans-serif] after:text-[#EB98C8] hover:after:opacity-80 after:transition-all z-20" />
          </div>
          {/* Pagination */}
          <div className="custom-swiper-pagination absolute bottom-4 inset-x-0 flex justify-center z-10" />
        </>
      )}
    </div>
  );
};

export default React.memo(PortraiBannerEXSH);
