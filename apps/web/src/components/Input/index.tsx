import React, { useState, useEffect, useRef, useLayoutEffect, useCallback } from 'react';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames/bind';
import Dropdown from '@components/Dropdown';
import IconEye from '@components/Icons/IconEye';
import useClickOutside from '@hooks/useClickOutside';
import { TYPE_INPUT } from '@constants/types';
import { TEXT } from '@constants/text';
import Style from './Input.module.scss';

const cx = classNames.bind(Style);

const Input = ({
  type = TYPE_INPUT.TEL,
  id,
  placeholder,
  label,
  error,
  iconPrefix,
  iconPostfix,
  dropdownData,
  onChange,
  onSelectDropdown,
  maxLength,
  autoFocus,
  className,
  ...rest
}: any) => {
  const containerRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const iconRef = useRef<any>(null);
  const dropdownRef = useRef<any>(null);
  const [isFocusInput, setIsFocusInput] = useState(false);
  const [typeInput, setTypeInput] = useState(type);
  const [warning, setWarning] = useState<any>('');
  const [paddingLeftIconWidth, setPaddingLeftIconWidth] = useState(0);
  const [paddingLeftDropdownWidth, setPaddingLeftDropdownWidth] = useState(0);

  const classContainer = cx(
    {
      container: true,
      active: isFocusInput,
      hasError: error,
      hasWarning: warning && isFocusInput
    },
    className
  );
  const classLabel = cx({
    label: true,
    focus: isFocusInput
  });
  const classField = cx({
    field: true,
    focus: isFocusInput
  });
  const classInput = cx({
    input: true,
    isActive: isFocusInput
  });

  const onFocusInput = () => {
    setIsFocusInput(true);
  };

  const handleChange = (e: any) => {
    if (typeof onChange === 'function') onChange(e.target.value);
  };

  // Use useCallback to ensure stable function reference
  const handleClickOutside = useCallback(() => {
    if (inputRef.current) {
      setWarning('');
      if (!inputRef.current.value) {
        setIsFocusInput(false);
        inputRef.current.blur();
      }
    }
  }, [inputRef]);

  const handleShowAndHidePassword = () => {
    setTypeInput((prev: any) =>
      prev === TYPE_INPUT.PASSWORD ? TYPE_INPUT.TEXT : TYPE_INPUT.PASSWORD
    );
  };

  const handleSelectDropdown = (data: any) => {
    if (typeof onSelectDropdown === 'function') onSelectDropdown(data);
  };

  useClickOutside(containerRef, handleClickOutside);
  useEffect(() => {
    setTypeInput(type);
    if (type === TYPE_INPUT.PASSWORD) {
      const handleCapsLockWarning = (event: any) => {
        if (event.getModifierState && event.getModifierState('CapsLock')) {
          setWarning(TEXT.CHECK_CAPS_LOCK);
        } else {
          setWarning('');
        }
      };
      inputRef.current?.addEventListener('click', handleCapsLockWarning);
      inputRef.current?.addEventListener('keyup', handleCapsLockWarning);

      return () => {
        inputRef.current?.removeEventListener('click', handleCapsLockWarning);
        inputRef.current?.removeEventListener('keyup', handleCapsLockWarning);
      };
    }
    return;
  }, [type]);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useLayoutEffect(() => {
    if (id === 'userName' && iconPrefix) {
      if (iconRef.current?.offsetWidth) {
        setPaddingLeftIconWidth(iconRef.current.offsetWidth + 8);
      }
      if (dropdownRef.current?.offsetWidth) {
        setPaddingLeftDropdownWidth(dropdownRef.current.offsetWidth + 8);
      } else if (iconRef.current?.offsetWidth) {
        setPaddingLeftDropdownWidth(iconRef.current.offsetWidth + 8);
      } else {
        setPaddingLeftDropdownWidth(0);
      }
    } else {
      setPaddingLeftIconWidth(0);
      setPaddingLeftDropdownWidth(0);
    }
  }, [id, iconPrefix]);

  return (
    <div
      className={cx(classContainer, id !== 'userName' && 'space-x-2')}
      ref={containerRef}
      data-error={error}
      data-warning={warning}
    >
      {iconPrefix && (
        <div ref={iconRef} className={cx(id === 'userName' && 'absolute left-0')}>
          {iconPrefix}
        </div>
      )}
      <div className={classField}>
        <div
          ref={dropdownRef}
          className={cx(id === 'userName' && 'absolute')}
          style={{ paddingLeft: paddingLeftIconWidth }}
        >
          {!isEmpty(dropdownData) && (
            <Dropdown
              hasPrefix={!!iconPrefix}
              isFocusInput={isFocusInput}
              onSelect={handleSelectDropdown}
              inputRef={inputRef}
              data={dropdownData}
            />
          )}
        </div>
        <div className={Style.content}>
          <label
            htmlFor={id}
            className={classLabel}
            style={!isFocusInput ? { paddingLeft: paddingLeftDropdownWidth } : {}}
          >
            {label}
          </label>
          <input
            {...rest}
            ref={inputRef}
            className={classInput}
            onChange={handleChange}
            onFocus={onFocusInput}
            id={id}
            autoComplete="off"
            placeholder={placeholder}
            type={typeInput}
            maxLength={maxLength}
            style={{ paddingLeft: paddingLeftDropdownWidth }}
          />
        </div>
      </div>
      {type === TYPE_INPUT.PASSWORD && (
        <div className={Style.iconPostfix} onClick={handleShowAndHidePassword}>
          <IconEye isShow={typeInput !== TYPE_INPUT.PASSWORD} />
        </div>
      )}
      {iconPostfix && <div className={Style.iconPostfix}>{iconPostfix}</div>}
    </div>
  );
};

export default Input;
