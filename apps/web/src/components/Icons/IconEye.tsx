import React from 'react';

const IconEye = ({ isShow }: any) => {
  if (isShow) {
    return (
      <svg
        width={20}
        height={16}
        viewBox="0 0 20 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="Group 1156">
          <path
            id="Union"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M3.14125 14.0511C2.8555 14.344 2.8555 14.8189 3.14125 15.1118C3.427 15.4046 3.89029 15.4046 4.17604 15.1118L5.91261 13.3318C7.14816 13.9087 8.53983 14.3211 9.99995 14.3211C12.9869 14.3211 15.403 13.0134 17.068 11.7137C18.7332 10.4138 19.6951 9.08542 19.793 8.94631C20.0956 8.51777 20.0609 7.94139 19.725 7.5526L19.7242 7.55165C19.595 7.40261 17.7771 5.33023 15.1839 3.82866L16.9384 2.03028C17.2242 1.73738 17.2242 1.26251 16.9384 0.969616C16.6527 0.676723 16.1894 0.676723 15.9036 0.969616L11.2529 5.73667C10.8759 5.54192 10.4502 5.4322 9.99977 5.4322C8.45979 5.4322 7.20845 6.71482 7.20845 8.29331C7.20845 8.75499 7.3155 9.19135 7.5055 9.57772L3.14125 14.0511ZM8.48444 10.6956L7.02762 12.1889C7.98275 12.5774 8.98698 12.8211 9.99995 12.8211C12.5586 12.8211 14.6693 11.7006 16.1813 10.5204C17.3144 9.63586 18.0843 8.73821 18.4191 8.31529C17.8526 7.70893 16.2192 6.07275 14.1054 4.93416L12.3435 6.74009C12.6266 7.18758 12.7911 7.721 12.7911 8.29331C12.7911 9.8718 11.5398 11.1544 9.99977 11.1544C9.44142 11.1544 8.92102 10.9858 8.48444 10.6956ZM11.2567 7.8541C11.3027 7.99197 11.3277 8.13973 11.3277 8.29331C11.3277 9.04337 10.7315 9.65442 9.99977 9.65442C9.84994 9.65442 9.70579 9.6288 9.57128 9.58162L11.2567 7.8541ZM10.0839 6.9349L8.6745 8.3795C8.67275 8.35099 8.67186 8.32225 8.67186 8.29331C8.67186 7.54325 9.26801 6.9322 9.99977 6.9322C10.028 6.9322 10.0561 6.93311 10.0839 6.9349ZM3.75002 11.1903C3.99689 10.8624 3.93766 10.3914 3.61773 10.1384C2.65869 9.37986 1.9476 8.66245 1.60132 8.29333C2.00578 7.8622 2.90756 6.95628 4.12015 6.06496C5.75104 4.86617 7.84765 3.76554 9.99995 3.76554C10.1467 3.76554 10.2932 3.77065 10.4395 3.78058C10.8427 3.80794 11.1912 3.49507 11.2179 3.08176C11.2446 2.66845 10.9394 2.31121 10.5361 2.28385C10.3586 2.2718 10.1798 2.26554 9.99995 2.26554C7.39367 2.26554 4.99084 3.57913 3.26732 4.846C1.52887 6.12386 0.383482 7.42725 0.275648 7.55165L0.273761 7.55383L0.273765 7.55383C-0.0906093 7.9775 -0.0923236 8.6094 0.275034 9.03428L0.275648 9.03499C0.372915 9.1472 1.29404 10.1952 2.72372 11.3259C3.04366 11.579 3.50315 11.5183 3.75002 11.1903Z"
            fill="#9B9B9B"
          />
        </g>
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Icon/Show/S">
        <g id="Group">
          <path
            id="Vector (Stroke)"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.27099 15.2351C6.99008 16.4679 9.39166 17.75 11.9999 17.75C14.9867 17.75 17.4014 16.4743 19.0639 15.2082C20.7266 13.9419 21.6872 12.6478 21.7862 12.5108C22.0988 12.0788 22.0629 11.4978 21.716 11.106L21.7151 11.105C21.6067 10.983 20.4637 9.71413 18.7289 8.47006C17.0098 7.23725 14.6082 5.95515 11.9999 5.95515C9.39166 5.95515 6.99008 7.23725 5.27099 8.47006C3.53618 9.71413 2.39318 10.983 2.28475 11.105L2.28277 11.1073L2.28277 11.1073C1.90642 11.5342 1.90462 12.1712 2.28411 12.5994L2.28475 12.6001C2.39318 12.7222 3.53618 13.991 5.27099 15.2351ZM3.69157 11.8526C4.10543 11.4246 4.9887 10.5657 6.16755 9.72027C7.78927 8.55729 9.86892 7.49361 11.9999 7.49361C14.131 7.49361 16.2106 8.55729 17.8323 9.72027C19.0332 10.5814 19.9273 11.4567 20.331 11.8761C19.9878 12.2957 19.2324 13.1461 18.1318 13.9843C16.6301 15.1279 14.5364 16.2115 11.9999 16.2115C9.86892 16.2115 7.78927 15.1479 6.16755 13.9849C4.9887 13.1395 4.10543 12.2805 3.69157 11.8526Z"
            fill="#9B9B9B"
          />
          <path
            id="Vector (Stroke)_2"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9.17969 11.8533C9.17969 13.4094 10.4441 14.6738 12.0002 14.6738C13.5563 14.6738 14.8207 13.4094 14.8207 11.8533C14.8207 10.2972 13.5563 9.03281 12.0002 9.03281C10.4441 9.03281 9.17969 10.2972 9.17969 11.8533ZM12.0002 13.1354C11.2938 13.1354 10.7181 12.5598 10.7181 11.8533C10.7181 11.1469 11.2938 10.5713 12.0002 10.5713C12.7066 10.5713 13.2822 11.1469 13.2822 11.8533C13.2822 12.5598 12.7066 13.1354 12.0002 13.1354Z"
            fill="#9B9B9B"
          />
        </g>
      </g>
    </svg>
  );
};

export default IconEye;
