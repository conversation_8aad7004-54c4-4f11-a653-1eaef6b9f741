import React, { useEffect, useState } from 'react';
import Button from '@components/basic/Buttons/Button';
import { createTimeout } from '@helpers/common';
import { useSelector } from 'react-redux';
import { FAB_ID } from '@constants/constants';
import isEmpty from 'lodash/isEmpty';
import classNames from 'classnames';
import FABItem from './FABItem';
import styles from './FABItem.module.scss';

let scrollTimer: any = 0;
const FABWidget = () => {
  const { webConfig, isMobile } = useSelector((state: any) => state?.App || {});
  const { fabWidget } = webConfig || {};
  const [filterFABItem, setFilterFABItem] = useState([]);
  const [showWidget, setShowWidget] = useState(false);
  const [collapse, setCollapse] = useState(false);
  const [clickCollapse, setClickCollapse] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const { globalHost } = useSelector((state: any) => state?.App?.geoCheck);

  useEffect(() => {
    if (!isEmpty(fabWidget)) {
      let temp = fabWidget;
      if (globalHost) {
        temp = fabWidget?.filter((el: any) => !el.isNotGlobal) || [];
      }
      if (isMobile) {
        setFilterFABItem(temp?.filter((el: any) => el.id !== FAB_ID.DOWNLOAD_APP) || []);
      } else setFilterFABItem(temp);
    }
  }, [fabWidget, isMobile]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimer) clearTimeout(scrollTimer);
    };
  }, [showWidget, clickCollapse, collapse]);

  useEffect(() => {
    if (isMobile) {
      setShowWidget(true);
    }
  }, [isMobile]);

  const handleScroll = (e: any) => {
    if (scrollTimer) clearTimeout(scrollTimer);
    onDetectScroll(e);
    scrollTimer = createTimeout(() => {
      setIsScrolling(false);
      if (!clickCollapse) {
        setCollapse(false);
      }
    }, 500);
  };

  const onDetectScroll = (e: any) => {
    const scrollingElement = e?.target?.scrollingElement;
    const sliderMasterBanner = document.getElementById('sliderMasterBanner');
    const billboardHeight = (sliderMasterBanner?.offsetHeight || 0) / 2;
    const { scrollTop } = scrollingElement;
    const scrollMaxHeight: any = document.getElementById('footer')?.offsetTop;
    const scrollFooterHeight: any = document.getElementById('footer')?.offsetHeight;

    const isMoreThanHalfMasterBanner = scrollTop >= billboardHeight;
    const isScrollToBottom =
      scrollTop > scrollMaxHeight - (scrollFooterHeight + (isMobile ? 0 : 500));

    let isShow = false;
    let isCollapse = !!collapse;

    if (isMobile) {
      isCollapse = true;
      isShow = true;
    } else if (isMoreThanHalfMasterBanner) {
      isCollapse = true;
      isShow = true;
    }

    if (isScrollToBottom) {
      isShow = false;
    }

    if (isShow !== showWidget) setShowWidget(isShow);
    if (isCollapse !== collapse) setCollapse(isCollapse);
    setIsScrolling(true);
  };

  const handleCLickCollapse = () => {
    setClickCollapse(!clickCollapse);
    setCollapse(!collapse);
  };

  if (isEmpty(fabWidget)) return null;

  return (
    <>
      <div
        className={classNames(
          'widget widget-pos-fixed bottom-right z-20 md:w-[4.625rem] w-12',
          !showWidget && 'is-hide',
          isScrolling && 'inactive'
        )}
      >
        <div className="widget__wrap vertical pb-14 md:pb-[4.5rem]">
          <div
            className={classNames(
              'widget__item round-variant flex-box centering dark collapse',
              collapse && 'collapsed'
            )}
          >
            <Button
              className={classNames(styles.widget, isScrolling && styles['border-button'])}
              iconClass={`icon-v2 ${isMobile ? 'icon--s' : 'icon--m'}`}
              iconName="vie-chevron-down-red-medium"
              onClick={handleCLickCollapse}
            />
            <nav className="nav vertical nav--widget nav--for-dark child-divide">
              {(filterFABItem || []).map((item: any, index: number) => (
                <FABItem
                  isMobile={isMobile}
                  key={`${item?.id}_${index}`}
                  tooltipsText={item?.tipText || ''}
                  img={item?.img}
                  spClass={item?.spClass}
                  icon={item?.icon}
                  id={item?.id}
                  url={item?.url}
                />
              ))}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default React.memo(FABWidget);
