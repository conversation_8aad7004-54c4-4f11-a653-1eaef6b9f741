import React, { useState } from 'react';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import { FAB_ID, PAGE, POPUP } from '@constants/constants';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import { VALUE } from '@config/ConfigSegment';

const FABItem = ({ className, tooltipsText, img, icon, spClass, id, url, isMobile }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const [hoverTooltips, setHoverTooltips] = useState(false);
  const profile = useSelector((state: any) => state?.Profile?.profile);

  const handleOnMouseEnter = () => {
    setHoverTooltips(true);
  };

  const handleOnMouseLeave = () => {
    setHoverTooltips(false);
  };
  const handleOnClick = () => {
    TrackingMWebToApp.fabTouch({ flowName: VALUE.TRGGIER_DIRECTLY_FAB, data: id });
    if (id === FAB_ID.DOWNLOAD_APP) {
      return dispatch(openPopup({ name: POPUP.NAME.DOWNLOAD_APP }));
    }
    if (id === FAB_ID.RAP_VIET3 && profile?.token) {
      return window.open(`${url || 'https://voting.vieon.vn'}?t=${profile?.token}`, '_blank');
    }

    const isPathnameVod = (url || '').includes('.html');
    if (isPathnameVod) {
      router.push(PAGE.VOD, `/${url}`);
    } else {
      router.push({ pathname: url }, { pathname: url });
    }
    return;
  };

  return (
    <div className="nav__item relative">
      <Button
        className="flex-box centering size-w-full text-white hover:text-vo-green !text-[1.375rem]"
        imgSrc={img || ''}
        iconClass={spClass || ''}
        iconName={icon || ''}
        onMouseEnter={handleOnMouseEnter}
        onMouseLeave={handleOnMouseLeave}
        onClick={handleOnClick}
      />
      {hoverTooltips && !isMobile && (
        <div
          className={
            className ||
            'tooltip tooltip-arrow-available for-dark round flown-middle-left animate-fade-in size-mw-280'
          }
        >
          <div className="tooltip__wrap flex-box relative shadow-h-v4-b4-a25">
            <span className="text-white">{tooltipsText}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(FABItem);
