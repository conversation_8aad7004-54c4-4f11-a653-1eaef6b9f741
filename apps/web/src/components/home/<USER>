import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import { PAGE } from '@constants/constants';
import { TEXT } from '@constants/text';
import { setOffBindAccount } from '@actions/app';
import { handleSaveBindAccountInfo, encodeParamDestination } from '@helpers/common';
import LocalStorage from '@config/LocalStorage';
import { useVieRouter } from '@customHook';
import { bindAccountBanner } from '@tracking/functions/TrackingBindAccount';
import { VALUE } from '@config/ConfigSegment';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import Image from '../basic/Image/Image';

const BindAccountRibbon = React.memo(() => {
  const profile = useSelector((state: any) => state?.Profile?.profile);
  const dataMenu = useSelector((state: any) => state?.Menu?.menuList);
  const { bindAccount, isMobile, geoCheck } = useSelector((state: any) => state?.App || {});
  const dispatch = useDispatch();
  const [hide, setHide] = useState(false);
  const [hideLaterBtn, setHideLaterBtn] = useState(false);
  const router = useVieRouter();
  const isNotLocal = useMemo(() => geoCheck?.geo_country === 'US', [geoCheck]);
  const isHomePage = router?.asPath === PAGE.HOME || router?.asPath === dataMenu?.[0]?.menuUrl;

  useEffect(() => {
    if (bindAccount) {
      const savedData: any = ConfigLocalStorage.get(LocalStorage.BIND_ACCOUNT);
      const timeStamp = new Date().getTime() / 1000;
      const reOpenDayConfig = profile?.isPremium
        ? bindAccount?.reopenDayNumberVip
        : bindAccount?.reopenDayNumberNonVip;
      const allowLaterBtnConfig = profile?.isPremium
        ? bindAccount?.allowClicklaterVIP
        : bindAccount?.allowClicklaterNonVip;
      let isReOpen = false;
      if (savedData) {
        const dataParsed = JSON.parse(savedData);
        if (dataParsed?.timestamp) {
          const reOpenTimesTamp = +dataParsed?.timestamp + +reOpenDayConfig * 24 * 60 * 60; // (24 * 60 * 60) = 1 day
          if (timeStamp > reOpenTimesTamp) isReOpen = true;
        }
        if (dataParsed?.laterClicked) {
          const isHideLaterButton = +dataParsed?.laterClicked >= allowLaterBtnConfig + 1;
          setHideLaterBtn(isHideLaterButton);
        }
        if (dataParsed?.id === profile?.id) {
          isReOpen = true;
        }
      } else {
        isReOpen = true;
      }
      setHide(!isReOpen);
    }
  }, [bindAccount]);

  const onClickOpenPopupUpdateUserPhone = () => {
    bindAccountBanner({ buttonStatus: VALUE.UPDATE_NOW });
    const remakeDestination = encodeParamDestination(router?.asPath);
    router.push(
      `${PAGE.USER_UPDATE_PHONE_NUMBER}/?destination=${remakeDestination}&isBindPhoneNumber=true`
    );
  };

  const onClickCancelPopupUpdateUserPhone = () => {
    bindAccountBanner({ buttonStatus: VALUE.LATER });
    setHide(true);
    handleSaveBindAccountInfo();
    dispatch(setOffBindAccount(true));
  };

  if (
    isNotLocal ||
    !profile?.phoneRequired ||
    hide ||
    !bindAccount?.allowOpenBanner ||
    !isHomePage
  ) {
    return null;
  }

  return (
    <div className="rocopa rocopa--banner">
      <div className="rocopa__body">
        <div className="banner banner--market">
          <div className="banner__inner">
            <div className="banner__body">
              <div className="mask">
                <div className="mask__inner">
                  <picture>
                    <source
                      media="(min-width: 1400px)"
                      srcSet={isMobile ? bindAccount?.imageMobile : bindAccount?.image}
                    />
                    <source
                      media="(min-width: 1024px)"
                      srcSet={isMobile ? bindAccount?.imageMobile : bindAccount?.image}
                    />
                    <source
                      media="(min-width: 320px)"
                      srcSet={isMobile ? bindAccount?.imageMobile : bindAccount?.image}
                    />
                    <Image
                      src={isMobile ? bindAccount?.imageMobile : bindAccount?.image}
                      defaultSrc={
                        (isMobile ? bindAccount?.imageMobile : bindAccount?.image) ||
                        ConfigImage.bindAccountBanner
                      }
                      alt="VieON - Cập nhật số điện thoại"
                      title="VieON - Cập nhật số điện thoại"
                      className="mask__img"
                    />
                  </picture>
                </div>
              </div>
              <div className="banner__content absolute full-x middle-v top">
                <div className="grid-x align-middle">
                  <div className="cell large-5 xlarge-6 xxlarge-5" />
                  <div className="cell auto margin-lce-small-up-bottom-6 margin-lce-xxlarge-up-bottom-16 padding-small-up-8 padding-medium-up-20 padding-xlarge-up-28 padding-xlarge-up-right-56">
                    <div className="text text-white text-14 text-medium-up-18 text-large-up-20 text-xxxlarge-up-28 align-center-medium-down line-height-medium-down-1 m-b2">
                      {bindAccount?.text || TEXT.BIND_ACCOUNT_TITLE}
                    </div>
                    <div
                      className={`button-group justify-content-center-medium-down child-auto-medium-down${
                        !isMobile ? ' size-mw-65per' : ''
                      }${profile?.isPremium && !hideLaterBtn ? ' child-auto' : ' child-shrink'}`}
                    >
                      <button
                        className="button button--light button--small-up button--large-up m-b"
                        title={TEXT.UPDATE_NOW}
                        onClick={onClickOpenPopupUpdateUserPhone}
                      >
                        <span className="text">{TEXT.UPDATE_NOW}</span>
                      </button>
                      {profile?.isPremium && !hideLaterBtn && (
                        <button
                          className="button button--light hollow button--small-up button--large-up m-b"
                          title={TEXT.LATER}
                          onClick={onClickCancelPopupUpdateUserPhone}
                        >
                          <span className="text">{TEXT.LATER}</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});
export default BindAccountRibbon;
