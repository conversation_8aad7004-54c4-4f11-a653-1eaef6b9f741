import React, { useRef } from 'react';
import classNames from 'classnames';
import isEmpty from 'lodash/isEmpty';
import Image from '../basic/Image/Image';
import Card from '../basic/Card/Card';
import styles from './TriggerTouchPoint.module.scss';

const RibbonFullScreenRepayExpireNearly = ({
  action,
  dismiss,
  onContentSelected,
  onClickOpenPayment,
  onSkipBanner,
  onClickSkip,
  dataRibbon,
  message,
  isFullscreenPaymentTrigger,
  isMobile,
  disableInteraction,
  linkExternal,
  enableBanner,
  kvUrl
}: any) => {
  const ref = useRef([]);

  const handleOpenExternalUrl = () => {
    if (!isEmpty(linkExternal)) {
      window.open(linkExternal, '_blank');
    } else {
      onClickOpenPayment();
    }
  };

  const renderCardList = () => (
    <div className={classNames(styles.SRTRibbon)}>
      {(dataRibbon?.items || []).map((item: any, i: any) => (
        <div className={styles.SRTRibbonItem} key={item?.id + i || i}>
          <Card
            classNameC={styles.SRTRibbonCard}
            isFullscreenPaymentTrigger={isFullscreenPaymentTrigger}
            onSkipBanner={onSkipBanner}
            onContentSelected={onContentSelected}
            cardData={item}
            key={item?.id + i || i}
            ref={ref}
            index={i + 1}
            randomID={`${item?.id}_${i}`}
            disableInteraction={disableInteraction}
            enableBanner={enableBanner}
          />
        </div>
      ))}
    </div>
  );

  return (
    <div className="flex flex-col pb-6 md:pb-0 relative">
      <div className={classNames(styles.SRTContainer)}>
        {kvUrl && !isMobile && (
          <div className={classNames(styles.SRTThumb)}>
            <Image src={kvUrl} alt="Chi tiết gói" />
          </div>
        )}
        <div
          className={classNames(styles.SRTContent, !kvUrl && !isMobile && styles.SRTContentNonKv)}
        >
          <div
            className={classNames(styles.SRTDesc)}
            dangerouslySetInnerHTML={{ __html: message || '' }}
          />
          <div className={classNames(styles.SRTGroupCTA)}>
            <button
              className={classNames(styles.SRTCTAPrimary)}
              title={action}
              onClick={handleOpenExternalUrl}
            >
              {action}
            </button>
            <button
              className={classNames(styles.SRTCTASecondary)}
              title={dismiss}
              onClick={onClickSkip}
            >
              {dismiss}
            </button>
          </div>
        </div>
      </div>
      {!isEmpty(dataRibbon?.items) && renderCardList()}
    </div>
  );
};

export default RibbonFullScreenRepayExpireNearly;
