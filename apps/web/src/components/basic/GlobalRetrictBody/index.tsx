import React from 'react';
import cn from 'classnames';
import ConfigImage from '@config/ConfigImage';
import Image from '../Image/Image';
import ButtonBase from '../Buttons/ButtonBase';

function GlobalRetrictBody({ closePopup, isPayment }: any) {
  return (
    <div
      className={cn(
        'flex flex-col justify-center items-center space-y-[20px] max-w-[520px] px-[32px] pb-[40px] md:pb-0',
        !isPayment ? 'bg-[#333333]' : 'bg-white'
      )}
    >
      <Image alt={'limitCCU'} src={ConfigImage.limitCCU} notWebp className="w-[139px] h-[228px]" />
      <div className={cn(!isPayment ? 'text-[#DEDEDE]' : 'text-black', 'space-y-[8px]')}>
        <h1
          className={cn(
            'font-[700] !text-[22px] md:!text-[28px] leading-[32px] md:leading-[39.2px]',
            !isPayment ? 'text-white' : 'text-black'
          )}
        >
          Tạm ngừng cung cấp gói và dịch vụ VieON ở quốc gia của bạn
        </h1>
        <div className="flex flex-col space-y-[20px]">
          <p>
            VieON sẽ tạm ngừng cung cấp gói VieON Global từ ngày 20/11 và ngừng dịch vụ VieON vào
            ngày 31/12 ở quốc gia của bạn.
          </p>
          <p>Cảm ơn bạn đã tin tưởng và đồng hành cùng VieON.</p>
        </div>
      </div>
      {/* vip */}
      <hr className="bg-[#CCCCCC] h-px w-full" />
      <div className={!isPayment ? 'text-[#DEDEDE]' : 'text-black'}>
        <p
          className={cn(
            'font-[700] text-[16px] leading-[22.4px]',
            !isPayment ? 'text-white' : 'text-black'
          )}
        >
          Để huỷ gia hạn gói vui lòng thực hiện theo hướng dẫn sau:
        </p>
        <div className="flex flex-col modal-limit-region">
          <div className={isPayment ? 'text-[#646464]' : ''}>
            <p className="mb-[20px]">
              Nếu bạn mua gói trên App Store trên điện thoại iPhone làm theo hướng dẫn{' '}
              <a
                className="text-[#0AD418]"
                href="https://support.apple.com/en-vn/118428"
                target="_blank"
                rel="noreferrer"
              >
                tại đây
              </a>
            </p>
            <p>
              Nếu bạn mua gói trên Google Play trên điện thoại trên điện thoại Android làm theo
              hướng dẫn{' '}
              <a
                className="text-[#0AD418]"
                href="https://support.google.com/googleplay/answer/7018481"
                target="_blank"
                rel="noreferrer"
              >
                tại đây
              </a>
            </p>
          </div>
        </div>
      </div>
      <ButtonBase
        className={cn(
          'text-[16px] leading-[22.4px] font-[500] w-full h-[48px]',
          !isPayment ? 'bg-white text-[#333333]' : 'bg-[#3AC882] text-white'
        )}
        onClick={() => {
          if (closePopup && !isPayment) closePopup();
          else window.location.href = '/';
        }}
      >
        {!isPayment ? 'Tôi đã hiểu' : 'Về trang chủ'}
      </ButtonBase>
    </div>
  );
}

export default GlobalRetrictBody;
