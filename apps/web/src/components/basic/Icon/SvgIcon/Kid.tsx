import React from 'react';

const Kid = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.25 22C16.1699 22 19.5046 19.4939 20.7391 15.9965C20.7841 15.9988 20.8294 16 20.875 16C22.3247 16 23.5 14.8247 23.5 13.375C23.5 11.956 22.3741 10.8 20.9669 10.7516C19.9683 6.86902 16.4442 4 12.25 4C8.05583 4 4.53174 6.86897 3.53316 10.7515C2.12594 10.7999 1 11.9559 1 13.3749C1 14.8247 2.17525 15.9999 3.625 15.9999C3.67057 15.9999 3.71586 15.9987 3.76086 15.9965C4.99537 19.4939 8.33002 22 12.25 22ZM9.97694 17.8178C10.7063 18.4052 11.5557 18.7155 12.4332 18.7155C13.3107 18.7155 14.1601 18.4052 14.8894 17.8178C15.0896 17.657 15.1196 17.3659 14.9569 17.1686C14.7952 16.9708 14.5013 16.9408 14.3007 17.102C13.179 18.0058 11.6879 18.0049 10.5657 17.102C10.3665 16.9403 10.0721 16.9708 9.90944 17.1686C9.74679 17.3659 9.77679 17.657 9.97694 17.8178ZM7.46644 12.3241C7.35816 12.3241 7.24941 12.2866 7.16128 12.2116C6.96628 12.0442 6.94519 11.7527 7.11394 11.56C7.56441 11.0453 8.21738 10.75 8.90503 10.75C9.58378 10.75 10.2311 11.0392 10.6807 11.5427C10.8513 11.7339 10.833 12.0255 10.6399 12.1942C10.4463 12.3634 10.1519 12.3452 9.98128 12.1534C9.70847 11.8483 9.31613 11.673 8.90503 11.673C8.48831 11.673 8.09222 11.852 7.81941 12.1642C7.72706 12.2697 7.59722 12.3241 7.46644 12.3241ZM14.2234 12.2111C14.3115 12.2866 14.4207 12.3241 14.529 12.3241C14.6593 12.3241 14.7891 12.2697 14.8815 12.1642C15.1548 11.852 15.5509 11.673 15.9671 11.673C16.3787 11.673 16.771 11.8483 17.0438 12.1534C17.2145 12.3461 17.5093 12.3634 17.7024 12.1942C17.896 12.0255 17.9143 11.7339 17.7432 11.5427C17.2937 11.0392 16.6463 10.75 15.9676 10.75C15.2799 10.75 14.627 11.0453 14.1765 11.56C14.0077 11.7527 14.0288 12.0442 14.2234 12.2111Z"
          fill="white"
        />
        <path
          d="M13.4416 3.07094C13.4416 3.70375 12.9213 4.21844 12.282 4.21844V4.72281V5.14187C13.436 5.14187 14.3745 4.21281 14.3745 3.07094C14.3745 1.92906 13.436 1 12.282 1C11.1279 1 10.1895 1.92906 10.1895 3.07094C10.1895 3.32594 10.398 3.53266 10.6559 3.53266C10.9132 3.53266 11.1223 3.32594 11.1223 3.07094C11.1223 2.43812 11.6426 1.92344 12.282 1.92344C12.9213 1.92344 13.4416 2.43812 13.4416 3.07094Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.0002 22.5937C7.02016 22.5937 2.96875 18.5841 2.96875 13.6561C2.96875 8.72766 7.02016 4.71844 12.0002 4.71844C12.6395 4.71844 13.1598 4.20375 13.1598 3.57094C13.1598 2.93813 12.6395 2.42344 12.0002 2.42344C11.3608 2.42344 10.8405 2.93813 10.8405 3.57094C10.8405 3.82594 10.6314 4.03266 10.3741 4.03266C10.1162 4.03266 9.90766 3.82594 9.90766 3.57094C9.90766 2.42906 10.8461 1.5 12.0002 1.5C13.1542 1.5 14.0927 2.42906 14.0927 3.57094C14.0927 4.71281 13.1542 5.64187 12.0002 5.64187C7.53484 5.64187 3.90203 9.23719 3.90203 13.6561C3.90203 18.075 7.53484 21.6703 12.0002 21.6703C16.4655 21.6703 20.0983 18.075 20.0983 13.6561C20.0983 10.2591 17.9242 7.22016 14.688 6.09375C14.4452 6.00937 14.3172 5.74547 14.403 5.505C14.4887 5.26453 14.7545 5.13797 14.9978 5.22281C18.6062 6.47906 21.0311 9.86812 21.0311 13.6566C21.0311 18.5841 16.9797 22.5937 12.0002 22.5937Z"
          fill="#9B9B9B"
        />
        <path
          d="M3.43547 16.3135C1.95469 16.3135 0.75 15.1214 0.75 13.6561C0.75 12.1908 1.95469 10.9988 3.43547 10.9988V11.9222C2.46937 11.9222 1.68328 12.7003 1.68328 13.6561C1.68328 14.6119 2.46937 15.39 3.43547 15.39V16.3135Z"
          fill="#9B9B9B"
        />
        <path
          d="M20.5645 16.3135V15.39C21.531 15.39 22.3166 14.6124 22.3166 13.6561C22.3166 12.6999 21.5305 11.9222 20.5645 11.9222V10.9988C22.0452 10.9988 23.2499 12.1908 23.2499 13.6561C23.2499 15.1214 22.0452 16.3135 20.5645 16.3135Z"
          fill="#9B9B9B"
        />
        <path
          d="M11.9996 19.1935C11.1221 19.1935 10.2727 18.8832 9.54335 18.2958C9.34319 18.1351 9.31319 17.844 9.47585 17.6466C9.6385 17.4488 9.93288 17.4183 10.1321 17.5801C11.2543 18.4829 12.7454 18.4838 13.8671 17.5801C14.0677 17.4188 14.3616 17.4488 14.5233 17.6466C14.686 17.844 14.656 18.1351 14.4558 18.2958C13.7265 18.8832 12.8771 19.1935 11.9996 19.1935Z"
          fill="#9B9B9B"
        />
        <path
          d="M7.03285 12.8021C6.92456 12.8021 6.81581 12.7646 6.72769 12.6896C6.53269 12.5222 6.5116 12.2307 6.68035 12.038C7.13081 11.5233 7.78378 11.228 8.47144 11.228C9.15019 11.228 9.79753 11.5172 10.2471 12.0207C10.4177 12.2119 10.3994 12.5035 10.2063 12.6722C10.0127 12.8415 9.71831 12.8232 9.54769 12.6315C9.27488 12.3263 8.88253 12.151 8.47144 12.151C8.05472 12.151 7.65863 12.3301 7.38581 12.6422C7.29347 12.7477 7.16363 12.8021 7.03285 12.8021Z"
          fill="#9B9B9B"
        />
        <path
          d="M14.0954 12.8021C13.9871 12.8021 13.8779 12.7646 13.7898 12.6891C13.5952 12.5222 13.5741 12.2307 13.7429 12.038C14.1934 11.5233 14.8463 11.228 15.534 11.228C16.2127 11.228 16.8601 11.5172 17.3096 12.0207C17.4807 12.2119 17.4624 12.5035 17.2688 12.6722C17.0757 12.8415 16.7809 12.8241 16.6102 12.6315C16.3374 12.3263 15.9451 12.151 15.5335 12.151C15.1173 12.151 14.7212 12.3301 14.4479 12.6422C14.3555 12.7477 14.2257 12.8021 14.0954 12.8021Z"
          fill="#9B9B9B"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.0002 22.5937C7.02016 22.5937 2.96875 18.5841 2.96875 13.6561C2.96875 8.72766 7.02016 4.71844 12.0002 4.71844C12.6395 4.71844 13.1598 4.20375 13.1598 3.57094C13.1598 2.93813 12.6395 2.42344 12.0002 2.42344C11.3608 2.42344 10.8405 2.93813 10.8405 3.57094C10.8405 3.82594 10.6314 4.03266 10.3741 4.03266C10.1162 4.03266 9.90766 3.82594 9.90766 3.57094C9.90766 2.42906 10.8461 1.5 12.0002 1.5C13.1542 1.5 14.0927 2.42906 14.0927 3.57094C14.0927 4.71281 13.1542 5.64187 12.0002 5.64187C7.53484 5.64187 3.90203 9.23719 3.90203 13.6561C3.90203 18.075 7.53484 21.6703 12.0002 21.6703C16.4655 21.6703 20.0983 18.075 20.0983 13.6561C20.0983 10.2591 17.9242 7.22016 14.688 6.09375C14.4452 6.00937 14.3172 5.74547 14.403 5.505C14.4887 5.26453 14.7545 5.13797 14.9978 5.22281C18.6062 6.47906 21.0311 9.86812 21.0311 13.6566C21.0311 18.5841 16.9797 22.5937 12.0002 22.5937Z"
        fill="#CCCCCC"
      />
      <path
        d="M3.43547 16.3135C1.95469 16.3135 0.75 15.1214 0.75 13.6561C0.75 12.1908 1.95469 10.9988 3.43547 10.9988V11.9222C2.46937 11.9222 1.68328 12.7003 1.68328 13.6561C1.68328 14.6119 2.46937 15.39 3.43547 15.39V16.3135Z"
        fill="#CCCCCC"
      />
      <path
        d="M20.5645 16.3135V15.39C21.531 15.39 22.3166 14.6124 22.3166 13.6561C22.3166 12.6999 21.5305 11.9222 20.5645 11.9222V10.9988C22.0452 10.9988 23.2499 12.1908 23.2499 13.6561C23.2499 15.1214 22.0452 16.3135 20.5645 16.3135V16.3135Z"
        fill="#CCCCCC"
      />
      <path
        d="M11.9996 19.1935C11.1221 19.1935 10.2727 18.8832 9.54335 18.2958C9.34319 18.1351 9.31319 17.844 9.47585 17.6466C9.6385 17.4488 9.93288 17.4183 10.1321 17.5801C11.2543 18.4829 12.7454 18.4838 13.8671 17.5801C14.0677 17.4188 14.3616 17.4488 14.5233 17.6466C14.686 17.844 14.656 18.1351 14.4558 18.2958C13.7265 18.8832 12.8771 19.1935 11.9996 19.1935Z"
        fill="#CCCCCC"
      />
      <path
        d="M7.03285 12.8021C6.92456 12.8021 6.81581 12.7646 6.72769 12.6896C6.53269 12.5222 6.5116 12.2307 6.68035 12.038C7.13081 11.5233 7.78378 11.228 8.47144 11.228C9.15019 11.228 9.79753 11.5172 10.2471 12.0207C10.4177 12.2119 10.3994 12.5035 10.2063 12.6722C10.0127 12.8415 9.71831 12.8232 9.54769 12.6315C9.27488 12.3263 8.88253 12.151 8.47144 12.151C8.05472 12.151 7.65863 12.3301 7.38581 12.6422C7.29347 12.7477 7.16363 12.8021 7.03285 12.8021V12.8021Z"
        fill="#CCCCCC"
      />
      <path
        d="M14.0954 12.8021C13.9871 12.8021 13.8779 12.7646 13.7898 12.6891C13.5952 12.5222 13.5741 12.2307 13.7429 12.038C14.1934 11.5233 14.8463 11.228 15.534 11.228C16.2127 11.228 16.8601 11.5172 17.3096 12.0207C17.4807 12.2119 17.4624 12.5035 17.2688 12.6722C17.0757 12.8415 16.7809 12.8241 16.6102 12.6315C16.3374 12.3263 15.9451 12.151 15.5335 12.151C15.1173 12.151 14.7212 12.3301 14.4479 12.6422C14.3555 12.7477 14.2257 12.8021 14.0954 12.8021V12.8021Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default Kid;
