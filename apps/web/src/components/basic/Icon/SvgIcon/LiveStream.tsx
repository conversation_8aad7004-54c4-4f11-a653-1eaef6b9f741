import React from 'react';

const LiveStream = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.05539 18.9052C4.93337 19.0323 4.73466 19.0322 4.61658 18.9011C1.12781 15.0272 1.12781 8.97279 4.61658 5.09891C4.73466 4.9678 4.93337 4.96765 5.05539 5.09478L5.85873 5.93175C5.98075 6.05888 5.97996 6.2649 5.86276 6.39686C3.05759 9.5556 3.05759 14.4444 5.86276 17.6031C5.97996 17.7351 5.98075 17.9411 5.85873 18.0683L5.05539 18.9052Z"
          fill="white"
        />
        <path
          d="M19.3811 5.09891C19.263 4.9678 19.0643 4.96765 18.9423 5.09478L18.1389 5.93175C18.0169 6.05888 18.0177 6.26489 18.1349 6.39686C20.9401 9.5556 20.9401 14.4444 18.1349 17.6031C18.0177 17.7351 18.0169 17.9411 18.1389 18.0683L18.9423 18.9052C19.0643 19.0323 19.263 19.0322 19.3811 18.9011C22.8757 15.0215 22.8702 8.97267 19.3811 5.09891Z"
          fill="white"
        />
        <path
          d="M16.8882 7.69514C16.7724 7.56185 16.5738 7.56235 16.4518 7.68948L15.6485 8.52644C15.5265 8.65357 15.5276 8.85919 15.6408 8.99491C17.0749 10.7148 17.0749 13.2852 15.6408 15.0051C15.5276 15.1408 15.5265 15.3464 15.6485 15.4736L16.4518 16.3105C16.5738 16.4377 16.7724 16.4382 16.8882 16.3049C19.0105 13.8621 19.0105 10.1379 16.8882 7.69514Z"
          fill="white"
        />
        <path
          d="M7.10986 16.3049C7.22567 16.4382 7.42419 16.4377 7.54621 16.3105L8.34955 15.4736C8.47158 15.3464 8.47043 15.1408 8.35726 15.0051C6.92316 13.2852 6.92316 10.7148 8.35726 8.99492C8.47043 8.85919 8.47158 8.65357 8.34955 8.52644L7.54621 7.68948C7.42419 7.56235 7.22566 7.56185 7.10986 7.69514C4.98756 10.1379 4.98756 13.8621 7.10986 16.3049Z"
          fill="white"
        />
        <path
          d="M12.0741 14.7522C13.5329 14.7522 14.7156 13.5201 14.7156 12.0001C14.7156 10.4802 13.5329 9.24803 12.0741 9.24803C10.6152 9.24803 9.43254 10.4802 9.43254 12.0001C9.43254 13.5201 10.6152 14.7522 12.0741 14.7522Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.05539 18.9052C4.93337 19.0323 4.73466 19.0322 4.61658 18.9011C1.12781 15.0272 1.12781 8.97279 4.61658 5.09891C4.73466 4.9678 4.93337 4.96765 5.05539 5.09478L5.85873 5.93175C5.98075 6.05888 5.97996 6.2649 5.86276 6.39686C3.05759 9.5556 3.05759 14.4444 5.86276 17.6031C5.97996 17.7351 5.98075 17.9411 5.85873 18.0683L5.05539 18.9052Z"
          fill="#9B9B9B"
        />
        <path
          d="M19.3811 5.09891C19.263 4.9678 19.0643 4.96765 18.9423 5.09478L18.1389 5.93175C18.0169 6.05888 18.0177 6.26489 18.1349 6.39686C20.9401 9.5556 20.9401 14.4444 18.1349 17.6031C18.0177 17.7351 18.0169 17.9411 18.1389 18.0683L18.9423 18.9052C19.0643 19.0323 19.263 19.0322 19.3811 18.9011C22.8757 15.0215 22.8702 8.97267 19.3811 5.09891Z"
          fill="#9B9B9B"
        />
        <path
          d="M16.8882 7.69514C16.7724 7.56185 16.5738 7.56235 16.4518 7.68948L15.6485 8.52644C15.5265 8.65357 15.5276 8.85919 15.6408 8.99491C17.0749 10.7148 17.0749 13.2852 15.6408 15.0051C15.5276 15.1408 15.5265 15.3464 15.6485 15.4736L16.4518 16.3105C16.5738 16.4377 16.7724 16.4382 16.8882 16.3049C19.0105 13.8621 19.0105 10.1379 16.8882 7.69514Z"
          fill="#9B9B9B"
        />
        <path
          d="M7.10986 16.3049C7.22567 16.4382 7.42419 16.4377 7.54621 16.3105L8.34955 15.4736C8.47158 15.3464 8.47043 15.1408 8.35726 15.0051C6.92316 13.2852 6.92316 10.7148 8.35726 8.99492C8.47043 8.85919 8.47158 8.65357 8.34955 8.52644L7.54621 7.68948C7.42419 7.56235 7.22566 7.56185 7.10986 7.69514C4.98756 10.1379 4.98756 13.8621 7.10986 16.3049Z"
          fill="#9B9B9B"
        />
        <path
          d="M12.0741 14.7522C13.5329 14.7522 14.7156 13.5201 14.7156 12.0001C14.7156 10.4802 13.5329 9.24803 12.0741 9.24803C10.6152 9.24803 9.43254 10.4802 9.43254 12.0001C9.43254 13.5201 10.6152 14.7522 12.0741 14.7522Z"
          fill="#9B9B9B"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.05539 18.9052C4.93337 19.0323 4.73466 19.0322 4.61658 18.9011C1.12781 15.0272 1.12781 8.97279 4.61658 5.09891C4.73466 4.9678 4.93337 4.96765 5.05539 5.09478L5.85873 5.93175C5.98075 6.05888 5.97996 6.2649 5.86276 6.39686C3.05759 9.5556 3.05759 14.4444 5.86276 17.6031C5.97996 17.7351 5.98075 17.9411 5.85873 18.0683L5.05539 18.9052Z"
        fill="#CCCCCC"
      />
      <path
        d="M19.3811 5.09891C19.263 4.9678 19.0643 4.96765 18.9423 5.09478L18.1389 5.93175C18.0169 6.05888 18.0177 6.26489 18.1349 6.39686C20.9401 9.5556 20.9401 14.4444 18.1349 17.6031C18.0177 17.7351 18.0169 17.9411 18.1389 18.0683L18.9423 18.9052C19.0643 19.0323 19.263 19.0322 19.3811 18.9011C22.8757 15.0215 22.8702 8.97267 19.3811 5.09891Z"
        fill="#CCCCCC"
      />
      <path
        d="M16.8882 7.69514C16.7724 7.56185 16.5738 7.56235 16.4518 7.68948L15.6485 8.52644C15.5265 8.65357 15.5276 8.85919 15.6408 8.99491C17.0749 10.7148 17.0749 13.2852 15.6408 15.0051C15.5276 15.1408 15.5265 15.3464 15.6485 15.4736L16.4518 16.3105C16.5738 16.4377 16.7724 16.4382 16.8882 16.3049C19.0105 13.8621 19.0105 10.1379 16.8882 7.69514Z"
        fill="#CCCCCC"
      />
      <path
        d="M7.10986 16.3049C7.22567 16.4382 7.42419 16.4377 7.54621 16.3105L8.34955 15.4736C8.47158 15.3464 8.47043 15.1408 8.35726 15.0051C6.92316 13.2852 6.92316 10.7148 8.35726 8.99492C8.47043 8.85919 8.47158 8.65357 8.34955 8.52644L7.54621 7.68948C7.42419 7.56235 7.22566 7.56185 7.10986 7.69514C4.98756 10.1379 4.98756 13.8621 7.10986 16.3049Z"
        fill="#CCCCCC"
      />
      <path
        d="M12.0741 14.7522C13.5329 14.7522 14.7156 13.5201 14.7156 12.0001C14.7156 10.4802 13.5329 9.24803 12.0741 9.24803C10.6152 9.24803 9.43254 10.4802 9.43254 12.0001C9.43254 13.5201 10.6152 14.7522 12.0741 14.7522Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default LiveStream;
