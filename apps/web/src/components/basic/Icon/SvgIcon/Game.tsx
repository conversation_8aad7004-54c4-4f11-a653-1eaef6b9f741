import React from 'react';

const Game = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.98706 9.43227H8.18396V8.64465C8.18396 8.26377 7.86902 7.95508 7.48084 7.95508C7.09247 7.95508 6.77771 8.26377 6.77771 8.64465V9.43227H5.97461C5.58624 9.43227 5.27148 9.74096 5.27148 10.1218C5.27148 10.5027 5.58624 10.8114 5.97461 10.8114H6.77771V11.599C6.77771 11.9799 7.09247 12.2886 7.48084 12.2886C7.86902 12.2886 8.18396 11.9799 8.18396 11.599V10.8114H8.98706C9.37543 10.8114 9.69019 10.5027 9.69019 10.1218C9.69019 9.74096 9.37543 9.43227 8.98706 9.43227Z"
          fill="white"
        />
        <path
          d="M17.2207 8.64465C17.2207 9.02553 16.9058 9.33422 16.5176 9.33422C16.1292 9.33422 15.8145 9.02553 15.8145 8.64465C15.8145 8.26377 16.1292 7.95508 16.5176 7.95508C16.9058 7.95508 17.2207 8.26377 17.2207 8.64465Z"
          fill="white"
        />
        <path
          d="M15.7148 10.1219C15.7148 10.5028 15.3999 10.8115 15.0117 10.8115C14.6234 10.8115 14.3086 10.5028 14.3086 10.1219C14.3086 9.74106 14.6234 9.43237 15.0117 9.43237C15.3999 9.43237 15.7148 9.74106 15.7148 10.1219Z"
          fill="white"
        />
        <path
          d="M17.2207 11.599C17.2207 11.9799 16.9058 12.2886 16.5176 12.2886C16.1292 12.2886 15.8145 11.9799 15.8145 11.599C15.8145 11.2183 16.1292 10.9094 16.5176 10.9094C16.9058 10.9094 17.2207 11.2183 17.2207 11.599Z"
          fill="white"
        />
        <path
          d="M18.7285 10.1219C18.7285 10.5028 18.4136 10.8115 18.0254 10.8115C17.637 10.8115 17.3223 10.5028 17.3223 10.1219C17.3223 9.74106 17.637 9.43237 18.0254 9.43237C18.4136 9.43237 18.7285 9.74106 18.7285 10.1219Z"
          fill="white"
        />
        <path
          d="M23.9201 15.3693L22.3114 8.66396C21.8455 6.72202 20.0469 5.00024 17.5955 5.00024H6.40461C3.81935 5.00024 2.12178 6.8576 1.68855 8.66396L0.0799662 15.3693C-0.35729 17.1919 1.05409 18.9357 2.96058 18.9357C3.77174 18.9357 4.53474 18.5119 4.95167 17.8299L6.19093 15.8045C6.40278 15.4579 6.79042 15.2427 7.20241 15.2427H16.7977C17.2097 15.2427 17.5971 15.4579 17.8092 15.8046L19.0481 17.8297C19.4652 18.5119 20.2282 18.9357 21.0402 18.9357C22.9489 18.9357 24.3566 17.1886 23.9201 15.3693ZM21.0393 17.5566C20.7194 17.5566 20.4184 17.3894 20.2538 17.1202L19.0149 15.0951C18.5506 14.3355 17.7009 13.8636 16.7977 13.8636H7.20241C6.29915 13.8636 5.44954 14.3355 4.98518 15.0949L3.74592 17.1204C3.58149 17.3894 3.28047 17.5566 2.95967 17.5566C1.96009 17.5566 1.21943 16.6406 1.44868 15.6852L3.05726 8.97984C3.42457 7.44877 4.80098 6.37939 6.40461 6.37939H17.5955C19.1989 6.37939 20.5755 7.44877 20.9426 8.97984L22.5514 15.6852C22.7803 16.64 22.0411 17.5566 21.0393 17.5566Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.98706 9.43227H8.18396V8.64465C8.18396 8.26377 7.86902 7.95508 7.48084 7.95508C7.09247 7.95508 6.77771 8.26377 6.77771 8.64465V9.43227H5.97461C5.58624 9.43227 5.27148 9.74096 5.27148 10.1218C5.27148 10.5027 5.58624 10.8114 5.97461 10.8114H6.77771V11.599C6.77771 11.9799 7.09247 12.2886 7.48084 12.2886C7.86902 12.2886 8.18396 11.9799 8.18396 11.599V10.8114H8.98706C9.37543 10.8114 9.69019 10.5027 9.69019 10.1218C9.69019 9.74096 9.37543 9.43227 8.98706 9.43227Z"
          fill="#9B9B9B"
        />
        <path
          d="M17.2207 8.64465C17.2207 9.02553 16.9058 9.33422 16.5176 9.33422C16.1292 9.33422 15.8145 9.02553 15.8145 8.64465C15.8145 8.26377 16.1292 7.95508 16.5176 7.95508C16.9058 7.95508 17.2207 8.26377 17.2207 8.64465Z"
          fill="#9B9B9B"
        />
        <path
          d="M15.7148 10.1219C15.7148 10.5028 15.3999 10.8115 15.0117 10.8115C14.6234 10.8115 14.3086 10.5028 14.3086 10.1219C14.3086 9.74106 14.6234 9.43237 15.0117 9.43237C15.3999 9.43237 15.7148 9.74106 15.7148 10.1219Z"
          fill="#9B9B9B"
        />
        <path
          d="M17.2207 11.599C17.2207 11.9799 16.9058 12.2886 16.5176 12.2886C16.1292 12.2886 15.8145 11.9799 15.8145 11.599C15.8145 11.2183 16.1292 10.9094 16.5176 10.9094C16.9058 10.9094 17.2207 11.2183 17.2207 11.599Z"
          fill="#9B9B9B"
        />
        <path
          d="M18.7285 10.1219C18.7285 10.5028 18.4136 10.8115 18.0254 10.8115C17.637 10.8115 17.3223 10.5028 17.3223 10.1219C17.3223 9.74106 17.637 9.43237 18.0254 9.43237C18.4136 9.43237 18.7285 9.74106 18.7285 10.1219Z"
          fill="#9B9B9B"
        />
        <path
          d="M23.9201 15.3693L22.3114 8.66396C21.8455 6.72202 20.0469 5.00024 17.5955 5.00024H6.40461C3.81935 5.00024 2.12178 6.8576 1.68855 8.66396L0.0799662 15.3693C-0.35729 17.1919 1.05409 18.9357 2.96058 18.9357C3.77174 18.9357 4.53474 18.5119 4.95167 17.8299L6.19093 15.8045C6.40278 15.4579 6.79042 15.2427 7.20241 15.2427H16.7977C17.2097 15.2427 17.5971 15.4579 17.8092 15.8046L19.0481 17.8297C19.4652 18.5119 20.2282 18.9357 21.0402 18.9357C22.9489 18.9357 24.3566 17.1886 23.9201 15.3693ZM21.0393 17.5566C20.7194 17.5566 20.4184 17.3894 20.2538 17.1202L19.0149 15.0951C18.5506 14.3355 17.7009 13.8636 16.7977 13.8636H7.20241C6.29915 13.8636 5.44954 14.3355 4.98518 15.0949L3.74592 17.1204C3.58149 17.3894 3.28047 17.5566 2.95967 17.5566C1.96009 17.5566 1.21943 16.6406 1.44868 15.6852L3.05726 8.97984C3.42457 7.44877 4.80098 6.37939 6.40461 6.37939H17.5955C19.1989 6.37939 20.5755 7.44877 20.9426 8.97984L22.5514 15.6852C22.7803 16.64 22.0411 17.5566 21.0393 17.5566Z"
          fill="#9B9B9B"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.98706 9.43227H8.18396V8.64465C8.18396 8.26377 7.86902 7.95508 7.48084 7.95508C7.09247 7.95508 6.77771 8.26377 6.77771 8.64465V9.43227H5.97461C5.58624 9.43227 5.27148 9.74096 5.27148 10.1218C5.27148 10.5027 5.58624 10.8114 5.97461 10.8114H6.77771V11.599C6.77771 11.9799 7.09247 12.2886 7.48084 12.2886C7.86902 12.2886 8.18396 11.9799 8.18396 11.599V10.8114H8.98706C9.37543 10.8114 9.69019 10.5027 9.69019 10.1218C9.69019 9.74096 9.37543 9.43227 8.98706 9.43227Z"
        fill="#CCCCCC"
      />
      <path
        d="M17.2207 8.64465C17.2207 9.02553 16.9058 9.33422 16.5176 9.33422C16.1292 9.33422 15.8145 9.02553 15.8145 8.64465C15.8145 8.26377 16.1292 7.95508 16.5176 7.95508C16.9058 7.95508 17.2207 8.26377 17.2207 8.64465Z"
        fill="#CCCCCC"
      />
      <path
        d="M15.7148 10.1219C15.7148 10.5028 15.3999 10.8115 15.0117 10.8115C14.6234 10.8115 14.3086 10.5028 14.3086 10.1219C14.3086 9.74106 14.6234 9.43237 15.0117 9.43237C15.3999 9.43237 15.7148 9.74106 15.7148 10.1219Z"
        fill="#CCCCCC"
      />
      <path
        d="M17.2207 11.599C17.2207 11.9799 16.9058 12.2886 16.5176 12.2886C16.1292 12.2886 15.8145 11.9799 15.8145 11.599C15.8145 11.2183 16.1292 10.9094 16.5176 10.9094C16.9058 10.9094 17.2207 11.2183 17.2207 11.599Z"
        fill="#CCCCCC"
      />
      <path
        d="M18.7285 10.1219C18.7285 10.5028 18.4136 10.8115 18.0254 10.8115C17.637 10.8115 17.3223 10.5028 17.3223 10.1219C17.3223 9.74106 17.637 9.43237 18.0254 9.43237C18.4136 9.43237 18.7285 9.74106 18.7285 10.1219Z"
        fill="#CCCCCC"
      />
      <path
        d="M23.9201 15.3693L22.3114 8.66396C21.8455 6.72202 20.0469 5.00024 17.5955 5.00024H6.40461C3.81935 5.00024 2.12178 6.8576 1.68855 8.66396L0.0799662 15.3693C-0.35729 17.1919 1.05409 18.9357 2.96058 18.9357C3.77174 18.9357 4.53474 18.5119 4.95167 17.8299L6.19093 15.8045C6.40278 15.4579 6.79042 15.2427 7.20241 15.2427H16.7977C17.2097 15.2427 17.5971 15.4579 17.8092 15.8046L19.0481 17.8297C19.4652 18.5119 20.2282 18.9357 21.0402 18.9357C22.9489 18.9357 24.3566 17.1886 23.9201 15.3693ZM21.0393 17.5566C20.7194 17.5566 20.4184 17.3894 20.2538 17.1202L19.0149 15.0951C18.5506 14.3355 17.7009 13.8636 16.7977 13.8636H7.20241C6.29915 13.8636 5.44954 14.3355 4.98518 15.0949L3.74592 17.1204C3.58149 17.3894 3.28047 17.5566 2.95967 17.5566C1.96009 17.5566 1.21943 16.6406 1.44868 15.6852L3.05726 8.97984C3.42457 7.44877 4.80098 6.37939 6.40461 6.37939H17.5955C19.1989 6.37939 20.5755 7.44877 20.9426 8.97984L22.5514 15.6852C22.7803 16.64 22.0411 17.5566 21.0393 17.5566Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default Game;
