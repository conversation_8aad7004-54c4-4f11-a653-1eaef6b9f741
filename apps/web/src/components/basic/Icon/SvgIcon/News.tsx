import React from 'react';

const News = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.33836 19.7141H19.7143V6.33817C19.7143 5.98331 20.0023 5.69531 20.3571 5.69531C20.712 5.69531 21 5.98331 21 6.33817V20.357C21 20.7125 20.712 20.9998 20.3571 20.9998H6.33836C5.98286 20.9998 5.6955 20.7125 5.6955 20.357C5.6955 20.0015 5.9835 19.7141 6.33836 19.7141Z"
          fill="white"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.64305 3H17.6618C18.0167 3 18.3047 3.288 18.3047 3.64286V17.6616C18.3047 18.0171 18.0167 18.3045 17.6618 18.3045H3.64305C3.28755 18.3045 3.00019 18.0171 3.00019 17.6616V3.64286C3.00019 3.288 3.28755 3 3.64305 3ZM7.14727 7.7906H14.157C14.5118 7.7906 14.7998 7.5026 14.7998 7.14774C14.7998 6.79288 14.5118 6.50488 14.157 6.50488H7.14727C6.79177 6.50488 6.50441 6.79288 6.50441 7.14774C6.50441 7.5026 6.79177 7.7906 7.14727 7.7906ZM14.157 11.2947H7.14727C6.79177 11.2947 6.50441 11.0067 6.50441 10.6519C6.50441 10.297 6.79177 10.009 7.14727 10.009H14.157C14.5118 10.009 14.7998 10.297 14.7998 10.6519C14.7998 11.0067 14.5118 11.2947 14.157 11.2947ZM7.14727 14.7989H14.157C14.5118 14.7989 14.7998 14.5115 14.7998 14.156C14.7998 13.8005 14.5118 13.5132 14.157 13.5132H7.14727C6.79177 13.5132 6.50441 13.8005 6.50441 14.156C6.50441 14.5115 6.79177 14.7989 7.14727 14.7989Z"
          fill="white"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.33836 19.7143H19.7143V6.33841C19.7143 5.98356 20.0023 5.69556 20.3571 5.69556C20.712 5.69556 21 5.98356 21 6.33841V20.3572C21 20.7127 20.712 21.0001 20.3571 21.0001H6.33836C5.98286 21.0001 5.6955 20.7127 5.6955 20.3572C5.6955 20.0017 5.9835 19.7143 6.33836 19.7143Z"
          fill="#9B9B9B"
        />
        <path
          d="M3.64305 3H17.6618C18.0167 3 18.3047 3.288 18.3047 3.64286V17.6616C18.3047 18.0171 18.0167 18.3045 17.6618 18.3045H3.64305C3.28755 18.3045 3.00019 18.0171 3.00019 17.6616V3.64286C3.00019 3.288 3.28755 3 3.64305 3ZM4.2859 17.0188H17.019V4.28571H4.2859V17.0188Z"
          fill="#9B9B9B"
        />
        <path
          d="M14.156 7.7906H7.14626C6.79076 7.7906 6.5034 7.5026 6.5034 7.14774C6.5034 6.79288 6.79076 6.50488 7.14626 6.50488H14.156C14.5108 6.50488 14.7988 6.79288 14.7988 7.14774C14.7988 7.5026 14.5108 7.7906 14.156 7.7906Z"
          fill="#9B9B9B"
        />
        <path
          d="M14.156 11.2947H7.14626C6.79076 11.2947 6.5034 11.0067 6.5034 10.6519C6.5034 10.297 6.79076 10.009 7.14626 10.009H14.156C14.5108 10.009 14.7988 10.297 14.7988 10.6519C14.7988 11.0067 14.5108 11.2947 14.156 11.2947Z"
          fill="#9B9B9B"
        />
        <path
          d="M14.156 14.7989H7.14626C6.79076 14.7989 6.5034 14.5115 6.5034 14.156C6.5034 13.8005 6.79076 13.5132 7.14626 13.5132H14.156C14.5108 13.5132 14.7988 13.8005 14.7988 14.156C14.7988 14.5115 14.5108 14.7989 14.156 14.7989Z"
          fill="#9B9B9B"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.33836 19.7143H19.7143V6.33841C19.7143 5.98356 20.0023 5.69556 20.3571 5.69556C20.712 5.69556 21 5.98356 21 6.33841V20.3572C21 20.7127 20.712 21.0001 20.3571 21.0001H6.33836C5.98286 21.0001 5.6955 20.7127 5.6955 20.3572C5.6955 20.0017 5.9835 19.7143 6.33836 19.7143Z"
        fill="#CCCCCC"
      />
      <path
        d="M3.64305 3H17.6618C18.0167 3 18.3047 3.288 18.3047 3.64286V17.6616C18.3047 18.0171 18.0167 18.3045 17.6618 18.3045H3.64305C3.28755 18.3045 3.00019 18.0171 3.00019 17.6616V3.64286C3.00019 3.288 3.28755 3 3.64305 3ZM4.2859 17.0188H17.019V4.28571H4.2859V17.0188Z"
        fill="#CCCCCC"
      />
      <path
        d="M14.156 7.7906H7.14626C6.79076 7.7906 6.5034 7.5026 6.5034 7.14774C6.5034 6.79288 6.79076 6.50488 7.14626 6.50488H14.156C14.5108 6.50488 14.7988 6.79288 14.7988 7.14774C14.7988 7.5026 14.5108 7.7906 14.156 7.7906Z"
        fill="#CCCCCC"
      />
      <path
        d="M14.156 11.2947H7.14626C6.79076 11.2947 6.5034 11.0067 6.5034 10.6519C6.5034 10.297 6.79076 10.009 7.14626 10.009H14.156C14.5108 10.009 14.7988 10.297 14.7988 10.6519C14.7988 11.0067 14.5108 11.2947 14.156 11.2947Z"
        fill="#CCCCCC"
      />
      <path
        d="M14.156 14.7989H7.14626C6.79076 14.7989 6.5034 14.5115 6.5034 14.156C6.5034 13.8005 6.79076 13.5132 7.14626 13.5132H14.156C14.5108 13.5132 14.7988 13.8005 14.7988 14.156C14.7988 14.5115 14.5108 14.7989 14.156 14.7989Z"
        fill="#CCCCCC"
      />
    </svg>
  );
};

export default News;
