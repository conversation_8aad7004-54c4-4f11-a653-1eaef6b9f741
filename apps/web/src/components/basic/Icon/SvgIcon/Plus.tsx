import React from 'react';
import { useSelector } from 'react-redux';

const Plus = () => {
  const isMobile = useSelector((state: any) => state?.App?.isMobile || false);
  return (
    <svg
      width={isMobile ? '24' : '36'}
      height={isMobile ? '24' : '36'}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M32.1591 19.4615L32.1591 16.2568H19.4539L19.4539 3.55157H16.2492L16.2492 16.2568H3.54401L3.54401 19.4615L16.2492 19.4615L16.2492 32.1667H19.4539L19.4539 19.4615L32.1591 19.4615Z"
        fill="#9B9B9B"
      />
    </svg>
  );
};

export default Plus;
