import React from 'react';

const SmartTv = () => (
  <svg
    width="100%"
    height="100%"
    viewBox="0 0 20 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99981 1.04284C15.1612 1.04284 19.6774 0.612721 19.6774 0.612721C19.763 0.612721 19.845 0.646708 19.9055 0.707204C19.966 0.767701 20 0.849752 20 0.935307L20 12.5484C20 12.634 19.966 12.716 19.9055 12.7765C19.845 12.837 19.763 12.871 19.6774 12.871H18.172L18.172 16.5808C18.1718 16.7946 18.0868 16.9996 17.9356 17.1508C17.7844 17.302 17.5794 17.387 17.3655 17.3872H12.8493C12.6355 17.387 12.4305 17.302 12.2793 17.1508C12.1281 16.9996 12.0431 16.7946 12.0429 16.5808V15.0216L9.56969 14.9271L6.12877 15.0216C6.04321 15.0216 5.96116 14.9876 5.90066 14.9271C5.84017 14.8666 5.80618 14.7846 5.80618 14.699C5.80618 14.6135 5.84017 14.5314 5.90066 14.4709C5.96116 14.4104 6.04321 14.3764 6.12877 14.3764L9.03205 14.1614V12.656L0.322207 12.871C0.236652 12.871 0.1546 12.837 0.0941036 12.7765C0.0336069 12.716 -0.000380397 12.634 -0.000380397 12.5484V0.935308C-0.000380397 0.849753 0.0336063 0.767702 0.0941031 0.707205C0.1546 0.646708 0.236651 0.612721 0.322206 0.612721C0.322206 0.612721 4.83842 1.04284 9.99981 1.04284ZM10.1073 14.1614L12.0429 14.3764V12.871L10.1073 12.656V14.1614ZM17.4796 16.6948C17.5098 16.6646 17.5268 16.6235 17.5268 16.5808V6.58058C17.5268 6.5378 17.5098 6.49677 17.4796 6.46652C17.4493 6.43628 17.4083 6.41928 17.3655 6.41928L12.8493 6.41928C12.8065 6.41928 12.7655 6.43628 12.7353 6.46652C12.705 6.49677 12.688 6.5378 12.688 6.58058V16.5808C12.688 16.6235 12.705 16.6646 12.7353 16.6948C12.7655 16.7251 12.8065 16.7421 12.8493 16.7421H17.3655C17.4083 16.7421 17.4493 16.7251 17.4796 16.6948ZM18.172 12.2258H19.3548V1.25789C19.3548 1.25789 13.6686 1.68801 9.99981 1.68801C6.33103 1.68801 0.644793 1.25789 0.644793 1.25789L0.644794 12.2258L9.56969 12.0108L12.0429 12.2258V6.58058C12.0431 6.36675 12.1281 6.16175 12.2793 6.01055C12.4305 5.85936 12.6355 5.77432 12.8493 5.77411L17.3655 5.77411C17.5794 5.77432 17.7844 5.85936 17.9356 6.01055C18.0868 6.16175 18.1718 6.36675 18.172 6.58058V12.2258ZM14.2051 12.151C14.4881 12.34 14.8207 12.4409 15.161 12.4409C15.6171 12.4404 16.0544 12.259 16.377 11.9364C16.6995 11.6139 16.8809 11.1766 16.8814 10.7205C16.8814 10.3802 16.7805 10.0476 16.5915 9.76462C16.4024 9.4817 16.1337 9.26118 15.8194 9.13096C15.505 9.00075 15.1591 8.96667 14.8253 9.03306C14.4916 9.09944 14.185 9.2633 13.9444 9.50391C13.7038 9.74452 13.54 10.0511 13.4736 10.3848C13.4072 10.7186 13.4413 11.0645 13.5715 11.3789C13.7017 11.6932 13.9222 11.9619 14.2051 12.151Z"
      fill="currentColor"
    />
  </svg>
);

export default SmartTv;
