import React from 'react';

const HomeStroke = ({ title, size }: any) => (
  <svg
    width={size || '24'}
    height={size ? 'auto' : '24'}
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.5921 0.107602C8.8185 -0.0377889 9.10943 -0.0356855 9.33371 0.112964L17.3061 5.39693C17.4201 5.47247 17.5039 5.57648 17.5542 5.69332C17.6118 5.79304 17.6448 5.90878 17.6448 6.03221V17.3214C17.6448 17.6962 17.3409 18 16.9661 18H11.2914C11.279 18 11.2667 17.9997 11.2545 17.9991C11.2423 17.9997 11.23 18 11.2176 18C10.8428 18 10.539 17.6962 10.539 17.3214V12.4832C10.539 12.0194 10.3565 11.576 10.0339 11.2501C9.7114 10.9244 9.2755 10.7427 8.82242 10.7427C8.36934 10.7427 7.93344 10.9244 7.61098 11.2501C7.28831 11.576 7.10583 12.0194 7.10583 12.4832V17.3214C7.10583 17.6962 6.802 18 6.4272 18C6.41287 18 6.39865 17.9996 6.38455 17.9987C6.37042 17.9996 6.35618 18 6.34184 18H0.678634C0.303835 18 0 17.6962 0 17.3214V6.03221C0 5.92461 0.0250418 5.82286 0.0696154 5.73246C0.119995 5.60445 0.209649 5.49051 0.334252 5.41049L8.5921 0.107602ZM5.74856 16.6428V12.4832C5.74856 11.6638 6.0708 10.8766 6.6465 10.2951C7.22243 9.71345 8.005 9.38543 8.82242 9.38543C9.63985 9.38543 10.4224 9.71345 10.9983 10.2951C11.574 10.8766 11.8963 11.6638 11.8963 12.4832V16.6428H16.2875V6.35012L8.95293 1.48891L1.35727 6.36657V16.6428H5.74856Z"
      fill="currentColor"
    />
  </svg>
);

export default HomeStroke;
