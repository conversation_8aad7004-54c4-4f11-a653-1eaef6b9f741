import React from 'react';

const Sport = ({ isHovering, isActive }: any) => {
  if (isActive) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.4246 4.57539C17.4414 2.59221 14.8046 1.5 12 1.5C9.19535 1.5 6.55858 2.59221 4.57535 4.57539C2.59216 6.55858 1.5 9.19535 1.5 12C1.5 14.8047 2.59216 17.4415 4.57535 19.4246C6.55854 21.4078 9.19535 22.5 12 22.5C14.8046 22.5 17.4414 21.4078 19.4246 19.4246C21.4078 17.4415 22.5 14.8047 22.5 12C22.5 9.19535 21.4078 6.55858 19.4246 4.57539ZM12.6152 5.06463L15.0954 3.26261C16.9504 3.92177 18.5351 5.1556 19.6345 6.74914L18.7367 9.5122L15.5727 10.5403L12.6152 8.39173V5.06463ZM8.90451 3.26265L11.3848 5.06463V8.39181L8.42774 10.5401L5.26347 9.51199L4.3656 6.74897C5.46502 5.15552 7.04966 3.92177 8.90451 3.26265ZM4.83826 17.8785C3.57002 16.3363 2.78916 14.3788 2.73371 12.2439L4.88326 10.6822L8.04732 11.7103L9.17694 15.187L7.22139 17.8785H4.83826ZM15.0886 20.7398C14.122 21.0824 13.0825 21.2695 12 21.2695C10.9175 21.2695 9.87806 21.0824 8.91152 20.7398L8.2168 18.6018L10.1723 15.9103L13.8277 15.9101L15.7833 18.6017L15.0886 20.7398ZM19.1618 17.8784H16.7787L14.8232 15.1869L15.9528 11.7106L19.1169 10.6825L21.2663 12.2441C21.2108 14.3789 20.43 16.3362 19.1618 17.8784Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  if (isHovering) {
    return (
      <svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 1C9.08257 1 6.28476 2.15886 4.2219 4.2219C2.15886 6.28476 1 9.08238 1 12C1 14.9176 2.15886 17.7152 4.2219 19.7781C6.28476 21.8411 9.08238 23 12 23C14.9176 23 17.7152 21.8411 19.7781 19.7781C21.8411 17.7152 23 14.9176 23 12C22.9965 9.08371 21.8365 6.2881 19.7744 4.22571C17.712 2.16357 14.9163 1.00362 12.0001 1.0001L12 1ZM20.1457 17.78H17.29C17.1453 17.7806 17.0017 17.8023 16.8633 17.8445L15.3492 15.7603C15.4325 15.641 15.498 15.5103 15.5436 15.3722L16.8518 11.3456C16.895 11.2104 16.9184 11.0698 16.9218 10.9278L19.3693 10.1327C19.4565 10.248 19.5598 10.3499 19.6758 10.436L21.9937 12.1204V12.1202C21.971 14.1514 21.326 16.1267 20.1457 17.7802L20.1457 17.78ZM7.13571 17.8445C6.99732 17.8023 6.85353 17.7806 6.709 17.78H3.8541C2.67367 16.1265 2.02858 14.1507 2.00605 12.119L4.326 10.4343C4.44208 10.3486 4.54513 10.2466 4.63218 10.1317L7.07937 10.9269C7.08272 11.0688 7.10616 11.2094 7.14931 11.3447L8.45755 15.3713V15.3715C8.50275 15.5097 8.56767 15.6406 8.65063 15.7602L7.13571 17.8445ZM3.27986 7.11883L3.9149 9.06693C3.9815 9.27266 3.90951 9.49792 3.73596 9.62702L2.07415 10.8348C2.22575 9.52841 2.63572 8.26521 3.27987 7.11883H3.27986ZM9.40652 15.0626L8.09829 11.036V11.0358C8.03039 10.8296 8.10387 10.6032 8.27983 10.4759L11.7067 7.98784C11.8816 7.86042 12.1186 7.86042 12.2934 7.98784L15.7188 10.4767C15.8948 10.6039 15.9683 10.8303 15.9004 11.0366L14.5921 15.0632V15.0634C14.5259 15.2701 14.3336 15.4103 14.1165 15.4099H9.88308C9.66582 15.4103 9.4733 15.2697 9.40763 15.0625L9.40652 15.0626ZM20.2646 9.62693C20.0913 9.49784 20.0191 9.27276 20.0855 9.06683L20.7191 7.12021C21.3636 8.26621 21.7739 9.52907 21.9259 10.8353L20.2646 9.62693ZM20.0161 6.04712L19.1361 8.75379C19.0916 8.8918 19.0673 9.0356 19.0639 9.1805L16.6175 9.97364C16.5295 9.85682 16.4252 9.75359 16.3074 9.66709L12.882 7.179C12.7648 7.09492 12.6363 7.02814 12.5001 6.9807V4.4119C12.637 4.36483 12.7663 4.29843 12.8844 4.21472L15.1962 2.53468C17.1219 3.18944 18.8029 4.41453 20.0162 6.04715L20.0161 6.04712ZM13.9599 2.19574L12.2986 3.40407V3.40389C12.1217 3.53316 11.8818 3.53409 11.704 3.40612L10.0399 2.19574C11.3336 1.93513 12.6662 1.93513 13.9599 2.19574V2.19574ZM8.80372 2.53354L11.1181 4.21678H11.1183C11.2353 4.30011 11.3638 4.36596 11.4998 4.41246V6.98108C11.3636 7.02833 11.2351 7.0951 11.1179 7.17919L7.69249 9.66766C7.57474 9.75397 7.47039 9.85702 7.38241 9.97365L4.93708 9.17918C4.93354 9.03502 4.90936 8.89216 4.86509 8.75488L3.98509 6.04655C5.1979 4.41393 6.87852 3.18883 8.80366 2.53355L8.80372 2.53354ZM4.67034 18.7802H6.70996C6.92592 18.7804 7.11733 18.9195 7.18429 19.1251L7.81581 21.0717C6.62777 20.5205 5.5591 19.7419 4.67015 18.78L4.67034 18.7802ZM9.02115 21.547L8.13444 18.8141C8.08924 18.679 8.02488 18.551 7.94359 18.434L9.46145 16.3448C9.59798 16.3859 9.73991 16.4073 9.88277 16.408H14.1168C14.2596 16.4073 14.4016 16.3859 14.5381 16.3448L16.0561 18.434H16.0563C15.9745 18.5518 15.9099 18.6805 15.8644 18.8163L14.9782 21.547C13.0385 22.1513 10.9607 22.1513 9.02107 21.547L9.02115 21.547ZM16.184 21.0708L16.8146 19.1268C16.8812 18.9205 17.073 18.7804 17.2898 18.7801H19.3293C18.4403 19.7415 17.3717 20.5196 16.184 21.0706L16.184 21.0708Z"
          fill="currentColor"
        />
      </svg>
    );
  }
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 1C9.08257 1 6.28476 2.15886 4.2219 4.2219C2.15886 6.28476 1 9.08238 1 12C1 14.9176 2.15886 17.7152 4.2219 19.7781C6.28476 21.8411 9.08238 23 12 23C14.9176 23 17.7152 21.8411 19.7781 19.7781C21.8411 17.7152 23 14.9176 23 12C22.9965 9.08371 21.8365 6.2881 19.7744 4.22571C17.712 2.16357 14.9163 1.00362 12.0001 1.0001L12 1ZM20.1457 17.78H17.29C17.1453 17.7806 17.0017 17.8023 16.8633 17.8445L15.3492 15.7603C15.4325 15.641 15.498 15.5103 15.5436 15.3722L16.8518 11.3456C16.895 11.2104 16.9184 11.0698 16.9218 10.9278L19.3693 10.1327C19.4565 10.248 19.5598 10.3499 19.6758 10.436L21.9937 12.1204V12.1202C21.971 14.1514 21.326 16.1267 20.1457 17.7802L20.1457 17.78ZM7.13571 17.8445C6.99732 17.8023 6.85353 17.7806 6.709 17.78H3.8541C2.67367 16.1265 2.02858 14.1507 2.00605 12.119L4.326 10.4343C4.44208 10.3486 4.54513 10.2466 4.63218 10.1317L7.07937 10.9269C7.08272 11.0688 7.10616 11.2094 7.14931 11.3447L8.45755 15.3713V15.3715C8.50275 15.5097 8.56767 15.6406 8.65063 15.7602L7.13571 17.8445ZM3.27986 7.11883L3.9149 9.06693C3.9815 9.27266 3.90951 9.49792 3.73596 9.62702L2.07415 10.8348C2.22575 9.52841 2.63572 8.26521 3.27987 7.11883H3.27986ZM9.40652 15.0626L8.09829 11.036V11.0358C8.03039 10.8296 8.10387 10.6032 8.27983 10.4759L11.7067 7.98784C11.8816 7.86042 12.1186 7.86042 12.2934 7.98784L15.7188 10.4767C15.8948 10.6039 15.9683 10.8303 15.9004 11.0366L14.5921 15.0632V15.0634C14.5259 15.2701 14.3336 15.4103 14.1165 15.4099H9.88308C9.66582 15.4103 9.4733 15.2697 9.40763 15.0625L9.40652 15.0626ZM20.2646 9.62693C20.0913 9.49784 20.0191 9.27276 20.0855 9.06683L20.7191 7.12021C21.3636 8.26621 21.7739 9.52907 21.9259 10.8353L20.2646 9.62693ZM20.0161 6.04712L19.1361 8.75379C19.0916 8.8918 19.0673 9.0356 19.0639 9.1805L16.6175 9.97364C16.5295 9.85682 16.4252 9.75359 16.3074 9.66709L12.882 7.179C12.7648 7.09492 12.6363 7.02814 12.5001 6.9807V4.4119C12.637 4.36483 12.7663 4.29843 12.8844 4.21472L15.1962 2.53468C17.1219 3.18944 18.8029 4.41453 20.0162 6.04715L20.0161 6.04712ZM13.9599 2.19574L12.2986 3.40407V3.40389C12.1217 3.53316 11.8818 3.53409 11.704 3.40612L10.0399 2.19574C11.3336 1.93513 12.6662 1.93513 13.9599 2.19574V2.19574ZM8.80372 2.53354L11.1181 4.21678H11.1183C11.2353 4.30011 11.3638 4.36596 11.4998 4.41246V6.98108C11.3636 7.02833 11.2351 7.0951 11.1179 7.17919L7.69249 9.66766C7.57474 9.75397 7.47039 9.85702 7.38241 9.97365L4.93708 9.17918C4.93354 9.03502 4.90936 8.89216 4.86509 8.75488L3.98509 6.04655C5.1979 4.41393 6.87852 3.18883 8.80366 2.53355L8.80372 2.53354ZM4.67034 18.7802H6.70996C6.92592 18.7804 7.11733 18.9195 7.18429 19.1251L7.81581 21.0717C6.62777 20.5205 5.5591 19.7419 4.67015 18.78L4.67034 18.7802ZM9.02115 21.547L8.13444 18.8141C8.08924 18.679 8.02488 18.551 7.94359 18.434L9.46145 16.3448C9.59798 16.3859 9.73991 16.4073 9.88277 16.408H14.1168C14.2596 16.4073 14.4016 16.3859 14.5381 16.3448L16.0561 18.434H16.0563C15.9745 18.5518 15.9099 18.6805 15.8644 18.8163L14.9782 21.547C13.0385 22.1513 10.9607 22.1513 9.02107 21.547L9.02115 21.547ZM16.184 21.0708L16.8146 19.1268C16.8812 18.9205 17.073 18.7804 17.2898 18.7801H19.3293C18.4403 19.7415 17.3717 20.5196 16.184 21.0706L16.184 21.0708Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Sport;
