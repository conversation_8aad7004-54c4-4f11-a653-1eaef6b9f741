import React from 'react';

const Expand = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={32} height={32} viewBox="0 0 32 32" fill="none">
      <path
        d="M27 6V12C27 12.2652 26.8946 12.5196 26.7071 12.7071C26.5196 12.8946 26.2652 13 26 13C25.7348 13 25.4804 12.8946 25.2929 12.7071C25.1054 12.5196 25 12.2652 25 12V8.41375L19.7075 13.7075C19.5199 13.8951 19.2654 14.0006 19 14.0006C18.7346 14.0006 18.4801 13.8951 18.2925 13.7075C18.1049 13.5199 17.9994 13.2654 17.9994 13C17.9994 12.7346 18.1049 12.4801 18.2925 12.2925L23.5863 7H20C19.7348 7 19.4804 6.89464 19.2929 6.70711C19.1054 6.51957 19 6.26522 19 6C19 5.73478 19.1054 5.48043 19.2929 5.29289C19.4804 5.10536 19.7348 5 20 5H26C26.2652 5 26.5196 5.10536 26.7071 5.29289C26.8946 5.48043 27 5.73478 27 6ZM12.2925 18.2925L7 23.5863V20C7 19.7348 6.89464 19.4804 6.70711 19.2929C6.51957 19.1054 6.26522 19 6 19C5.73478 19 5.48043 19.1054 5.29289 19.2929C5.10536 19.4804 5 19.7348 5 20V26C5 26.2652 5.10536 26.5196 5.29289 26.7071C5.48043 26.8946 5.73478 27 6 27H12C12.2652 27 12.5196 26.8946 12.7071 26.7071C12.8946 26.5196 13 26.2652 13 26C13 25.7348 12.8946 25.4804 12.7071 25.2929C12.5196 25.1054 12.2652 25 12 25H8.41375L13.7075 19.7075C13.8951 19.5199 14.0006 19.2654 14.0006 19C14.0006 18.7346 13.8951 18.4801 13.7075 18.2925C13.5199 18.1049 13.2654 17.9994 13 17.9994C12.7346 17.9994 12.4801 18.1049 12.2925 18.2925ZM26 19C25.7348 19 25.4804 19.1054 25.2929 19.2929C25.1054 19.4804 25 19.7348 25 20V23.5863L19.7075 18.2925C19.5199 18.1049 19.2654 17.9994 19 17.9994C18.7346 17.9994 18.4801 18.1049 18.2925 18.2925C18.1049 18.4801 17.9994 18.7346 17.9994 19C17.9994 19.2654 18.1049 19.5199 18.2925 19.7075L23.5863 25H20C19.7348 25 19.4804 25.1054 19.2929 25.2929C19.1054 25.4804 19 25.7348 19 26C19 26.2652 19.1054 26.5196 19.2929 26.7071C19.4804 26.8946 19.7348 27 20 27H26C26.2652 27 26.5196 26.8946 26.7071 26.7071C26.8946 26.5196 27 26.2652 27 26V20C27 19.7348 26.8946 19.4804 26.7071 19.2929C26.5196 19.1054 26.2652 19 26 19ZM8.41375 7H12C12.2652 7 12.5196 6.89464 12.7071 6.70711C12.8946 6.51957 13 6.26522 13 6C13 5.73478 12.8946 5.48043 12.7071 5.29289C12.5196 5.10536 12.2652 5 12 5H6C5.73478 5 5.48043 5.10536 5.29289 5.29289C5.10536 5.48043 5 5.73478 5 6V12C5 12.2652 5.10536 12.5196 5.29289 12.7071C5.48043 12.8946 5.73478 13 6 13C6.26522 13 6.51957 12.8946 6.70711 12.7071C6.89464 12.5196 7 12.2652 7 12V8.41375L12.2925 13.7075C12.4801 13.8951 12.7346 14.0006 13 14.0006C13.2654 14.0006 13.5199 13.8951 13.7075 13.7075C13.8951 13.5199 14.0006 13.2654 14.0006 13C14.0006 12.7346 13.8951 12.4801 13.7075 12.2925L8.41375 7Z"
        fill="white"
      />
    </svg>
  );
};

export default Expand;
