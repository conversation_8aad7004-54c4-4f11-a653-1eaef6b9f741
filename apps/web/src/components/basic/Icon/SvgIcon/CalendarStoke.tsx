import React from 'react';

const CalendarStroke = ({ size, title }: any) => (
  <svg
    width={size || 24}
    height={size ? 'auto' : 24}
    viewBox="0 0 18 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>{title}</title>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.5 1.50049V0.749989C4.5 0.48205 4.64301 0.23445 4.87504 0.100479C5.10708 -0.033493 5.39296 -0.033493 5.625 0.100479C5.85704 0.23445 6.00004 0.482046 6.00004 0.749989V1.50049H12.0001V1.50007V0.749989C12.0001 0.48205 12.1431 0.23445 12.3752 0.100479C12.6072 -0.033493 12.8931 -0.033493 13.1251 0.100479C13.3572 0.23445 13.5002 0.482046 13.5002 0.749989V1.50007V1.50049H15.75C16.3468 1.50049 16.9191 1.73754 17.3409 2.15942C17.7629 2.58142 18 3.15372 18 3.75049V15.7504C18 16.3471 17.7629 16.9194 17.3409 17.3413C16.9191 17.7633 16.3468 18.0004 15.75 18.0004H2.25C1.65324 18.0004 1.08093 17.7633 0.659057 17.3413C0.237054 16.9194 0 16.3471 0 15.7504V3.75049C0 3.15372 0.237054 2.58142 0.659057 2.15942C1.08093 1.73754 1.65324 1.50049 2.25 1.50049H4.5ZM1.71964 3.22C1.86027 3.07937 2.05112 3.00027 2.25 3.00027H4.5C4.50008 3.19914 4.57905 3.38976 4.71973 3.5303C4.86035 3.67093 5.05107 3.75003 5.24995 3.75003C5.44895 3.75003 5.63969 3.67093 5.78032 3.5303C5.92087 3.38976 5.99996 3.19916 6.00004 3.00027H12.0001C12.0002 3.19914 12.0793 3.38976 12.2198 3.5303C12.3605 3.67093 12.5512 3.75003 12.7502 3.75003C12.9491 3.75003 13.1398 3.67093 13.2804 3.5303C13.4211 3.38976 13.5001 3.19916 13.5002 3.00027H15.75C15.9489 3.00027 16.1397 3.07937 16.2804 3.22C16.421 3.36062 16.5 3.55135 16.5 3.75036V6H1.50023H1.50004V3.75036C1.50004 3.55136 1.57902 3.36062 1.71964 3.22ZM1.50004 7.49991V15.7502C1.50004 15.9491 1.57902 16.14 1.71964 16.2806C1.86027 16.4212 2.0511 16.5002 2.25 16.5002H15.75C15.9489 16.5002 16.1397 16.4212 16.2804 16.2806C16.421 16.14 16.5 15.9491 16.5 15.7502V7.49991H1.50023H1.50004ZM3 10.65C3 10.3186 3.26863 10.05 3.6 10.05H11.4C11.7314 10.05 12 10.3186 12 10.65C12 10.9814 11.7314 11.25 11.4 11.25H3.6C3.26863 11.25 3 10.9814 3 10.65ZM3.6 13.05C3.26863 13.05 3 13.3186 3 13.65C3 13.9814 3.26863 14.25 3.6 14.25H8.4C8.73137 14.25 9 13.9814 9 13.65C9 13.3186 8.73137 13.05 8.4 13.05H3.6Z"
      fill="currentColor"
    />
  </svg>
);

export default CalendarStroke;
