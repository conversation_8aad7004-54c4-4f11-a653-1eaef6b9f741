import React from 'react';

const Phone = ({ size }: any) => (
  <svg
    width={size === 'small' ? '20' : '25'}
    height={size === 'small' ? '20' : '24'}
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4729_94175)">
      <path
        d="M17.8502 14.0999C17.4502 13.6999 16.9502 13.4999 16.3502 13.4999C15.7502 13.4999 15.2502 13.6999 14.8502 14.0999L13.8502 15.0999V14.9999H13.6502C13.5502 14.9999 13.5502 14.8999 13.4502 14.8999C13.3502 14.8999 13.3502 14.8999 13.2502 14.7999C12.2502 14.1999 11.2502 13.2999 10.2502 12.0999C10.1502 11.8999 9.95016 11.6999 9.85016 11.5999C9.65016 11.2999 9.45016 10.9999 9.35016 10.6999C9.55016 10.5999 9.65016 10.3999 9.75016 10.2999C9.85016 10.1999 9.85016 10.1999 9.95016 10.0999L10.3502 9.6999C10.8502 9.1999 11.0502 8.6999 11.0502 8.0999C11.0502 7.5999 10.7502 7.0999 10.3502 6.6999L9.35016 5.6999C9.35016 5.5999 9.25016 5.4999 9.15016 5.3999C9.15016 5.2999 9.05016 5.2999 9.05016 5.1999L8.35016 4.4999C7.95016 4.0999 7.45016 3.8999 6.85016 3.8999C6.35016 3.8999 5.95016 4.0999 5.45016 4.4999L4.15016 5.7999C3.65016 6.4999 3.35016 7.0999 3.25016 7.7999C3.15016 9.0999 3.55016 10.2999 3.75016 10.6999C4.35016 12.3999 5.35016 13.8999 6.65016 15.5999C8.35016 17.6999 10.4502 19.1999 12.6502 20.2999C13.7502 20.7999 14.8502 21.1999 16.0502 21.2999H16.2502C17.2502 21.2999 18.0502 20.9999 18.6502 20.2999L19.8502 19.0999C20.2502 18.6999 20.4502 18.1999 20.4502 17.5999C20.4502 17.0999 20.2502 16.4999 19.8502 16.0999L17.8502 14.0999ZM18.3502 18.4999C18.1502 18.6999 17.8502 18.9999 17.6502 19.2999C17.2502 19.6999 16.8502 19.8999 16.4502 19.8999H16.2502C15.3502 19.7999 14.4502 19.4999 13.3502 18.9999C11.2502 17.9999 9.35016 16.4999 7.75016 14.5999C6.65016 13.2999 5.75016 11.8999 5.05016 10.0999C4.85016 9.2999 4.65016 8.5999 4.75016 7.8999C4.75016 7.4999 4.95016 7.1999 5.25016 6.8999L6.55016 5.6999C6.65016 5.4999 6.75016 5.4999 6.95016 5.4999C7.15016 5.4999 7.25016 5.5999 7.35016 5.6999L8.05016 6.3999L9.35016 7.7999C9.55016 7.9999 9.65016 8.0999 9.65016 8.1999C9.65016 8.3999 9.55016 8.4999 9.45016 8.5999L9.35016 8.6999C9.25016 8.6999 9.15016 8.7999 9.15016 8.7999C9.15016 8.7999 9.15016 8.8999 9.05016 8.8999C8.75016 9.1999 8.45016 9.4999 8.05016 9.7999C7.65016 10.2999 7.75016 10.5999 7.85016 10.8999C8.15016 11.5999 8.55016 12.1999 9.05016 12.8999C10.1502 14.1999 11.2502 15.1999 12.4502 15.9999C12.5502 15.9999 12.6502 16.0999 12.6502 16.0999C12.7502 16.0999 12.7502 16.1999 12.8502 16.1999C12.9502 16.2999 12.9502 16.2999 13.0502 16.2999C13.1502 16.2999 13.1502 16.2999 13.2502 16.3999C13.6502 16.6999 14.0502 16.6999 14.5502 16.2999L15.8502 14.9999C15.9502 14.8999 16.1502 14.7999 16.2502 14.7999C16.3502 14.7999 16.5502 14.8999 16.6502 14.9999L18.7502 17.0999C19.0502 17.3999 19.0502 17.5999 18.7502 17.9999V18.0999L18.4502 18.3999L18.3502 18.4999Z"
        fill="white"
      />
      <path
        d="M13.1508 7.79997C14.1508 7.79997 15.0508 8.19997 15.7508 8.99997C16.4508 9.69997 16.9508 10.6 17.0508 11.6C17.1508 12 17.4508 12.2 17.7508 12.2H17.8508C17.9508 12 18.1508 11.9 18.2508 11.8C18.3508 11.6 18.4508 11.5 18.3508 11.3C18.1508 9.99997 17.5508 8.79997 16.6508 7.99997C15.7508 7.09997 14.5508 6.49997 13.4508 6.29997C13.2508 6.19997 13.0508 6.29997 12.8508 6.39997C12.6508 6.49997 12.5508 6.69997 12.5508 6.89997C12.5508 7.09997 12.5508 7.19997 12.6508 7.39997C12.8508 7.59997 12.9508 7.69997 13.1508 7.79997Z"
        fill="white"
      />
      <path
        d="M18.9484 5.7C17.4484 4.2 15.6484 3.3 13.5484 3C13.3484 3 13.2484 3 13.0484 3.1C12.8484 3.2 12.7484 3.3 12.6484 3.6C12.6484 3.8 12.6484 4 12.7484 4.2C12.9484 4.3 13.0484 4.4 13.2484 4.4C14.9484 4.7 16.6484 5.5 17.8484 6.7C19.0484 7.9 19.9484 9.5 20.2484 11.2C20.2484 11.6 20.6484 11.8 20.9484 11.8H21.0484C21.3484 11.8 21.4484 11.7 21.5484 11.5C21.6484 11.4 21.6484 11.2 21.6484 10.9C21.3484 9 20.4484 7.2 18.9484 5.7Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_4729_94175">
        <rect width="18.4" height="18.4" fill="white" transform="translate(3.25 3)" />
      </clipPath>
    </defs>
  </svg>
);

export default Phone;
