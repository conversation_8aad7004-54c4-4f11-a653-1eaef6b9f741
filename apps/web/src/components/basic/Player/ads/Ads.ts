import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import { ADS_URL, EL_ID } from '@constants/constants';
import { createTimeout } from '@helpers/common';
import { getVASTTagByInventory } from '@tracking/TrackingDMP';
import { ENABLE_SDK_GG_IMA } from '@config/ConfigEnv';
import { requestAdsInstream } from '@tracking/functions/TrackingAds';

declare const window: any;
declare const google: any;

let resizeTimer: any = 0;
let stateLoopVideoPlayer = false;
let retryCount = 1;
let firstPre = false;

function loadGoogleIMASDK() {
  if (!ENABLE_SDK_GG_IMA || ENABLE_SDK_GG_IMA === 'false') {
    return;
  }
  return new Promise<void>((resolve, reject) => {
    if (window.google && window.google.ima) {
      resolve();
      return;
    }

    const script: any = document.createElement('script');
    script.src = '//imasdk.googleapis.com/js/sdkloader/ima3.js';
    script.async = true;
    script.defer = true;
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

async function initAd({
  props,
  videoElement,
  adContainer,
  subtitleElement,
  playerThis,
  adsMsgHandler,
  linkPlay,
  isIOS,
  handleShowSkipAds,
  isSafari,
  setupAds,
  startupLoadPreroll,
  contentId
}: any) {
  try {
    await loadGoogleIMASDK();
  } catch (error) {
    console.error('Failed to load Google IMA SDK:', error);
    return;
  }
  let adsPlayedDuration = Math.floor(new Date().getTime() / 1000);
  if (!linkPlay) return;
  const me = playerThis;
  const { adsEnable = true, ads, timeCodeAds = [] } = props;

  if (adContainer) {
    adContainer.innerHTML = '';
  }

  // Ads not enable
  if (!adsEnable) return;
  // Dont have ads
  if (!ads) return console.log('[ADS] Dont have ads');
  if (ads.length <= 0) return console.log('[ADS] Dont have ads');
  // Not have video element
  if (!videoElement) return console.log('[ADS] Not have video element');
  // Not have ads container
  if (!adContainer) return console.log('[ADS] Not have ads container');
  if (!window || !window.google || !window?.google?.ima) return;
  me.adDisplayContainer = new google.ima.AdDisplayContainer(adContainer, videoElement);
  me.adsLoader = new google.ima.AdsLoader(me.adDisplayContainer);

  // WHEN ADS LOADED
  me.adsLoader.addEventListener(
    google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,
    onAdsManagerLoaded,
    false
  );

  // WHEN ADS LOADED AN ERROR AD
  me.adsLoader.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, onAdError, false);
  videoElement.addEventListener('ended', () => {
    me.adsLoader.contentComplete();
  });
  if (setupAds) setupAds({ requestAds });

  // REQUEST ADS LOGIC
  // Pre ads
  const pre = find(ads, ['type', 'pre']);
  const mid = find(ads, ['type', 'mid']);
  const post = find(ads, ['type', 'post']);
  if (!me.adPoints.pre && !isEmpty(pre)) {
    videoElement.addEventListener('playing', () => {
      if (me.adPoints.pre) return;
      me.adPoints.pre = true;
      me.adPoints.currentAds = {
        urlKey: ADS_URL.URL1,
        ads: pre,
        slot: 0
      };
      retryCount = pre?.count >= 2 ? 2 : 1;
      firstPre = false;
      requestAds(pre?.[ADS_URL.URL1]?.url);
    });
  }

  // Mid ads
  if (!props?.contentDetail?.isDVR) {
    if (!isEmpty(mid)) {
      if (isIOS && isSafari) return;
      const repeat = mid.repeat * 60; // second
      let checkPlayAds = false;
      videoElement.addEventListener('timeupdate', () => {
        const current = Math.floor(videoElement.currentTime); // second
        if (current === 0) return;
        if (current !== me.adPoints.mid && checkPlayAds) {
          if (!(isSafari && isIOS)) checkPlayAds = false;
        }
        if (checkPlayAds) return;
        if (isEmpty(timeCodeAds)) {
          if (current % repeat !== 0) return;
          me.adPoints.mid = current;
          me.adPoints.currentAds = {
            urlKey: ADS_URL.URL1,
            ads: mid,
            slot: 0
          };
          retryCount = mid?.count >= 2 ? 2 : 1;
          requestAds(mid?.[ADS_URL.URL1]?.url);
          checkPlayAds = true;
        } else {
          timeCodeAds.map((item: any) => {
            if (item.secondsStartAds === current) {
              me.adPoints.mid = current;
              const adsToShow = find(ads, ['type', item.typeAds]);
              if (!isEmpty(adsToShow)) {
                me.adPoints.currentAds = {
                  urlKey: ADS_URL.URL1,
                  ads: adsToShow,
                  slot: 0
                };
                retryCount = adsToShow?.count >= 2 ? 2 : 1;
                requestAds(adsToShow?.[ADS_URL.URL1]?.url);
                checkPlayAds = true;
              }
            }
          });
        }
      });
    }
  }

  // Post ads
  if (!isEmpty(post)) {
    if (!(isIOS && isSafari)) {
      videoElement.addEventListener('timeupdate', () => {
        const current = Math.floor(videoElement.currentTime); // second
        const duration = Math.floor(videoElement.duration);
        if (current === 0) return; // not start
        if (me.adPoints.post) return; // already display
        if (current !== duration - 1) return; // not at post point
        me.adPoints.post = true;
        me.adPoints.currentAds = {
          urlKey: ADS_URL.URL1,
          ads: post
        };
        requestAds(post?.[ADS_URL.URL1]?.url);
      });
    }
  }

  function handleNextUrl() {
    const { urlKey } = me?.adPoints?.currentAds || {};

    if (retryCount <= 0) return { currentAd: me?.adPoints?.currentAds?.ads };

    let nextUrlKey = '';
    switch (urlKey) {
      case ADS_URL.URL1:
        nextUrlKey = ADS_URL.URL2;
        break;
      case ADS_URL.URL2:
        nextUrlKey = ADS_URL.URL3;
        break;
      case ADS_URL.URL3:
        nextUrlKey = ADS_URL.URL1;
        retryCount -= 1;
        break;
      default:
        nextUrlKey = ADS_URL.URL1;
        break;
    }

    return {
      nextUrlKey,
      nextUrl: me?.adPoints?.currentAds?.ads?.[nextUrlKey]?.url
    };
  }

  function onAdError(adErrorEvent: any) {
    const errorData = adErrorEvent.getError();
    const errorCode = errorData?.data?.errorCode;
    const errorMessages = errorData?.data?.errorMessage;
    const { nextUrl, nextUrlKey } = handleNextUrl();
    me.adPoints.currentAds = { ...me?.adPoints?.currentAds, urlKey: nextUrlKey };
    // const currentAd = me?.adPoints?.currentAds?.ads;

    console.log('onAdError', nextUrl, errorCode, adErrorEvent);

    if (nextUrl && errorCode !== 1205) {
      // 1205: The browser prevented playback initiated without user interaction.
      requestAds(nextUrl);
    } else {
      handleNextUrlOrSlot();
    }

    const adsRequest = me?.adPoints?.currentAds;

    const dataTrackingAdsRequest = {
      inventoryId: adsRequest?.ads?.url1?.url.replace('aiactiv://', ''),
      contentId,
      type: `${adsRequest?.ads.type}-roll`,
      slotNumber: adsRequest.slot,
      status: 'failed',
      errorMsg: errorMessages || ''
    };
    requestAdsInstream(dataTrackingAdsRequest);
  }

  function handleNextUrlOrSlot() {
    const { nextUrl, nextUrlKey } = handleNextUrl();
    me.adPoints.currentAds = { ...me?.adPoints?.currentAds, urlKey: nextUrlKey };
    if (nextUrl) {
      requestAds(nextUrl);
    } else if (me.adPoints.currentAds.slot <= me.adPoints.currentAds?.ads?.count) {
      retryCount = 1;
      me.adPoints.currentAds.slot += 1;
      me.adPoints.currentAds.urlKey = ADS_URL.URL1;
      requestAds(me.adPoints.currentAds?.ads?.[ADS_URL.URL1]?.url);
    } else {
      finalizeAds();
    }
  }

  function requestAds(url: any, retry = 0) {
    me.requestingAds = true;
    if (retry) retryCount = retry;
    console.log(
      '[ADS] Request Ads',
      me.adPoints?.currentAds?.ads?.type,
      me.adPoints?.currentAds?.urlKey,
      url
    );
    me.setState({ isAdsPaused: false });
    const regexPatternAIActiv = /^aiactiv:\/\/(?<inventoryId>.*$)/i.exec(url);
    const inventoryId = get(regexPatternAIActiv, 'groups.inventoryId');

    if (regexPatternAIActiv && inventoryId) {
      getVASTTagByInventory(Number(inventoryId), (vastTag: any) => {
        if (vastTag !== '') {
          console.log('[ADS AiA] VAST Tag:', vastTag);
          requestAds(vastTag, retry + 1);
        } else {
          console.log('[ADS AiA] VAST Tag not found');
          handleNextUrlOrSlot();
        }
      });
      return;
    }

    if (playerThis?.video) playerThis.resumeTime = playerThis.video.currentTime;
    if (me.adsManager) me.adsManager.destroy();
    if (me.adsLoader) me.adsLoader.contentComplete();
    if (!window || !window.google) return;
    me.adsRequest = new google.ima.AdsRequest();
    me.adsRequest.adTagUrl = url;
    me.adsRequest.linearAdSlotWidth = videoElement.clientWidth;
    me.adsRequest.linearAdSlotHeight = videoElement.clientHeight;
    me.adsRequest.nonLinearAdSlotWidth = videoElement.clientWidth;
    me.adsRequest.nonLinearAdSlotHeight = videoElement.clientHeight / 3;
    stateLoopVideoPlayer = videoElement.loop;
    if (videoElement.loop) videoElement.loop = false;
    me.adsLoader.requestAds(me.adsRequest);
  }

  function finalizeAds() {
    if (stateLoopVideoPlayer) {
      videoElement.loop = stateLoopVideoPlayer;
    }
    if (me.adsManager) {
      me.adsManager.destroy();
    }
    if (me.adsLoader) {
      me.adsLoader.contentComplete();
    }
    me.setState({
      isAdPlay: false,
      timeSkip: 0,
      isAdsEnd: true
    });
    adsMsgHandler(
      'play',
      me?.adsRequest?.adTagUrl || '',
      null,
      adsPlayedDuration,
      me?.adPoints?.currentAds?.ads,
      !!isIOS
    );
  }

  // EVENT LISTENER FUNCTION
  function onAdsManagerLoaded(adsManagerLoadedEvent: any) {
    console.log('[ADS] Ads Manager Loaded');
    if (!window || !window.google) return;
    const adsRenderingSettings = new google.ima.AdsRenderingSettings();
    if (isIOS) {
      adsRenderingSettings.restoreCustomPlaybackStateOnAdBreakComplete = true;
    }
    adsRenderingSettings.uiElements = [
      google.ima.UiElements.COUNTDOWN,
      google.ima.UiElements.AD_ATTRIBUTION
    ];
    me.adsManager = adsManagerLoadedEvent.getAdsManager(videoElement, adsRenderingSettings);
    // INIT LISTENER FOR ADS MANAGER
    me.adsManager.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR, onAdError);
    me.adsManager.addEventListener(google.ima.AdEvent.Type.LOADED, onAdLoaded);
    me.adsManager.addEventListener(google.ima.AdEvent.Type.STARTED, onAdStarted);
    me.adsManager.addEventListener(google.ima.AdEvent.Type.SKIPPED, onSkipAd);
    me.adsManager.addEventListener(google.ima.AdEvent.Type.RESUMED, onResumed);
    me.adsManager.addEventListener(google.ima.AdEvent.Type.PAUSED, onPausedAd);
    me.adsManager.addEventListener(
      google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,
      onContentPauseRequested
    );
    me.adsManager.addEventListener(
      google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,
      onContentResumeRequested
    );
    me.adsManager.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED, onAllAdsCompleted);

    me.adDisplayContainer.initialize();
    const width = videoElement.clientWidth;
    const height = videoElement.clientHeight;

    window.addEventListener('resize', () => {
      if (resizeTimer) clearTimeout(resizeTimer);
      resizeTimer = createTimeout(() => {
        if (me && me.adsManager) {
          me.adsManager.resize(
            videoElement.clientWidth,
            videoElement.clientHeight,
            google.ima.ViewMode.NORMAL
          );
        }
      }, 1000);
      if (me && me.adsManager) {
        me.adsManager.resize(
          videoElement.clientWidth,
          videoElement.clientHeight,
          google.ima.ViewMode.NORMAL
        );
      }
    });

    try {
      me.adsManager.init(width, height, google.ima.ViewMode.NORMAL);
      me.adsManager.start();
    } catch (adError) {
      // Play the video without ads, if an error occurs
      console.log('[ADS] AdsManager could not be started');
      const currentAd = me?.adPoints?.currentAds?.ads;
      adsMsgHandler('play', me?.adsRequest?.adTagUrl || '', adError, adsPlayedDuration, currentAd);
    }

    const adsRequest = me?.adPoints?.currentAds;

    const dataTrackingAdsRequest = {
      inventoryId: adsRequest?.ads?.url1?.url.replace('aiactiv://', ''),
      contentId,
      type: `${adsRequest?.ads.type}-roll`,
      slotNumber: adsRequest.slot,
      status: 'success'
    };
    requestAdsInstream(dataTrackingAdsRequest);
  }

  function onPausedAd() {
    const playAdButton = document.getElementById(EL_ID.PLAY_ADS_BUTTON);
    if (playAdButton) {
      if (playAdButton.classList) playAdButton.classList.remove('hide');
      playAdButton.onclick = () => {
        if (playAdButton.classList) playAdButton.classList.add('hide');
        if (me.adsLoader && typeof me?.adsManager?.resume === 'function') {
          me.adsManager.resume();
        }
      };
    }
    adsMsgHandler('pausedAds');
  }

  function onResumed() {
    adsMsgHandler('resumed');
  }

  function onSkipAd(adEvent: any) {
    console.log('[ADS onSkipAd]', adEvent);
    adsPlayedDuration = Math.floor(new Date().getTime() / 1000) - adsPlayedDuration - 1;
    me.setState({ isAdPlay: false, timeSkip: 0, isAdsEnd: true });
  }

  function onAdStarted(adEvent: any) {
    const getAd = adEvent.getAd();
    let isSkipAds = !!getAd?.g?.skippable || !!getAd?.h?.skippable;

    if (!isSkipAds && me.adsManager) {
      const currentAds = me.adsManager.getCurrentAd();
      if (currentAds) {
        isSkipAds = currentAds.isSkippable();
      }
    }

    if (me?.adPoints?.currentAds) {
      me.adPoints.currentAds.slot = me.adPoints.currentAds.slot || 0;
      me.adPoints.currentAds.slot += 1;
      me.setState({
        adsCount: me.adPoints.currentAds?.ads?.count,
        adsSlot: me.adPoints.currentAds.slot
      });

      if (me?.adPoints?.currentAds?.ads?.type === 'pre' && !firstPre) {
        firstPre = true;
        if (startupLoadPreroll) {
          startupLoadPreroll();
        }
      }
    }

    if (typeof handleShowSkipAds === 'function') {
      handleShowSkipAds({
        isSkipAds,
        currentAd: me.adPoints.currentAds,
        onSkipAd,
        adEvent,
        adsManager: me.adsManager,
        adsLoader: me.adsLoader
      });
    }
  }

  function onAdLoaded(adEvent: any) {
    const ad = adEvent.getAd();
    adsPlayedDuration = Math.floor(new Date().getTime() / 1000);
    if (!ad.isLinear()) {
      const currentAd = me?.adPoints?.currentAds?.ads;
      adsMsgHandler('play', me?.adsRequest?.adTagUrl || '', adEvent, adsPlayedDuration, currentAd);
    }
    if (playerThis.video) {
      playerThis.video.pause();
    }
    if (subtitleElement) {
      subtitleElement.innerHTML = '';
    }
  }

  function onContentPauseRequested(adEvent: any) {
    me.setState({
      isAdPlay: true,
      isAdsEnd: false
    });
    adsMsgHandler('pause', me?.adsRequest?.adTagUrl || '', adEvent, adsPlayedDuration);
  }

  function onContentResumeRequested() {}

  function onAllAdsCompleted() {
    requestNextSlot();
  }

  function requestNextSlot(adEvent?: any) {
    if (me?.adPoints?.currentAds) {
      if (me.adPoints.currentAds.slot < me.adPoints.currentAds?.ads?.count) {
        me.adPoints.currentAds.slot += 1;
        me.adPoints.currentAds.urlKey = ADS_URL.URL1; // Reset to URL1 for the new slot
        requestAds(me.adPoints.currentAds?.ads?.[ADS_URL.URL1]?.url);
      } else {
        if (stateLoopVideoPlayer) {
          videoElement.loop = stateLoopVideoPlayer;
        }
        if (me.adsManager) {
          me.adsManager.destroy();
        }
        if (me.adsLoader) {
          me.adsLoader.contentComplete();
        }
        me.setState({
          isAdPlay: false,
          timeSkip: 0,
          isAdsEnd: true
        });
        adsMsgHandler(
          'play',
          me?.adsRequest?.adTagUrl || '',
          adEvent,
          adsPlayedDuration,
          me?.adPoints?.currentAds?.ads,
          !!isIOS,
          !!me.adsLoader //Handle case trigger X mins
        );
      }
    }
  }
}

export { initAd };
