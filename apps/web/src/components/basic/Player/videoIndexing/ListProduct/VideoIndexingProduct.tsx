import React, { useEffect, useMemo, useState } from 'react';
import classnames from 'classnames';
import { VIDEO_INDEXING } from '@constants/constants';
import isEmpty from 'lodash/isEmpty';
import { setTypeActionVideoIndexing } from '@actions/detail';
import { useDispatch } from 'react-redux';
import styles from './videoIndexing.module.scss';
import IndexingList from './IndexingList';
import IndexingTitle from './IndexingTitle';

const VideoIndexingProduct = ({
  onPlayPause,
  indexingType,
  listProductBrand,
  contentDetail,
  itemsInMarks,
  data,
  itemsInMarksBrand
}: any) => {
  const dispatch = useDispatch();
  // Variables
  const [indexingData, setIndexingData] = useState([]);
  const [isCloseFullScreen, setIsCloseFullScreen] = useState(false);
  const [isShowAllProduct, setIsShowAllProduct] = useState(false);
  const [idItemClick, setIdItemClick] = useState<any>('');
  const [isEnabled, setIsEnabled] = useState(false);
  const { meta } = data || {};

  const getMetaDataBrandById = useMemo(() => {
    if (!isEmpty(itemsInMarksBrand) && !isEmpty(meta?.objects)) {
      return meta?.objects.filter(
        (obj: any) =>
          itemsInMarksBrand?.some((x: any) => x.objectId === obj?.id) &&
          obj.type === VIDEO_INDEXING.BRAND
      );
    }
  }, [itemsInMarksBrand, meta?.objects]);

  const getDataProductVideo = useMemo(() => {
    if (!isEmpty(itemsInMarks) && !isEmpty(meta?.objects)) {
      return meta?.objects.filter(
        (obj: any) =>
          itemsInMarks?.some((x: any) => x.objectId === obj?.id) &&
          obj.type === VIDEO_INDEXING.PRODUCT
      );
    }
    return [];
  }, [itemsInMarks, meta?.objects]);

  const handleClicked = ({ id, status }: any) => {
    setIdItemClick(id);
    setIsEnabled(status);
  };

  // ClassName
  const fullPlayerClassName = classnames(
    'absolute left-0 size-w-full size-h-full player-max',
    styles['full-player-indexing']
  );

  // Functions
  const handleCloseIndexingFullScreen = (status: any) => {
    if (typeof onPlayPause === 'function') onPlayPause();
    setIsCloseFullScreen(status);
    dispatch(
      setTypeActionVideoIndexing({
        isShowToastFirst: true,
        showProduct: false,
        indexingType: ''
      })
    );
  };

  const handleShowAllProduct = (status: any) => {
    setIsShowAllProduct(status);
  };

  useEffect(() => {
    if (indexingType === VIDEO_INDEXING.ALL_BRAND || indexingType === VIDEO_INDEXING.ALL_PRODUCT) {
      return setIndexingData(meta?.objects);
    }
    if (indexingType === VIDEO_INDEXING.BRAND && !isEmpty(listProductBrand?.data)) {
      return setIndexingData(listProductBrand?.data);
    }
    return setIndexingData(getDataProductVideo);
  }, [indexingType, listProductBrand?.data, getDataProductVideo]);

  if (isCloseFullScreen) return null;
  return (
    <div className={fullPlayerClassName}>
      <IndexingTitle
        indexingData={indexingData}
        type={indexingType}
        handleShowAllProduct={handleShowAllProduct}
        handleCloseIndexingFullScreen={handleCloseIndexingFullScreen}
        handleClicked={handleClicked}
      />
      <IndexingList
        itemsInMarks={itemsInMarks}
        indexingData={indexingData}
        noPrevious={listProductBrand?.noPrevious}
        type={indexingType}
        isShowAllProduct={isShowAllProduct}
        handleShowAllProduct={handleShowAllProduct}
        dataProductAndBrand={meta?.objects}
        contentDetail={contentDetail}
        getMetaDataBrandById={getMetaDataBrandById}
        handleClicked={handleClicked}
        idItemClick={idItemClick}
        isEnabled={isEnabled}
      />
    </div>
  );
};

export default VideoIndexingProduct;
