import React, { useMemo } from 'react';
import classnames from 'classnames';
import Image from '@components/basic/Image/Image';
import { TEXT } from '@constants/text';
import Button from '@components/basic/Buttons/Button';
import isEmpty from 'lodash/isEmpty';
import { ICON_KEY, VIDEO_INDEXING } from '@constants/constants';
import { getDataProductBrand } from '@actions/detail';
import { useDispatch, useSelector } from 'react-redux';
import TrackingVieIndexing from '@tracking/functions/TrackingVieIndexing';
import styles from './videoIndexing.module.scss';

const AllBrandItem = ({
  data,
  handleShowAllProductFromBrand,
  singleBrand,
  dataProductAndBrand,
  contentData
}: any) => {
  const dispatch = useDispatch();
  const { sessionId } = useSelector((state: any) => state?.Detail) || {};
  // Classnames
  const itemClassName = classnames(
    'relative text-white',
    styles.item,
    singleBrand && styles['no-background']
  );
  const imageClassName = classnames(
    'cell shrink flex-box justify-content-center align-items-center',
    styles.image,
    !singleBrand && styles['image-max-width']
  );
  const titleClassName = classnames(
    'margin-bottom-4 text-uppercase text-medium text',
    singleBrand ? styles['font-size-18'] : 'font-size-14'
  );
  const descriptionClassName = classnames(
    'text-gray239 line-clamp',
    singleBrand ? 'font-size-14' : `text-bold ${styles['font-size-10']}`
  );
  const buttonGroupClassName = classnames('grid-x align-items-center');
  const redirectButtonClassName = classnames(
    'align-items-center justify-content-center text-white font-size-14 line-height-2 text-left text text-medium',
    styles['button-line-clamp'],
    singleBrand
      ? `display-flex-inline margin-top-12 ${styles['single-redirect-button']}`
      : `flex-box cell auto ${styles['redirect-button']}`
  );

  const handleListProductBrand = (itemBrand: any) =>
    dataProductAndBrand?.filter((item: any) => item?.props?.brand?.id === itemBrand?.id);

  const handleClickViewAllProduct = ({ itemBrand }: any) => {
    const listProductBrand = handleListProductBrand(itemBrand);
    handleShowAllProductFromBrand({ showProduct: true, indexingType: VIDEO_INDEXING.BRAND });
    if (!isEmpty(listProductBrand)) {
      dispatch(
        getDataProductBrand({
          data: listProductBrand,
          noPrevious: false,
          idBrand: itemBrand?.id
        })
      );
    }
  };
  const idProductHaveBrand = useMemo(() => {
    if (!isEmpty(data) && !isEmpty(dataProductAndBrand)) {
      return dataProductAndBrand.findIndex((item: any) => item?.props?.brand?.id === data?.id) > -1;
    }
    return false;
  }, [dataProductAndBrand, data]);

  const handleExploreMoreBrand = () => {
    TrackingVieIndexing.exploreBrandButtonSelected({ data: contentData, item: data, sessionId });
    window.open(data?.detailsUrl, '_blank');
  };

  return (
    <div className={itemClassName}>
      <div className="grid-x align-items-center">
        <div className={imageClassName}>
          <Image src={data?.thumbnailUrl || ''} notWebp />
        </div>
        <div className="cell auto padding-x-6">
          <div className={titleClassName}>{data?.name}</div>
          <div className={descriptionClassName} data-line-clamp="3">
            {data?.shortDescription}
          </div>
          {singleBrand && (
            <Button
              className={redirectButtonClassName}
              title={TEXT.VIDEO_INDEXING.LINK_ALL_BRAND}
              textClass="p-l2"
              subTitle={TEXT.GET_MORE}
              iconType={ICON_KEY.REDIRECT}
              onClick={handleExploreMoreBrand}
            />
          )}
        </div>
      </div>
      {!singleBrand && (
        <div className={buttonGroupClassName}>
          <Button
            className={redirectButtonClassName}
            textClass="p-l2"
            subTitle={TEXT.GET_MORE}
            iconType={ICON_KEY.REDIRECT}
            title={TEXT.VIDEO_INDEXING.LINK_ALL_BRAND}
            onClick={handleExploreMoreBrand}
          />
          {idProductHaveBrand && (
            <Button
              className={redirectButtonClassName}
              title={TEXT.CLICK_SHOW_ALL}
              onClick={() => handleClickViewAllProduct({ itemBrand: data })}
              textClass="p-l2"
              subTitle={TEXT.CLICK_SHOW_ALL}
              iconType={ICON_KEY.CART}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default React.memo(AllBrandItem);
