import React, { useEffect, useMemo, useState } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useSelector } from 'react-redux';
import ContentApi from '@apis/cm/ContentApi';
import { groupAndAddObjectId } from '@services/videoIndexingService';
import VideoToastGroup from './ToastVideo/VideoToastGroup';
import VideoIndexingProduct from './ListProduct/VideoIndexingProduct';
import InfoBox from './InfoBox';

const VideoIndexing = ({
  currentTime,
  handleActionVideoIndexing,
  isDisplayInfoBox,
  showController,
  onPlayPause,
  contentDetail,
  isAdPlay
}: any) => {
  const { listProductBrand, typeActionVideoIndexing } =
    useSelector((state: any) => state?.Detail) || {};
  const [newDataIndexing, setNewDataIndexing] = useState<any>({});
  const [enableIconRed, setEnableIconRed] = useState(false);
  const [isToastAnimation, setToastAnimation] = useState(true);
  const [isShowIndicator, setShowIndicator] = useState(true);
  const [idClick, setIdClick] = useState<any>('');
  // check seek value have product
  const [isSeekVideoShowToast, setIsSeekVideoShowToast] = useState(false);

  const { indexingType, showProduct, activeIcon, isShowToastFirst } = typeActionVideoIndexing || {};

  const itemsInMarks = useMemo(
    () =>
      newDataIndexing?.productList?.filter(
        (item: any) => item?.startAt <= currentTime && item?.endAt >= currentTime && item?.isShow
      ),
    [currentTime]
  );

  const itemsInMarksBrand = useMemo(
    () =>
      newDataIndexing?.brandList?.filter(
        (item: any) => item?.startAt <= currentTime && item?.endAt >= currentTime
      ),
    [currentTime]
  );

  const handleClickIndicator = (item: any) => {
    if (item?.objectId) {
      setIdClick(item?.objectId);
    } else {
      setIdClick('');
    }
  };

  const getItemIndicator = ({ itemProduct }: any) => {
    if (!isEmpty(itemProduct)) {
      setEnableIconRed(true);
    } else if (itemProduct?.length === 0) {
      setEnableIconRed(false);
    }
  };

  const handleShowIndicator = (showIndicator: any) => {
    setShowIndicator(showIndicator);
  };

  useEffect(() => {
    if (activeIcon) setEnableIconRed(false);
    if (isShowToastFirst) setToastAnimation(false);
  }, [activeIcon, isShowToastFirst]);

  useEffect(() => {
    if (currentTime >= newDataIndexing?.itemProductFirst?.startAt && !isSeekVideoShowToast) {
      setIsSeekVideoShowToast(true);
    }
  }, [currentTime, newDataIndexing?.itemProductFirst]);

  useEffect(() => {
    if (contentDetail?.id) {
      ContentApi.getAllIndicator({ contentId: contentDetail?.id }).then((res: any) => {
        const newRes = groupAndAddObjectId({
          data: res
        });
        console.log(newRes);
        setNewDataIndexing(newRes);
      });
    }
  }, [contentDetail?.id]);

  useEffect(() => {
    getItemIndicator({
      itemProduct: itemsInMarks || []
    });
  }, [itemsInMarks?.length]);

  useEffect(() => {
    const clickItem = (itemsInMarks || []).find((it: any) => it.objectId === idClick);
    if (!clickItem) setIdClick('');
  }, [itemsInMarks]);

  return (
    <div className="layer-10">
      {!isAdPlay &&
        (itemsInMarks || []).map((item: any, index: any) => (
          <InfoBox
            key={`${item.objectId}_${index}`}
            item={item}
            data={newDataIndexing}
            currentTime={currentTime}
            handleActionVideoIndexing={handleActionVideoIndexing}
            isDisplayInfoBox={isDisplayInfoBox}
            isShowListProduct={showProduct}
            isShowIndicator={isShowIndicator}
            contentDetail={contentDetail}
            handleClickIndicator={handleClickIndicator}
            idClick={idClick}
          />
        ))}
      {(!isEmpty(itemsInMarks) || !isEmpty(itemsInMarksBrand) || isSeekVideoShowToast) && (
        <VideoToastGroup
          showController={showController}
          itemsInMarksBrand={itemsInMarksBrand}
          isShowListProduct={showProduct}
          enableIconRed={enableIconRed}
          handleActionVideoIndexing={handleActionVideoIndexing}
          data={newDataIndexing}
          isToastAnimation={isToastAnimation}
          handleShowIndicator={handleShowIndicator}
          isSeekVideoShowToast={isSeekVideoShowToast}
          itemsInMarks={itemsInMarks}
        />
      )}
      {showProduct && (
        <VideoIndexingProduct
          itemsInMarks={itemsInMarks}
          itemsInMarksBrand={itemsInMarksBrand}
          listProductBrand={listProductBrand}
          data={newDataIndexing}
          indexingType={indexingType}
          contentDetail={contentDetail}
          onPlayPause={onPlayPause}
        />
      )}
    </div>
  );
};

export default React.memo(VideoIndexing);
