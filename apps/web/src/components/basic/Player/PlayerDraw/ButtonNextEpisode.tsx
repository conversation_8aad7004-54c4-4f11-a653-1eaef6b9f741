import { TEXT } from '@constants/text';
import React, { useEffect, useState } from 'react';
import { PAGE } from '@constants/constants';
import { createTimeout } from '@helpers/common';
import { useVieRouter } from '@customHook';
import { fastTrackRegisterAtEndScreen } from '@tracking/functions/TrackingPVod';
import Button from '@components/basic/Buttons/Button';

let isHandGlobal = false;
let timer: any = 0;
const unmounted = false;
const ButtonNextEpisode = React.memo(({ nextEpisode, viewCredits }: any) => {
  const router = useVieRouter();
  const [isHands, setHands] = useState(false);
  const title = nextEpisode?.isPremiumPVod
    ? TEXT.REGISTER_NEXT_EPISODE_FAST_TRACK
    : TEXT.NEXT_EPISODE;

  useEffect(() => {
    if (!viewCredits && !nextEpisode?.isPremiumPVod) {
      timer = createTimeout(() => {
        if (!isHandGlobal) {
          onNextEpisode();
        }
      }, 10000);
    }

    window.addEventListener('mousemove', cancelAutoNext);
    document.addEventListener('keyup', cancelAutoNext);
    return () => {
      clearTimeout(timer);
      window.removeEventListener('mousemove', cancelAutoNext);
      document.removeEventListener('keyup', cancelAutoNext);
    };
  }, []);

  const cancelAutoNext = () => {
    setHands(true);
    clearTimeout(timer);
  };

  const onNextEpisode = () => {
    if (unmounted) return;
    // handle tracking pvod
    if (nextEpisode?.isPremiumPVod) {
      fastTrackRegisterAtEndScreen({
        id: nextEpisode?.id,
        title: nextEpisode?.title
      });
    }
    if (nextEpisode?.seo?.url) {
      clearTimeout(timer);
      router.push(PAGE.VOD, nextEpisode?.seo?.url);
    }
  };

  let buttonClass = `player__button player__button--light player__button--next${
    !nextEpisode?.isPremiumPVod && ' player__button--next-progress'
  }`;
  if (isHands || viewCredits) {
    buttonClass = 'player__button player__button--light player__button--next';
  }
  isHandGlobal = isHands;
  return (
    <Button
      className={buttonClass}
      size="large"
      theme="primarySolid"
      customizeClass="md:px-4 xl:px-8"
      iconName="vie-play-solid-rc"
      iconClass="relative z-[2]"
      title={title}
      onClick={onNextEpisode}
      textClass="relative z-[2]"
    />
  );
});

export default ButtonNextEpisode;
