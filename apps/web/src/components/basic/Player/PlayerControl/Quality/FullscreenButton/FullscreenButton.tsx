import { TEXT } from '@constants/text';
import React from 'react';

const FullscreenButton = ({ onControlFullscreen, fullscreen }: any) => (
  <button
    className="controls-button controls-button-full-screen shrink"
    title={!fullscreen ? TEXT.FULLSCREEN : TEXT.EXIT_FULLSCREEN}
    aria-label="Visionner"
    onClick={onControlFullscreen || (() => {})}
  >
    {fullscreen && <i className="vie vie-screen-exit" />}
    {!fullscreen && <i className="vie vie-screen-full-light" />}
  </button>
);
export default FullscreenButton;
