import React, { useEffect, useState } from 'react';
import classNames from 'classnames';

function LiveButtonTooltip({ visible, isFullscreen }: any) {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (visible) setOpen(visible);
    const timer = setTimeout(() => {
      setOpen(false);
    }, 3000);
    return () => clearTimeout(timer);
  }, [visible]);

  return (
    <div className="absolute -top-[3.5rem] left-[2.2rem] !z-20">
      {open && (
        <div
          className={classNames(
            isFullscreen ? 'text-[16px]' : 'text-[18px]',
            'text-white px-3 py-1.5 bg-[#0091FF] rounded-md relative'
          )}
        >
          <div className="absolute left-5 -bottom-[7px] border-solid border-t-[#0091FF] border-t-[12px] border-x-transparent border-x-8 border-b-0 rounded-[1px]" />
          <PERSON><PERSON><PERSON> để tiếp tục xem trực tiếp
        </div>
      )}
    </div>
  );
}

export default LiveButtonTooltip;
