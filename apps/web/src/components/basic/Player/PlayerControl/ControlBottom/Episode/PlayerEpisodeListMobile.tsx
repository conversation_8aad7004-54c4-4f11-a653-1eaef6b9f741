import React, { useEffect, useRef, useState } from 'react';
import { handleGetSlugEpsFromPath } from '@services/detailServices';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { getEpisodeBySlug, setNextEpisode } from '@actions/episode';
import { EL_ID, PAGE } from '@constants/constants';
import { createTimeout, openAppMobile } from '@helpers/common';
import { TEXT } from '@constants/text';
import { usePorTrait, useOnClickOutside, useVieRouter } from '@customHook';
import TrackingMWebToApp from '@tracking/functions/TrackingMWebToApp';
import { VALUE } from '@config/ConfigSegment';
import BannerTriggerPVod from '@components/home/<USER>';
import { fastTrackEpisodeSelected } from '@tracking/functions/TrackingPVod';
import EpisodeCard from '../../../../Card/EpisodeCard';
import FilterDropDown from '../../../../FilterDropDown';
const PlayerEpisodeListMobile = React.memo(({ onSetupEpisode, setOpenEpisode }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const [activeEpisode, setActiveEpisode] = useState<any>(null);
  const { porTrait } = usePorTrait();
  const currentEpisode = useSelector((state: any) => state?.Episode?.currentEpisode);

  const scrollTimerRef = useRef<any>(null);
  const getEpisodeTimerRef = useRef<any>(0);
  const countRef = useRef<any>(0);
  const clickEpisodeTimerRef = useRef<any>(null);
  const ref = useRef<any>(null);
  const scrollRef = useRef<any>(null);
  const episodeData = useSelector((state: any) => state?.Episode?.episodeData || null);
  const content = useSelector((state: any) => state?.Vod?.content || null);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const { appDownload, featureFlag } = useSelector((state: any) => state?.App?.webConfig || {});
  const relatedSeason = content?.relatedSeason || [];
  const [activeSeason, setActiveSeason] = useState<any>(null);
  const { slugFromPath }: any = handleGetSlugEpsFromPath({ path: activeSeason?.seoUrl });
  const episodeSlugData = episodeData?.[activeSeason?.slug || slugFromPath] || {};
  const episodeList = episodeSlugData?.items || [];
  const episode = activeEpisode || currentEpisode;
  useOnClickOutside(ref, () => setOpenEpisode && setOpenEpisode());

  useEffect(() => {
    if (!porTrait) {
      TrackingMWebToApp.tvseriesTrialEpisodesInfoboxListLandscapeLoad({
        flowName: VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_TRIAL_EPISODES
      });
    }
  }, [porTrait]);

  useEffect(() => {
    if (typeof onSetupEpisode === 'function') {
      onSetupEpisode();
    }
    return () => {
      if (getEpisodeTimerRef.current) clearInterval(getEpisodeTimerRef.current);
      clearTimeout(scrollTimerRef.current);
      clearTimeout(clickEpisodeTimerRef.current);
    };
  }, []);

  useEffect(() => {
    if (isEmpty(episodeList) || activeSeason?.id) {
      const { rangePageIndex } = currentEpisode || {};
      getEpisodeList({
        slug: slugFromPath,
        rangePageIndex
      });
    }
  }, [activeSeason?.id]);

  useEffect(() => {
    const activeSS = (content?.relatedSeason || []).find((it: any) => it?.id === content?.id);
    setActiveSeason(activeSS);
  }, [content?.id]);

  useEffect(() => {
    setNextEpisodeHandler();
  }, [currentEpisode?.id, episodeList]);

  const getEpisodeList = ({ slug, page, limit, rangePageIndex }: any) => {
    if (rangePageIndex > 0) {
      getEpisodeTimerRef.current = setInterval(() => {
        if (countRef.current > rangePageIndex) {
          clearInterval(getEpisodeTimerRef.current);
          return;
        }
        dispatch(
          getEpisodeBySlug({
            slug,
            page: countRef.current,
            limit: 30,
            isGlobal
          })
        );
        countRef.current += 1;
      }, 500);
    } else {
      dispatch(
        getEpisodeBySlug({
          slug,
          page,
          limit,
          isGlobal
        })
      );
    }
  };

  const setNextEpisodeHandler = () => {
    if (currentEpisode?.id && (episodeList || []).length > 0) {
      const currentIndex = (episodeList || []).findIndex(
        (eps: any) => eps?.id === currentEpisode?.id
      );
      if (currentIndex >= 0) {
        const nextEpisode = episodeList?.[currentIndex + 1];
        dispatch(setNextEpisode(nextEpisode));
      }
    }
  };

  const onClickItem = (episode: any) => {
    if (clickEpisodeTimerRef.current) clearTimeout(clickEpisodeTimerRef.current);
    // handle tracking pvod
    if (!isEmpty(content?.pvod)) {
      fastTrackEpisodeSelected({
        id: episode?.id,
        title: episode?.title
      });
    }
    setActiveEpisode(episode);
    if (episode?.id === activeEpisode?.id) {
      router.push(PAGE.VOD, episode?.seo?.url);
    } else {
      const index = (episodeList || []).findIndex((it: any) => it?.id === episode?.id);
      if (index > -1 && index === (episodeList || []).length - 1) {
        clickEpisodeTimerRef.current = createTimeout(() => {
          const scrollEl = scrollRef.current;
          if (scrollEl) scrollEl.scrollTop = scrollEl.scrollHeight;
        }, 100);
      }
    }
  };

  const changeSeason = (e: any, index: any, season: any) => {
    setActiveSeason(season);
    const seasonEpisodeData = episodeData?.[season?.slug];
    const { metadata, items } = seasonEpisodeData || {};

    const page = metadata?.page;
    const total = metadata?.total;
    if (!isEmpty(metadata)) {
      getEpisodeList({ slug: season?.slug });
    } else if (typeof page === 'number' && items?.length < total) {
      getEpisodeList({
        slug: season?.slug,
        page: page + 1
      });
    }
  };

  const onScroll = () => {
    if (scrollTimerRef.current) clearTimeout(scrollTimerRef.current);
    scrollTimerRef.current = createTimeout(() => {
      const slug = activeSeason?.slug || slugFromPath;
      const { metadata } = episodeSlugData || {};
      if (episodeList?.length < metadata?.total) {
        getEpisodeList({
          slug,
          page: (metadata?.page || 0) + 1
        });
      }
    }, 200);
  };

  const onOpenMwebToApp = () => {
    TrackingMWebToApp.tvseriesTrialEpisodesInfoboxListLandscapeTouch({
      flowName: VALUE.TRIGGER_BY_CONTENT_MOVIE_TVSERIES_TRIAL_EPISODES
    });

    if (scrollTimerRef.current) clearTimeout(scrollTimerRef.current);
    scrollTimerRef.current = createTimeout(() => {
      openAppMobile(appDownload);
    }, 200);
  };

  if (porTrait) return null;

  return (
    <div
      ref={ref}
      className="player-drawer animate-slide-in-right absolute right top layer-6 size-w-280 size-h-full scrollable-y over-scroll-contain bg-gray40 p-b"
    >
      <div className="relative">
        {featureFlag?.mwebToApp && (
          <div
            className="flex-box align-middle padding-small-up-12 bg-gray20"
            onClick={onOpenMwebToApp}
          >
            <div className="cell small-1 m-r2">
              <div className="icon icon--medium">
                <i className="vie vie-mobile-download text-gradient text-gradient-green" />
              </div>
            </div>
            <div className="cell auto">
              <div className="text text-14 text-white text-medium">{TEXT.WATCH_ON_VIEON_APP}</div>
              <div className="text text-14 text-gray117">{TEXT.CONTENT_WATCH_ON_VIEON_APP}</div>
            </div>
          </div>
        )}
        <FilterDropDown
          id="EPISODE_SEASON_LIST"
          className="filter filter--season filter--dark padding-x-small-up-16 padding-y-small-up-12 border-for-dark"
          buttonClass="button button--for-dark p-l relative"
          // iconClass="icon--tiny icon--absolute"
          itemIconSlotRight="icon--tiny icon--absolute"
          itemIconClass="vie-chevron-down-red-medium"
          changeFilter={changeSeason}
          filterName={activeSeason?.title || content?.seasonName}
          filterList={relatedSeason}
          textClass="break-line text-left"
          isPlayerEpisode
        />
        <div
          className="group vertical"
          onScroll={onScroll}
          ref={scrollRef}
          id={EL_ID.EPISODE_LIST_SCROLL}
        >
          {episodeList.map((item: any) => (
            <EpisodeCard
              key={item?.id}
              item={item}
              active={item?.id === episode?.id}
              currentEpisode={currentEpisode}
              onClickItem={onClickItem}
            />
          ))}
        </div>
        {!isEmpty(content?.pvod) && (
          <div className="sticky bottom-0 right-0">
            <BannerTriggerPVod className="mt-3" isEpisodeList data={content} />
          </div>
        )}
      </div>
    </div>
  );
});

export default PlayerEpisodeListMobile;
