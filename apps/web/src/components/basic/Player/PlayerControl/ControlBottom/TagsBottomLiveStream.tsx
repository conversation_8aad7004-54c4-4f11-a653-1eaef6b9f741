import React from 'react';
import { ON_OFF } from '@constants/constants';
import TagsLiveStream from './TagsLiveStream';

const TagsBottomLiveStream = React.memo(
  ({ isLive, totalCCU, showCCU, buttonLive, classPosition, dataEventDetails, isPremiere }: any) => {
    const isShow = classPosition?.isShow;
    const isCheck = showCCU === ON_OFF.ON || buttonLive === ON_OFF.ON;

    return (
      isShow &&
      isCheck && (
        <div className="player__controls-container flex-box align-bottom player__status-live-box absolute full-x">
          <TagsLiveStream
            dataEventDetails={dataEventDetails}
            classPosition={classPosition}
            isLive={isLive}
            totalCCU={totalCCU}
            showCCU={showCCU}
            buttonLive={buttonLive}
            isPremiere={isPremiere && isLive}
          />
        </div>
      )
    );
  }
);

export default TagsBottomLiveStream;
