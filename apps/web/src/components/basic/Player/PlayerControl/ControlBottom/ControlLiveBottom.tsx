import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { isIOS } from 'react-device-detect';
import Button from '@components/basic/Buttons/Button';
import EpgSchedule from '@components/liveTV/epgSchedule/EpgSchedule';
import Categories from '@components/liveTV/categories';
import { closeAllTabFullScreen } from '@actions/popup';
import { setActiveCategory } from '@actions/liveTV';
import { EL_ID, TAB_LISTS } from '@constants/constants';
import { createTimeout } from '@helpers/common';
import { useVieRouter } from '@customHook';
import classNames from 'classnames';
import styles from '@components/basic/Player/PlayerControl/ControlBottom/ControlBottom.module.scss';
import { TEXT } from '@constants/text';
import PlayButton from '../Quality/PlayButton/PlayButton';
import ControlPlay from '../ControlPlay/ControlPlay';
import ControlTopLive from '../livetv/ControlTopLive';
import SeekBar from '../Quality/SeekBar/SeekBar';
import VolumeBar from '../Quality/VolumeBar/VolumeBar';
import PlayerControl from '../PlayerControl';
import TriggerTrialDuration from '../../TriggerTrialDuration';
import { categorySelected } from '@tracking/functions/TrackingApp';

const ControlLiveBottom = (props: any) => {
  const {
    isFullscreen,
    isAdPlay,
    isPaused,
    isLoading,
    isMuted,
    isLiveStream,
    isOnEnded,
    playerReady,
    playerError,
    notSeekBar,
    noSeeker,
    currentTimeText,
    durationText,
    volumeValue,
    seekValue,
    onBackward,
    onForward,
    onClickVolume,
    onVoluming,
    onPlayPause,
    onLiveButton,
    onFullscreen,
    onControlSeeking,
    onControlSeekChange,
    onControlThumbnail,
    cancelPlayer,
    videoContainerHeight,
    onCheckInfoDebugPlayer,
    isPrevent,
    totalCCU,
    behindLive,
    isEndStream,
    trialDuration,
    currentProfile,
    triggerSource,
    isTriggerTrialLoginDuration,
    isTrialDurationDisplay,
    profile,
    currentEpisode
  } = props || {};
  const router = useVieRouter();
  const dispatch = useDispatch();
  const statusCloseAllTabs = useSelector((state: any) => state?.Popup?.dataCloseTabLivetv?.status);
  const { eventRelated } = useSelector((state: any) => state?.Livestream) || {};
  const { isMobile, isTablet } = useSelector((state: any) => state?.App || {});
  const { isGlobal } = useSelector((state: any) => state?.App?.geoCheck || {});
  const { activeEpg, detailChannel, activeCategoryFullscreen } = useSelector(
    (state: any) => state?.LiveTV || {}
  );

  const [showController, setShowController] = useState(true);
  const mouseMoveTimer = useRef<any>(null);
  const { epg } = router?.query || {};
  const { isLiveTv, isSeekAllow } = detailChannel || {};
  const { isLive, isComingSoon, isCatchUp } = activeEpg || {};
  const isCatchUpContent = useMemo(
    () => epg && isCatchUp && !isComingSoon,
    [epg, isCatchUp, isComingSoon]
  );

  const bottomClass = useMemo(() => {
    let classTemp = 'player__controls-bottom layer-5';
    if (isLoading && !playerReady) classTemp += ' hide';
    if (!showController) classTemp += ' hide';
    return classTemp;
  }, [isLoading, playerReady, showController]);
  const showBottomControl = useMemo(
    () => !isMobile && isFullscreen && showController,
    [showController, isFullscreen, isMobile]
  );

  useEffect(() => {
    const playerContainer = document.getElementById(EL_ID.PLAYER_CONTAINER);
    if (playerContainer) {
      playerContainer.addEventListener('mousemove', onMouseMove);
      if (isMobile) {
        playerContainer.addEventListener('touchstart', onMouseMove);
      }
    }
    handleFirstLoadController();

    return () => {
      clearTimeout(mouseMoveTimer.current);
      if (playerContainer) {
        playerContainer.removeEventListener('mousemove', onMouseMove);
        if (isMobile) {
          playerContainer.removeEventListener('touchstart', onMouseMove);
        }
      }
    };
  }, []);

  useEffect(() => {
    if (detailChannel?.id) {
      handleFirstLoadController();
    }
  }, [detailChannel?.id]);

  useEffect(() => {
    onOffAllTabs();
    if (!isFullscreen) {
      if (statusCloseAllTabs) dispatch(closeAllTabFullScreen({ off: false }));
    }
  }, [isFullscreen]);

  useEffect(() => {
    if (statusCloseAllTabs) {
      onOffAllTabs();
    }
  }, [statusCloseAllTabs]);

  useEffect(() => {
    if (isFullscreen && router?.asPath) {
      onOffAllTabs();
    }
  }, [router?.asPath, isFullscreen]);

  const handleFirstLoadController = () => {
    mouseMoveTimer.current = createTimeout(() => {
      setShowController(false);
      addClassUserActive(false);
    }, 4000);
  };

  const onSelectedCategoryInFullscreen = (data: any) => {
    if (!isEmpty(data)) {
      categorySelected({ data, isLiveTv: true });
      dispatch(setActiveCategory(data, true));
    }
  };

  const onOffAllTabs = () => {
    dispatch(setActiveCategory(null, true));
  };

  const onMouseMove = () => {
    if (isFullscreen && isEmpty(activeCategoryFullscreen)) {
      setShowController(false);
      addClassUserActive(false);
      return;
    }
    setShowController(true);
    addClassUserActive(true);
    clearTimeout(mouseMoveTimer.current);
    mouseMoveTimer.current = createTimeout(() => {
      setShowController(false);
      addClassUserActive(false);
    }, 3000);
  };

  const addClassUserActive = (isShowController: any) => {
    const playerContainer = document.getElementById(EL_ID.PLAYER_CONTAINER);
    if (playerContainer) {
      if (isShowController) {
        playerContainer.classList.remove('user-inactive');
        playerContainer.classList.add('user-active');
      } else {
        playerContainer.classList.remove('user-active');
        playerContainer.classList.add('user-inactive');
      }
    }
  };
  const renderListEpgInLiveTv = () => {
    if (isMobile || isTablet) return null;
    if (!isEmpty(activeCategoryFullscreen) && isFullscreen) {
      return (
        <div className="epg-mini layer-3 enable-pse animate-slide-in-right">
          <Categories />
          <Button
            className="close button absolute top-right-3 size-square-38"
            onClick={onOffAllTabs}
            iconName="vie-times-medium"
            customizeClass="!z-[99]"
          />
        </div>
      );
    }
    if (!isFullscreen) {
      return (
        <div className="epg-mini layer-3">
          <EpgSchedule
            videoContainerHeight={videoContainerHeight}
            onCloseTabsInFullscreen={onOffAllTabs}
          />
        </div>
      );
    }
    return null;
  };

  if (isLiveStream) {
    return (
      <PlayerControl
        {...props}
        isHideButtonPlayer={(isEndStream || props.statusLiveEvent === 2) && !isEmpty(eventRelated)}
        isMobile={isMobile}
      />
    );
  }

  return (
    <>
      {renderListEpgInLiveTv()}
      {/* handle control button */}
      {!isAdPlay && !isPrevent && (
        <div className="player__controls">
          <ControlPlay
            isPaused={isPaused}
            onPlayPause={onPlayPause}
            playerError={playerError}
            onControlFullscreen={onFullscreen}
            cancelPlayer={cancelPlayer}
            isLiveTv
          />

          {(isLiveStream ? triggerSource?.isLive === 1 : triggerSource?.id) &&
            !triggerSource.isPremium &&
            !profile?.id &&
            (isTrialDurationDisplay || isTriggerTrialLoginDuration) &&
            !isGlobal && (
              <div className="player__controls-highway absolute layer-8">
                <TriggerTrialDuration
                  trialDuration={trialDuration}
                  currentProfile={currentProfile}
                  showControllerForPlayer={showController}
                  content={triggerSource}
                  contentDetail={triggerSource}
                  isTrialDurationOverText={isTriggerTrialLoginDuration}
                  episodeData={currentEpisode}
                />
              </div>
            )}
          {showController && (
            <ControlTopLive
              isMobile={isMobile}
              timeTitle={activeEpg?.timeTitle}
              mainTitle={activeEpg?.title}
              totalCCU={totalCCU}
              isLive={isLive || isLiveTv}
              channelDetail={detailChannel}
              onCheckInfoDebugPlayer={onCheckInfoDebugPlayer}
            />
          )}
          <div className={bottomClass}>
            {showController && isSeekAllow && (!activeEpg?.id || (activeEpg?.id && isLive)) && (
              <div className="player__controls-container">
                <div
                  className=" cursor-pointer w-fit flex justify-start items-center overflow-hidden gap-1.5 p-2 rounded-sm bg-[#111]/50 border border-[#9b9b9b] mb-3"
                  onClick={onLiveButton}
                >
                  <div
                    className={classNames(
                      'flex-grow-0 flex-shrink-0 w-[22px] h-[22px] relative  rounded-full',
                      behindLive
                        ? 'bg-white'
                        : 'bg-gradient-to-r from-red-600 via-red-500 to-red-400'
                    )}
                  />
                  <p className="self-stretch flex-grow-0 flex-shrink-0 w-[33px] h-[22px] text-sm font-medium text-left text-white">
                    LIVE
                  </p>
                </div>
              </div>
            )}
            {showController && (isCatchUpContent || isSeekAllow) && (
              <SeekBar
                currentTimeText={currentTimeText}
                durationText={durationText}
                showController={showController}
                seekValue={seekValue}
                onControlSeeking={onControlSeeking}
                onControlSeekChange={onControlSeekChange}
                onControlThumbnail={onControlThumbnail}
                notSeekBar={notSeekBar}
                noSeeker={noSeeker}
                isFullscreen={isFullscreen}
                isOnEnded={isOnEnded}
              />
            )}
            <div className="player__controls-container play">
              {showController && (isCatchUpContent || isSeekAllow) && (
                <PlayButton
                  isPaused={isPaused}
                  playerError={playerError}
                  onPlayPause={onPlayPause}
                />
              )}
              {showController && (isCatchUpContent || isSeekAllow) && (
                <Button
                  className={styles.Button}
                  iconName="vie-present-ten-back-medium"
                  onClick={onBackward}
                  iconClass={styles.ButtonIconI}
                  subTitle={TEXT.BACK_FORWARD_TEN_S}
                />
              )}
              {((showController && (isCatchUpContent || isSeekAllow) && behindLive) ||
                (activeEpg?.id && !isLive)) && (
                <Button
                  className={styles.Button}
                  iconName="vie-present-ten-next-medium"
                  iconClass={styles.ButtonIconI}
                  onClick={onForward}
                  subTitle={TEXT.FAST_FORWARD_TEN_S}
                />
              )}
              {!isMobile && showController && (
                <VolumeBar
                  volumeValue={volumeValue}
                  onClickVolume={onClickVolume}
                  onVoluming={onVoluming}
                  isMuted={isMuted}
                />
              )}
              {showBottomControl &&
                !isIOS &&
                !isTablet &&
                TAB_LISTS.map((item: any) =>
                  isGlobal && item.id === 'DPS' ? null : (
                    <Button
                      key={item.id}
                      className={item.btnClass}
                      customizeClass="space-x-2 md:space-x-3.5 !text-white hover:!text-vo-green"
                      iconName={item.iconClass}
                      title={item.title}
                      onClick={() => onSelectedCategoryInFullscreen(item)}
                      textClass={styles.ButtonText}
                      iconClass={styles.ButtonIconI}
                    />
                  )
                )}
              {showController && (
                <Button
                  className={classNames(
                    'player__button player__button--screen shrink !text-white hover:!text-vo-green',
                    isFullscreen ? '' : '!ml-auto'
                  )}
                  iconName={isFullscreen ? 'vie-screen-off-medium' : 'vie-screen-on-medium'}
                  iconClass={styles.ButtonIconI}
                  onClick={onFullscreen}
                  subTitle={isFullscreen ? TEXT.EXIT_FULLSCREEN : TEXT.FULLSCREEN}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(ControlLiveBottom);
