import React, { useEffect, useMemo, useState, useRef } from 'react';
import { onOpenPayment } from '@helpers/common';
import { useDispatch, useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import {
  removeAdsSubscribePackageButtonSelected,
  qualitySubSubscribePackageButtonSelected,
  vipFeatureSubscribePackageLoaded
} from '@tracking/functions/TrackingPaymentConversion';
import { segmentRegistrationEventsPopupAuth } from '@tracking/functions/TrackingContentForceLoginPopupAuth';
import { VALUE } from '@config/ConfigSegment';
import { useVieRouter } from '@customHook';
import { POPUP } from '@constants/constants';
import { openPopup } from '@actions/popup';
import Button from '../../../Buttons/Button';
import Image from '../../../Image/Image';
import NotifyTriggerTrialDuration from './NotifyTriggerTrialDuration';
import { PERMISSION } from '@constants/constants';

const NotifyVipFeature = (props: any) => {
  const dispatch = useDispatch();
  const {
    showController,
    isAdsEnd,
    contentData,
    hasCalledTrackingVipFeature,
    currentTime,
    contentDetail,
    durationVideo,
    currentEpisode
  } = props || {};
  const router = useVieRouter();
  const [isEnableNotifyXMins, setEnableNotifyXMins] = useState(false);
  const timeoutRef = useRef<any>(null);
  const paymentConversion = useSelector((state: any) => state?.App?.paymentConversion);
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isMobile = useSelector((state: any) => state?.App?.isMobile);
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const userType = useSelector((state: any) => state?.User?.USER_TYPE);
  const { vipFeature, packageId } = paymentConversion || {};

  const { quality, audioSub, ads } = vipFeature || {};
  const buttonRegister = quality?.button || audioSub?.button || ads?.button;
  const playerData = useSelector((state: any) => state?.Player);
  const { isVipAudioSub, isVipQuality, isVipSub, titleVipAudio } = playerData;
  const isKid = useSelector((state: any) => state?.MultiProfile?.currentProfile?.isKid || false);

  const messageAudioVip = useMemo(
    () => audioSub?.message?.replace('{titleVipAudio}', titleVipAudio),
    [titleVipAudio]
  );
  const [messageVipFeature, setMessageVipFeature] = useState({
    image: isGlobal ? '' : quality?.image || ConfigImage.vipFeatureQuality,
    message: isGlobal ? '' : quality?.message
  });

  const [isEnable, setEnable] = useState({
    isCheckQuality: props?.ads?.length <= 0 && isVipQuality && !isGlobal,
    isCheckAudioSub:
      (isVipSub || isVipAudioSub) && !isVipQuality && (props?.ads?.length || 0) <= 0 && !isGlobal,
    isCheckAds: isAdsEnd
  });

  useEffect(() => {
    if (router?.pathname) {
      handleVipFeature();
    }
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [showController, router?.pathname]);

  useEffect(() => {
    const isShowBannerNotifyTriggerDuration =
      contentDetail?.triggerLoginDuration > 0 && contentDetail?.permission === PERMISSION.NON_LOGIN;
    if (!isEnable?.isCheckAds && isShowBannerNotifyTriggerDuration) {
      setEnableNotifyXMins(true);
      segmentRegistrationEventsPopupAuth({
        contentType: currentEpisode?.type || contentData?.type || contentDetail?.type,
        contentId: currentEpisode?.id || contentData?.id || contentDetail?.id,
        contentTitle: currentEpisode?.title || contentData?.title || contentDetail?.title,
        triggerFrom: VALUE.FORCE_LOGIN_INFOBOX,
        flowNameCustom: VALUE.REGISTRATION_TRIAL
      });
      return;
    }
  }, [contentDetail, isEnable]);

  useEffect(() => {
    if (!hasCalledTrackingVipFeature.current && !isEnable?.isCheckAds) {
      hasCalledTrackingVipFeature.current = true;
      return action();
    }
  }, [isEnable, hasCalledTrackingVipFeature]);

  const action = () => {
    const { message } = messageVipFeature || {};
    const triggerFrom =
      message === quality?.message
        ? VALUE.QUALITY_INFO_BOX
        : message === messageAudioVip
        ? VALUE.SUB_AUDIO_INFO_BOX
        : isEnable?.isCheckAds
        ? VALUE.REMOVE_ADS_INFO_BOX
        : '';
    return vipFeatureSubscribePackageLoaded({
      triggerFrom,
      userType: userType?.userType,
      contentId: contentData?.id,
      contentName: contentData?.title
    });
  };
  const onClickOpenPayment = () => {
    const { message } = messageVipFeature || {};
    localStorage.setItem('currentAuthFlow', 'payment_trigger');
    if (message === ads?.message) {
      removeAdsSubscribePackageButtonSelected({
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    } else {
      qualitySubSubscribePackageButtonSelected({
        triggerFrom:
          message === quality?.message
            ? VALUE.QUALITY_INFO_BOX
            : message === messageAudioVip
            ? VALUE.SUB_AUDIO_INFO_BOX
            : isEnable?.isCheckAds
            ? VALUE.REMOVE_ADS_INFO_BOX
            : '',
        userType: userType?.userType,
        contentId: contentData?.id,
        contentName: contentData?.title
      });
    }
    if (isKid) {
      dispatch(openPopup({ name: POPUP.NAME.KID_LIMITED_VIP_DIALOG }));
    }

    onOpenPayment(router, {
      returnUrl: window?.location?.href,
      pkg: isGlobal ? '' : packageId,
      referralCode:
        message === ads?.message
          ? 2
          : message === quality?.message || message === messageAudioVip?.message
          ? 3
          : 0,
      newTriggerPaymentBuyPackage: {
        isGlobal,
        profileId: profile?.id
      }
    });
  };

  const handleVipFeature = () => {
    const isShowBannerNotifyTriggerDuration =
      contentDetail?.triggerLoginDuration > 0 && contentDetail?.permission === PERMISSION.NON_LOGIN;
    if (!isEnable?.isCheckAds && isShowBannerNotifyTriggerDuration) {
      setEnableNotifyXMins(true);
      return;
    }

    if (isEnable?.isCheckAds) {
      setMessageVipFeature({
        image: ads?.image || ConfigImage.vipFeatureAds,
        message: ads?.message
      });

      if (isShowBannerNotifyTriggerDuration) {
        timeoutRef.current = setTimeout(() => {
          setEnableNotifyXMins(true);
          setMessageVipFeature({ image: '', message: '' });
        }, 4000);
        return;
      }

      if (isVipQuality) {
        setEnable({ ...isEnable, isCheckQuality: true, isCheckAds: false });
      } else if (isVipSub || isVipAudioSub) {
        setMessageVipFeature({
          image: audioSub?.image || ConfigImage.vipFeatureAudioSub,
          message: messageAudioVip
        });
        setEnable({ ...isEnable, isCheckAudioSub: true, isCheckQuality: false });
      }
    }

    if (isEnable.isCheckQuality) {
      if (isVipSub || isVipAudioSub) {
        setMessageVipFeature({
          image: quality?.image || ConfigImage.vipFeatureQuality,
          message: quality?.message
        });
        setEnable({
          ...isEnable,
          isCheckAudioSub: showController,
          isCheckQuality: !showController
        });
      } else {
        setMessageVipFeature({
          image: quality?.image || ConfigImage.vipFeatureQuality,
          message: quality?.message
        });
      }
    }

    if (isEnable.isCheckAudioSub) {
      if (isVipQuality) {
        setMessageVipFeature({
          image: audioSub?.image || ConfigImage.vipFeatureAudioSub,
          message: messageAudioVip
        });
        setEnable({
          ...isEnable,
          isCheckAudioSub: !showController,
          isCheckQuality: showController
        });
      } else {
        setMessageVipFeature({
          image: audioSub?.image || ConfigImage.vipFeatureAudioSub,
          message: messageAudioVip
        });
      }
    }
  };

  let className = 'player__controls-wrap';
  if (!showController && !isEnableNotifyXMins) className += ' hide';
  // if (isEnableNotifyXMins && !showController && !isGlobal) className += ' lg:top-10 top-6';
  if (isEnableNotifyXMins && !isGlobal) {
    return (
      <div className={className}>
        <NotifyTriggerTrialDuration
          currentTime={currentTime}
          contentDetail={contentDetail}
          content={contentData}
          durationVideo={durationVideo}
          currentEpisode={currentEpisode}
        />
      </div>
    );
  }
  if ((!messageVipFeature.image && !messageVipFeature.message) || isMobile) {
    return null;
  }

  return (
    <div className={className}>
      <div className="banner player__banner-appeal-vip radius-2 overflow">
        <div className="banner__inner flex-box">
          <div className="banner__body relative">
            <div className="mask absolute layer-1 intrinsic">
              <div className="mask__inner">
                <Image
                  className="mask__img"
                  src={messageVipFeature?.image}
                  alt="VieON package poster"
                  notWebp
                />
              </div>
            </div>
            <div className="banner__content relative layer-2">
              <div className="grid-x align-middle">
                <div className="cell auto">
                  <div className="text text-white text-small-up-12 text-medium-up-14 text-bold">
                    {messageVipFeature?.message}
                  </div>
                </div>
                <div className="cell shrink padding-medium-up-20">
                  <Button
                    className="button button--light hollow button--medium !text-white"
                    title={buttonRegister}
                    onClick={onClickOpenPayment}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default React.memo(NotifyVipFeature);
