import React, { useEffect, useRef } from 'react';
import Card from '@components/basic/Card/Card';
import isEmpty from 'lodash/isEmpty';
import { backFromPlayer } from '@functions/functions';
import { useRouter } from 'next/router';
import classNames from 'classnames';
import {
  trackingEndScreenSuggestionVodSelected,
  trackingEndScreenSuggestionVodShow
} from '@tracking/functions/TrackingEndScreen';

const EndScreenRibbon = ({ endScreenItems, isFullscreen, isEndScreenVod }: any) => {
  const router = useRouter();
  const refEl = useRef<any>(null);
  const ref = useRef([]);

  const onDetail = (data: any) => {
    backFromPlayer({ router, newContentId: data?.id || data?.cardData?.id, isIntro: true });
  };

  const onContentSelected = ({ cardData }: any) => {
    trackingEndScreenSuggestionVodSelected({ contentId: cardData?.id });
  };

  useEffect(() => {
    trackingEndScreenSuggestionVodShow();
  }, []);

  return (
    <>
      {!isEmpty(endScreenItems) && (
        <div ref={refEl} className="flex items-center space-x-3 lg:space-x-6 md:space-x-4 w-full">
          {endScreenItems?.slice(0, 5)?.map((item: any, index: any) => (
            <div
              className={classNames(
                'relative',
                isFullscreen || isEndScreenVod
                  ? '!w-[200px] 2xl:!w-[280px] 3xl:!w-[320px] min-[2560px]:!w-[480px]'
                  : '!w-[200px] min-[2560px]:!w-[300px]'
              )}
              key={item?.id}
            >
              <Card
                lengthEventRelated={endScreenItems?.length}
                cardData={item}
                ref={ref}
                index={index + 1}
                randomID={item?.randomID || `${item?.id}_${index}`}
                notLazy
                onContentSelected={onContentSelected}
                isEndScreenVod={isEndScreenVod}
                isFullscreen={isFullscreen}
                onBackToPlayer={onDetail}
              />
            </div>
          ))}
        </div>
      )}
    </>
  );
};
export default EndScreenRibbon;
