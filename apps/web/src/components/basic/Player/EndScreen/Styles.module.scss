.collapse {
  @apply w-full cursor-pointer ml-auto flex items-center justify-end h-full rounded-xl z-50;
  @apply transition-all duration-300 delay-150 overflow-hidden absolute right-0;
  background-color: #111 !important;
  .button {
    @apply w-10 min-[2560px]:w-20 h-full text-white flex justify-center items-center;
    background-color: #333 !important;
  }
  .box {
    @apply flex items-center w-full h-full lg:pl-6 pl-4 py-3 overflow-hidden;
    .thumb {
      @apply w-[41%] relative rounded-lg h-[85%] flex items-center mt-auto;
      box-shadow: 0px -23.226px 5.806px 0px rgba(0, 0, 0, 0),
        0px -15.484px 5.806px 0px rgba(0, 0, 0, 0.01), 0px -7.742px 5.806px 0px rgba(0, 0, 0, 0.05),
        0px -3.871px 3.871px 0px rgba(0, 0, 0, 0.09), 0px 0px 1.935px 0px rgba(0, 0, 0, 0.1);
      img {
        @apply w-full h-full bg-black rounded-lg object-cover z-[3];
      }
      &::after {
        @apply content-[''] w-[90%] absolute -top-2 left-1/2 -translate-x-1/2 h-full bg-[#D9D9D9] rounded-lg z-[2];
        box-shadow: 0px -19.355px 4.839px 0px rgba(0, 0, 0, 0),
          0px -12.903px 4.839px 0px rgba(0, 0, 0, 0.01),
          0px -6.452px 4.839px 0px rgba(0, 0, 0, 0.05), 0px -3.226px 3.226px 0px rgba(0, 0, 0, 0.09),
          0px 0px 1.613px 0px rgba(0, 0, 0, 0.1);
      }
      &::before {
        @apply content-[''] absolute -top-4 left-1/2 -translate-x-1/2 w-[80%] bg-[#D9D9D9] rounded-lg h-full z-[1];
        box-shadow: 0px -23.226px 5.806px 0px rgba(0, 0, 0, 0),
          0px -15.484px 5.806px 0px rgba(0, 0, 0, 0.01),
          0px -7.742px 5.806px 0px rgba(0, 0, 0, 0.05), 0px -3.871px 3.871px 0px rgba(0, 0, 0, 0.09),
          0px 0px 1.935px 0px rgba(0, 0, 0, 0.1);
      }
    }
    .info {
      @apply w-[59%] p-2 lg:ml-5 ml-4;
      .title {
        @apply text-white text-[20px] min-[2560px]:text-[36px];
      }
      .description {
        @apply text-[rgba(255,255,255,0.6)] min-[2560px]:text-[20px] pt-1.5;
      }
    }
  }
}
