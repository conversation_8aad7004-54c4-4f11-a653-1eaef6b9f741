import React, { useEffect, useState } from 'react';

const CountdownButton = ({ time, buttonName, className, style, forceDisabled, onResend }: any) => {
  const [currentWait, setCurrentWait] = useState(time);
  let initCall: any = null;

  useEffect(() => {
    showTimeResendOTP();
    return () => clearInterval(initCall);
  }, []);

  const showTimeResendOTP = () => {
    if (initCall) clearInterval(initCall);

    initCall = setInterval(() => {
      setCurrentWait((prevWait: any) => {
        const updatedWait = prevWait - 1;
        if (updatedWait >= 0) {
          return updatedWait;
        }
        clearInterval(initCall);
        return prevWait;
      });
    }, 1000);
  };

  const clickResend = () => {
    if (currentWait > 0) return;
    setCurrentWait(time);
    showTimeResendOTP();
    if (onResend) onResend();
  };

  const disabled = !(currentWait === 0 && !forceDisabled);

  return (
    <button
      className={className || 'button button--tertiary'}
      type="button"
      disabled={disabled}
      onClick={clickResend}
      style={style}
    >
      {buttonName + (currentWait > 0 && !forceDisabled ? ` (${currentWait}s)` : '')}
    </button>
  );
};

export default CountdownButton;
