import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import SwiperCore, { Pagination } from 'swiper';
import { CONTENT_TYPE, RIBBON_TYPE } from '@constants/constants';
import { createTimeout } from '@helpers/common';
import classNames from 'classnames';
import { NextArrow, PrevArrow } from '../Buttons/ButtonArrow';

SwiperCore.use([Pagination]);

interface SliderState {
  swiper: any;
  next: boolean;
  prev: boolean;
}

interface SliderProps {
  id?: string;
  itemCount?: number;
  dataViewItem?: number;
  setupSlider?: (params: any) => void;
  notLoop?: boolean;
  data?: any[];
  isMobile?: boolean;
  isTablet?: boolean;
  metadata?: { total: number };
  loadMoreItems?: (params: any) => void;
  type?: any;
  onClickNextEpisode?: (params: any) => void;
  onClickPrevEpisode?: (params: any) => void;
  ribbonData?: { type: number };
  renderSliderItem: (item: any, index: number) => React.ReactNode;
  className?: string;
  itemClass?: string;
  setting?: any;
}

export class Slider extends React.PureComponent<SliderProps, SliderState> {
  currentSlideNext: any;
  delayUpdate: any;
  limitItemLoadmore: any;
  loadMoreTimer: any;
  swiperEl: any;
  constructor(props: any) {
    super(props);
    this.state = {
      swiper: null,
      next: true,
      prev: false
    };
    this.limitItemLoadmore = 40;
    this.swiperEl = null;
    this.currentSlideNext = 0;
    this.delayUpdate = null;
    this.loadMoreTimer = 0;
  }

  componentDidMount() {
    this.setupSlider();
  }

  componentDidUpdate(prevProps: any) {
    const { id } = this.props || {};
    if (id !== prevProps?.id) {
      this.setupSlider();
    }
  }

  componentWillUnmount() {
    if (this.delayUpdate) clearTimeout(this.delayUpdate);
    if (this.loadMoreTimer) clearTimeout(this.loadMoreTimer);
  }

  setupSlider = () => {
    const { itemCount, dataViewItem, setupSlider }: any = this.props || {};
    this.setState({
      swiper: this.swiperEl,
      next: itemCount > (dataViewItem || 6)
    });

    if (this.swiperEl) {
      this.delayUpdate = createTimeout(() => {
        this.swiperEl.update();
      }, 1000);
    }
    if (setupSlider) {
      setupSlider({
        slider: this.swiperEl,
        setupSlider: this.resetSlider,
        forceUpdateNavigation: this.forceUpdateNavigation
      });
    }
  };

  handleDefaultLoopItems = () => {
    const { itemCount, notLoop, data, isMobile }: any = this.props || {};
    if (isMobile) return;
    const maskItem = (data || []).find((el: any) => el.id === CONTENT_TYPE.MASK_ID);
    if (itemCount >= 10 && itemCount <= this.limitItemLoadmore && !maskItem && !notLoop) {
      const dataMask = {
        allowClick: 1,
        groupId: CONTENT_TYPE.MASK_ID,
        id: CONTENT_TYPE.MASK_ID,
        ribbonType: 1,
        type: 203
      };
      data.push(dataMask);
    }
  };

  handleRemoveDefaultLoopItems = () => {
    const { isMobile, data }: any = this.props || {};
    if (isMobile) return;
    const maskItem = (data || []).find((el: any) => el.id === CONTENT_TYPE.MASK_ID);
    if (maskItem) {
      for (let i = 0; i < data.length; i += 1) {
        if (data[i].id === CONTENT_TYPE.MASK_ID) {
          data.splice(i, 1);
          break;
        }
      }
    }
  };

  resetSlider = () => {
    const { itemCount, dataViewItem }: any = this.props || {};
    this.setState({
      swiper: this.swiperEl,
      next: itemCount > (dataViewItem || 6)
    });
  };

  forceUpdateNavigation = ({ pre }: any) => {
    this.setState({
      prev: pre
    });
  };

  onHandleLoadMoreItems = ({ activeIndex, defaultItemsView }: any) => {
    const { metadata, loadMoreItems, id, type } = this.props || {};
    const defaultItems = defaultItemsView || 6;
    const activePage = Math.floor(activeIndex / defaultItems);

    let { total } = metadata || {};
    if (total === 0) total = 40; // handle khi data nhận được là 0
    if (total) {
      const totalPage = Math.floor(+total / (defaultItemsView || 12));
      if (activePage <= totalPage && totalPage !== 0) {
        const maxDataLimit = total < 60 ? total : 60;
        if (this.loadMoreTimer) clearTimeout(this.loadMoreTimer);
        this.loadMoreTimer = createTimeout(() => {
          if (typeof loadMoreItems === 'function') {
            loadMoreItems({
              id,
              page: 0, // activePage
              type,
              limit: maxDataLimit
            });
          }
        }, 400);
      }
    }
  };

  onNext = () => {
    const swiper: any = this.state?.swiper || {};
    if (!swiper) return;
    const { itemCount, dataViewItem, data, isMobile }: any = this.props || {};
    const activeIndex = swiper?.activeIndex;
    const isEndData = activeIndex + (dataViewItem || 6) * 2 >= itemCount;
    const isEndDataProps = +(dataViewItem || 6) * 2 >= data?.length;
    let isEnd = swiper?.isEnd || isEndData || isEndDataProps;

    // const totalBullet = Math.ceil(this.props?.data?.length/6)
    this.currentSlideNext += 1;
    this.handleRemoveDefaultLoopItems();

    if (data?.length < this.limitItemLoadmore && !isMobile) {
      if (data?.length === 12) isEnd = false;
      this.onHandleLoadMoreItems({ activeIndex });
    }

    if (isEnd) {
      this.swiperEl.allowSlidePrev = true;
    }
    swiper.slideNext();
    this.setState({
      next: !isEnd,
      prev: true
    });
  };

  onPrev = () => {
    const swiper: any = this.state?.swiper || {};
    if (!swiper) return;
    const { notLoop, dataViewItem }: any = this.props || {};

    const activeIndex = swiper?.activeIndex;
    // const totalBullet  =  Math.ceil(this.props?.data?.length/6)
    const isBeginning = swiper?.isBeginning || +activeIndex - (dataViewItem || 6) === 0;
    if (isBeginning) {
      if (!notLoop) swiper.slideToLoop(-1);
    }
    swiper.slidePrev();
    this.setState({
      next: true,
      prev: !isBeginning
    });
  };

  onSlidePrevTransitionEnd = (e: any) => {
    const swiper: any = this.state?.swiper || {};
    const { onClickPrevEpisode }: any = this.props || {};
    const isBeginning = swiper?.isBeginning;
    this.setState({
      next: true,
      prev: !isBeginning
    });

    const activeIndex = e?.activeIndex;
    if (typeof onClickPrevEpisode === 'function') onClickPrevEpisode({ activeIndex });
  };

  onSlideNextTransitionEnd = (e: any) => {
    const swiper: any = this.state?.swiper || {};
    const { onClickNextEpisode }: any = this.props || {};
    this.setState({ next: !swiper?.isEnd, prev: true });

    const activeIndex = e?.activeIndex;
    if (typeof onClickNextEpisode === 'function') onClickNextEpisode({ activeIndex });
  };

  onSlideChange = () => {
    const swiper: any = this.state?.swiper || {};
    const { isMobile, ribbonData, data }: any = this.props || {};
    // handle slide change
    if (isMobile) {
      const activeIndex = swiper?.activeIndex;
      const isRanking = ribbonData?.type === RIBBON_TYPE.TOP_VIEWS;
      this.handleRemoveDefaultLoopItems();
      if (data?.length < this.limitItemLoadmore && !isRanking) {
        this.onHandleLoadMoreItems({ activeIndex, defaultItemsView: 4 });
      }
    }
  };

  getSwiperBreakPointParams = () => {
    const { isMobile, isTablet, dataViewItem }: any = this.props || {};
    if (isMobile) {
      if (isTablet) {
        return {
          slidesPerView: 4,
          dataViewItem: 4
        };
      }
      return {
        slidesPerView: 2,
        dataViewItem: 2
      };
    }
    return {
      slidesPerView: 6,
      dataViewItem
    };
  };

  render() {
    const { renderSliderItem, data, className, id, itemClass, isMobile }: any = this.props || {};
    const { slidesPerView, dataViewItem } = this.getSwiperBreakPointParams();
    let nextClass = '';
    let prevClass = '';
    if (!this.state.next) nextClass = ' swiper-button-disabled';
    if (!this.state.prev) prevClass = ' swiper-button-disabled';
    return (
      <Swiper
        className={classNames(className && className, 'slider relative')}
        onSlidePrevTransitionEnd={this.onSlidePrevTransitionEnd}
        onSlideNextTransitionEnd={this.onSlideNextTransitionEnd}
        onSlideChange={this.onSlideChange}
        allowTouchMove={!!isMobile}
        spaceBetween={8}
        slidesPerView={dataViewItem || slidesPerView || 6}
        slidesPerGroup={dataViewItem || slidesPerView || 6}
        onSwiper={(swiper) => (this.swiperEl = swiper)}
        data-item-view={dataViewItem || slidesPerView || 6}
        id={id}
        // loop={!!!notLoop && data?.length >= 10 && !this.props.isMobile }
        // pagination={{  // custom bullet pagination in progress
        //   clickable: true,
        //   type: 'custom',
        //   renderCustom: (swiper, current, total) => this.onRenderCustomPagination({ swiper, current, total })
        // }}
      >
        {(data || []).map((item: any, index: any) => (
          <SwiperSlide key={index} className={itemClass || 'slider__item'}>
            {renderSliderItem(item, index)}
          </SwiperSlide>
        ))}

        {!isMobile && <NextArrow onClickNext={this.onNext} nextClass={nextClass} />}
        {!isMobile && <PrevArrow onClickPrev={this.onPrev} prevClass={prevClass} />}
      </Swiper>
    );
  }
}

export default Slider;
