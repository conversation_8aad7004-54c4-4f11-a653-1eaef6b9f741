import React, { useRef } from 'react';
import { ID } from '@constants/constants';
import Card from '../Card/Card';
import CardList from '../Card/CardList';
import SeoText from '../../seo/SeoText';

const ContentList = (props: any) => {
  const ref = useRef([]);
  const { listDataAllPage, subHeader, title, onScrollDown, seoData } = props || {};
  const renderCardList = () => {
    return (
      <>
        {(listDataAllPage?.items || []).map((item: any, i: any) => {
          if (!item?.id || item?.id === ID.VIEW_MORE) return null;
          return (
            <Card
              key={item?.id + i || i}
              cardData={item}
              ref={ref}
              index={i + 1}
              notLazy
              randomID={`${item?.id}_${i}`}
            />
          );
        })}
      </>
    );
  };

  let headerStyle = {};
  if (subHeader) {
    headerStyle = {
      position: 'absolute',
      zIndex: -1000,
      color: 'transparent',
      clip: 'rect(0 0 0 0)'
    };
  }
  return (
    listDataAllPage && (
      <>
        <div className="section__header" style={headerStyle}>
          {!seoData?.title && <h1 className="section__title">{title || ''}</h1>}
          <SeoText seo={seoData} classH1="section__title" />
        </div>
        <div className="section__body">
          <CardList
            renderContent={renderCardList}
            heightCheckScroll={320}
            onScrollDown={onScrollDown}
          />
        </div>
      </>
    )
  );
};

export default ContentList;
