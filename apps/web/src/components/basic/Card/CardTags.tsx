import React, { useMemo } from 'react';
import { TEXT } from '@constants/text';
import {
  CONTENT_TYPE,
  EL_PROPERTY,
  EL_ROUNDED_CLASS,
  EL_SIZE_CLASS,
  EL_THEME_CLASS,
  RIBBON_TYPE,
  TAG_KEY,
  TAG_VIP,
  TAG_VIP_LABEL,
  TVOD
} from '@constants/constants';
import { numberWithCommas } from '@helpers/common';
import { useSelector } from 'react-redux';
import { setStartTextTag } from '@services/datetimeServices';
import Tags from '@components/basic/Tags/Tags';
import ConfigImage from '@config/ConfigImage';
import Styles from './Card.module.scss';
import classNames from 'classnames';
import { isMobileOnly } from 'react-device-detect';

const CardTags = React.memo((props: any) => {
  const { cardData, isEventRelated, ribbonType, hideMonopolyTag } = props || {};
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);

  const { labelPublicDay } = cardData;
  const {
    isPremium,
    isNew,
    ranking,
    isLive,
    isTopView,
    isPremiumDisplay,
    hasPVOD,
    type,
    isLiveStream,
    isPremiere,
    tvod,
    isEpisode,
    startTime,
    // isGlobal,
    isOriginal,
    isPremiumTVod,
    isSvodTvod,
    tagDisPlay
  } = cardData || {};
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const isRanking = useMemo(() => ranking > 0 && ranking <= 10, [ranking]);
  const startTextTag = useMemo(() => setStartTextTag(startTime), [startTime]);
  const isDisplayTagFree = useMemo(
    () =>
      isGlobal &&
      (!isLive || !isPremiere) &&
      isEpisode &&
      !isPremium &&
      !profile?.isPremium &&
      isPremiumDisplay === TEXT.FREE.toUpperCase(),
    [isLive, isPremiere, isPremium, isPremiumDisplay, profile?.isPremium, isGlobal, isEpisode]
  );

  const isPremiereOrLiveTag = (isLive || (isPremiere && isLive)) && isLiveStream && !startTextTag;

  // tagDisPlay is array, i wanna find if it has TAG_KEY.PARALLEL

  const isParallelTag = useMemo(() => {
    if (!tagDisPlay) return false;
    const isParallel = tagDisPlay?.find((tag: any) => {
      if (tag.toUpperCase().includes(TEXT.PARALLEL.toUpperCase())) return tag;
    });
    if (isParallel) {
      return isParallel;
    }
    return null;
  }, [tagDisPlay]);

  const isMonopolyTag = useMemo(() => {
    if (!tagDisPlay) return false;
    const isParallel = tagDisPlay?.find((tag: any) => {
      if (tag.toUpperCase().includes(TEXT.MONOPOLY.toUpperCase())) return tag;
    });
    if (isParallel) {
      return isParallel;
    }
    return null;
  }, [tagDisPlay]);

  const shouldDisplayGroupTagTopLeft = () => {
    const conditionShowTag = isPremiumTVod && tvod?.price && !isEpisode;
    return (conditionShowTag || isSvodTvod) &&
      (tvod?.benefitType === TVOD.USER_TYPE.NONE ||
        tvod?.benefitType === TVOD.USER_TYPE.EXPIRED) ? (
      <Tags
        content={cardData}
        tagKey={TAG_KEY.PRICE}
        price={numberWithCommas(
          tvod?.preOrder?.isPreOrdering ? tvod?.preOrder?.price : tvod?.price
        )}
        rounded={!isPremiumDisplay.includes('VIP') ? EL_ROUNDED_CLASS.BOTTOM_RIGHT : ''}
        size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
      />
    ) : null;
  };

  return (
    <>
      <div
        className={classNames(
          cardData?.isSvodTvod ? Styles.tagGroup1 : Styles.tagGroup2,
          cardData?.isSvodTvod &&
            isMobileOnly &&
            isRanking &&
            'mobile-375:!max-w-[100px] min-[376px]:!max-w-none overflow-hidden'
        )}
      >
        {isParallelTag ? (
          <Tags
            content={isParallelTag}
            tagKey={TAG_KEY.PARALLEL}
            rounded={EL_ROUNDED_CLASS.BOTTOM_RIGHT_SM}
            size={EL_SIZE_CLASS.MEDIUM}
            subClass="w-max"
          />
        ) : (
          <>
            {shouldDisplayGroupTagTopLeft()}
            {isPremium && isPremiumDisplay && (
              <Tags
                content={cardData}
                isPremiumDisplay={isPremiumDisplay}
                rounded={!isPremiereOrLiveTag ? EL_ROUNDED_CLASS.BOTTOM_RIGHT : ''}
                size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
              />
            )}
          </>
        )}
        {isPremiereOrLiveTag && (
          <Tags
            tagKey={TAG_KEY.LIVE}
            title={isPremiere ? TAG_KEY.PREMIERE : ''}
            rounded={EL_ROUNDED_CLASS.BOTTOM_RIGHT}
            size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
            subClass="!w-max"
          />
        )}
      </div>
      {isDisplayTagFree && (
        <Tags
          theme={EL_THEME_CLASS.GREEN}
          size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
          title={TEXT.FREE}
          position={EL_PROPERTY.TOP_LEFT}
          rounded={EL_ROUNDED_CLASS.BOTTOM_RIGHT}
        />
      )}
      {isNew && !isRanking && !labelPublicDay && !isTopView && !isEventRelated && (
        <Tags
          position={EL_PROPERTY.TOP_RIGHT}
          tagKey={TAG_KEY.NEW}
          size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
          rounded={EL_ROUNDED_CLASS.BOTTOM_LEFT_SM}
        />
      )}
      {isRanking && !isTopView && !labelPublicDay && (
        <Tags
          position={EL_PROPERTY.TOP_RIGHT}
          tagKey={TAG_KEY.TOP_VIEW}
          size={isOriginal ? EL_SIZE_CLASS.LARGE : EL_SIZE_CLASS.MEDIUM}
        />
      )}
      {!(cardData?.categoryTracking?.id === 'DPS') && !hideMonopolyTag && isMonopolyTag && (
        <div className={Styles.tagGroup3}>
          <Tags
            title={isMonopolyTag}
            size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
            theme={EL_THEME_CLASS.GREEN}
            rounded={EL_ROUNDED_CLASS.TOP_SM}
          />
        </div>
      )}
      {!(cardData?.categoryTracking?.id === 'DPS') &&
        !isMonopolyTag &&
        ribbonType !== RIBBON_TYPE.EPG && (
          <div className={Styles.tagGroup3}>
            {hasPVOD && type === CONTENT_TYPE.SEASON && !isEventRelated && (
              <Tags
                title={TAG_VIP_LABEL[TAG_VIP.PVOD]}
                theme={EL_THEME_CLASS.GREEN}
                size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
              />
            )}
            {!isLive && startTextTag && isLiveStream && (
              <Tags
                title={startTextTag}
                iImage={ConfigImage.clock}
                theme={EL_THEME_CLASS.GREEN}
                size={isOriginal ? EL_SIZE_CLASS.VARIANT_LARGE : EL_SIZE_CLASS.VARIANT_MEDIUM}
              />
            )}
          </div>
        )}
    </>
  );
});
export default CardTags;
