// Modules
@use 'sass:math';
/* =============== function calculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

.tagTopView {
  label {
    font-size: rem(10) !important;
  }
}

@media only screen and (min-width: 1024px) {
  .tagTopView {
    label {
      font-size: rem(8) !important;
    }
  }
}

@media only screen and (min-width: 1200px) {
  .tagTopView {
    label {
      font-size: rem(10) !important;
    }
  }
}

.tagGroup1 {
  @apply flex flex-row items-center w-max absolute top-0 left-0 z-10;
}

.tagGroup2 {
  @apply flex flex-col space-y-1 w-max max-w-[calc(100%-36px)] absolute top-0 left-0 z-10;
}

.tagGroup3 {
  @apply flex w-max absolute bottom-0 left-1/2 -translate-x-1/2 z-10 rounded-t overflow-hidden;

  &ForSection {
    @apply flex w-max rounded-t overflow-hidden mx-auto;
  }
}

.channelBrand {
  @apply w-[26px] h-[26px] lg:w-11 lg:h-11 pt-[3px] pr-[4px] pb-[3px] pl-[2px] md:pt-[4px] md:pr-[6px] md:pb-[4px] md:pl-[2px];
  @apply absolute top-[4px] right-[4px] z-10;

  &Backdrop {
    @apply absolute w-full h-full top-0 left-0;
  }

  &C {
    @apply w-full h-full relative p-0.5;
    @apply overflow-hidden bg-white rounded-full;

    & > img {
      @apply absolute w-[calc(100%-.25rem)] h-auto max-h-[calc(100%-.25rem)] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
    }
  }
}

.Progress {
  @apply absolute h-[.1875rem] w-[calc(100%-1.5rem)] top-[calc(100%+3px)] left-3 bg-white/30;

  &Meter {
    @apply relative block w-0 h-full bg-[#3adb76] rounded-[.125rem];
  }
}
