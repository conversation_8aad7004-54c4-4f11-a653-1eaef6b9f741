import React from 'react';

const ImageMask = ({ image, imageMobile, className }: any) => (
  <div className="mask__inner">
    <picture className={className}>
      <source media="(min-width: 1400px)" srcSet={image} />
      <source media="(min-width: 1024px)" srcSet={image} />
      <source media="(min-width: 320px)" srcSet={imageMobile} />
      <img className="mask__img" srcSet={image} alt="Combo" />
    </picture>
  </div>
);
export default ImageMask;
