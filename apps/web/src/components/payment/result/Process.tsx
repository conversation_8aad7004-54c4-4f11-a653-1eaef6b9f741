import React from 'react';
import { useSelector } from 'react-redux';
import ConfigImage from '@config/ConfigImage';
import LocalStorage from '@config/LocalStorage';
import { TEXT } from '@constants/text';
import { PAGE, VieON_TEL } from '@constants/constants';
import { encodeParamDestination } from '@helpers/common';
import ConfigLocalStorage from '@config/ConfigLocalStorage';
import CloseWebInApp from './CloseWebInApp';

declare const window: any;

const Process = ({ orderId, router, name, tel, inAppZalo, supportSmartTv, txnRef }: any) => {
  const currentProfile = useSelector((state: any) => state?.MultiProfile?.currentProfile || {});

  const returnUrl = ConfigLocalStorage.get(LocalStorage.FROM_URL);

  const handleClickContinueWatching = () => {
    if (!currentProfile?.id) {
      const remakeDestination = encodeParamDestination(returnUrl);
      router.push(`${PAGE.LOBBY_PROFILES}/?destination=${remakeDestination}`);
    } else {
      window.location = returnUrl || '/';
    }
  };

  return (
    <div className="block block--result block--result-pm block--result-pm-process">
      <div className="mask">
        <div className="mask__inner ratio-variant-result">
          <div className="mask__img absolute">
            <img src={ConfigImage.catProcessing} alt={TEXT.PROCESSING_TRANSACTION_TITLE} />
          </div>
        </div>
      </div>
      <div className="block__header">
        <h2 className="block__title text-center">{TEXT.PROCESSING_TRANSACTION_TITLE}</h2>
      </div>

      <div className="block__body">
        <div className="list-group">
          <div className="list-group__item divide--dashed-non-last">
            {(orderId || txnRef) && (
              <div className="text-center">
                <span className="text normal">{TEXT.TRANSACTION_CODE}: </span>
                <span className="text-f-m normal">{`#${txnRef || orderId || ''}`}</span>
              </div>
            )}
            <div className="text-center">
              Kết quả giao dịch sẽ được cập nhật đến bạn trong giây lát (tối đa 24 giờ làm việc){' '}
              <br />
              Truy cập mục Tài Khoản/Gói Dịch vụ/Lịch sử giao dịch để biết thêm chi tiết.
            </div>
          </div>
          <div className="list-group__item divide--dashed-non-last">
            <div className="text-center">
              Vui lòng liên hệ hotline VieON (miễn phí){' '}
              <a className="highlight" href={`tel:${VieON_TEL}`} title={VieON_TEL}>
                {VieON_TEL}
              </a>{' '}
              hoặc hotline {name}{' '}
              <a className="highlight" href={`tel:${tel}`} title={tel}>
                {tel}
              </a>{' '}
              để được hỗ trợ
            </div>
          </div>
        </div>
        {!inAppZalo && !supportSmartTv && (
          <div className="button-group child-shrink align-center">
            <a
              className="highlight underline"
              onClick={handleClickContinueWatching}
              title={TEXT.CONTINUE_WATCH_VieON}
            >
              <span className="!text-[1rem]">{TEXT.CONTINUE_WATCH_VieON}</span>
            </a>
          </div>
        )}
      </div>
      <CloseWebInApp inApp={inAppZalo} />
    </div>
  );
};

export default Process;
