import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import TrackingPayment from '@tracking/functions/payment';
import { UtmParams } from '@models/subModels';
import { selectPackage } from '@actions/payment';
import classNames from 'classnames';
import Promotion from '@components/payment/Step1/package-item/Promotion';
import { ICON_KEY, PAGE, PAYMENT_METHOD } from '@constants/constants';
import { TYPE_TRIGGER_AUTH } from '@constants/types';
import TagsMostBuyers from '@components/basic/Tags/TagsMostBuyers';
import Image from '@/components/basic/Image/Image';
import Button from '@/components/basic/Buttons/Button';
import { TEXT } from '@/constants/text';
import styles from '../Style.module.scss';
import Info from './Info';
import SvgIcon from '@components/basic/Icon/SvgIcon';

const trackingPayment = new TrackingPayment();

const Package = ({ data, defaultPackage, hoveredPackageId, onHover, packageConfig }: any) => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { pathname, query } = router || {};
  const { benefits, packages } = useSelector((state: any) => state?.Payment) || {};
  const { profile } = useSelector((state: any) => state?.Profile || {});
  const { isMobile } = useSelector((state: any) => state?.App || {});
  const inAppFromPath = pathname.includes('in-app') ? PAYMENT_METHOD.ZALO_PAY : '';
  const isInAppZalo = useMemo(() => (pathname || '').includes(PAGE.ZALOPAY), [pathname]);

  const styleMap: any = {
    vip: 'Green',
    hbo: 'Blue',
    sport: 'Red',
    all_access: 'Yellow'
  };

  const getStyleByPackageGroup = (key: any) => styleMap[key] || 'Green';
  const setColorByData = getStyleByPackageGroup(data?.textId);
  const getBackDropBalloonClass = (styles: any, isBalloon?: any) => {
    const baseClass = `${styles.packageBackDropBalloon} ${
      styles[`packageBackDrop${setColorByData}ConicGradient`]
    }`;
    if (isBalloon) {
      return [
        baseClass,
        styles.packageBackDropBalloon1,
        styles.packageBackDropBalloon2,
        styles.packageBackDropBalloon3
      ];
    }
    return styles[`packageBackDrop${setColorByData}`];
  };

  const promotionData = useMemo(
    () =>
      inAppFromPath
        ? packageConfig?.promotionProgram?.[inAppFromPath]
        : packageConfig?.promotionProgram || [],
    [inAppFromPath, packageConfig]
  );

  const handleSelectPackageAndNavigate = () => {
    dispatch(selectPackage(data));
    trackingPayment.selectPackage({ selectedPackage: data });

    if (data?.id) {
      trackingPayment.paymentPageLoaded();
      const urmParams = UtmParams(query);

      const targetPath = isInAppZalo ? PAGE.ZALOPAY_METHOD : PAGE.PAYMENT_METHOD;

      const navigateToPayment = () => {
        router.push({
          pathname: targetPath,
          query: { ...urmParams, pkg: data?.id }
        });
      };

      if (profile?.id) {
        navigateToPayment();
      } else {
        if (isInAppZalo) {
          router.push(
            `${PAGE.AUTH}/?destination=${`${PAGE.ZALOPAY_METHOD}/?pkg=${data?.id}`}&trigger=${
              TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE
            }`
          );
        } else {
          localStorage.setItem('currentAuthFlow', 'register_for_payment');
          router.push(
            `${PAGE.AUTH}/?destination=${`${PAGE.PAYMENT_METHOD}/?pkg=${data?.id}`}&trigger=${
              TYPE_TRIGGER_AUTH.PAYMENT_BUY_PACKAGE
            }`
          );
        }
      }
    }
  };

  const activePackage = useMemo(
    () =>
      hoveredPackageId ? packages.find((p: any) => p.id === hoveredPackageId) : defaultPackage,
    [hoveredPackageId, packages, defaultPackage]
  );

  const handleMouseEnter = () => {
    onHover(data.id);
  };

  const handleMouseLeave = () => {
    onHover(null);
  };

  const handleShowTagRemainDayOrInUse = useMemo(() => {
    if (data?.remainingDays) {
      return (
        <div className={styles.packageRemainingDays}>
          <span>{`Gói sẽ hết hạn sau ${data?.remainingDays} ngày`}</span>
          <SvgIcon size={13} type={ICON_KEY.CLOCK_COUNTDOWN} title={data?.remainingDays} />
        </div>
      );
    }
    if (data?.userIsBuy) {
      return (
        <div className={styles.packageUsing}>
          <span className={classNames(styles.text, 'text-bold')}>{TEXT.USING_PACKAGE}</span>
          <SvgIcon size={13} type={ICON_KEY.PENTAGRAM} title={TEXT.USING_PACKAGE} />
        </div>
      );
    }
    return null;
  }, [data]);

  return (
    <>
      <div
        className={classNames(
          styles.package,
          packages?.length === 1 ? styles.oneItem : '',
          !!packageConfig?.tag && [styles.packageMostPopular],
          'hover:cursor-pointer'
        )}
        onClick={handleSelectPackageAndNavigate}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {data?.textId && !!packageConfig?.tag && (
          <div className={classNames(styles.packageBackDrop, getBackDropBalloonClass(styles))}>
            <div className={classNames(styles.packageBackDropWrap)}>
              {[1, 2, 3].map((index) => (
                <span
                  key={index}
                  className={classNames(
                    getBackDropBalloonClass(styles, true)[0],
                    getBackDropBalloonClass(styles, true)[index]
                  )}
                />
              ))}
            </div>
          </div>
        )}
        {!!packageConfig?.tag && (
          <TagsMostBuyers customClass={styles.mostBuyers} text={packageConfig?.tag} />
        )}
        <div
          className={classNames(
            styles.packageContainer,
            activePackage?.id === data?.id ? [styles.forceHover] : ''
          )}
        >
          <div className={classNames(styles.box, styles[`box${setColorByData}`])}>
            <div className="block relative overflow-hidden w-full md:aspect-[332/161]">
              <Image
                className={classNames(styles.image, 'w-full')}
                notWebp
                src={data?.banner}
                alt={data?.name}
              />
              <div className="w-[61%] h-[calc(100%-1.75rem)] absolute top-4 left-4">
                <Image
                  className="w-auto h-auto max-w-full max-h-full"
                  notWebp
                  src={data?.packageGroups?.[0]?.background || data?.bannerInfoGroup}
                  alt={data?.name}
                />
              </div>
            </div>
            {handleShowTagRemainDayOrInUse}
          </div>
          <Info benefits={benefits} packageConfig={packageConfig} packages={packages} />
          <Button
            className={classNames(styles.button)}
            title={`${TEXT.SUBSCRIBE_PACKAGE} ${data?.name?.toUpperCase()}`}
          />
        </div>
      </div>
      {isMobile && promotionData && (
        <Promotion data={promotionData} id={data?.id} classNameItem="h-auto lg:h-16" />
      )}
    </>
  );
};

export default Package;
