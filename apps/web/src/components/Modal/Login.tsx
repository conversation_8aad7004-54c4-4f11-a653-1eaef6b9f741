import React, { useState, useEffect, useMemo } from 'react';
import isEmpty from 'lodash/isEmpty';
import Input from '@components/Input';
import Button from '@components/Button';
import IconKey from '@components/Icons/IconKey';
import { TYPE_INPUT } from '@constants/types';
import { TEXT } from '@constants/text';
import { FLOW_GLOBAL_AUTH, HTTP_CODE } from '@constants/constants';
import { trackingAuth, EVENT_NAME } from '@tracking/functions/TrackingAuthentication';
import { formatPhoneNumberVN } from '@helpers/common';
import ButtonBack from './ButtonBack';
import TextSupport from './TextSupport';
import Modal from './index';
import MessageCasesInput from './MessageCasesInput';

const Login = ({
  userName,
  countryKey,
  isPhoneNumber,
  onChangePassword,
  onSubmit,
  onForgotPassword,
  dataLogin,
  dataForgotPassword,
  onBack,
  canBack
}: any) => {
  const [password, setPassword] = useState<any>('');
  const [error, setError] = useState<any>('');
  const [errorForm, setErrorForm] = useState<any>('');
  const [disabled, setDisabled] = useState(true);
  const phoneNumberFormat = useMemo(() => {
    if (isPhoneNumber && countryKey === '+84' && userName) {
      return formatPhoneNumberVN(userName);
    }
    return userName;
  }, [isPhoneNumber, countryKey, userName]);

  const onTrackingLoginButtonSelected = () => {
    trackingAuth({
      event: EVENT_NAME.LOGIN_BUTTON_SELECTED,
      typeTrigger: isPhoneNumber ? 'mobile' : 'email',
      isUseFlowAuthenProperty: true
    });
  };

  const onTrackingForgotButtonSelected = () => {
    const channel = isPhoneNumber ? 'mobile' : 'email';
    trackingAuth({
      event: EVENT_NAME.FORGOT_PASSWORD_BUTTON,
      typeTrigger: channel,
      isUseFlowAuthenProperty: true,
      flowName: 'forgot_password'
    });

    if (typeof onChangePassword === 'function') {
      trackingAuth({
        event: EVENT_NAME.OTP_SCREEN_LOADED,
        typeTrigger: channel,
        isUseFlowAuthenProperty: true,
        flowName: 'forgot_password'
      });
    }
  };

  const handleSubmit = () => {
    if (!disabled) {
      onTrackingLoginButtonSelected();
      if (typeof onSubmit === 'function') onSubmit();
    }
  };

  const onKeyDown = (e: any) => {
    if (e?.keyCode === 13 || e?.key === 'Enter') {
      handleSubmit();
    }
  };

  const handleChangePassword = (value: any) => {
    setErrorForm('');
    setError('');
    if (value.length >= 6) {
      setDisabled(false);
    } else {
      setDisabled(true);
    }
    setPassword(value);
    if (typeof onChangePassword === 'function') onChangePassword(value);
  };

  const onBlurPassword = () => {
    if (password && password.length < 6) {
      setError(TEXT.PASSWORD_REQUIRED_VALUE);
    }
  };

  const handleForgotPassword = () => {
    onTrackingForgotButtonSelected();
    if (typeof onForgotPassword === 'function') {
      onForgotPassword(
        isPhoneNumber
          ? FLOW_GLOBAL_AUTH.PHONE_FORGOT_PASSWORD_OTP
          : FLOW_GLOBAL_AUTH.EMAIL_FORGOT_PASSWORD_OTP
      );
    }
  };

  const handleBack = () => {
    if (typeof onBack === 'function') {
      onBack();
    }
  };

  useEffect(() => {
    if (!isEmpty(dataLogin)) {
      const message =
        dataLogin.data?.message || dataLogin.message || TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE;
      if (!dataLogin.success || dataLogin.data?.code === HTTP_CODE.FAIL) {
        setErrorForm(message);
      } else {
        setErrorForm('');
      }
    }
  }, [dataLogin]);

  useEffect(() => {
    if (!isEmpty(dataForgotPassword)) {
      const message =
        dataForgotPassword.data?.message ||
        dataForgotPassword.message ||
        TEXT.ERROR_RETRY_AFTER_SOME_MINUTES_PLEASE;
      if (!dataForgotPassword.success || dataForgotPassword.data?.code === HTTP_CODE.FAIL) {
        setErrorForm(message);
      } else {
        setErrorForm('');
      }
    }
  }, [dataForgotPassword]);

  useEffect(() => {
    setErrorForm('');
  }, []);

  const config = {
    title: TEXT.LOGIN,
    description: isPhoneNumber ? (
      <>
        {TEXT.AUTH_INPUT_PASSWORD_PHONE_PLEASE}
        <div className="md:inline block">
          <span className="text-white font-bold p-x1">{countryKey}</span>
          <span className="text-white font-bold">{phoneNumberFormat}</span>
        </div>
      </>
    ) : (
      <>
        {TEXT.AUTH_INPUT_PASSWORD_EMAIL_PLEASE}
        <span className="text-white font-bold p-l1">{userName}</span>
      </>
    ),
    alignType: 'center',
    buttonBack: canBack ? <ButtonBack onClick={handleBack} /> : null,
    extraInput: (
      <button
        type="button"
        onClick={handleForgotPassword}
        className="text-white text-sm font-medium"
      >
        {TEXT.FORGOT_PASS}
      </button>
    ),
    inputs: (
      <Input
        id="password"
        autoFocus
        placeholder="6-20 ký tự"
        type={TYPE_INPUT.PASSWORD}
        label={TEXT.USER_LABEL.PASSWORD}
        iconPrefix={<IconKey />}
        error={error}
        onChange={handleChangePassword}
        onBlur={onBlurPassword}
        onKeyDown={onKeyDown}
      />
    ),
    messageCasesInput: MessageCasesInput({
      error: errorForm
    }),
    buttons: <Button title={TEXT.LOGIN} isPrimary disabled={disabled} onClick={handleSubmit} />,
    textSupport: <TextSupport />
  };

  return <Modal {...config} />;
};

export default Login;
