import QRCode from 'qrcode';
import { isAndroid } from 'react-device-detect';
import { parsePayDuration } from '@helpers/common';
import ConfigImage from '@config/ConfigImage';
import { CURRENCY, PAYMENT_METHOD, PAYMENT_METHOD_BE, PLATFORM } from '@constants/constants';

export const setValueTitle = (item: any) => {
  const price = `${new Intl.NumberFormat(['ban', 'id']).format(item?.price || 0)} ${CURRENCY.VND}`;
  const duration = item?.duration;

  let durationTitle = parsePayDuration(duration);
  durationTitle = `${price} (${durationTitle})`;
  return durationTitle;
};

export const exportSmsQrCode = async ({ phone, text }: any) => {
  const qrText = `SMSTO:${phone || ''}:${text || ''}`;
  try {
    return await QRCode.toDataURL(qrText);
  } catch (err) {
    return ConfigImage.smsQrCodeDefault;
  }
};

export const exportHrefSms = (phone: any, body: any) => {
  const urlBody = encodeURIComponent(body);
  return `sms:${phone}${isAndroid ? '?' : '&'}body=${urlBody}`;
};

export const parseMethodId = (methodId: any) => {
  switch (methodId) {
    case PAYMENT_METHOD.NAPAS:
      return PAYMENT_METHOD_BE.CARD;
    case PAYMENT_METHOD.CAKE:
      return PAYMENT_METHOD_BE.ASIAPAY;
    case PAYMENT_METHOD.ASIAPAY:
      return PAYMENT_METHOD_BE.ASIAPAY;
    case PAYMENT_METHOD.VN_PAY:
      return PAYMENT_METHOD_BE.VN_PAY;
    case PAYMENT_METHOD.MOBI:
    case PAYMENT_METHOD.VIETTEL:
    case PAYMENT_METHOD.VINA:
      return PAYMENT_METHOD_BE.SMS;
    case PAYMENT_METHOD.MOCA:
      return PAYMENT_METHOD_BE.MOCA;
    case PAYMENT_METHOD.MOMO:
      return PAYMENT_METHOD_BE.MOMO;
    case PAYMENT_METHOD.VIETTEL_PAY:
      return PAYMENT_METHOD_BE.VIETTEL_PAY;
    case PAYMENT_METHOD.IAP:
      return PAYMENT_METHOD_BE.IAP;
    case PAYMENT_METHOD.TP_BANK:
      return PAYMENT_METHOD_BE.TP_BANK;
    case PAYMENT_METHOD.ZALO_PAY:
      return PAYMENT_METHOD_BE.ZALO_PAY;
    case PAYMENT_METHOD.SHOPEE_PAY:
      return PAYMENT_METHOD_BE.SHOPEE_PAY;
    default:
      return '';
  }
};

export const getListPackageDiscount = (data: any, isMobile: any) => {
  const platform = isMobile ? PLATFORM.MOBILE_WEB : PLATFORM.WEB;

  const res = (data?.result?.promos || []).map((promos: any) => {
    const filteredInfo = promos?.info?.filter((item: any) => item?.platform === platform) || [];
    return filteredInfo.map((item: any) => ({
      ...item,
      type: promos?.type || '',
      packageGroups: promos?.package_groups || []
    }));
  });

  return res.flat();
};

export const handleMaskedNumber = (value: any, count: any) => {
  if ((value || '').length <= count || count <= 0) return value;
  const { length } = value || '';
  const tempCount = length % count;
  let tempLength = Math.floor(length / count);
  if (tempCount === 0) tempLength -= 1;
  const maskedChars = '•'.repeat(tempLength * count);
  const visibleChars = value.slice(tempLength * count - length);
  return maskedChars + visibleChars;
};
