import moment from 'moment';
import get from 'lodash/get';
import has from 'lodash/has';
import isEmpty from 'lodash/isEmpty';

export const parseParamsUrlLiveTv = (url: any) => {
  if (!url) return {};
  const urlFormat = url.split('?')?.[0];
  const params: any = urlFormat.split('/');
  if (params?.[1] !== 'truyen-hinh-truc-tuyen') return {};
  return { slug: params?.[2], epg: params?.[3] };
};

export const setDataIsNotifyForEpg = (data: any, listFilterDate: any) => {
  if (isEmpty(listFilterDate)) return setDateFilterListEpgs();
  if (isEmpty(data)) return listFilterDate;
  const listUpdate = listFilterDate.map((item: any, i: any) => {
    if (i < 3) return item;
    return {
      ...item,
      listEpgs: item.listEpgs.map((epg: any) => ({
        ...epg,
        isNotifyComingSoon: has(data, epg.id) ? get(data, epg.id, false) : epg.isNotifyComingSoon
      }))
    };
  });
  return listUpdate;
};

export const setListEpgsInListDateFilter = (listFilterDate: any, listEpgs: any, strDate: any) => {
  if (isEmpty(listFilterDate)) return setDateFilterListEpgs();
  const listUpdate = listFilterDate.map((item: any) => {
    if (item.strDate !== strDate) return item;
    return {
      ...item,
      listEpgs: listEpgs || [],
      isFetched: true
    };
  });
  return listUpdate;
};

const parseDayOfWeek = (number: any) => {
  let dayOfWeek = 'Thứ ';
  const day = parseInt(number, 10) + 1;
  dayOfWeek += day;
  if (parseInt(number, 10) === 0) dayOfWeek = 'Chủ Nhật';

  return dayOfWeek;
};

export const setDateFilterListEpgs = () => {
  const arrayListDate = [];
  for (let i = -3; i <= 3; i += 1) {
    if (i === 0) {
      arrayListDate[i + 3] = {
        id: i + 3,
        strDate: moment().format('YYYY-MM-DD'),
        title: `Hôm nay (${moment().format('DD/MM')})`,
        titleM: 'Hôm nay',
        listEpgs: []
      };
    } else if (i < 0) {
      const curTime = moment().subtract(0 - i, 'days');
      arrayListDate[i + 3] = {
        id: i + 3,
        strDate: curTime.format('YYYY-MM-DD'),
        title: `${parseDayOfWeek(curTime.format('d'))} (${curTime.format('DD/MM')})`,
        titleM: curTime.format('DD/MM'),
        listEpgs: []
      };
    } else {
      const curTime = moment().add(i, 'days');
      arrayListDate[i + 3] = {
        id: i + 3,
        strDate: curTime.format('YYYY-MM-DD'),
        title: `${parseDayOfWeek(curTime.format('d'))} (${curTime.format('DD/MM')})`,
        titleM: curTime.format('DD/MM'),
        listEpgs: []
      };
    }
  }
  return arrayListDate;
};
