import { TEXT } from '@constants/text';
import ConfigImage from '@config/ConfigImage';
import { isMobile } from 'react-device-detect';
import { TVOD } from '@constants/constants';

export const parseTitleAndClassTVod = ({
  isDataPreOrder,
  isDataReminder,
  typeSingleTVod,
  benefitType,
  titleTVod,
  typeSingleTVodPreOder,
  title,
  textTimeEvent,
  imgSrc,
  images,
  isLiveEvent,
  dataPreOrder
}: any) => {
  let imageSrc = ConfigImage.bgTVodFullscreen;
  let className = 'mask absolute full ';

  let titleTvod = '';
  let titleButton = TEXT.VIEW_RENT_LIST;
  let classModalBody = 'flex-box align-middle size-h-full';
  let isButton = false;

  if ((!isDataPreOrder || !isLiveEvent) && isDataReminder) {
    isButton = true;
    if (typeSingleTVod) {
      imageSrc = imgSrc;
      className += 'mask--tvod';
      if (benefitType === TVOD.USER_TYPE.RENTED) {
        titleButton = TEXT.WATCH_NOW;
      }
      if (benefitType === TVOD.USER_TYPE.WATCHED) {
        titleButton = TEXT.CONTINUE_WATCH;
      }
      titleTvod = titleTVod;
    } else {
      className += 'mask--pm-conversion';
      classModalBody = 'size-square-full flex-box align-middle';
      titleTvod = TEXT.CONTENT_RENT_EXPIRE;
    }
  } else if (!isDataReminder && isDataPreOrder) {
    if (typeSingleTVodPreOder) {
      if (isLiveEvent) {
        imageSrc = images?.thumbnail;
        className += 'mask--tvod';
        titleButton = TEXT.WATCH_NOW;
        titleTvod = `Nội dung “${title}” ${textTimeEvent}.`;
        isButton = true;
      } else {
        className += 'mask--pm-conversion';
        classModalBody = 'size-square-full flex-box align-middle';
        titleTvod = `Nội dung đã đặt trước “${title}” sẽ phát sóng trong ${textTimeEvent}.
       Trong thời gian chờ đợi hãy khám phá những nội dung hấp dẫn khác.`;
      }
    } else {
      className += 'mask--pm-conversion';
      classModalBody = 'size-square-full flex-box align-middle';
      let titleContent = (dataPreOrder?.filter((item: any, index: any) => index < 3) || []).map(
        (item: any) => ` “${item?.title}“ `
      );
      if (dataPreOrder?.length > 3) titleContent += ',...';
      const isNotLiveEvent =
        (dataPreOrder || []).findIndex((item: any) => item?.isLive === false) === -1;
      if (isLiveEvent) {
        if (isNotLiveEvent) titleTvod = TEXT.CONTENT_PRE_ORDER;
        else titleTvod = TEXT.TITLE_CONTENT_COMING_SOON_LIVE_PRE_ODER;
      } else {
        titleTvod = `Nội dung đã đặt trước ${titleContent} sắp phát sóng.
       Trong thời gian chờ đợi hãy khám phá những nội dung hấp dẫn khác.`;
      }
      isButton = true;
    }
  } else if (isDataReminder && isDataPreOrder && isLiveEvent) {
    className += 'mask--pm-conversion';
    classModalBody = 'size-square-full flex-box align-middle';
    titleTvod = TEXT.CONTENT_RENTING;
    isButton = true;
  }
  return {
    titleButton,
    titleTvod,
    classModalBody,
    isButton,
    imageSrc,
    className
  };
};

export const parseTimeToText = (seconds: any) => {
  if (!seconds) return '';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor(seconds / 60);
  let text = '';
  if (hours >= 1) {
    text += ` ${hours} giờ`;
    const remainSec = seconds - hours * 3600;
    const parseText = parseTimeToText(remainSec);
    text += ` ${parseText}`;
  } else if (minutes >= 1) {
    text += ` ${minutes} phút`;
    const remainSec = seconds - minutes * 60;
    const parseText1 = parseTimeToText(remainSec);
    text += ` ${parseText1}`;
  }
  return text;
};

export const parseTimeEventTVod = ({ startTime, isLive }: any) => {
  const currentTime = new Date().getTime();
  const secondOneDay = 24 * 60 * 60;
  const secondTwoDay = 2 * secondOneDay;
  const secondThreeDay = 3 * secondOneDay;
  const timeStartEvent = startTime - currentTime / 1000;
  const isAboutTimeReminder = timeStartEvent >= secondOneDay && timeStartEvent <= secondThreeDay;
  let textTimeEvent = '';
  let isNotAreaReminder = false;
  if (startTime) {
    if (isLive) {
      textTimeEvent = 'đang phát sóng';
    } else if (isAboutTimeReminder) {
      if (timeStartEvent >= secondOneDay && timeStartEvent <= secondTwoDay) {
        textTimeEvent = parseTimeToText(timeStartEvent);
      } else if (timeStartEvent > secondTwoDay && timeStartEvent <= secondThreeDay) {
        textTimeEvent = '3 ngày';
      }
    } else isNotAreaReminder = true;
  }
  return { textTimeEvent, isNotAreaReminder };
};
export const parseTitlePreOrderTVod = ({
  typeSingleTVodPreOder,
  isLiveEvent,
  dataPreOrder
}: any) => {
  const isNotLiveEvent =
    (dataPreOrder || []).findIndex((item: any) => item?.isLive === false) === -1;
  let titlePreOrder = '';
  if (isLiveEvent) {
    if (typeSingleTVodPreOder) titlePreOrder = TEXT.CONTENT_PRE_ORDER;
    else if (isNotLiveEvent) titlePreOrder = TEXT.CONTENT_PRE_ORDER;
    else titlePreOrder = TEXT.CONTENT_COMING_SOON_LIVE_PRE_ODER;
  }
  return titlePreOrder;
};

export const filterTVod = ({ data, isDataPreOrder, isDataReminder }: any) => {
  const dataFilter =
    data?.length > 0
      ? data?.filter((item: any, index: any) => {
          let newIndex: any = '';
          if (isDataReminder) newIndex = isMobile ? index < 2 : index < 5;
          else if (isDataPreOrder) newIndex = isMobile ? index < 6 : index < 5;
          else newIndex = isMobile ? index < 8 : index < 15;
          return newIndex;
        })
      : [];
  return dataFilter;
};

export const checkFilterTVod = ({ data, dataCheck }: any) => {
  const arrFilter = (data || []).filter(
    (data: any) => (dataCheck || []).findIndex((item: any) => item?.id === data?.id) === -1
  );
  return arrFilter;
};
export const checkReminderPreOrder = ({
  typeSingleTVodPreOder,
  isLiveEvent,
  isDataReminder,
  typeSingleTVod,
  isDataPreOrder
}: any) => {
  let isPreOrderCase1ComingSoon = false;
  let isPreOrderCase2Live = false;
  let isPreOrderCase4ComingSoon = false;
  let isPreOrderCase4Live = false;
  let isSingleContentTVod = false;
  const isReminder = (!typeSingleTVod || (isDataPreOrder && isLiveEvent)) && isDataReminder;

  if (isDataReminder) {
    if (isLiveEvent) isPreOrderCase2Live = true;
  } else if (isLiveEvent) {
    if (!typeSingleTVodPreOder) isPreOrderCase4Live = true;
  } else if (typeSingleTVodPreOder) {
    isPreOrderCase1ComingSoon = true;
  } else isPreOrderCase4ComingSoon = true;

  if (isLiveEvent) {
    if (typeSingleTVodPreOder && !isDataReminder) isSingleContentTVod = true;
  } else if (typeSingleTVod) isSingleContentTVod = true;

  return {
    isPreOrderCase1ComingSoon,
    isPreOrderCase2Live,
    isPreOrderCase4ComingSoon,
    isPreOrderCase4Live,
    isSingleContentTVod,
    isReminder
  };
};
export const sortDataLive = (data = []) => {
  data.sort((a: any, b: any) => a.startTime - b.startTime);
  return data;
};
