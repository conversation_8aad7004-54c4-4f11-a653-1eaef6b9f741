import { getLiveTime, isToday } from '@helpers/common';
import Moment from 'moment/moment';

export const setStartTextTag = (start: any) => {
  const timeNow = Date.now() / 1000;
  if (timeNow >= start) return '';
  const dateStart = new Date(start * 1000);
  const minutesStart = dateStart.getMinutes();
  const today = isToday(dateStart);
  if (today) {
    if (minutesStart === 0) {
      return `${Moment(start * 1000).format('HH:00')}`;
    }
    return `${Moment(start * 1000).format('HH:mm')}`;
  }
  if (minutesStart === 0) {
    return `${Moment(start * 1000).format('HH:00')} | ${Moment(start * 1000).format('DD/MM')}`;
  }
  return `${Moment(start * 1000).format('HH:mm')} | ${Moment(start * 1000).format('DD/MM')}`;
};

export const setStartTimeLiveStream = (start: any, isLive: any, isPremiere?: any) => {
  const timeLivestream = getLiveTime(start);
  const currentTime = new Date().getTime() / 1000;
  const dateTime = Moment(new Date(start * 1000));
  const hoursText = dateTime.format('HH:mm');
  const dateText = dateTime.format('DD/MM');
  let startText = '';
  let isFuture = false;
  if (currentTime >= start && isLive) startText = `Bắt đầu phát trực tiếp ${timeLivestream}`;
  if (currentTime < start) isFuture = true;
  if (isFuture) {
    startText = `${isPremiere ? 'Công chiếu' : 'Trực tiếp'} lúc ${hoursText}, ngày ${dateText}`;
  }
  return startText;
};
