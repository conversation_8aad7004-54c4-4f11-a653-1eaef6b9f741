import isEmpty from 'lodash/isEmpty';
import { EL_ID, POSITION, VIDEO_INDEXING } from '@constants/constants';

export function groupAndAddObjectId({ data }: any) {
  const temp = data.items || [];
  const video = data?.meta?.video || {};
  const objects = uniqueIDsItemsWithSameID(data?.meta?.objects || []);
  const filterProductList = (temp || []).filter(
    (item: any) => item.type === VIDEO_INDEXING.PRODUCT
  );
  const productList = groupItemX(
    (temp || [])
      .filter((item: any) => item.type === VIDEO_INDEXING.PRODUCT)
      .sort((a: any, b: any) => {
        if (a.objectId < b.objectId) {
          return -1;
        }
        if (a.objectId > b.objectId) {
          return 1;
        }
        return a.startAt - b.startAt;
      })
      .map((it: any) => {
        const checkedItem = checkShowIndicator({
          video,
          viItem: it
        });
        const positionInfobox = positionInfoBox(checkedItem || {});
        return { ...it, ...checkedItem, positionInfobox };
      })
  );

  const brandList = (temp || []).filter((item: any) => item.type === VIDEO_INDEXING.BRAND);
  return {
    ...data,
    productList,
    brandList,
    items: (productList || []).concat(brandList),
    meta: { ...data.meta, objects },
    itemProductFirst: filterProductList?.[0]
  };
}
export const positionInfoBox = ({ xPos, yPos }: any) => {
  let position = '';
  if (
    (parseInt(xPos, 10) > 30 && parseInt(xPos, 10) < 75 && parseInt(yPos, 10) < 25) ||
    (parseInt(yPos, 10) >= 25 && parseInt(xPos, 10) < 30)
  ) {
    position = POSITION.TOP_RIGHT;
  } else if (parseInt(yPos, 10) < 20) position = POSITION.TOP_LEFT;
  else if (parseInt(xPos, 10) >= 75) position = POSITION.BOTTOM_LEFT;
  else position = POSITION.BOTTOM_RIGHT;
  return position;
};

export const checkShowIndicator = ({ video, viItem }: any) => {
  const item = viItem?.predBoxes?.[0];
  if (isEmpty(item) || isEmpty(video)) return false;
  const { x, y, width, height } = item || {};

  const videoEL = document.getElementById(EL_ID.VIE_PLAYER);
  if (!videoEL) return null;
  const { clientHeight } = videoEL;
  const { clientWidth } = videoEL;

  const ratio = video.width / video.height;
  const videoRatio = clientWidth / clientHeight;

  const blackBar = videoRatio > ratio ? 'left_right' : 'top_bottom';

  let a = 0;
  let b = 0;

  let xPos = 0;
  let yPos = 0;

  let realW = clientWidth;
  let realH = clientHeight;

  if (blackBar === 'left_right') {
    realW = realH * ratio;
    a = (clientWidth - realW) / 2;
  } else {
    realH = realW / ratio;
    b = (clientHeight - realH) / 2;
  }

  const wBox = (width * realW) / clientWidth;
  const hBox = (height * realH) / clientHeight;

  xPos = realW * x + a + (realW * width) / 2 - 20;
  yPos = realH * y + b + (realH * height) / 2 - 20;

  let xBoxPos: any = realW * x + a;
  let yBoxPos: any = realH * y + b;
  xBoxPos = `${(xBoxPos / clientWidth) * 100}%`;
  yBoxPos = `${(yBoxPos / clientHeight) * 100}%`;

  xPos = (xPos / clientWidth) * 100;
  yPos = (yPos / clientHeight) * 100;

  const isAreaSafeZone = checkAreaSafeZoneVideo({
    xPos,
    yPos
  });

  const isAreaToast = checkAreaToastVideo({
    xPos,
    yPos
  });
  const isShow = isAreaSafeZone && !isAreaToast;

  return {
    xBoxPos,
    yBoxPos,
    wBox: `${wBox * 100}%`,
    hBox: `${hBox * 100}%`,
    isShow,
    xPos: `${xPos}%`,
    yPos: `${yPos}%`
  };
};

export const checkAreaSafeZoneVideo = ({ xPos, yPos }: any) => {
  const widthLeftTop = 7.5;
  const widthRightBottom = 100 - 7.5;
  const heightLeftTop = 20;
  const heightRightBottom = 100 - 12;
  const widthTitle = 35;

  return (
    (yPos >= heightLeftTop || xPos >= widthTitle) &&
    xPos >= widthLeftTop &&
    xPos <= widthRightBottom &&
    yPos <= heightRightBottom
  );
};

export const checkAreaToastVideo = ({ xPos, yPos }: any) => {
  const heightToastVideo = 90;
  const widthToastVideo = 13;
  return xPos <= widthToastVideo && yPos >= heightToastVideo;
};

export const uniqueIDsItemsWithSameID = (arr: any) => {
  const uniqueIDs = new Set();
  const filteredArr = [];

  for (let i = 0; i < arr.length; i += 1) {
    const item = arr[i];
    if (!uniqueIDs.has(item.id)) {
      filteredArr.push(item);
      uniqueIDs.add(item.id);
    }
  }

  return filteredArr;
};

export function moveToPosition(movingEL: any, { topPos, leftPos }: any) {
  if (!movingEL) return;
  const currentTop = movingEL.offsetTop;
  const currentLeft = movingEL.offsetLeft;
  movingEL.style.top = `${currentTop}px`;
  movingEL.style.left = `${currentLeft}px`;
  // Trigger reflow to ensure transition works
  // void movingEL.offsetWidth; // Accessing offsetWidth without using void
  movingEL.style.transition = 'top .2s ease, left .2s ease';
  movingEL.style.top = topPos;
  movingEL.style.left = leftPos;
}

export function groupItemX(temp: any) {
  const groupedArray = [];
  let currentItem = null;

  for (let i = 0; i < temp.length; i += 1) {
    const item = temp[i];
    const nextItem = temp[i + 1]; // Get the next item

    if (nextItem && nextItem.objectId === item.objectId) {
      if (currentItem === null || item.startAt - currentItem.endAt < 1) {
        if (currentItem === null) {
          currentItem = {
            objectId: item.objectId,
            groupedItems: [item],
            ...item
          };
          groupedArray.push(currentItem);
        } else {
          currentItem.endAt = item.endAt;
          currentItem.groupedItems.push(item);
        }
      } else {
        currentItem = {
          objectId: item.objectId,
          groupedItems: [item],
          ...item
        };
        groupedArray.push(currentItem);
      }
    } else {
      currentItem = {
        objectId: item.objectId,
        groupedItems: [item],
        ...item
      };
      groupedArray.push(currentItem);
      currentItem = null;
    }
  }

  return groupedArray;
}
