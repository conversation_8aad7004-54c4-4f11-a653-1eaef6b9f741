import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

export const trackingMultiProfileEditLoad = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_LOAD, params, false);
};
export const trackingMultiProfileNameEdit = ({ profileName, userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType,
    [PROPERTY.PROFILE_NAME]: profileName || null
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_NAME, params, false);
};
export const trackingMultiProfileGenderEdit = ({ profileGender, userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.PROFILE_GENDER]: profileGender,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_GENDER, params, false);
};
export const trackingMultiProfileAvatarEdit = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_EDIT_AVATAR, params, false);
};
export const trackingMultiProfileAvatarChose = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_AVATAR, params, false);
};
export const trackingMultiProfileAvatarBack = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_BACK, params, false);
};
export const trackingMultiProfilePinCodeEdit = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_CHANGE_PIN, params, false);
};
export const trackingMultiProfilePinCodeConfirm = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_CONFIRM_PIN, params, false);
};
export const trackingMultiProfilePinCodeDelete = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CHOOSE_CLOSE_PIN, params, false);
};
export const trackingMultiProfileClose = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_CLOSE, params, false);
};
export const trackingMultiProfileAccept = ({ profileAge, profileGender, userType }: any) => {
  const params: any = {
    [PROPERTY.PROFILE_AGE]: profileAge,
    [PROPERTY.PROFILE_GENDER]: profileGender,
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_ACCEPT, params, false);
};
export const trackingMultiProfileUserDeletion = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_DELETION, params, false);
};
export const trackingMultiProfileUserDialogDeleteLoad = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_DELELE_LOAD, params, false);
};
export const trackingMultiProfileUserDialogDeleteCancel = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_DELETE_CANCEL, params, false);
};
export const trackingMultiProfileUserDialogDeleteConfirm = ({ userType }: any) => {
  const params: any = {
    [PROPERTY.FLOW_NAME]: VALUE.MULTI_PROFILE_CONFIG,
    [PROPERTY.USER_TYPE]: userType
  };
  segmentEvent(NAME.MULTI_PROFILE_CONFIG_DELETE, params, false);
};
