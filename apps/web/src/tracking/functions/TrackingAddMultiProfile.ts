import { segmentEvent } from '../TrackingSegment';

const { NAME, PROPERTY } = require('@config/ConfigSegment');

const multiProfileAddProfileLoad = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_LOAD,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileType = ({ flowName, userType, profileTypeUx }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_TYPE,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_TYPE_UX]: profileTypeUx
    },
    false
  );
};
const multiProfileAddProfileChooseAge = ({ flowName, userType, profileAge }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_AGE,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_AGE]: profileAge
    },
    false
  );
};
const multiProfileAddProfileChooseName = ({ flowName, userType, profileNameUx }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_NAME,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_NAME_UX]: profileNameUx
    },
    false
  );
};
const multiProfileAddProfileChooseGender = ({ flowName, userType, profileGender }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_GENDER,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_GENDER]: profileGender
    },
    false
  );
};
const multiProfileAddProfileChooseEditAvatar = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_EDIT_AVATAR,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileChooseAvatar = ({ flowName, userType, data }: any) => {
  const { avatarId, avatarName, avatarOrder, ribbonId, ribbonName, ribbonOrder } = data || {};
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_AVATAR,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.AVATAR_ID]: avatarId,
      [PROPERTY.AVATAR_NAME]: avatarName,
      [PROPERTY.AVATAR_ORDER]: avatarOrder,
      [PROPERTY.RIBBON_ID]: ribbonId,
      [PROPERTY.RIBBON_NAME]: ribbonName,
      [PROPERTY.RIBBON_ORDER]: ribbonOrder
    },
    false
  );
};

const multiProfileAddProfileChooseBack = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_BACK,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileChooseCreatePin = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_CREATE_PIN,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileChooseConfirmPin = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_CONFIRM_PIN,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileChooseClosePin = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_CLOSE_PIN,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileChooseDeletePin = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CHOOSE_DELETE_PIN,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileClose = ({ flowName, userType }: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_CLOSE,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType
    },
    false
  );
};
const multiProfileAddProfileAccept = ({
  flowName,
  userType,
  profileTypeUx,
  profileNameUx,
  profileGender,
  profileAge
}: any) => {
  segmentEvent(
    NAME.MULTI_PROFILE.LOBBY_ADD_PROFILE.MULTI_PROFILE_ADD_PROFILE_ACCEPT,
    {
      [PROPERTY.FLOW_NAME]: flowName,
      [PROPERTY.USER_TYPE]: userType,
      [PROPERTY.PROFILE_TYPE_UX]: profileTypeUx,
      [PROPERTY.PROFILE_GENDER]: profileGender,
      [PROPERTY.PROFILE_AGE]: profileAge,
      [PROPERTY.PROFILE_NAME_UX]: profileNameUx
    },
    false
  );
};

export {
  multiProfileAddProfileLoad,
  multiProfileAddProfileType,
  multiProfileAddProfileChooseAge,
  multiProfileAddProfileChooseName,
  multiProfileAddProfileChooseGender,
  multiProfileAddProfileChooseEditAvatar,
  multiProfileAddProfileChooseAvatar,
  multiProfileAddProfileChooseBack,
  multiProfileAddProfileChooseCreatePin,
  multiProfileAddProfileChooseConfirmPin,
  multiProfileAddProfileChooseClosePin,
  multiProfileAddProfileChooseDeletePin,
  multiProfileAddProfileClose,
  multiProfileAddProfileAccept
};
