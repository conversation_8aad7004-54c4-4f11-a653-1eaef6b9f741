import { NAME, PROPERTY, VALUE } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

const bannerRegisterForRecommendationSelected = () => {
  segmentEvent(NAME.REGISTRATION_TRIGGER.BANNER_RECOMMENDATION_SELECTED, {
    [PROPERTY.FLOW_NAME]: VALUE.FLOW_NAME_REGISTER_RECOMMENDATION
  });
};
const signUpCancel = ({ registrationTrigger }: any) => {
  const flowName = registrationTrigger
    ? registrationTrigger?.textButtonComment
      ? VALUE.FLOW_NAME_TEXT_BUTTON_COMMENT
      : registrationTrigger?.recommendation
      ? VALUE.FLOW_NAME_REGISTER_RECOMMENDATION
      : VALUE.FLOW_NAME_BANNER_COMMENT
    : '';
  segmentEvent(NAME.SIGN_UP_CANCEL, {
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.CANCEL]: VALUE.CANCEL_BUTTON_X_REGISTER
  });
};
const banner_register_for_comment_selected = () => {
  segmentEvent(NAME.REGISTRATION_TRIGGER.BANNER_COMMENT_SELECTED);
};
const text_button_register_for_comment_selected = () => {
  segmentEvent(NAME.REGISTRATION_TRIGGER.TEXT_BUTTON_COMMENT_SELECTED);
};

export {
  bannerRegisterForRecommendationSelected,
  signUpCancel,
  banner_register_for_comment_selected,
  text_button_register_for_comment_selected
};
