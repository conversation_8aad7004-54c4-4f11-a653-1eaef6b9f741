import { NAME, PROPERTY } from '@config/ConfigSegment';
import { segmentEvent } from '../TrackingSegment';

export const loaded = (flowName: any) => {
  const params: any = {
    [PROPERTY.USER_TYPE]: flowName.userType || 'free',
    [PROPERTY.PACKAGE_GROUP_NAME]: flowName.packageGroupName || '',
    [PROPERTY.CAMPAIGN_NAME]: flowName.campaignName || ''
  };
  segmentEvent(NAME.DIALOG_FIRST_PAYMENT_LOADED, params, false);
};

export const closed = (flowName: any) => {
  const params: any = {
    [PROPERTY.USER_TYPE]: flowName.userType || 'free',
    [PROPERTY.PACKAGE_GROUP_NAME]: flowName.packageGroupName || '',
    [PROPERTY.CAMPAIGN_NAME]: flowName.campaignName || ''
  };
  segmentEvent(NAME.DIALOG_FIRST_PAYMENT_CLOSED, params, false);
};

export const accepted = (flowName: any) => {
  const params: any = {
    [PROPERTY.USER_TYPE]: flowName.userType || 'free',
    [PROPERTY.PACKAGE_GROUP_NAME]: flowName.packageGroupName || '',
    [PROPERTY.CAMPAIGN_NAME]: flowName.campaignName || ''
  };
  segmentEvent(NAME.DIALOG_FIRST_PAYMENT_ACCEPTED, params, false);
};
