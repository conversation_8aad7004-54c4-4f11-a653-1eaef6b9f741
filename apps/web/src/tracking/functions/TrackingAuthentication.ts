import get from 'lodash/get';
import { segmentEvent } from '../TrackingSegment';
import ConfigLocalStorage from '@/config/ConfigLocalStorage';
import LocalStorage from '@/config/LocalStorage';
import { handleGTMEvent } from '@tracking/TrackingGTM';
import { PROPERTY } from '@config/ConfigSegment';

// import { PROPERTY } from '@/config/ConfigSegment';

export const EVENT_NAME = {
  MOBILE_AUTHEN_BUTTON_SELECTED: 'MO<PERSON><PERSON>_AUTHEN_BUTTON_SELECTED',
  EMAIL_AUTHEN_BUTTON_SELECTED: 'EMAIL_AUTHEN_BUTTON_SELECTED',
  AUTHEN_STARTED: 'AUTHEN_STARTED',
  LOGIN_BUTTON_SELECTED: 'LOGIN_BUTTON_SELECTED',
  LOGIN_SUCCESSFULLY: 'LOGIN_SUCCESSFULLY',
  RESEND_OTP_SELECTED: 'RESEND_OTP_SELECTED',
  SIGN_UP_SUCCESSFULLY: 'SIGN_UP_SUCCESSFULLY',
  FORGOT_PASSWORD_BUTTON: 'FORGOT_PASSWORD_BUTTON',
  OTP_SCREEN_LOADED: 'OTP_SCREEN_LOADED',
  SEND_OTP_SMS: 'SEND_OTP_SMS',
  OTP_INPUTTED: 'OTP_INPUTTED',
  CONFIRMED_BUTTON_SELECTED: 'CONFIRMED_BUTTON_SELECTED'
};

export const FLOW_TRIGGER_AUTH = {
  MOBILE_AUTHEN_BUTTON_SELECTED: 'mobile_authen_button_selected',
  EMAIL_AUTHEN_BUTTON_SELECTED: 'email_authen_button_selected',
  LOGIN_BUTTON_SELECTED: 'login_button_selected',
  RESEND_OTP_SELECTED: 'resend_otp_selected',
  FORGOT_PASSWORD_BUTTON: 'forgot_password_button_selected',
  LOGIN_SUCCESSFULLY: 'login_successfully',
  SIGN_UP_SUCCESSFULLY: 'sign_up_successfully',
  AUTHEN_STARTED: 'authen_started',
  OTP_SCREEN_LOADED: 'otp_screen_loaded',
  SEND_OTP_SMS: 'send_otp_sms',
  OTP_INPUTTED: 'otp_inputted',
  CONFIRMED_BUTTON_SELECTED: 'confirmed_button_selected',
  REGISTER_SCREEN_LOADED: 'register_screen_loaded'
};

export const FLOW_AUTHEN = {
  SIGN_UP: 'sign_up',
  SIGN_IN: 'sign_in'
};

const TYPE_TRIGGER = {
  ADD_TO_LIST: 'registration_for_add_to_list',
  REMIND_ME: 'registration_for_remind_me',
  COMMENT: 'registration_for_comment',
  RATING: 'registration_for_rating',
  LIVESTREAM_COMING_SOON: 'registration_for_livestream_coming_soon',
  LIVE_TV_COMING_SOON: 'registration_for_livetv_coming_soon',
  REPORT: 'registration_for_report_issues',
  PAYMENT: 'registration_for_payment',
  PAYMENT_LOGIN: 'registration_for_payment',
  PAYMENT_TVOD: 'registration_for_payment_tvod',
  CONTENT: 'registration_for_content',
  SETTING: 'registration_for_setting',
  PROMOTION: 'registration_for_promotion',
  INPUT_PROMOTION: 'registration_for_promotion'
};

export const trackingAuth = ({
  event,
  typeTrigger,
  isUseFlowAuthenProperty,
  typeRegister,
  method,
  status,
  message,
  flowName,
  isAuto,
  query,
  userId,
  mobile,
  email,
  loginMethod,
  triggerFrom,
  content
}: any) => {
  const eventName = get(FLOW_TRIGGER_AUTH, event, '');
  if (eventName) {
    const flowNameOld = typeTrigger ? get(TYPE_TRIGGER, typeTrigger, null) : null;
    let data: any = {
      flow_name: flowNameOld
    };
    if (isUseFlowAuthenProperty) {
      data = { flow_authen: typeTrigger };

      if (
        event === EVENT_NAME.FORGOT_PASSWORD_BUTTON ||
        event === EVENT_NAME.OTP_SCREEN_LOADED ||
        event === EVENT_NAME.SEND_OTP_SMS ||
        event === EVENT_NAME.OTP_INPUTTED ||
        event === EVENT_NAME.RESEND_OTP_SELECTED ||
        event === EVENT_NAME.CONFIRMED_BUTTON_SELECTED ||
        event === EVENT_NAME.LOGIN_BUTTON_SELECTED
      ) {
        data.current_page = window?.location?.href;
        data.flow_name = flowName || '';
      }

      if (event === EVENT_NAME.OTP_INPUTTED || event === EVENT_NAME.CONFIRMED_BUTTON_SELECTED) {
        data.result = status ? 'success' : 'failed';
        data.text_error = message || '';
      }

      if (event === EVENT_NAME.SEND_OTP_SMS || event === EVENT_NAME.RESEND_OTP_SELECTED) {
        data.method = method || '';
      }
    }

    if (typeRegister) {
      data = {
        flow_name: flowNameOld,
        flow_authen: typeRegister
      };
    }
    const loginType = ConfigLocalStorage.get(LocalStorage.LOGIN_TYPE);
    const loginAuto = !loginType;
    const isLoginTV = window.location.search.includes('code');
    const trackingData = {
      ...data,
      is_auto: loginAuto,
      log_in_method: loginType,
      flow_name: isLoginTV ? 'tv_registration' : flowName,
      trigger_from: triggerFrom || null,
      content_id: content?.id || null,
      content_type: content?.type || null
    };
    handleGTMEvent({
      eventName,
      params: {
        [PROPERTY.USER_ID]: userId || '',
        [PROPERTY.MOBILE]: mobile || '',
        [PROPERTY.EMAIL]: email || '',
        [PROPERTY.LOGIN_METHOD]: loginMethod || '',
        [PROPERTY.IS_AUTO]: !!isAuto
      },
      query
    });
    segmentEvent(eventName, trackingData);
  }
};

export const trackingStartedAuthen = (typeTrigger: any, flowAuthen: any) => {
  const eventName = FLOW_TRIGGER_AUTH.AUTHEN_STARTED;
  const currentFeature = localStorage.getItem('currentAuthFeature') || null;
  const flowName = localStorage.getItem('currentAuthFlow') || null;

  const featureName = currentFeature;
  const isLoginTV = window.location.search.includes('code');

  const trackingData = {
    [PROPERTY.CURRENT_PAGE]: 'register_screen',
    [PROPERTY.FLOW_NAME]: isLoginTV ? 'tv_registration' : flowName,
    [PROPERTY.FLOW_AUTHEN]: flowAuthen,
    [PROPERTY.FEATURE_NAME]: featureName
  };
  segmentEvent(eventName, trackingData);
};

export const trackAuthenLoaded = () => {
  const eventName = FLOW_TRIGGER_AUTH.REGISTER_SCREEN_LOADED;

  const currentFlow = localStorage.getItem('currentAuthFlow') || null;
  const currentFeature = localStorage.getItem('currentAuthFeature') || null;

  const flowName = currentFlow;
  const featureName = currentFeature;

  const trackingData = {
    [PROPERTY.CURRENT_PAGE]: 'register_screen',
    [PROPERTY.FLOW_NAME]: flowName,
    [PROPERTY.FEATURE_NAME]: featureName
  };
  segmentEvent(eventName, trackingData);
};

// export const
