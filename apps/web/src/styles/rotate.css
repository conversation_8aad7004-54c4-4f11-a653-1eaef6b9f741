.intro--preview-vod--seo-detail .billboard__pane {
  padding-bottom: 11.25% !important;
}
.intro--preview-vod--seo-detail .billboard__backdrop {
  padding-top: 0.625rem;
}
.intro--preview-vod--seo-detail .billboard__info .billboard__title {
  max-width: 50%;
}
.intro--preview-vod--seo-detail .title {
  display: flex;
}
.intro--preview-vod--seo-detail .billboard .billboard__image:after {
  content: '';
  display: block;
  position: absolute;
  right: 0;
  bottom: -0.125rem;
  left: 0;
  width: 100%;
  height: 100%;
}
.player--live-tv .player__controls-wrap {
  justify-content: end !important;
}
.masked-items-slider {
  transform: translate(-100%, 100%);
}
.masked-items {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 99999;
}
.seo-metadata-text * {
  color: transparent;
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  #descM {
    transform: scale(0);
    transform-origin: bottom right;
    height: 0;
  }
  .intro__info__desc__animated {
    height: auto;
    transform: scale(1);
    transform-origin: bottom right;
    transition: height 0.6s ease-in-out;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .intro--preview-vod--seo-detail .billboard__pane {
    padding-bottom: 16.25% !important;
  }
  .player.player--vod .player-inner,
  .section.section--vod-detail {
    height: auto;
  }
  .player.player--vod .player-stage {
    padding-bottom: 56.25%;
  }
  .epg-mini {
    display: none;
  }
}

@media screen and (min-width: 320px) and (max-width: 767px) and (orientation: portrait) {
  .form .plugin .button-group .button .text {
    display: block;
  }
  .rocopa.rocopa--banner {
    min-height: auto;
  }
  .player--sub-title {
    bottom: 0;
    width: 100%;
  }
}
