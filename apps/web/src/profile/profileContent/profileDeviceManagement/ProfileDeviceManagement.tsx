import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import SvgIcon from '@components/basic/Icon/SvgIcon';
import { ID, ICON_KEY } from '@constants/constants';
import { TEXT } from '@constants/text';
import { disabledDevicesManagement } from '@actions/user';
import classnames from 'classnames';
import ProfileDeviceItem from './ProfileDeviceItem';
import styles from './profileDeviceManagement.module.scss';

const ProfileDeviceManagement = ({ tabId }: any) => {
  const dispatch = useDispatch();
  const { devicesManagement } = useSelector((state: any) => state?.User || {});
  const { currentDevice, otherDevices } = devicesManagement;
  const [otherList, setOtherList] = useState(otherDevices);
  const [stickyState, setStickyState] = useState(false);
  const [deviceCheckAll, setDeviceCheckAll] = useState(false);
  const scrollRef = useRef<any>(null);

  useEffect(() => {
    setOtherList(devicesManagement?.otherDevices);
  }, [devicesManagement]);

  const unCheck = (otherList || []).findIndex((d: any) => !!d.checked) < 0;
  const titleStickyClassName = classnames(
    'grid-x justify-content-between align-items-center p-t3',
    styles['title-divider'],
    stickyState && `${styles['text-sticky']} sticky left top`
  );
  const buttonDeviceClassName = classnames(
    'flex-box align-items-center font-size-14 text-white p-y1 on-click font-size-sm-down-13'
  );
  const logOutButtonClassName = classnames(
    styles['logout-button'],
    unCheck && styles['disabled-button']
  );
  const iconClassName = classnames('icon icon--small', styles.icon);

  const onScroll = (e: any) => {
    // When scrollTop === 0 => add className text-sticky
    const { scrollTop } = e.target;
    const itemTranslate = Math.min(0, scrollTop / 3 - 60);

    if (itemTranslate === 0) {
      return setStickyState(true);
    }
    setStickyState(false);
  };

  const handleDeviceCheckAll = () => {
    setDeviceCheckAll(!deviceCheckAll);
    setOtherList(
      (otherList || []).map((de: any) => ({
        ...de,
        checked: !deviceCheckAll
      }))
    );
  };

  const handleDeviceChecked = ({ deviceId, checked }: any) => {
    const tempList = [...otherList].map((item: any) => ({
      ...item,
      checked: item.deviceId === deviceId ? !checked : !!item.checked
    }));

    const checkedCount = (tempList.filter((de) => de.checked) || []).length;
    const allChecked = checkedCount > 0 && checkedCount === (otherList || []).length;
    if (allChecked !== deviceCheckAll) setDeviceCheckAll(allChecked);
    setOtherList(tempList);
  };

  const handleDeviceDisabled = () => {
    const checkedList = (otherList || [])
      .filter((de: any) => de.checked)
      .map((item: any) => ({
        device_id: item.deviceId,
        platform: item.platform,
        model: item.model
      }));
    dispatch(disabledDevicesManagement(checkedList));
  };

  useEffect(() => {
    // Handle device-scroll scroll
    scrollRef?.current?.addEventListener('scroll', onScroll, false);
    return () => {
      scrollRef?.current?.removeEventListener('scroll', onScroll, false);
    };
  }, []);

  return (
    <div
      className={`tabs-panel${tabId === ID.DEVICE_MANAGEMENT ? ' active' : ''} ${
        styles['device-scroll']
      }`}
      ref={scrollRef}
    >
      <div className="p-t4 large-9">
        <div className="card card--device">
          <div className={`${styles['title-divider']}`}>
            <div className="title title-white text-medium">{TEXT.USING_DEVICE}</div>
          </div>
          <div className="card-section">
            <ProfileDeviceItem {...currentDevice} isCurrent />
          </div>
        </div>

        <div className="card card--device">
          <div className={titleStickyClassName}>
            <div className="cell auto title title-white text-medium">{TEXT.OTHER_DEVICE}</div>
            {!isEmpty(otherDevices) && (
              <div className="cell shrink size-mw-65per flex-box justify-content-end align-items-center">
                <div
                  className={`${buttonDeviceClassName}`}
                  role="button"
                  tabIndex={0}
                  onClick={handleDeviceCheckAll}
                  // onKeyDown={handleKeyDown}
                >
                  <span className={iconClassName}>
                    <SvgIcon type={deviceCheckAll ? ICON_KEY.CHECKED_ALL : ICON_KEY.CHECK_ALL} />
                  </span>
                  {TEXT.SELECT_ALL}
                </div>
                <div
                  className={`${buttonDeviceClassName} ${logOutButtonClassName}`}
                  role="button"
                  tabIndex={0}
                  onClick={handleDeviceDisabled}
                  // onKeyDown={handleKeyDown}
                >
                  <span className={iconClassName}>
                    <SvgIcon type={ICON_KEY.LOG_OUT} />
                  </span>
                  {TEXT.LOGOUT}
                </div>
              </div>
            )}
          </div>
          <div className="card-section">
            {isEmpty(otherList) ? (
              <div className="grid-x align-middle large-6 border-bottom-non-lce">
                <div className="cell shrink">
                  <span className="icon icon--small text-muted">
                    <SvgIcon type={ICON_KEY.EMPTY_BOX} />
                  </span>
                </div>
                <div className="cell auto p-l2">
                  <div className="text m-b font-size-14 text-muted">{TEXT.EMPTY_DEVICE}</div>
                </div>
              </div>
            ) : (
              otherList.map((device: any) => (
                <ProfileDeviceItem
                  key={device.deviceId}
                  handleDeviceChecked={handleDeviceChecked}
                  {...device}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(ProfileDeviceManagement);
