import { TEXT } from '@constants/text';
import React from 'react';
import { ID } from '@constants/constants';
import ProfileCard from '@profile/profileContent/profileInfoSection/ProfileCard';
import ProfileCardItem from '../profileInfoSection/ProfileCardItem';

const AccountInvoice = ({ profile, onHandleAction, isKid }: any) => {
  const renderContent = () => (
    <>
      <ProfileCardItem
        label={`${TEXT.ACCOUNT_INVOICE.COMPANY_NAME}:  `}
        value={profile?.invoiceInfo?.companyName || TEXT.NOT_UPDATE}
        action={{
          name: TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_NAME,
          func: () => onHandleAction(ID.UPDATE_COMPANY_NAME)
        }}
        isDisabled={isKid}
        customClassName="address-div"
      />
      <ProfileCardItem
        label={`${TEXT.ACCOUNT_INVOICE.COMPANY_TIN}:  `}
        value={profile?.invoiceInfo?.taxCode || TEXT.NOT_UPDATE}
        action={{
          name: TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_TIN,
          func: () => onHandleAction(ID.UPDATE_COMPANY_TIN)
        }}
        isDisabled={isKid}
        customClassName="address-div"
      />
      <ProfileCardItem
        label={`${TEXT.ACCOUNT_INVOICE.COMPANY_ADDRESS}:  `}
        value={profile?.invoiceInfo?.address || TEXT.NOT_UPDATE}
        action={{
          name: TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_COMPANY_ADDRESS,
          func: () => onHandleAction(ID.UPDATE_COMPANY_ADDRESS)
        }}
        isDisabled={isKid}
        customClassName="address-div"
      />
      <ProfileCardItem
        label={`${TEXT.ACCOUNT_INVOICE.INVOICE_EMAIL}:  `}
        value={profile?.invoiceInfo?.email || TEXT.NOT_UPDATE}
        action={{
          name: TEXT.ACCOUNT_INVOICE.ACTION_NAME.UPDATE_INVOICE_EMAIL,
          func: () => onHandleAction(ID.UPDATE_INVOICE_EMAIL)
        }}
        isDisabled={isKid}
        customClassName="address-div"
      />
    </>
  );

  return <ProfileCard title={TEXT.ACCOUNT_INVOICE.TITLE_BLOCK} renderContent={renderContent} />;
};

export default AccountInvoice;
