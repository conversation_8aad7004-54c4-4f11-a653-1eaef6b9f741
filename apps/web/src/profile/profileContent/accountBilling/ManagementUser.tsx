import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ProfileCard from '@profile/profileContent/profileInfoSection/ProfileCard';
import { TEXT } from '@constants/text';
import AvatarProfile from '@components/basic/AvatarProfile';
import Button from '@components/basic/Buttons/Button';
import { useVieRouter } from '@customHook';
import { LOBBY_PROFILE_STEP, PAGE } from '@constants/constants';
import { getMultiProfile, setResultForm } from '@actions/multiProfile';
import isEmpty from 'lodash/isEmpty';
import styles from './ManagementUser.module.scss';

const ManagementUser = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const { multiProfile, currentProfile } = useSelector((state: any) => state?.MultiProfile || {});

  const handleClick = () => {
    dispatch(setResultForm({ status: LOBBY_PROFILE_STEP.EDIT }));
    router.push(PAGE.LOBBY_PROFILES);
  };

  useEffect(() => {
    if (isEmpty(multiProfile)) dispatch(getMultiProfile());
  }, [multiProfile]);

  const renderContent = () => (
    <div className={`grid-x ${styles['section--lobby-view']}`}>
      <div className="cell small-auto medium-8 large-7">
        <div className={`${styles.scrollLobby} group`}>
          {(multiProfile?.items || []).slice(0, 5).map((item: any) => (
            <AvatarProfile
              key={item.id}
              imgSrc={item.avatarUrl}
              name={item.name}
              size="small"
              active
              current={item?.id === currentProfile?.id}
              kids={item.isKid}
              locked={item.hasPinCode}
            />
          ))}
        </div>
      </div>
      <div className={`cell small-4 medium-5 text-right ${styles.right}`}>
        <Button
          className="button button--action p-0 size-h-auto button--medium"
          textClass="text-green"
          onClick={handleClick}
          title={TEXT.EDIT_INFORMATION}
        />
      </div>
    </div>
  );

  if (isEmpty(multiProfile)) return null;
  return <ProfileCard title={TEXT.MANAGEMENT_USER} renderContent={renderContent} />;
};
export default ManagementUser;
