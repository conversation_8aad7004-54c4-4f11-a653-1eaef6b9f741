import React, { memo, useEffect } from 'react';
import Table from '@components/basic/Table/Table';
import { isEmptyObject, createTimeout } from '@helpers/common';
import { useDispatch, useSelector } from 'react-redux';
import { useVieRouter } from '@customHook';
import Head from 'next/head';
import { TEXT } from '@constants/text';
import { PAGE } from '@constants/constants';
import { getTransactions } from '@actions/user';
import { ACTION_TYPE, createAction } from '@actions/actionType';

let scrollTimer: any = null;
let scrollDownTimer: any = null;

const TransactionDetail = () => {
  const router = useVieRouter();
  const dispatch = useDispatch();
  const transactions = useSelector((state: any) => state?.User?.transactions);
  useEffect(() => {
    if (isEmptyObject(transactions)) {
      dispatch(getTransactions({ page: 0, pageSize: 30 }));
    }
    return () => {
      dispatch(createAction(ACTION_TYPE.CLEAR_TRANSACTION));
    };
  }, []);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimer) clearTimeout(scrollTimer);
    };
  }, [transactions]);

  const handleScroll = (e: any) => {
    if (scrollTimer) clearTimeout(scrollTimer);
    scrollTimer = createTimeout(() => {
      onDetectScrollDown(e);
    }, 300);
  };
  const onDetectScrollDown = (e: any) => {
    const scrollingElement = e?.target?.scrollingElement;
    const { clientHeight } = scrollingElement;
    const { scrollTop } = scrollingElement;
    if (scrollTop + clientHeight > scrollingElement.scrollHeight - 200) {
      // if (typeof onScrollDown === 'function') {
      //   onScrollDown && onScrollDown();
      // }
      if (typeof onScrollDown === 'function') onScrollDown();
    }
  };

  const onScrollDown = () => {
    if (scrollDownTimer) clearTimeout(scrollDownTimer);
    scrollDownTimer = setTimeout(() => {
      loadMoreData();
    }, 200);
  };
  const onBack = () => {
    router.push({
      pathname: PAGE.PROFILE
    });
  };

  const loadMoreData = () => {
    const { nextPage, pageSize, pageIndex } = transactions?.data?.metadata || {};
    if (nextPage > 0) {
      dispatch(
        getTransactions({
          page: (pageIndex || 0) + 1,
          pageSize
        })
      );
    }
  };

  return (
    <>
      <Head>
        <title>Lịch sử giao dịch - VieON</title>
      </Head>
      <section className="section section--user section--user-profile !py-4 md:!py-6 overflow">
        <div className="container canal-v">
          <div className="section__header">
            <div className="grid-x align-middle">
              <div className="cell medium-auto">
                <h1 className="title title-white">{TEXT.USER_ACTION.BILLING_DETAILS}</h1>
              </div>
              <div className="cell shrink">
                <div className="filter">
                  <div className="grid-x align-middle">
                    <div className="cell shrink">
                      <button className="button link button--action" onClick={onBack}>
                        {TEXT.BACK}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="section__body">
            <div className="block block--billing-detail">
              <div className="block-body" onScroll={handleScroll}>
                <Table tableData={transactions?.data?.tableData} />
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default memo(TransactionDetail);
