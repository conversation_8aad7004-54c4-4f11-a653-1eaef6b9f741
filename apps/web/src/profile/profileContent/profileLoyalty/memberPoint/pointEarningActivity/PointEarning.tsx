import classNames from 'classnames';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Button from '@components/basic/Buttons/Button';
import { seeAllVoucher, getActivities } from '@actions/user';
import { useVieRouter } from '@customHook';
import { ID, PAGE_MAX_SIZE, POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import { openPopup } from '@actions/popup';
import styles from './PointEarningActivity.module.scss';

const PointEarning = ({ data }: any) => {
  const dispatch = useDispatch();
  const seeAllStatus = useSelector((state: any) => state?.User?.loyalty?.seeAllStatus || '');
  const router = useVieRouter();

  useEffect(() => {
    if (router.query.slug !== ID.LOYALTY_POINT) {
      dispatch(seeAllVoucher(''));
    }
  }, [router.query.slug]);

  const handleClickSeeAll = () => {
    dispatch(getActivities({ pageSize: PAGE_MAX_SIZE, isClickSeeAll: true }));
  };

  return (
    <div className="padding-small-up-top-12 padding-xlarge-up-top-24">
      {seeAllStatus && (
        <div className="text-16 text-large-up-18 text-white p-b2 relative padding-small-up-right-64">
          <span className="text-medium">{TEXT.EARNING_POINT_ACTIVITY}</span>
          <Button
            title={TEXT.BACK}
            subTitle={TEXT.BACK}
            className="text-green text-12 text-large-up-14 text-bold absolute right p-y1 p-x2 top"
            onClick={() => dispatch(seeAllVoucher(''))}
          />
        </div>
      )}
      <div
        className={classNames(
          'grid-x align-middle',
          !seeAllStatus ? styles['-m-x-36'] : styles['-m-x-12'],
          !seeAllStatus && styles['mw-1099']
        )}
      >
        {(!seeAllStatus ? data?.items?.slice(0, 9) : data?.items)?.map((item: any) => (
          <PointEarningItem key={item.id} item={item} seeAllStatus={seeAllStatus} />
        ))}
      </div>
      {data?.hasNextPage && !seeAllStatus && !seeAllStatus && (
        <div className="flex-box justify-content-center padding-small-up-top-6 padding-large-up-top-12">
          <button
            title={TEXT.SEE_ALL}
            className={classNames('button button--large-up text-white', styles['button-see-more'])}
            onClick={() => handleClickSeeAll()}
          >
            <span className="text">{TEXT.SEE_ALL}</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default React.memo(PointEarning);

export const PointEarningItem = ({ item, seeAllStatus }: any) => {
  const { name, basePoint } = item || {};
  const dispatch = useDispatch();

  const handleClickDetail = (e: any, data: any) => {
    e.preventDefault();
    dispatch(openPopup({ name: POPUP.NAME.DETAIL_LOYALTY, data }));
  };

  return (
    <div
      className={classNames(
        'padding-y-6 size-w-full',
        seeAllStatus
          ? 'padding-x-medium-up-12 medium-4 xxlarge-3'
          : 'padding-x-medium-up-36 medium-6 xlarge-4',
        seeAllStatus && styles['row-5']
      )}
    >
      <div
        className={classNames('relative padding-x-small-up-16 padding-y-small-up-8', styles.item)}
      >
        <div className="grid-x align-middle text-12">
          <div className="small-9 p-r3">
            <div
              className="margin-small-up-bottom-12 size-w-full text-white line-clamp text-12 text-medium p-t1"
              data-line-clamp="2"
            >
              {name}
            </div>
            <Button
              className="text-12 text-gray239 text-medium absolute"
              title={TEXT.SEE_DETAIL}
              subTitle={TEXT.SEE_DETAIL}
              onClick={(e: any) => handleClickDetail(e, item)}
            />
          </div>
          <div
            className={classNames(
              'small-3 size-square-full absolute flex-box align-middle top right-1 justify-content-center',
              styles.center
            )}
          >
            <div className={classNames('text-green flex-box align-middle', styles.column)}>
              <span className="text-14 text-bold ">+{basePoint || 0}</span>
              <div className="text-14 text-bold ">{TEXT.VIECOIN}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
