import React, { useMemo } from 'react';
import classNames from 'classnames';
import { TIER } from '@constants/constants';
import ConfigImage from '@config/ConfigImage';
import styles from './Banner.module.scss';

const MEMBER_RANK = [
  {
    id: TIER.BRONZE,
    key: ConfigImage.bronzeBadge
  },

  {
    id: TIER.SILVER,
    key: ConfigImage.silverBadge
  },

  {
    id: TIER.GOLD,
    key: ConfigImage.goldBadge
  },

  {
    id: TIER.DIAMOND,
    key: ConfigImage.diamondBadge
  }
];
const ProcessSteps = ({ currentTier }: any) => {
  const activeClassProcess = useMemo(() => {
    const map = {
      [TIER.MEMBER]: '',
      [TIER.SILVER]: styles.silver,
      [TIER.GOLD]: styles.gold,
      [TIER.DIAMOND]: styles.diamond
    };
    return map[currentTier];
  }, [currentTier]);

  return (
    <div
      className={classNames(
        styles['step-wrapper'],
        activeClassProcess,
        'margin-small-up-bottom-20 margin-medium-up-bottom-32 relative flex-box layer-1 align-middle'
      )}
    >
      {MEMBER_RANK.map((item: any) => (
        <div
          key={item.id}
          className={classNames(
            'relative flex-box layer-1',
            styles['step-item'],
            currentTier === item.id && styles.active
          )}
        >
          <img
            src={item.key}
            alt={item.id}
            className={classNames(
              currentTier === item.id && currentTier !== TIER.BRONZE && 'scaleUp absolute',
              currentTier === TIER.BRONZE && currentTier === item.id && styles.bronze,
              currentTier === TIER.BRONZE && currentTier === item.id && 'absolute'
            )}
          />
        </div>
      ))}
    </div>
  );
};

export default React.memo(ProcessSteps);
