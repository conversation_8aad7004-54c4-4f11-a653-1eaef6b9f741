// Mo<PERSON>les
@use 'sass:math';

/* =============== function caculate rem =============== */
@function rem($size) {
  //
  $remSize: math.div($size, 16);

  //
  @return #{$remSize}rem;
}

/* =============== style =============== */
// Banner point
.banner-point {
  border-radius: rem(16);
  height: rem(530);
  background-color: rgba(255, 255, 255, 0.02);
  border: rem(1) solid #fff;
  & div div div > .divide {
    &::after {
      width: rem(2);
    }
  }
  .background-image {
    object-fit: cover;
    object-position: right;
  }
}
.linear-text-gold {
  background: linear-gradient(180deg, #eddd50, #f6f0b7, #9b7c0c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.linear-text-diamond {
  background: linear-gradient(180deg, #c1fe91, #cafebe, #01bc04);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.linear-text-silver {
  background: linear-gradient(180deg, #fcfcfc, #cacaca);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.linear-text-bronze {
  background: linear-gradient(180deg, #ffedd3, #d6af76);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.benefit-item {
  .icon-check {
    min-width: rem(20);
    width: rem(20);
    height: rem(20);
    border-radius: 100%;
    border: 1px solid #444;
    svg {
      width: rem(12);
    }
  }
}

.border-right {
  border-right: 1px solid rgba(222, 222, 222, 0.3);
}
.divide {
  &::after {
    content: '';
    display: block;
    position: absolute;
    width: rem(1);
    height: 64%;
    left: rem(-1);
    top: 50%;
    transform: translateY(-50%);
    background: radial-gradient(
      rgba(255, 255, 255, 0.5) 0%,
      rgba(255, 255, 255, 0) 100%,
      rgba(255, 255, 255, 0) 100%,
      rgba(255, 255, 255, 0.5) 0%
    );
  }
  &-mobile {
    &::after {
      left: 50%;
      top: rem(12);
      transform: translate(-50%, 0);
      width: 64%;
      height: rem(1);
    }
  }
}

/* > Process Styles */
.step-wrapper {
  justify-content: space-between;
  min-height: rem(48);

  &::before {
    position: absolute;
    left: rem(8);
    top: calc(50% - rem(1));
    height: rem(6);
    width: 95%;
    transform: translateY(calc(-50% + rem(1)));
    background-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.25), inset 0px 0px 5px rgba(0, 0, 0, 0.25);
    content: '';
  }

  &::after {
    position: absolute;
    left: rem(8);
    top: 50%;
    height: rem(3);
    width: 0;
    transform: translateY(-50%);
    content: '';
    background-color: unset;
    background-image: linear-gradient(to right, rgba(255, 184, 0, 0.7), rgba(252, 203, 146, 0.7));
    // box-shadow: -3px 0px 7px rgba(255, 195, 80, 0.4), inset 0px 2px 10px rgba(255, 255, 255, 0.25);
    box-shadow: 0px 2px 10px 0px rgba(255, 255, 255, 0.25) inset;
    filter: drop-shadow(-3px 0px 7px rgba(255, 195, 80, 0.4));
    animation: drawBorder 2s ease forwards;
  }
  &.silver {
    &::after {
      width: calc(33.33% - rem(8));
    }
  }
  &.gold {
    &::after {
      width: calc(66.66% - rem(8));
    }
  }
  &.diamond {
    &::after {
      width: calc(100% - rem(12));
    }
  }

  .step-item {
    align-items: center;
    justify-content: center;
    width: rem(32);
    height: rem(32);
    .bronze {
      width: rem(48) !important;
      height: rem(48);
      max-width: rem(48);
    }
  }
}

.flex {
  display: flex;
}

// button shadow
.button-shadow {
  border: 1px solid #585858;
  color: #a5a5a5;
  line-height: normal;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: rem(32);
  &:not(:last-child) {
    margin-right: rem(8);
  }

  &.diamond {
    background: #0a9f1b;
  }
  &.bronze {
    background: #b48102;
  }
  &.gold {
    background: #a89010;
  }
  &.silver {
    background: #898286;
  }
  &.active {
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.35);
    color: #fff;
  }
}
.bg-diamond {
  background: #0a9f1b;
}
.bg-bronze {
  background: #b48102;
}
.bg-icon-silver {
  background: #494949;
}
.bg-icon-bronze {
  background: transparent;
  border-color: #fff !important;
}
.bg-gold {
  background: #a89010;
}
.bg-silver {
  background: #494949;
}
.mobile-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
}

@keyframes drawBorder {
  from {
    max-width: 0%;
  }
  to {
    max-width: 100%;
  }
}

@media screen and (min-width: rem(640)) {
  .step-wrapper {
    width: 82%;
    max-width: rem(453);
  }
  .mobile-bottom {
    position: relative;
  }
  .w-82 {
    width: 82% !important;
  }

  .benefit-item {
    .icon-check {
      width: rem(24);
      height: rem(24);
      svg {
        width: rem(16);
      }
    }
  }
}

@media screen and (min-width: rem(768)) {
  .text-large-up-48 {
    font-size: rem(48) !important;
  }
}

.w-75 {
  max-width: 75%;
}

@media screen and (min-width: rem(1024)) {
  .flex-md {
    display: flex;
  }
  .banner-point {
    height: rem(360);
  }
  .divide-mobile {
    &::after {
      left: 0;
      top: 50%;
      transform: translate(0, -50%);
      width: rem(1);
      height: 64%;
    }
  }
}
