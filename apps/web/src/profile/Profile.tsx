import React, { useEffect, useState } from 'react';
import CustomTab from '@components/basic/CustomTab/CustomTab';
import { ID, PAGE, POPUP } from '@constants/constants';
import { TEXT } from '@constants/text';
import TrackingApp from '@tracking/functions/TrackingApp';
import { UtmParams } from '@models/subModels';
import { useDispatch, useSelector } from 'react-redux';
import { openPopup } from '@actions/popup';
import { getMultiProfile, isRemoveContentInTitleRestrictionFlow } from '@actions/multiProfile';
import { ACTION_TYPE, createAction } from '@actions/actionType';
import ProfileContent from './profileContent/ProfileContent';

const Profile = ({
  router,
  profile,
  currentProfile,
  onHandleAction,
  updateAllowPush,
  onScrollDownWatchMore,
  onScrollDownWatchLater,
  handleLoadMoreFavorite,
  handleLoadMoreWatching,
  purchased,
  isLoadMore,
  transactions,
  favoriteData,
  watchingData
}: any) => {
  const slug = router?.query?.slug;
  const dispatch = useDispatch();
  const [tabData, setTabData] = useState(TAB_DATA);
  const [activeRestriction, setActiveRestriction] = useState(false);
  const { isOnKidsActivity } = useSelector(
    (state: any) => state?.App?.webConfig?.multiProfile || {}
  );
  const { isOnLoyalty, isShowTitleRestriction } = useSelector(
    (state: any) => state?.App?.webConfig?.featureFlag || false
  );
  const isInRemoveContentFlowRestriction = useSelector(
    (state: any) => state?.MultiProfile?.titleRestriction?.isInRemoveContentFlow
  );
  const isGlobal = useSelector((state: any) => state?.App?.geoCheck?.isGlobal);
  const isProfileDefault = profile?.id === currentProfile?.id;
  const onClickTab = (itemTab: any) => {
    const utmParams = UtmParams(router?.query || {});
    if (activeRestriction) {
      dispatch(
        openPopup({
          name: POPUP.NAME.CANCEL_CHANGE,
          onConfirm: () => {
            setActiveRestriction(false);
            router.push(
              { pathname: PAGE.PROFILE_SLUG, query: utmParams },
              { pathname: `${PAGE.PROFILE}/${itemTab?.id}`, query: utmParams }
            );
          }
        })
      );
    }
    router.push(
      { pathname: PAGE.PROFILE_SLUG, query: utmParams },
      { pathname: `${PAGE.PROFILE}/${itemTab?.id}`, query: utmParams }
    );
    if (itemTab?.id === ID.DEVICE_MANAGEMENT) {
      TrackingApp.categorySelected({
        data: {
          name: itemTab.name
        },
        isCategoryProfile: true
      });
    }
    if (isInRemoveContentFlowRestriction) {
      dispatch(isRemoveContentInTitleRestrictionFlow(false));
    }

    if (itemTab?.id === ID.PAYMENT_INFO) {
      const itemTabIndex = tabData.findIndex((item) => item.id === itemTab?.id);
      TrackingApp.menuSelected({
        name: itemTab?.name,
        id: itemTab?.id,
        order: itemTabIndex,
        slug: itemTab?.id
      });
    }
  };

  const onViewDetail = () => {
    const utmParams = UtmParams(router?.query || {});
    router.push({ pathname: PAGE.TRANSACTION, query: utmParams });
  };

  useEffect(() => {
    const tabId = slug || ID.ACCOUNT_BILLING;
    let listTemp = tabData;
    if (currentProfile?.isKid) {
      listTemp = listTemp.filter((item) => TAB_DATA_FOR_KID.includes(item?.id));
    }
    if (currentProfile?.id !== profile?.id || isOnKidsActivity === false) {
      listTemp = listTemp.filter((item) => TAB_ID_KID !== item?.id);
      if (slug === TAB_ID_KID) {
        router.push(`${PAGE.PROFILE}/${ID.ACCOUNT_BILLING}`);
      }
    }
    if (!isOnLoyalty) {
      listTemp = listTemp.filter((item) => item?.id !== ID.LOYALTY_POINT);
    }
    if (!isShowTitleRestriction || !isProfileDefault) {
      listTemp = listTemp.filter((item) => item?.id !== ID.RESTRICTION_CONTENT);
    }
    if (isGlobal) {
      listTemp = listTemp.filter((item) => item?.id !== ID.CONTENT_RENT);
    }
    const listRemake = (listTemp || []).map((item: any) => ({
      ...item,
      active: item?.id === tabId
    }));
    setTabData(listRemake);
  }, [slug, currentProfile?.isKid, currentProfile?.id, profile?.id]);

  const handleRestriction = (active: any) => {
    setActiveRestriction(active);
  };
  useEffect(() => {
    if (
      (currentProfile?.isKid &&
        slug &&
        (slug === ID.LOYALTY_POINT ||
          slug === ID.KIDS_MANAGEMENT ||
          slug === ID.DEVICE_MANAGEMENT)) ||
      (slug === ID.LOYALTY_POINT && !isOnLoyalty) ||
      (slug === ID.RESTRICTION_CONTENT && (!isShowTitleRestriction || !isProfileDefault))
    ) {
      router.push(PAGE.PROFILE);
    }
  }, [slug, currentProfile?.isKid, isOnLoyalty]);

  useEffect(() => {
    dispatch(getMultiProfile({ kid: true }));
    return () => {
      dispatch(dispatch(createAction(ACTION_TYPE.GET_RESTRICTION_CONTENT, {})));
      dispatch(createAction(ACTION_TYPE.GET_MULTI_PROFILE_KID, []));
    };
  }, []);

  useEffect(
    () => () => {
      if (isInRemoveContentFlowRestriction) {
        dispatch(isRemoveContentInTitleRestrictionFlow(false));
      }
    },
    []
  );

  return (
    <section className="section section--user section--user-profile !py-4 md:!py-6">
      <div className="container canal-v">
        <div className="section__header">
          <h1 className="title title-white">Trang cá nhân</h1>
        </div>
        <div className="section__body">
          <CustomTab onClickTab={onClickTab} data={tabData} className="tabs tabs--profile" />
          <ProfileContent
            purchased={purchased}
            tabId={slug || ID.ACCOUNT_BILLING}
            profile={profile}
            isKid={currentProfile?.isKid}
            transactions={transactions}
            favoriteData={favoriteData}
            watchingData={watchingData}
            onHandleAction={onHandleAction}
            updateAllowPush={updateAllowPush}
            onViewDetail={onViewDetail}
            isLoadMore={isLoadMore}
            handleLoadMoreFavorite={handleLoadMoreFavorite}
            handleLoadMoreWatching={handleLoadMoreWatching}
            onScrollDownWatchMore={onScrollDownWatchMore}
            onScrollDownWatchLater={onScrollDownWatchLater}
            isOnKidsActivity={isOnKidsActivity}
            isOnLoyalty={isOnLoyalty}
            isShowTitleRestriction={isShowTitleRestriction}
            handleRestriction={handleRestriction}
            isProfileDefault={isProfileDefault}
          />
        </div>
      </div>
    </section>
  );
};

const TAB_DATA_FOR_KID = [ID.ACCOUNT_BILLING, ID.WATCHING, ID.FAVORITE, ID.CONTENT_RENT];
const TAB_ID_KID = ID.KIDS_MANAGEMENT;

const TAB_DATA = [
  {
    name: TEXT.ACCOUNT_SETTING,
    id: ID.ACCOUNT_BILLING,
    active: true
  },
  {
    name: TEXT.PURCHASED_SERVICES,
    id: ID.PAYMENT_INFO,
    active: false
  },
  {
    name: TEXT.WATCH_MORE,
    id: ID.WATCHING,
    active: false
  },
  {
    name: TEXT.WATCH_LATER,
    id: ID.FAVORITE,
    active: false
  },
  {
    name: TEXT.CONTENT_RENT,
    id: ID.CONTENT_RENT,
    active: false
  },
  {
    name: TEXT.DEVICE_MANAGEMENT,
    id: ID.DEVICE_MANAGEMENT,
    active: false
  },
  { name: TEXT.KIDS_MANAGEMENT, id: ID.KIDS_MANAGEMENT, active: false },
  { name: TEXT.RESTRICTION_CONTENT, id: ID.RESTRICTION_CONTENT, active: false },
  { name: TEXT.LOYALTY_POINT, id: ID.LOYALTY_POINT, active: false }
];

export default Profile;
