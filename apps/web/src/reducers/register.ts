import { ACTION_REGITER_MOBILE, ACTION_RESEND_OTP_CODE_REGISTER } from '@actions/register';

const registerReducer = (state = {}, action: any) => {
  const result: any = { ...state };
  const dataPayload = action.payload && action.payload.data ? action.payload.data : [];

  switch (action.type) {
    case ACTION_REGITER_MOBILE:
    case ACTION_RESEND_OTP_CODE_REGISTER:
      result[action.type] = dataPayload;
      break;
    default:
      break;
  }

  return result;
};

export default registerReducer;
