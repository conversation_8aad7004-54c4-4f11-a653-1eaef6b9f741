import { ACTION_TYPE } from '@actions/actionType';
import initialState from './initialState';

const episodeReducer = (state: any = initialState.episode, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.GET_EPISODE_LIST_SUCCESS: {
      const slug = data?.slug;
      const seasonEpisodeData: any = state?.episodeData?.[slug] || {};
      const dataItems: any = data?.items || [];
      if (seasonEpisodeData?.items?.[0]) {
        seasonEpisodeData.items = [...seasonEpisodeData?.items, ...dataItems];
        if (seasonEpisodeData?.items?.length) {
          // filter get unique items
          seasonEpisodeData.items = [...seasonEpisodeData?.items, ...dataItems].reduce(
            (unique, o) => {
              if (!unique.some((obj: any) => obj.id === o.id)) {
                unique.push(o);
              }
              return unique;
            },
            []
          );
        }
      } else if (seasonEpisodeData) seasonEpisodeData.items = dataItems;

      return {
        ...state,
        episodeData: {
          ...(state?.episodeData || {}),
          [slug]: {
            ...(state?.episodeData?.[slug] || {}),
            ...data,
            items: seasonEpisodeData?.items
          }
        }
      };
    }
    case ACTION_TYPE.SET_EPISODE: {
      return { ...state, currentEpisode: data };
    }
    case ACTION_TYPE.SET_NEXT_EPISODE: {
      return { ...state, nextEpisode: data };
    }
    case ACTION_TYPE.CLEAR_EPISODE_LIST: {
      return { ...state, episodeData: null };
    }
    default:
      return state;
  }
};

export default episodeReducer;
