import isEmpty from 'lodash/isEmpty';
import { ACTION_TYPE } from '@actions/actionType';
import initialState from './initialState';

const player = (state: any = initialState.player, { type, data }: any) => {
  switch (type) {
    case ACTION_TYPE.SET_BLOCK_PLAYER:
      return { ...state, blockPlayer: data };
    case ACTION_TYPE.SET_STATUS_FULLSCREEN:
      return { ...state, isFullscreen: data };
    case ACTION_TYPE.GET_PLAYER_DATA_SUCCESS:
      return { ...state, ...data };
    case ACTION_TYPE.SET_SETTING_SUCCESS: {
      if (isEmpty(data)) {
        return initialState.player;
      }
      return { ...state, ...data };
    }
    default:
      return state;
  }
};

export default player;
