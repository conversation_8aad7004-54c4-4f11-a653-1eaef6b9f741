import { RANKING_TAB } from '@constants/constants';

export default {
  // app state
  app: {
    tokenProfile: '',
    token: '',
    deviceId: '',
    isMobile: false,
    isSafari: false,
    loading: false,
    seoText: {},
    isLoadedData: false,
    isTablet: false,
    aboutUs: null,
    faqs: '',
    privacy: '',
    agreement: '',
    usage: '',
    license: '',
    regulation: '',
    policyCancellation: '',
    geoCheck: {},
    floatButton: {},
    outStreamAds: {},
    social: {},
    featureFlag: {},
    isTokenExpired: false,
    offBindAccount: false,
    paymentConversion: {},
    configPersonalizationFlow: {},
    introPackages: {},
    toastData: {
      topLeft: [],
      topRight: [],
      bottomLeft: [],
      bottomRight: []
    },
    statusLoadOutStreamAds: '',
    closeOnboarding: false,
    offDownloadApp: false,
    enableTVodReminderScreen: false,
    enablePaymentConversion: false,
    enableDialogOnboarding: false,
    isHasCompanionBanner: false
  },

  appConfig: {
    content: null,
    playerConfig: null
  },

  // menu state
  menu: {
    menuList: [],
    activeMenu: null,
    activeSubMenu: null,
    subHeader: null,
    enableMenu: false
  },

  // page state
  page: {
    pageBanner: null,
    pageRibbon: null,
    ribbonData: null,
    tipData: {},
    seoTemplateConfig: null,
    isMasterBanner: null,
    isInPlayer: null,
    comingSoon: {
      dataContentBroadcasting: null,
      dataContentUpComingSoon: null,
      isComingSoonLoading: false,
      totalUpComingSoon: 0,
      totalBroadcasting: 0
    }
  },

  // sport state
  sport: {
    isRankingBoard: false,
    rankingTab: RANKING_TAB.SCHEDULE,
    activeCompetition: null,
    competitions: []
  },

  webConfig: null,

  // profile state
  profile: null,
  restoreAccount: null,

  // popup state: {popupName, ... params}
  popup: {
    popupName: '',
    cardHover: {},
    previewCard: {
      expand: false,
      data: null,
      dataEpisode: null,
      detailData: null
    },
    configs: null
  },

  // detail state

  vod: {
    content: null,
    contentEpisode: null,
    contentDetail: null
  },
  episode: {
    episodeData: null,
    currentEpisode: null,
    nextEpisode: null
  },

  detail: {
    content: null,
    contentEpisode: null,
    contentDetail: null,
    episodeList: [],
    dataIndexing: {}
  },

  // liveTV state
  liveTV: {
    channel: null,
    channelDetail: null,
    epg: null
  },

  // player state
  player: {
    player: null,
    video: null,
    isLive: false,
    isDVR: false,
    isDRM: false,
    isPlaying: false,
    isFullscreen: false,
    linkPlay: '',
    currentTime: '00:00',
    duration: '',
    playerName: '',
    volume: 100,
    error: { code: -1 },
    blockPlayer: false,
    qualities: [],
    audios: [],
    subtitles: [],
    quality: null,
    audio: null,
    subtitle: null
  },

  payment: {
    checkFirstPay: {},
    dataTemporary: {},
    sliderBenefitVip: {},
    listPackagesConfig: [],
    tokensSaved: {},
    packages: null,
    selectedPackage: null,
    selectedTerm: null,
    config: null,
    listMethodsConfig: null,
    selectedMethod: null,
    transaction: {},
    cardInfo: {
      cardNumber: '',
      cardNumberMasked: '',
      cardName: '',
      cardExpired: '',
      cardCVV: '',
      cardNumberError: '',
      cardExpiredError: '',
      isMaster: false,
      isVisa: false
    },
    billingInfo: {
      order_id: '',
      country_code: '',
      country: '',
      province: '',
      district: '',
      ward: '',
      address: '',
      iso_subdivision: '',
      postal_code: ''
    },
    vnPayList: null,
    bank: null,
    promotionData: {
      promotionCode: '',
      used: 0,
      valid: 0,
      promotionPrice: 0
    },
    infoTransaction: null,
    transactionCreated: null,
    confirmResultTransaction: null,
    transactionResult: null,
    zaloPayLinked: false,
    shopeePayLinked: false,
    tvodInfo: null,
    tvodOffer: null,
    pvodInfo: null,
    pvodOffer: null,
    packageDiscount: null,
    valueReferralCode: '',
    isCollapseFooterRecommend: false
  },

  // user
  user: {
    dataTvod: {},
    enableTVod: false,
    transactions: {},
    dataTvodPreOder: {},
    preorderReminder: [],
    referralProgCode: {},
    otpUpdatePassword: {},
    confirmedOtpUpdatePassword: {},
    devicesManagement: {},
    devicesCache: [],
    loyalty: {
      infoRedeemData: '',
      seeAllStatus: '',
      tierBenefits: [],
      info: {},
      earningActivities: {},
      earningActivityHistory: {},
      usedPointHistory: {},
      vouchers: {},
      vouchersByCategory: {},
      redeemVoucher: {},
      isLoading: false,
      isGetFailInfoLoyalty: false,
      subItemCategory: 0,
      deviceTokenData: ''
    },
    isTriggerFirstPay: false,
    isCancelFirstPay: false
  },

  // livestream
  livestream: {
    tvodInfo: {},
    statusEvent: {}
  },

  // Search
  search: {
    searchShow: false
  }
};
