import { setLinkAttribute } from '@helpers/common';
import { Images, LinkPlay, SeoItem, Triggers } from './subModels';

class PageBanner {
  description: any;
  href: any;
  id: any;
  images: any;
  isMasterBanner: any;
  linkPlay: any;
  name: any;
  ranking: any;
  seo: any;
  title: any;
  triggers: any;
  type: any;
  constructor({ data }: any) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description || data.short_description;
    this.name = data.title;
    this.ranking = data.ranking || 4;
    this.linkPlay = LinkPlay(data);
    this.images = Images({ images: data.images });
    this.seo = SeoItem(data.seo);
    const { href } = setLinkAttribute(data?.type);
    this.href = href;
    this.type = data.type;
    this.isMasterBanner = true;
    this.triggers = Triggers(data);
  }
}

export default PageBanner;
