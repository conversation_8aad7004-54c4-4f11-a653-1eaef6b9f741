class Profile {
  address: any;
  allowPasswordChange: any;
  allowPush: any;
  apple: any;
  avatar: any;
  countryCode: any;
  createdAt: any;
  deletedAt: any;
  dob: any;
  email: any;
  emailVerified: any;
  facebook: any;
  forceBindPhone: any;
  gender: any;
  givenName: any;
  google: any;
  hadTvod: any;
  haveSearchHistory: any;
  haveWatchlater: any;
  haveWatchmore: any;
  httpCode: any;
  id: any;
  invoiceInfo: any;
  isPremium: any;
  mobile: any;
  passwordRequired: any;
  phoneRequired: any;
  phoneVerified: any;
  provider: any;
  showGuestFlow: any;
  status: any;
  token: any;
  trialCode: any;
  uuCode: any;
  constructor(props: any) {
    this.address = props?.address;
    this.allowPush = props?.allow_push;
    this.avatar = props?.avatar;
    this.createdAt = props?.created_at;
    this.deletedAt = props?.deleted_at;
    this.dob = props?.dob;
    this.email = props?.email;
    this.emailVerified = props?.email_verified;
    this.gender = props?.gender;
    this.givenName = props?.given_name;
    this.hadTvod = props?.had_tvod;
    this.haveSearchHistory = props?.have_search_history;
    this.haveWatchlater = props?.have_watchlater;
    this.haveWatchmore = props?.have_watchmore;
    this.id = props?.id;
    this.isPremium = props?.is_premium >= 1;
    this.mobile = props?.mobile;
    this.phoneVerified = props?.phone_verified;
    this.status = props?.status;
    this.trialCode = props?.trial_code;
    this.uuCode = props?.uu_code;
    this.showGuestFlow = props?.show_guestid_flow;
    this.httpCode = props?.httpCode;
    this.phoneRequired = props?.phone_required;
    this.passwordRequired = props?.password_required;
    this.forceBindPhone = props?.force_bind_phone;
    this.token = props?.accessToken || '';
    this.countryCode = `+${props?.calling_no}`;
    this.provider = props?.provider;
    this.allowPasswordChange = props?.allow_password_change;
    this.google = socialLinked(props?.google);
    this.facebook = socialLinked(props?.facebook);
    this.apple = socialLinked(props?.apple);
    this.invoiceInfo = invoiceInfo(props?.invoice_info);
  }
}

const socialLinked = (props: any) => ({
  fullname: props?.full_name,
  linked: props?.linked
});

const invoiceInfo = (props: any) => ({
  companyName: props?.company_name,
  taxCode: props?.tax_code,
  address: props?.address,
  email: props?.email
});

export default Profile;
