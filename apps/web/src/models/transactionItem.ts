import {
  numberWithCommas,
  parseExpiredDate,
  parseExpiredDateTime,
  parseHoursToDate
} from '@helpers/common';
import { CURRENCY, TRANSACTION_CODE } from '@constants/constants';

class TransactionItem {
  amount: any;
  created_date: any;
  duration: any;
  durationDisplay: any;
  expired_date: any;
  expiry_date: any;
  gift: any;
  name: any;
  next_buy: any;
  old_price: any;
  package_value: any;
  payment_method: any;
  percent_discount: any;
  price: any;
  promotion: any;
  recurring: any;
  service_name: any;
  start_date: any;
  status: any;
  txn_ref: any;
  constructor(props: any) {
    //
    this.name = { value: props?.product_name_msg };
    this.amount = { value: props?.amount_msg };
    this.promotion = { value: props?.promotion };
    this.recurring = { value: props?.recurring };
    this.durationDisplay = { value: props?.duration_msg };
    this.txn_ref = { value: props?.txn_id };
    this.payment_method = { value: props?.payment_method };
    this.percent_discount = { value: props?.discount_msg };
    this.service_name = { value: props?.payment_service_msg };
    this.package_value = { value: props?.package_value };
    this.status = {
      value: props?.status_msg,
      style: {
        color:
          props?.status_code === TRANSACTION_CODE.SUCCESS
            ? '#3ac882'
            : props?.status_code === TRANSACTION_CODE.PROCESSING
            ? 'white'
            : 'red'
      }
    };
    this.expiry_date = {
      value: parseExpiredDate(props?.expiry_date)
    };
    this.created_date = { value: parseExpiredDate(props?.txn_date) };
    this.duration = {
      value: props?.duration_msg || `${parseHoursToDate(props?.duration)} tháng`
    };
    this.price = { value: props?.price_msg || `${numberWithCommas(props?.price)} ${CURRENCY.VND}` };
    this.old_price = {
      value: props?.price_msg || `${numberWithCommas(props?.old_price)} ${CURRENCY.VND}`
    };
    this.next_buy = { value: props?.recurring === 1 ? this.expiry_date?.value || '' : '' };
    this.gift = { value: props?.gift || '' }; // use for Voucher
    this.expired_date = { value: parseExpiredDateTime(props?.expired_date) }; // use for Voucher
    this.start_date = { value: parseExpiredDateTime(props?.start_date) }; // use for Voucher
  }
}

export default TransactionItem;
