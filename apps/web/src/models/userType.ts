import UserServices from '@services/userServices';

class UserType {
  hideButtonBuyPackage: any;
  livetvGroupId: any;
  livetvId: any;
  livetvSlug: any;
  packageGroupId: any;
  type: any;
  userType: any;
  constructor(props: any) {
    this.hideButtonBuyPackage = props?.hide_button_buy_package;
    this.livetvGroupId = props?.livetv_group_id;
    this.livetvId = props?.livetv_id;
    this.livetvSlug = props?.livetv_slug;
    this.packageGroupId = props?.package_group_id;
    this.type = props?.type;
    this.userType = UserServices.parseUserType(props?.type);
  }
}

export default UserType;
