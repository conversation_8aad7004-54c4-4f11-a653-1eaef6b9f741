# Web

[![Bugs](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=bugs&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Coverage](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=coverage&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Maintainability Rating](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=sqale_rating&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Quality Gate Status](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=alert_status&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Reliability Rating](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=reliability_rating&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Security Rating](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=security_rating&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)
[![Vulnerabilities](https://sonarqube.vieon.vn/api/project_badges/measure?project=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA&metric=vulnerabilities&token=9d591faf56bf68386ce786cf6d113efd9938659d)](https://sonarqube.vieon.vn/dashboard?id=vieon-platforms_web-v5_AYFvujBLhdcO5awwv8sA)

## Features

- ▲ Based on latest [Next.js](https://github.com/zeit/next.js)
- 🚄 Dynamic routing with [express](https://github.com/expressjs/express) and [next-routes](https://github.com/fridays/next-routes).
- 🗄 State management with [redux](https://github.com/reactjs/redux), [react-redux](https://github.com/reactjs/react-redux), and [next-redux-wrapper](https://github.com/kirill-konshin/next-redux-wrapper)
- 💅 Styling with [styled-components](https://github.com/styled-components/styled-components)
- 🐐 Unit testing with [react-testing-library](https://github.com/testing-library/react-testing-library)
- 🛀 Linting staged changes on [pre-commit](https://github.com/pre-commit/pre-commit) with [standard](https://github.com/standard/standard)
- ⛑ [react-helmet](https://github.com/nfl/react-helmet), [Immutable.js
  ](https://github.com/facebook/immutable-js/), [dotenv](https://github.com/motdotla/dotenv), and more...

## Getting started

```
[LOCAL]
Step 1: .env.example -> .env
Step 2: yarn install / npm install
Step 3: yarn start / npm start

[DEV/TESTING/PRODUCTION] [MAIN PROCESS]
Step 1: .env.example -> .env
Step 2: yarn install / npm install
Step 3: yarn run build / npm run build
Step 4: yarn run export-ssr / npm run export-ssr
Step 5: yarn run serve / npm run serve

[STATIC PRODUCTION] [BACKUP-CLIENT-SIDE]
Step 1: .env.example -> .env
Step 2: yarn install / npm install
Step 3: yarn run build / npm run build
Step 4: yarn run serve-static / npm run serve-static

Then open `http://localhost:3100/` to see your app.

```
### Render Static HOME File (After run [MAIN PROCESS])

```
[STATIC HOME PAGE] [FULL DATA VIEWSOURCE]
Step 1: yarn run export-ssr / npm run export-ssr
Step 2: Sleep 5 minute, run Step 1

Now synce "_next/static/index.html" to CDN with dir `static/index.html`
Domain setup route home read to file `static/index.html` (CDN)
```

### Deployment

After `npm run build` finished, run

```
yarn serve
```

### Static CDN File

Synce all file/dir in folder "\_next"

### Now

If you prefer using `now`, just modify `now.json` config.

## Structure overview

```
├── README.md
├── next.config.js
├── package.json
├── pages
│   ├── _app.js
│   ├── _document.js
│   ├── about.js
│   └── index.js
├── routes.js
├── server
│   └── index.js
├── server-static
|   └── build
│   └── index.js
├── src
│   ├── actions
│   │   └── repos.js
│   ├── components
│   │   └── SearchResults.js
│   ├── config.js
│   ├── containers
│   │   └── SearchRepoContainer.js
│   ├── libs
│   │   └── github.js
│   ├── reducers
│   │   ├── index.js
│   │   └── repos.js
│   ├── store
│   │   └── createStore.js
│   └── test
│       ├── components
│       │   └── SearchResults.test.js
│       └── test-utils.js
└── yarn.lock
```
