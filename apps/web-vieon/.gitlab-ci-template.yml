.dockerfile: &dockerfile
  - |
    cat << EOF > Dockerfile
    ## Stage Build
    FROM $DOCKER_HUB/$DOCKER_IMAGE_NODE20_ALPINE_S3CMD as builder
    # Create working directory
    WORKDIR /usr/src/app
    # Install app dependencies
    COPY . .

    RUN node -v
    RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime
    RUN npm config set registry $NPM_REGISTRY
    RUN npm install -g pm2
    RUN npm install -g pnpm
    RUN pnpm install
    RUN pnpm run build
    RUN chmod -R 777 ./_next/_next/cache/ || echo "chmod failed"
    RUN rm -rf ./_next/_next/cache/webpack || echo "rm failed, directory may not exist"

    # Đặt lệnh upload s3 ở step cuối của docker, để sát với stage deploy nhất có thể
    # Trên node-20-alpine cần dùng ~/pyvenv/bin/s3cmd, thay vì s3cmd trên node-18-alpine trở xuống
    RUN ~/pyvenv/bin/s3cmd --host-bucket='https://hcm-vt-s3.vieon.vn' \
      --host=https://hcm-vt-s3.vieon.vn  \
      --access_key=$S3_ACCESS_KEY  \
      --secret_key=$S3_SECRET_KEY \
      --no-mime-magic --guess-mime-type \
      put -r --acl-public -f /usr/src/app/_next/ s3://$DIR_NAME/

    # Stage Runtime
    FROM $DOCKER_HUB/$DOCKER_IMAGE_NODE20_ALPINE_S3CMD
    WORKDIR /usr/src/app
    COPY --from=builder /usr/src/app/ .

    # Cài đặt lại app, lib, package cần thiết ở level global/OS cho runtime
    RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime
    # Gom chung vào 1 lệnh RUN, để giảm size của runtime image
    RUN npm config set registry $NPM_REGISTRY && \
      npm install -g pm2 && \
      npm install -g pnpm && \
      npm cache clean --force

    CMD pm2 start /usr/src/app/build/index.js -i 2 --no-daemon

    EOF

.dockerize:
  stage: build
  variables:
    TRIVY_AUTH_URL: ${DOCKER_HUB}
    TRIVY_USERNAME: ${DOCKER_HUB_USERNAME}
    TRIVY_PASSWORD: ${DOCKER_HUB_PASSWORD}
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME}
  script:
    - cat .env
    - cat Dockerfile
    - docker build -t ${IMAGE_TAG} --build-arg PLATFORM=${PLATFORM} --build-arg GITLAB_READ_TOKEN=${GITLAB_READ_TOKEN} --build-arg APP_VERSION=${IMAGE_TAG} -f ${DOCKER_FILE} .
    # - docker images
    # - docker history ${IMAGE_TAG}
    - docker push ${IMAGE_TAG}
    - docker rmi ${IMAGE_TAG} || echo "No such image"
  retry:
    max: 2
    when:
      - always

# dev (override ci-template: dockerize:master)
dockerize:dev:
  extends: .dockerize
  stage: build
  environment:
    name: dev
  before_script:
    - cat $BUILD_ENV_FILE > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  only:
    refs:
      #      - master
      #      - /^release\/*/i
      - develop

dockerize:dev-global:
  extends: .dockerize
  stage: build
  environment:
    name: dev
  variables:
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME_GLOBAL}
  before_script:
    - cat $BUILD_ENV_FILE $BUILD_ENV_FILE_GLOBAL > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  only:
    refs:
      #      - master
      #      - /^release\/*/i
      - develop

# testing:
dockerize:testing:
  extends: .dockerize
  stage: build
  environment:
    name: testing
  before_script:
    - cat $BUILD_ENV_FILE > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  #  when: manual
  only:
    refs:
      #      - master
      - /^release\/*/i

dockerize:testing-global:
  extends: .dockerize
  stage: build
  environment:
    name: testing
  variables:
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME_GLOBAL}
  before_script:
    - cat $BUILD_ENV_FILE $BUILD_ENV_FILE_GLOBAL > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  #  when: manual
  only:
    refs:
      #      - master
      - /^release\/*/i

# staging
dockerize:staging:
  extends: .dockerize
  stage: build
  environment:
    name: staging
  before_script:
    - cat $BUILD_ENV_FILE > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-staging:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual
  only:
    refs:
      - /^release\/*/i
      - rebuild

dockerize:staging-global:
  extends: .dockerize
  stage: build
  environment:
    name: staging
  variables:
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME_GLOBAL}
  before_script:
    - cat $BUILD_ENV_FILE $BUILD_ENV_FILE_GLOBAL > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-staging:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual
  only:
    refs:
      - /^release\/*/i
      - rebuild

# production (override ci-template: dockerize:release)
dockerize:release:
  extends: .dockerize
  stage: build
  environment:
    name: production
  variables:
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME}-2
  before_script:
    - cat $BUILD_ENV_FILE > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual
  only:
    refs:
      - /^release\/*/i

dockerize:release-global:
  extends: .dockerize
  stage: build
  environment:
    name: production
  variables:
    DIR_NAME: ${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME_GLOBAL}
  before_script:
    - cat $BUILD_ENV_FILE $BUILD_ENV_FILE_GLOBAL > .env
    - *dockerfile
    - DOCKER_FILE=Dockerfile
    - IMAGE_TAG=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}:${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual
  only:
    refs:
      - /^release\/*/i

# staging env
.deploy-staging:
  stage: deploy
  environment:
    name: testing
  only:
    refs:
      - /^release\/*/i
      - rebuild

.deploy-k8s:
  image:
    name: curlimages/curl:7.79.1
  stage: deploy
  script:
    - |
      curl -u ${OPERATOR_USER}:${OPERATOR_PASS} -H "Content-type: application/json" -X POST ${OPERATOR_HOST} --data "{\"env\": \"${ENVIRONMENT}\", \"project\": \"${PROJECT_NAME}\", \"image\": \"${IMAGE_NAME}\", \"tag\": \"${IMAGE_TAG}\"}"

.clear-cache-cdn:
  image:
    name: curlimages/curl:7.79.1
  script:
    - DIR_TIME=$(date +%s)
    - DIR_NAME=${CI_ENVIRONMENT_NAME}-${DOCKER_IMAGE_NAME}
    - |
      curl -X POST -F token="${CLEAR_CACHE_TOKEN}" -F "ref=main" -F "variables[CDN_DOMAIN]=static2.vieon.vn" -F "variables[CDN_PATH]=/${DIR_NAME}" "${CLEAR_CACHE_PIPELINE}"
  only:
    refs:
      - /^release\/*/i
      - develop
      - rebuild

.send-mail:
  script:
    - gitlab send ${CI_PROJECT_ID} --host=${CI_SERVER_URL} --token=${AUTO_BOT_TOKEN} --version=${CI_COMMIT_BRANCH:8} --send-to=${SEND_EMAIL_TO} --send-cc=${SEND_EMAIL_CC} --send-bcc=${SEND_EMAIL_BCC}
    - echo "gitlab send ${CI_PROJECT_ID} --host=${CI_SERVER_URL} --token=${AUTO_BOT_TOKEN} --version=${CI_COMMIT_BRANCH:8} --send-to=${SEND_EMAIL_TO} --send-cc=${SEND_EMAIL_CC} --send-bcc=${SEND_EMAIL_BCC}"

# Use create-tag from ci-template/static-web.gitlab-ci.yml
create-tag:
  stage: build
  before_script:
    - VERSION=${CI_COMMIT_BRANCH:8}
  script:
    - docker pull ${DOCKER_HUB}/git-operator:2.1.0
    - docker run --rm ${DOCKER_HUB}/git-operator:2.1.0 --host=${CI_SERVER_URL} --token=${AUTO_BOT_TOKEN} gitlab ${CI_PROJECT_ID} tag --version=${VERSION} --ref=${CI_COMMIT_SHA}
  only:
    refs:
      - /^release\/*/i
  allow_failure: true
