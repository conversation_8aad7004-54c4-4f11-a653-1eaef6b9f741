{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es7", "esnext", "dom"], "moduleResolution": "node", "resolveJsonModule": true, "removeComments": true, "preserveConstEnums": true, "strict": true, "alwaysStrict": true, "strictNullChecks": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "outDir": "build", "declaration": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "noEmit": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@vieon/core": ["../../packages/core/src"], "@vieon/core/*": ["../../packages/core/src/*"], "@vieon/ui-kits": ["../../packages/ui-kits/src"], "@vieon/ui-kits/*": ["../../packages/ui-kits/src/*"], "@vieon/auth": ["../../packages/auth/src"], "@vieon/auth/*": ["../../packages/auth/src/*"], "@vieon/tracking": ["../../packages/tracking/src"], "@vieon/tracking/*": ["../../packages/tracking/src/*"], "@vieon/models": ["../../packages/models/src"], "@vieon/models/*": ["../../packages/models/src/*"], "@vieon/player": ["../../packages/player/src"], "@vieon/player/*": ["../../packages/player/src/*"], "@vieon/payment": ["../../packages/payment/src"], "@vieon/payment/*": ["../../packages/payment/src/*"], "@vieon/ads": ["../../packages/ads/src"], "@vieon/ads/*": ["../../packages/ads/src/*"]}}, "include": ["pages", "server", "server-static", "global.d.ts"], "exclude": ["build/**/*", "node_modules/**/*", "cypress/**/*.ts", "_next/**/*"], "types": ["node", "jest", "react-datepicker", "react-select", "hls.js", "@testing-library/jest-dom", "fingerprintjs2"], "extends": "../../tsconfig.json"}