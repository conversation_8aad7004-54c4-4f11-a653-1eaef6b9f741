import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';
import TrackingApp from '@vieon/tracking/functions/TrackingApp';
import { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';
import { PAGE } from '@vieon/core/constants/constants';
import LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';
import AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';
import createStore from '@vieon/core/store/createStore';
import '@vieon/ui-kits/styles/globals.scss';
import '@vieon/ui-kits/styles/methodItem.css';
import '@vieon/ui-kits/styles/rotate.css';
import '@vieon/ui-kits/styles/style.css';
import withRedux from 'next-redux-wrapper';
import { AppProps } from 'next/app';
import { withRouter } from 'next/router';
import React, { useEffect } from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-popper-tooltip/dist/styles.css';
import { Provider } from 'react-redux';

const EXPORT_SSR =
  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'
    ? process.env.NEXT_PUBLIC_EXPORT_SSR
    : 'true';
if (!process.browser) React.useLayoutEffect = React.useEffect;

interface CustomAppProps extends AppProps {
  store: any;
  layoutProps?: any;
}

function MyApp(props: any): any {
  const { Component, store, pageProps, layoutProps, router } = props || {};

  const componentProps = { router, layoutProps, pageProps };
  const pathname = router?.pathname;
  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);
  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);

  useEffect(() => {
    const checkAndProcessCache = async () => {
      const cache = await caches.open('offline-cache-v1');
      const response = await cache.match('/tracking-data');

      if (response && response.ok) {
        const clonedResponse = response.clone();
        const trackingData = await clonedResponse.json();
        TrackingApp.offlineDetect(trackingData);
        await cache.delete('/tracking-data');
      }
    };

    checkAndProcessCache();

    window.addEventListener('online', checkAndProcessCache);

    return () => {
      window.removeEventListener('online', checkAndProcessCache);
    };
  }, []);

  if (isTPBank || isListWinners) {
    return (
      <Provider store={store}>
        <Component {...componentProps} />
      </Provider>
    );
  }

  return (
    <>
      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}
      <ReCaptchaProvider>
        <Provider store={store}>
          <LayoutContainer {...layoutProps}>
            <Component {...componentProps} />
          </LayoutContainer>
        </Provider>
      </ReCaptchaProvider>
    </>
  );
}

MyApp.getInitialProps = async ({ Component, ctx }: any) => {
  const usingSSR = EXPORT_SSR === 'true';
  if (usingSSR) {
    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };
    try {
      const layoutProps = LayoutContainer.getInitialProps
        ? await LayoutContainer.getInitialProps(ctx)
        : {};
      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
      if (pageProps && Object.keys(pageProps).length > 0) {
        return { layoutProps, pageProps };
      }
      return { layoutProps };
    } catch (error: any) {
      const { req } = ctx;
      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
    }
  }
  return { layoutProps: {} };
};
const WrappedApp = withRedux(createStore)(MyApp);
export default withRouter(WrappedApp as any);
