import React, { useEffect } from 'react';
import Head from 'next/head';
import { useDispatch, useSelector } from 'react-redux';
import { setLoading } from '@vieon/core/store/actions/app';
import { openAppMobile } from '@vieon/core/utils/common';

const DownloadApp = () => {
  const dispatch = useDispatch();
  const { appDownload } = useSelector((state: any) => state?.App?.webConfig || {});

  useEffect(() => {
    dispatch(setLoading(true));
    openAppMobile(appDownload);
    return () => {
      dispatch(setLoading(false));
    };
  }, []);

  return (
    <div>
      <Head>
        <title>Tải ứng dụng VieON | VieON</title>
      </Head>
      <p>Redirecting ...</p>
    </div>
  );
};

export default DownloadApp;
