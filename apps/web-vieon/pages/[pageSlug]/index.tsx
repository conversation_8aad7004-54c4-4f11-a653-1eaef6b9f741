import React from 'react';
import HomeContainer from '@vieon/ui-kits/components/containers/Home/HomeContainer';
import { useSelector } from 'react-redux';
import ComingSoonContainer from '@vieon/ui-kits/components/containers/ComingSoon/ComingSoonContainer';
import { MENU_TYPE } from '@vieon/core/constants/constants';

const Index = () => {
  const { activeMenu, activeSubMenu } = useSelector((state: any) => state?.Menu || {});
  if (
    activeMenu?.menuType === MENU_TYPE.SCHEDULE ||
    activeSubMenu?.menuType === MENU_TYPE.SCHEDULE
  ) {
    return <ComingSoonContainer />;
  }
  return <HomeContainer />;
};
export default Index;
