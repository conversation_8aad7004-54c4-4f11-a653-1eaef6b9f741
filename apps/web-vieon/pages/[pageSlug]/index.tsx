import React from 'react';
import HomeContainer from '@containers/Home/HomeContainer';
import { useSelector } from 'react-redux';
import ComingSoonContainer from '@containers/ComingSoon/ComingSoonContainer';
import { MENU_TYPE } from '@constants/constants';

const Index = () => {
  const { activeMenu, activeSubMenu } = useSelector((state: any) => state?.Menu || {});
  if (
    activeMenu?.menuType === MENU_TYPE.SCHEDULE ||
    activeSubMenu?.menuType === MENU_TYPE.SCHEDULE
  ) {
    return <ComingSoonContainer />;
  }
  return <HomeContainer />;
};
export default Index;
