import React from 'react';
import Document, { Head, Html, Main, NextScript, DocumentProps } from 'next/document';
import { PAGE } from '@constants/constants';
import { BUILD_ID, DOMAIN_WEB, ENABLE_SDK_GPT, STATIC_DOMAIN } from '@config/ConfigEnv';
import { GTMVieONScript } from '@tracking/TrackingGTM';

interface CustomDocumentProps extends DocumentProps {
  url?: string;
}

export default class CustomDocument extends Document<CustomDocumentProps> {
  static async getInitialProps(ctx: any) {
    const originalRenderPage = ctx.renderPage;
    ctx.renderPage = () =>
      originalRenderPage({
        // useful for wrapping the whole react tree
        enhanceApp: (App: any) => App,
        // useful for wrapping in a per-page basis
        enhanceComponent: (Component: any) => Component
      });
    const documentProps = await Document.getInitialProps(ctx);
    return {
      ...documentProps,
      url: ctx.req.url
    };
  }
  loadHead = ({ url }: any) => {
    const isTPBank = (url || '').includes(PAGE.PAYMENT_TPBANK);
    if (isTPBank) {
      return (
        <link
          rel="stylesheet"
          href={`${STATIC_DOMAIN}assets/css/vieon-in-app-tpbank.css?v=${BUILD_ID}`}
        />
      );
    }

    return (
      <>
        <link rel="stylesheet" href={`${STATIC_DOMAIN}assets/css/typography.css?v=${BUILD_ID}`} />
        <link rel="stylesheet" href={`${STATIC_DOMAIN}assets/css/app.css?v=${BUILD_ID}`} />
        <link
          data-type="vieon"
          rel="stylesheet"
          href={`${STATIC_DOMAIN}assets/css/components.css?v=${BUILD_ID}`}
        />
        <link
          data-type="vieon"
          rel="stylesheet"
          href={`${STATIC_DOMAIN}assets/css/modules.css?v=${BUILD_ID}`}
        />
        <link rel="stylesheet" href={`${STATIC_DOMAIN}assets/css/splash.css?v=${BUILD_ID}`} />
        <link rel="stylesheet" href={`${STATIC_DOMAIN}assets/css/player.css?v=${BUILD_ID}`} />
        <link rel="manifest" href={`${DOMAIN_WEB}/manifest.json?v=${BUILD_ID}`} />
        <link
          href={`${STATIC_DOMAIN}assets/img/VieON_16x16.png?v=${BUILD_ID}`}
          rel="icon"
          type="image/png"
          sizes="16x16"
        />
        <link
          href={`${STATIC_DOMAIN}assets/img/VieON_32x32.png?v=${BUILD_ID}`}
          rel="icon"
          type="image/png"
          sizes="32x32"
        />
        <link
          rel="apple-touch-icon"
          href={`${STATIC_DOMAIN}assets/img/VieON_32x32.png?v=${BUILD_ID}`}
        />
        {ENABLE_SDK_GPT === true ||
          (ENABLE_SDK_GPT === 'true' && (
            <link
              rel="preload"
              href="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
              as="script"
            />
          ))}
      </>
    );
  };
  loadScript = ({ url, isViewApp }: any) => {
    const isTPBank = (url || '').includes(PAGE.PAYMENT_TPBANK);
    const isSupportSmartTv = (url || '').includes(PAGE.PAGE_SUPPORT_SMART_TV);
    const isInAppZalopay = (url || '').includes(PAGE.ZALOPAY) ?? false;

    if (isSupportSmartTv || isViewApp) return null;
    if (isTPBank) {
      return (
        <>
          <script
            src={`${STATIC_DOMAIN}assets/js/tpbank/tpbankhandler.js?v=${BUILD_ID}`}
            type="text/javascript"
          />
          <script
            src={`${STATIC_DOMAIN}assets/js/tpbank/tpbanksdk-1.1.min.js?v=${BUILD_ID}`}
            type="text/javascript"
          />
        </>
      );
    }
    return (
      <>
        {isInAppZalopay && (
          <script
            src={`${STATIC_DOMAIN}assets/js/zalopay.js?v=${BUILD_ID}`}
            type="text/javascript"
          />
        )}
        {ENABLE_SDK_GPT === true ||
          (ENABLE_SDK_GPT === 'true' && (
            <script
              async
              src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
              type="text/javascript"
            />
          ))}
        {GTMVieONScript()}
      </>
    );
  };

  render() {
    const url = this?.props?.url;
    const isViewApp = this?.props?.__NEXT_DATA__?.query?.isViewApp;
    return (
      <Html lang="vi" className="user vieon-layout theme-dark overflow-x" id="html-head">
        <Head>{this.loadHead({ url })}</Head>
        <body className="App" id="app">
          <Main />
          <NextScript />
          {this.loadScript({ url, isViewApp })}
        </body>
      </Html>
    );
  }
}
