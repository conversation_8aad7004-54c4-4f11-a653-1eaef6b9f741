<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="373.725" height="272.132" viewBox="0 0 373.725 272.132">
  <defs>
    <clipPath id="clip-path">
      <path id="Path_3" data-name="Path 3" d="M297.123-210.39c-7.752,0-15.515.136-23.268,0-12.353-.261-20.095-12.549-14.6-23.278,3.532-6.881,10.079-8.889,17.239-9.014,11.634-.188,23.267,0,34.9-.073,6.642,0,9.582-3.137,9.455-9.642-.106-5.887-3.31-9.014-9.741-9.161s-13.04,0-19.555,0q-31.993-.073-64-.073c-5.034,0-9.624,3.326-10.343,7.236-1.058,5.5,1.174,9.213,6.494,11.691,3.247,1.506,6.589.92,9.878.857,3.691-.073,5.605,1.84,6.547,4.9s-.761,5.4-3.078,7.205c-2.041,1.569-4.548,1.569-7.054,1.569h-88.84a37.18,37.18,0,0,0-10.407,1.223c-31.728,9.077-43.362,43.921-26.5,69.845,8.556,13.187,21.977,19.691,38.011,19.869,15.684.2,31.369-.1,47.053.1a20.33,20.33,0,0,1,17.694,10.457c8.535,14.724-2.348,32.345-19.576,32.418H115.012c-6.134,0-12.258-.209-18.371.973A50.665,50.665,0,0,0,66.605-75.919C61.222-69.634,59.519-61.9,56.484-54.575c-.952,4.423.772,5.7,5.288,5.344,5.954-.471,11.972-.115,17.979-.115,2.644-5.95,7.329-8.429,13.823-8.418q83.551.073,167.1,0a29.639,29.639,0,0,0,6.779-.638c20.095-4.633,27.435-23.55,21.343-40.386-3.5-9.663-10.259-16.219-21.374-17.642a122.939,122.939,0,0,0-13.083-.92,7.146,7.146,0,0,1-7.319-6.212c-.709-3.681,1.639-5.8,5.288-6.525a21.8,21.8,0,0,1,4.082-.826c13.358,0,26.694-2.008,40.041-.983,18.18,1.4,36.932-14.536,39.8-32.418C339.967-188.085,321.977-210.579,297.123-210.39ZM179.654-174.03c-3.807.481-7.72.1-13.336.1-3.31-.209-8.461.648-13.432-.816-2.961-.868-5.394-2.353-5.288-5.521a5.79,5.79,0,0,1,1.73-4.3,5.927,5.927,0,0,1,4.351-1.7q13.749-.3,27.413,0c3.617.073,5.288,2.98,5.288,6.055C186.317-176.331,183.081-174.459,179.654-174.03Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_2" data-name="Path 2" d="M-789,451H1131V-629H-789Z" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="Path_6" data-name="Path 6" d="M64.769-170.684H214.192L192.846-91.105H83.089Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <path id="Path_12" data-name="Path 12" d="M127.791-131.9c0,1.8-1.047,3.262-2.338,3.262s-2.338-1.461-2.338-3.262,1.047-3.262,2.338-3.262,2.338,1.46,2.338,3.262" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <path id="Path_15" data-name="Path 15" d="M156.253-131.9c0,1.8-1.047,3.262-2.338,3.262s-2.338-1.461-2.338-3.262,1.047-3.262,2.338-3.262,2.338,1.46,2.338,3.262" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <path id="Path_20" data-name="Path 20" d="M175.087-47.763H69.721a22.666,22.666,0,0,1-16.077-6.659A22.666,22.666,0,0,1,46.984-70.5a22.662,22.662,0,0,1,6.405-15.819,22.681,22.681,0,0,1,13.075-6.686L48.528-174.38q-.091-.412-.143-.823L44-194.132h-31.9a9.208,9.208,0,0,1-6.531-2.705,9.208,9.208,0,0,1-2.705-6.531A9.208,9.208,0,0,1,5.574-209.9a9.208,9.208,0,0,1,6.531-2.705H50.549q.294,0,.583.018a9.233,9.233,0,0,1,5.85,1.9,9.2,9.2,0,0,1,3.388,5.256l4.591,19.828H222.695a9.208,9.208,0,0,1,6.531,2.705,9.208,9.208,0,0,1,2.705,6.532,9.24,9.24,0,0,1-.361,2.556L205.376-82.865a9.223,9.223,0,0,1-3.321,4.824,9.224,9.224,0,0,1-5.555,1.857H69.739a5.666,5.666,0,0,0-4.019,1.665A5.666,5.666,0,0,0,64.055-70.5a5.666,5.666,0,0,0,1.665,4.019,5.666,5.666,0,0,0,4.019,1.665H196.413a8.5,8.5,0,0,1,6.029,2.5,8.5,8.5,0,0,1,2.5,6.029,8.5,8.5,0,0,1-2.5,6.029,8.5,8.5,0,0,1-6.029,2.5ZM87.843-93.237H188.192a3.547,3.547,0,0,0,2.157-.73,3.547,3.547,0,0,0,1.271-1.89l18.562-68.21a3.555,3.555,0,0,0,.125-.933,3.542,3.542,0,0,0-1.04-2.512,3.541,3.541,0,0,0-2.512-1.04H71.932a3.548,3.548,0,0,0-.807.093,3.541,3.541,0,0,0-2.21,1.584,3.541,3.541,0,0,0-.443,2.683L84.384-95.982a3.547,3.547,0,0,0,1.251,1.976A3.547,3.547,0,0,0,87.843-93.237Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <path id="Path_23" data-name="Path 23" d="M-37-252.474H271.8V-7.895H-37ZM69.068-91.1,50.61-174.839a7.191,7.191,0,0,1-.122-.734L45.7-196.263H12.105A7.105,7.105,0,0,1,5-203.368a7.105,7.105,0,0,1,7.105-7.105H50.549q.275,0,.545.021a7.108,7.108,0,0,1,7.2,5.5h0l4.969,21.48H222.695a7.105,7.105,0,0,1,7.105,7.105,7.107,7.107,0,0,1-.277,1.966L203.327-83.455a7.105,7.105,0,0,1-6.828,5.139H69.739A7.816,7.816,0,0,0,61.923-70.5a7.816,7.816,0,0,0,7.816,7.816H196.413a6.4,6.4,0,0,1,6.395,6.395,6.4,6.4,0,0,1-6.395,6.395H69.721A20.605,20.605,0,0,1,49.115-70.5,20.606,20.606,0,0,1,69.068-91.1Zm18.775-.01H188.192a5.684,5.684,0,0,0,5.485-4.192l18.562-68.211a5.684,5.684,0,0,0,.2-1.493,5.684,5.684,0,0,0-5.684-5.684H71.932a5.687,5.687,0,0,0-1.291.149,5.684,5.684,0,0,0-4.244,6.827L82.308-95.5A5.684,5.684,0,0,0,87.843-91.105Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-13">
      <path id="Path_26" data-name="Path 26" d="M91.808-3A19.909,19.909,0,0,1,71.885-22.895,19.909,19.909,0,0,1,91.808-42.789a19.909,19.909,0,0,1,19.923,19.895A19.909,19.909,0,0,1,91.808-3Zm0-12.789a7.11,7.11,0,0,0,7.115-7.105A7.11,7.11,0,0,0,91.808-30a7.11,7.11,0,0,0-7.115,7.105A7.11,7.11,0,0,0,91.808-15.789Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-15">
      <path id="Path_31" data-name="Path 31" d="M168.654-3a19.909,19.909,0,0,1-19.923-19.895,19.909,19.909,0,0,1,19.923-19.895,19.909,19.909,0,0,1,19.923,19.895A19.909,19.909,0,0,1,168.654-3Zm0-12.789a7.11,7.11,0,0,0,7.115-7.105A7.11,7.11,0,0,0,168.654-30a7.11,7.11,0,0,0-7.115,7.105A7.11,7.11,0,0,0,168.654-15.789Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-17">
      <path id="Path_36" data-name="Path 36" d="M115.288-226.105a2.133,2.133,0,0,0,2.135-2.132,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,115.288-226.105Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-19">
      <path id="Path_39" data-name="Path 39" d="M115.288-203.368a2.133,2.133,0,0,0,2.135-2.132,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,115.288-203.368Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-21">
      <path id="Path_42" data-name="Path 42" d="M103.9-214.737a2.133,2.133,0,0,0,2.135-2.132A2.133,2.133,0,0,0,103.9-219a2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,103.9-214.737Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-23">
      <path id="Path_45" data-name="Path 45" d="M126.673-214.737a2.133,2.133,0,0,0,2.135-2.132A2.133,2.133,0,0,0,126.673-219a2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,126.673-214.737Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-25">
      <path id="Path_48" data-name="Path 48" d="M237.673-268.737a2.133,2.133,0,0,0,2.135-2.132A2.133,2.133,0,0,0,237.673-273a2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,237.673-268.737Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-27">
      <path id="Path_51" data-name="Path 51" d="M244.788-264.474a2.133,2.133,0,0,0,2.135-2.132,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,244.788-264.474Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-29">
      <path id="Path_54" data-name="Path 54" d="M230.558-264.474a2.133,2.133,0,0,0,2.135-2.132,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,230.558-264.474Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-31">
      <path id="Path_57" data-name="Path 57" d="M230.558-255.947a2.133,2.133,0,0,0,2.135-2.131,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,230.558-255.947Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-33">
      <path id="Path_60" data-name="Path 60" d="M244.788-255.947a2.133,2.133,0,0,0,2.135-2.131,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,244.788-255.947Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip-path-35">
      <path id="Path_63" data-name="Path 63" d="M237.673-250.263a2.133,2.133,0,0,0,2.135-2.132,2.133,2.133,0,0,0-2.135-2.132,2.133,2.133,0,0,0-2.135,2.132A2.133,2.133,0,0,0,237.673-250.263Z" fill="none" clip-rule="evenodd"/>
    </clipPath>
  </defs>
  <g id="transaction-history" transform="translate(-752 -356)">
    <g id="Group_2" data-name="Group 2" transform="translate(789 629)" clip-path="url(#clip-path)">
      <g id="Group_1" data-name="Group 1" clip-path="url(#clip-path-2)">
        <path id="Path_1" data-name="Path 1" d="M51.231-266.632H341.725V-44.175H51.231Z" fill="rgba(58,199,129,0.25)"/>
      </g>
    </g>
    <g id="Group_4" data-name="Group 4" transform="translate(789 629)" clip-path="url(#clip-path-3)">
      <g id="Group_3" data-name="Group 3" clip-path="url(#clip-path-2)">
        <path id="Path_4" data-name="Path 4" d="M59.769-175.684H219.192v89.579H59.769Z" fill="#fff"/>
      </g>
    </g>
    <g id="Group_5" data-name="Group 5" transform="translate(909.269 485.316)">
      <path id="Path_7" data-name="Path 7" d="M0,2.987C2.742,4.692,6.088,3.356,7.481,0" fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.968"/>
    </g>
    <g id="Group_6" data-name="Group 6" transform="translate(940.577 485.316)">
      <path id="Path_8" data-name="Path 8" d="M0,0A5.5,5.5,0,0,0,7.481,3.205" fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.968"/>
    </g>
    <g id="Group_9" data-name="Group 9" transform="translate(789 629)" clip-path="url(#clip-path-5)">
      <g id="Group_8" data-name="Group 8" clip-path="url(#clip-path-2)">
        <path id="Path_10" data-name="Path 10" d="M118.115-140.158h14.676v16.524H118.115Z" fill="#333"/>
      </g>
    </g>
    <g id="Group_11" data-name="Group 11" transform="translate(789 629)" clip-path="url(#clip-path-7)">
      <g id="Group_10" data-name="Group 10" clip-path="url(#clip-path-2)">
        <path id="Path_13" data-name="Path 13" d="M146.577-140.158h14.676v16.524H146.577Z" fill="#333"/>
      </g>
    </g>
    <g id="Group_12" data-name="Group 12" transform="translate(923.5 506.632)">
      <path id="Path_16" data-name="Path 16" d="M0,1.652A6.432,6.432,0,0,0,5.126,4.66c2.527,0,4.756-1.846,6.1-4.66" fill="none" stroke="#202020" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.968"/>
    </g>
    <g id="Group_15" data-name="Group 15" transform="translate(789 629)" clip-path="url(#clip-path-9)">
      <g id="Group_14" data-name="Group 14" clip-path="url(#clip-path-2)">
        <path id="Path_18" data-name="Path 18" d="M0-215.474H234.8V-44.895H0Z" fill="#a6f3a6"/>
      </g>
    </g>
    <g id="Group_18" data-name="Group 18" transform="translate(789 629)" clip-path="url(#clip-path-11)">
      <g id="Group_17" data-name="Group 17" clip-path="url(#clip-path-2)">
        <g id="Group_16" data-name="Group 16" transform="translate(5 -210.474)">
          <path id="Path_21" data-name="Path 21" d="M64.068,119.379,45.61,35.635a7.191,7.191,0,0,1-.122-.734L40.7,14.211H7.105A7.105,7.105,0,0,1,7.105,0H45.549q.275,0,.545.021a7.108,7.108,0,0,1,7.2,5.5h0L58.267,27H217.695a7.105,7.105,0,0,1,6.828,9.072l-26.195,90.947a7.105,7.105,0,0,1-6.828,5.139H64.739a7.816,7.816,0,0,0,0,15.632H191.413a6.395,6.395,0,1,1,0,12.789H64.721a20.605,20.605,0,0,1-.653-41.2Zm18.775-.01H183.192a5.684,5.684,0,0,0,5.485-4.192l18.562-68.211a5.684,5.684,0,0,0-5.485-7.177H66.932A5.684,5.684,0,0,0,61.4,46.765l15.911,68.211A5.684,5.684,0,0,0,82.843,119.368Z" fill="none" stroke="#676767" stroke-width="8.526"/>
        </g>
      </g>
    </g>
    <g id="Group_20" data-name="Group 20" transform="translate(789 629)" clip-path="url(#clip-path-13)">
      <g id="Group_19" data-name="Group 19" clip-path="url(#clip-path-2)">
        <path id="Path_24" data-name="Path 24" d="M66.885-47.789h49.846V2H66.885Z" fill="#a6f3a6"/>
      </g>
    </g>
    <g id="Group_21" data-name="Group 21" transform="translate(860.885 586.211)">
      <path id="Path_27" data-name="Path 27" d="M19.923,39.789A19.895,19.895,0,1,1,39.846,19.895,19.909,19.909,0,0,1,19.923,39.789Zm0-12.789a7.105,7.105,0,1,0-7.115-7.105A7.11,7.11,0,0,0,19.923,27Z" fill="none" stroke="#676767" stroke-width="4.263"/>
    </g>
    <g id="Group_24" data-name="Group 24" transform="translate(789 629)" clip-path="url(#clip-path-15)">
      <g id="Group_23" data-name="Group 23" clip-path="url(#clip-path-2)">
        <path id="Path_29" data-name="Path 29" d="M143.731-47.789h49.846V2H143.731Z" fill="#a6f3a6"/>
      </g>
    </g>
    <g id="Group_25" data-name="Group 25" transform="translate(937.731 586.211)">
      <path id="Path_32" data-name="Path 32" d="M19.923,39.789A19.895,19.895,0,1,1,39.846,19.895,19.909,19.909,0,0,1,19.923,39.789Zm0-12.789a7.105,7.105,0,1,0-7.115-7.105A7.11,7.11,0,0,0,19.923,27Z" fill="none" stroke="#676767" stroke-width="4.263"/>
    </g>
    <g id="Group_28" data-name="Group 28" transform="translate(789 629)" clip-path="url(#clip-path-17)">
      <g id="Group_27" data-name="Group 27" clip-path="url(#clip-path-2)">
        <path id="Path_34" data-name="Path 34" d="M108.154-235.368h14.269v14.263H108.154Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_30" data-name="Group 30" transform="translate(789 629)" clip-path="url(#clip-path-19)">
      <g id="Group_29" data-name="Group 29" clip-path="url(#clip-path-2)">
        <path id="Path_37" data-name="Path 37" d="M108.154-212.632h14.269v14.263H108.154Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_32" data-name="Group 32" transform="translate(789 629)" clip-path="url(#clip-path-21)">
      <g id="Group_31" data-name="Group 31" clip-path="url(#clip-path-2)">
        <path id="Path_40" data-name="Path 40" d="M96.769-224h14.269v14.263H96.769Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_34" data-name="Group 34" transform="translate(789 629)" clip-path="url(#clip-path-23)">
      <g id="Group_33" data-name="Group 33" clip-path="url(#clip-path-2)">
        <path id="Path_43" data-name="Path 43" d="M119.538-224h14.269v14.263H119.538Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_36" data-name="Group 36" transform="translate(789 629)" clip-path="url(#clip-path-25)">
      <g id="Group_35" data-name="Group 35" clip-path="url(#clip-path-2)">
        <path id="Path_46" data-name="Path 46" d="M230.538-278h14.269v14.263H230.538Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_38" data-name="Group 38" transform="translate(789 629)" clip-path="url(#clip-path-27)">
      <g id="Group_37" data-name="Group 37" clip-path="url(#clip-path-2)">
        <path id="Path_49" data-name="Path 49" d="M237.654-273.737h14.269v14.263H237.654Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_40" data-name="Group 40" transform="translate(789 629)" clip-path="url(#clip-path-29)">
      <g id="Group_39" data-name="Group 39" clip-path="url(#clip-path-2)">
        <path id="Path_52" data-name="Path 52" d="M223.423-273.737h14.269v14.263H223.423Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_42" data-name="Group 42" transform="translate(789 629)" clip-path="url(#clip-path-31)">
      <g id="Group_41" data-name="Group 41" clip-path="url(#clip-path-2)">
        <path id="Path_55" data-name="Path 55" d="M223.423-265.211h14.269v14.263H223.423Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_44" data-name="Group 44" transform="translate(789 629)" clip-path="url(#clip-path-33)">
      <g id="Group_43" data-name="Group 43" clip-path="url(#clip-path-2)">
        <path id="Path_58" data-name="Path 58" d="M237.654-265.211h14.269v14.263H237.654Z" fill="#d7d7d7"/>
      </g>
    </g>
    <g id="Group_46" data-name="Group 46" transform="translate(789 629)" clip-path="url(#clip-path-35)">
      <g id="Group_45" data-name="Group 45" clip-path="url(#clip-path-2)">
        <path id="Path_61" data-name="Path 61" d="M230.538-259.526h14.269v14.263H230.538Z" fill="#d7d7d7"/>
      </g>
    </g>
  </g>
</svg>
