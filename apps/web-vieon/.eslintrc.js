module.exports = {
  root: true,
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:prettier/recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'prettier'],
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
      modules: true
    },
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx']
      }
    }
  },
  rules: {
    'import/no-cycle': 'off',
    'import/no-named-as-default': 'off',
    'import/no-named-as-default-member': 'off',
    'import/default': 'off',
    'import/no-unresolved': 'off',
    'operator-linebreak': 'off',
    'no-param-reassign': 'off',
    'implicit-arrow-linebreak': 'off',
    'max-len': 'off',
    indent: 'off',
    'no-shadow': 'off',
    'arrow-parens': 'off',
    'no-confusing-arrow': 'off',
    'no-use-before-define': 'off',
    'react/destructuring-assignment': 'off',
    'react/function-component-definition': 'off',
    'object-curly-newline': [
      'off',
      {
        ObjectExpression: [
          'warn',
          {
            multiline: true,
            minProperties: 1
          }
        ]
      }
    ],
    'function-paren-newline': 'off',
    'import/prefer-default-export': 'off',
    'max-classes-per-file': 'off',
    'react/jsx-filename-extension': 'off',
    'import/extensions': 'off',
    '@typescript-eslint/ban-ts-ignore': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-use-before-define': 'off',
    'no-useless-constructor': 'off',
    '@typescript-eslint/no-useless-constructor': 'error',
    'react/prop-types': 'off',
    'react/button-has-type': 'off',
    'consistent-return': 'off',
    'react/no-array-index-key': 'off',
    'react/jsx-props-no-spreading': 'off',
    'react/no-danger': 'off',
    'no-undef': 'off',
    'no-underscore-dangle': 'off',
    camelcase: 'off',
    'new-cap': 'off',
    'no-nested-ternary': 'off',
    'no-return-assign': 'off',
    'react/jsx-one-expression-per-line': 'off',
    radix: 'off',
    'prefer-const': 'off',
    'comma-dangle': [
      'error',
      {
        arrays: 'never',
        objects: 'never',
        imports: 'never',
        exports: 'never',
        functions: 'never'
      }
    ],
    'array-callback-return': 'off',
    'no-console': 'off',
    'no-restricted-syntax': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unnecessary-type-constraint': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-require-imports': 'off',
    'no-unsafe-optional-chaining': 'off',
    'react/display-name': 'off',
    'react/style-prop-object': 'error'
  }
};
