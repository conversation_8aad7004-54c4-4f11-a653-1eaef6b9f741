// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './'
});

// Add any custom config to be passed to Jest
/** @type {import('jest').Config} */
const customJestConfig = {
  // Add more setup options before each test is run
  // setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  // if using TypeScript with a baseUrl set to the root directory then you need the below for alias' to work
  moduleDirectories: ['node_modules', '<rootDir>/'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    '^@actions/(.*)$': '<rootDir>/src/actions/$1',
    '^@apis/(.*)$': '<rootDir>/src/apis/$1',
    '^@reducers/(.*)$': '<rootDir>/src/reducers/$1',
    '^@script/(.*)$': '<rootDir>/src/script/$1',
    '^@store/(.*)$': '<rootDir>/src/store/$1',
    '^@styles/(.*)$': '<rootDir>/src/styles/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@config/(.*)$': '<rootDir>/src/config/$1',
    '^@constants/(.*)$': '<rootDir>/src/constants/$1',
    '^@containers/(.*)$': '<rootDir>/src/containers/$1',
    '^@functions/(.*)$': '<rootDir>/src/functions/$1',
    '^@helpers/(.*)$': '<rootDir>/src/helpers/$1',
    '^@models/(.*)$': '<rootDir>/src/models/$1',
    '^@profile/(.*)$': '<rootDir>/src/profile/$1',
    '^@services/(.*)$': '<rootDir>/src/services/$1',
    '^@tracking/(.*)$': '<rootDir>/src/tracking/$1',
    '^@test/(.*)$': '<rootDir>/src/test/$1',
    '^@customHook': '<rootDir>/src/customHook'
  }
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
