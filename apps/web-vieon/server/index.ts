import https from 'https';
import fs from 'fs';
import path from 'path';
import express, { Request, Response, NextFunction } from 'express';
import compression from 'compression';
import next from 'next';
import cookieParser from 'cookie-parser';
import { loadEnvConfig } from '@next/env';
import pattern from './routing/pattern';
import paths from './routing/path';
// Load environment variables
const projectDir = process.cwd();
loadEnvConfig(projectDir);
declare const require: any; // Temporary workaround for dynamic requires

const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3100;
const dev = process.env.NEXT_PUBLIC_NODE_ENV !== 'production';

// Initialize Next.js app
const app = next({ dev });
const handle :any = app.getRequestHandler();

const PATTERN_LIST_410 = [pattern.P_410.CATEGORY, pattern.P_410.COLLECTION, pattern.P_410.TAG, pattern.P_410.VOD, pattern.P_410.VOD_EPS, pattern.P_410.CHAPTER_LIST, pattern.P_410.RECOMMENDED, pattern.P_410.VOD_REL];
const PATTERN_LIST_DUPLICATE = [pattern.PAGE_MENU, pattern.PAGE_TAG, pattern.PAGE_RIBBON];

app.prepare()
    .then(() => {
        const server = express();
        server.use(cookieParser());
        server.use(compression());

        const {staticPath} = paths;
        server.use(express.static(staticPath, {
            maxAge: "30d", immutable: true
        }));

        // only local
        if (dev) {
            server.get("*.map", (req: any, res: any) => {
                return res.sendStatus(404);
            });
            server.get("*/assets/*", (req: any, res: any) => {
                return res.sendStatus(404);
            });
            server.get("*/fonts/*", (req: any, res: any) => {
                return res.sendStatus(404);
            });
        }

        server.get('/_next/pages/:file', (req: any, res: any) => {
            const filePath = path.join(__dirname, '../_next/_next/pages', req.params.file);
            if (fs.existsSync(filePath)) {
              return res.sendFile(filePath);
            }
            return res.sendStatus(404);
        });

        server.get('/fonts/vie-font/:file', (req: any, res: any) => {
            const filePath = path.join(__dirname, '../_next/assets/fonts/vie-font', req.params.file);
            if (fs.existsSync(filePath)) {
              return res.sendFile(filePath);
            }
            return res.sendStatus(404);
        });



        server.post("/events", (req: any, res: any) => {
            return res.send("success");
        });

        server.get("/readiness", (req: any, res: any) => {
            return res.send("ok");
        });

        server.get("/:slug--rel-:rel.html", (req: any, res: any) => {
            return app.render(req, res, `/[slug].html`, {...req.params, ...req.query});
        });

        server.get("/:slug--eps-:eps.html", (req: any, res: any) => {
            return app.render(req, res, `/[slug].html`, {...req.params, ...req.query});
        });

        server.get("/:slug.html", (req: any, res: any) => {
            return app.render(req, res, `/[slug].html`, {...req.params, ...req.query});
        });

        PATTERN_LIST_DUPLICATE.forEach(item => {
            server.get(item, (req: any, res: any) => {
                return handle(req, res);
            });
        });

        PATTERN_LIST_410.forEach(item => {
            server.get(item, (req: any, res: any) => {
                if (req.params.category !== "truyen-hinh-truc-tuyen" && req.params.category !== "smart-tv" && req.params.category !== "in-app") {
                    res.status(410);
                    return app.render(req, res, "/page-410", req.params);
                } else {
                    return handle(req, res);
                }
            });
        });

        // Handle all other routes with Next.js
        server.all('*', (req: Request, res: Response) => {
            return handle(req, res);
        });

        const startServer = (): void => {
            server.listen(port, () => {
                console.log(`> Server Ready on http://localhost:${port}`);
            });
        };

        if (dev) {
            // For development with HTTPS
            const certOptions = {
                key: fs.readFileSync(path.join(__dirname, '../certificate/local.vieon.vn-key.pem')),
                cert: fs.readFileSync(path.join(__dirname, '../certificate/local.vieon.vn.pem'))
            };

            const httpsServer = https.createServer(certOptions, server);
            httpsServer.listen(443, (err?: Error) => {
                if (err) {
                    console.error('Failed to start HTTPS server:', err);
                    process.exit(1);
                }
                console.log('=> Server Ready on https://local.vieon.vn');
            });
        } else {
            // For production
            startServer();
        }
    }).catch((err: any) => {
    console.error("Error during server setup:", err);
    process.exit(1);
});
