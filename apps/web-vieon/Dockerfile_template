FROM $DOCKER_HUB/base-alpine-nodejs-s3cmd:3
# Create working directory
WORKDIR /usr/src/app
# Install app dependencies
COPY . .

RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime
RUN npm install
RUN npm run build

RUN s3cmd --host-bucket='https://hcm-vt-s3.vieon.vn' \
--host=https://hcm-vt-s3.vieon.vn  \
--access_key=$S3_ACCESS_KEY  \
--secret_key=$S3_SECRET_KEY \
--no-mime-magic --guess-mime-type \
put -r --acl-public -f /usr/src/app/_next/ s3://$DIR_NAME/
RUN npm install -g pm2

CMD pm2 start /usr/src/app/server/index.js -i 3 --no-daemon
