const path = require('path')
const express = require('express')
const compression = require('compression')
const app = express();
const DEFAULT_PORT = parseInt(process.env.PORT || '3100', 10)
const staticPath = path.join(__dirname, '../_next')

app.set('views', __dirname + '/build');
app.engine('html', require('ejs').renderFile);

app.use(compression())
app.use(express.static(staticPath, {
    maxAge: '30d',
    immutable: true
}));
app.get('/:category/:genre/:slug/tap-:episode', (req: any, res: any) => {
    return res.render('[category]/[genre]/[slug]/[episode].html')
})

app.get('/:category/:genre/:slug', (req: any, res: any) => {
    return res.render('[category]/[genre]/[slug].html')
})
app.get('/', (req: any, res: any) => {
    return res.render('index.html')
})

app.get('*', (req: any, res: any) => {
    return res.render('_error.html')
})
app.listen(DEFAULT_PORT, () => {
    // console.log(`>Server static Ready on http://localhost:${DEFAULT_PORT} `)
  })