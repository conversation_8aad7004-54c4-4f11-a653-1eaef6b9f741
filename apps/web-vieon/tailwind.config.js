/** @type {import('tailwindcss').Config} */
// const plugin = require('tailwindcss/plugin');
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}'
  ],
  blocklist: [
    'container',
    'visible',
    'collapse',
    'static',
    'sticky',
    'bottom-1',
    'bottom-2',
    'bottom-3',
    'left-1',
    'left-2',
    'right-1',
    'right-2',
    'top-1',
    'top-2',
    '-order-2',
    'mb-2',
    'inline',
    'table',
    'contents',
    'animate-spin',
    'break-all',
    'pr-2',
    'text-right',
    'align-bottom',
    'blur',
    'filter',
    '.ease-out'
  ],
  theme: {
    extend: {
      fontFamily: {
        Roboto: 'Roboto',
        Password: 'password'
      },
      screens: {
        xmd: '820px',
        '2xl': '1440px',
        '3xl': '1688px',
        '4xl': '1920px',
        'mobile-375': '375px'
      },
      boxShadow: {
        modalContainer: '0px 4px 24px 0px rgba(0, 0, 0, 0.35);'
      },
      colors: {
        'vo-green': {
          100: '#E9F9EA',
          DEFAULT: '#3ac882'
        },
        'vo-gray': {
          50: '#F2F2F2',
          100: '#E6E6E6',
          150: '#D9D9D9',
          200: '#CCCCCC',
          250: '#BFBFBF',
          300: '#B3B3B3',
          350: '#A6A6A6',
          400: '#999999',
          450: '#8D8D8D',
          DEFAULT: '#808080',
          550: '#737373',
          600: '#666666',
          650: '#5A5A5A',
          700: '#4D4D4D',
          750: '#404040',
          800: '#333333',
          850: '#262626',
          900: '#1A1A1A',
          950: '#0D0D0D'
          // t60: '#ccc'
        },
        'vo-dark-gray': {
          50: '#F6F6F6',
          100: '#EEEEEE',
          150: '#E5E5E5',
          200: '#DDDDDD',
          250: '#D4D4D4',
          300: '#CBCBCB',
          350: '#C3C3C3',
          400: '#BABABA',
          450: '#B2B2B2',
          DEFAULT: '#A9A9A9',
          550: '#989898',
          600: '#878787',
          650: '#767676',
          700: '#656565',
          750: '#555555',
          800: '#444444',
          850: '#333333',
          900: '#222222',
          950: '#111111'
        },
        'vo-dim-gray': {
          900: '#151515'
        }
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: 1 }
        },
        'fade-out': {
          '0%': { opacity: 1 },
          '100%': { opacity: 0 }
        },
        slideGradient: {
          '0%': { backgroundPosition: '0% 50%' },
          '100%': { backgroundPosition: '200% 50%' }
        },
        'scale-up': {
          '0%': { transform: 'scale(0)' },
          '100%': { transform: 'scale(1)' }
        },
        'scale-down': {
          '0%': { transform: 'scale(1)' },
          '100%': { transform: 'scale(0)' }
        }
      },
      animation: {
        'fade-in': 'fade-in .5s linear',
        'fade-out': 'fade-out .5s linear',
        'gradient-slide': 'slideGradient 3s ease-in-out infinite',
        'scale-up': 'scale-up 1s ease-in-out',
        'scale-up-delay': 'scale-up 1s ease-in-out 0.7s',
        'scale-down': 'scale-up 1s ease-in-out 0.4s'
      },
      backgroundImage: {
        // 'gradient-radial':
        //   'radial-gradient(var(--tw-gradient-shape) var(--tw-gradient-size) at var(--tw-gradient-position), var(--tw-gradient-stops))'
        'custom-radial':
          'radial-gradient(50% 50% at 50% 0%, #3AC882 0%, rgba(58, 200, 130, 0) 100%, rgba(58, 200, 130, 0) 100%)',
        'custom-radial-1':
          'radial-gradient(50% 75% at 50% 75%, #35E890 0%, rgba(56, 224, 141, 0.00) 100%)'
      }
    }
  },
  plugins: [
    ({ addUtilities, matchUtilities, theme }) => {
      const newUtilities = {
        '.hide-scrollbar': {
          overflowX: 'auto',
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      };
      addUtilities(newUtilities, ['responsive', 'hover']);
      matchUtilities(
        {
          keyframes: (value) => ({
            keyframes: value
          }),
          animation: (value) => ({
            animation: value
          })
        },
        { values: theme('keyframes', 'animation') }
      );
    },
    require('tailwind-scrollbar')
  ]
};
