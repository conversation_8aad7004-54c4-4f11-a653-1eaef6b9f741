include:
  - project: 'devops/ci/templates/ci-template'
    ref: latest
    file: 'single-image.gitlab-ci.yml'
  - local: '.gitlab-ci-template.yml'

stages:
  - test
  - build
  - deploy
  - clear-cache-cdn
  - release
  - promote

variables:
  DOCKER_IMAGE_NAME: vieon-web-v5
  DOCKER_IMAGE_NAME_GLOBAL: vieon-web-global
  PROJECT_NAME_GLOBAL: web-global

#Unit Test
test:unit-test:
  stage: test
  image: ${DOCKER_HUB}/${DOCKER_IMAGE_NODE20_ALPINE_S3CMD}
  before_script:
    - apk update && apk add git
  script:
    - node -v
  artifacts:
    paths:
      - coverage/lcov.info
  only:
    refs:
      - merge_requests
      - master
      - develop

deploy:dev:
  extends:
    - .deploy-k8s
    - .deploy-development
  stage: deploy
  before_script:
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  only:
    refs:
      #      - master
      #      - /^release\/*/i
      - develop
deploy:dev-global:
  extends:
    - .deploy-k8s
    - .deploy-development
  stage: deploy
  before_script:
    - ENVIRONMENT=develop
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${PROJECT_NAME_GLOBAL} # deploy lên global chỉ khác local ở PROJECT_NAME
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  only:
    refs:
      #      - master
      #      - /^release\/*/i
      - develop

deploy:testing:
  extends:
    - .deploy-k8s
    - .deploy-testing
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  #  when: manual
  only:
    refs:
      - /^release\/*/i

deploy:testing-global:
  extends:
    - .deploy-k8s
    - .deploy-testing
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${PROJECT_NAME_GLOBAL} # deploy lên global chỉ khác local ở PROJECT_NAME
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  #  when: manual
  only:
    refs:
      - /^release\/*/i

deploy:staging:
  extends:
    - .deploy-k8s
    - .deploy-testing
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-staging
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual

deploy:staging-global:
  extends:
    - .deploy-k8s
    - .deploy-testing
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${PROJECT_NAME_GLOBAL} # deploy lên global chỉ khác local ở PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-staging
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  when: manual

deploy:production:
  extends:
    - .deploy-k8s
    - .deploy-production
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  after_script:
    - !reference [.send-mail, script]

deploy:production-global:
  extends:
    - .deploy-k8s
    - .deploy-production
  stage: deploy
  before_script:
    - ENVIRONMENT=${CI_ENVIRONMENT_NAME}
    - PROJECT_NAME=${CI_PROJECT_NAMESPACE}/${PROJECT_NAME_GLOBAL} # deploy lên global chỉ khác local ở PROJECT_NAME}
    - IMAGE_NAME=${DOCKER_HUB}/${DOCKER_IMAGE_NAME_GLOBAL}-${CI_ENVIRONMENT_NAME}
    - IMAGE_TAG=${CI_COMMIT_REF_NAME##*/}-${CI_COMMIT_SHORT_SHA}
  after_script:
    - !reference [.send-mail, script]

clear-cache-cdn-dev:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=dev
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
  only:
    refs:
      - develop

clear-cache-cdn-dev-global:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=dev
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}-global
  only:
    refs:
      - develop

clear-cache-cdn-testing:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=testing
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
  when: manual
  only:
    refs:
      - /^release\/*/i

clear-cache-cdn-testing-global:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=testing
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME_GLOBAL}
  when: manual
  only:
    refs:
      - /^release\/*/i

clear-cache-cdn-staging:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=staging
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}
  when: manual
  only:
    refs:
      - /^release\/*/i

clear-cache-cdn-staging-global:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=staging
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME_GLOBAL}
  when: manual
  only:
    refs:
      - /^release\/*/i

clear-cache-cdn-production:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=production
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME}-2
  when: manual
  only:
    refs:
      - /^release\/*/i

clear-cache-cdn-production-global:
  extends:
    - .clear-cache-cdn
  stage: clear-cache-cdn
  before_script:
    - CI_ENVIRONMENT_NAME=production
    - DOCKER_IMAGE_NAME=${DOCKER_IMAGE_NAME_GLOBAL}
  when: manual
  only:
    refs:
      - /^release\/*/i
