# Introduction

This document describes the workflow of source code contribution to this repository.

_The is a sample workflow defined based on [Gitflow](https://nvie.com/posts/a-successful-git-branching-model/) workflow. The `maintainer` (tech-lead) of the repository is supposed to review carefully and make amendment to fit particular situation/condition of the project._

# Glossary

- Contributor: <PERSON><PERSON><PERSON>, who has access and contribute source code to the repository

- Maintainer: A contributor with `maintainer` or higher role

- <PERSON><PERSON><PERSON>: A contributor with `developer` or higher role

- Software/Application: Output from build process of the source code, in this document, "software" and "application" are used interchangly.

- Remote repository: A central reposity (Gitlab server) which is accessible by all contributors

- Local repository: Local repository on contributor's machine

# Ethernal branches

## `master`

- Default branch of the repository

- Always reflect a **production-ready** source code

- Only maintainer can push to `master` branch

- Only merge commit, no direct commit (always use `--no-ff` option when merging)

## `develop`

- When the repository is initialized, `develop` branch is branched off from `master` branch.

- `develop` branch is used to integrate all the developed features that are ready for the next release.

- Source code on `develop` branch must be compilable and meet quality standard, which is described in [a later section](#code_quality).

- Only maintainer can push to `develop` branch.

- Only merge commit, no direct commit (always use `--no-ff` option when merging)

- Source code on `develop` branch will be built and deployed to `DEV` environment for integrating with other components in the system.

# Supporting branches

## `Feature` branches

- `Feature` branches are branched off from `develop` branch to develop a new feature or fix a bug.

- Naming convention:

  - `feature/feature-name` for developing a new feature
    - Use dash (`-`) character to separate words in feature name.
    - Use splash character (`/`) to separate sub-feature. E.g `feature/user-management/delete-user`
    - Do not use upper-characters
    - Using JIRA ticket number as a `feature-name` is not recommended as JIRA ticket number can be changed.
  - `fix/issue-name` for fixing a bug
    - Use dash (`-`) character to separate words in issue name.
    - Do not use upper-characters
    - Using JIRA ticket number as a `issue-name` is not recommended as JIRA ticket number can be changed.

- Developer should push `feature` branch to remote every day, even it has not been completed.

- When development of the feature or bug fix is completed, developer should submit a merge request to merge the `feature` branch to `develop` branch.

- The `feature` branch, which is requested to be merged must be compilable, has no conflict with `develop` branch and meets quality standard, which is described in [a later section](#code_quality). If the `feature` branch conflicts with `develop` branch, developer can merge `develop` branch to `feature` branch, resolve all the conflicts before requesting to merge to `develop` branch again.

- During developing the `feature` branch, developer can merge `develop` branch to `feature` branch to get updated source code from other developers.

- When there's a merge request, the maintainer reviews the `feature` branch before merging to `develop`. `feature` branches are always merged to `develop` with `--no-ff` option, merge commit is always created even there's a fast-forward merge.

- After being merged to `develop` branch, the `feature` branch should be deleted.

## `Release` branches<a id ='release_branches'></a>

- `release` branches are branched off from `develop` branch to prepare for new release.

- Naming convention: `release/version-name`. E.g `release/1.0.0`

  - Do not use upper-characters

- There is no new feature development on `release` branches, only bug fixing and meta-data updating (e.g. version number in build file)

- Source code on `release` branch will be built and deployed to `TESTING` and `STAGING` environments for QA testing.

- When the source code meets production standard, all `TESTING`/`STAGING` test cases are passed, it will be merged to `master` branch for `PRODUCTION` deployment. Finally, it has to be merged back to `develop` branch so that `develop` branch also contains bug fixes and meta-data update. (_During preparing for release, `release` branch can also be merged to `develop` to have the bug fixes early_)

- After a `release` branch is merged to `master` branch for production deployment, a tag according to release version has to be created on `master` branch.

- The `release` branch should be deleted after it has been merged to `master` and `develop`.

## `Hotfix` branches

- `hotfix` branches are branched off from `master` branch or the tag according to a release version to immediately solve issue on production environment.

- Naming convention: `hotfix/issue-name`
  - Use dash (`-`) character to separate words in bug name.
  - Do not use upper-characters
  - Using JIRA ticket number as a `issue-name` is not recommended as JIRA ticket number can be changed.
- When the bug fixing is finished, `hotfix` branch has to be merged back to `master` for `PRODUCTION` deployment. A tag according to version is to be created on `master` after merging.

- Finally, `hotfix` branch must also be merged to `develop` so that the `develop` branch also contains the bug-fix.

- The `hotfix` branch should be deleted after it has been merged to `master` and `develop`.

# Release process

- To prepare for a release, a `release` branch is created from `develop` branch. Refer [`release` branch](#release_branches).

- During preparing for the release, the maintainer build source code from `release` branch and deploy to `TESTING`/`STAGING` for QA testing.

- After merging `feature` branch to `master` branch. The maintainer builds the source code from `master` branch, upload to shared-drive and request for `PRODUCTION` deployment

- Deployment request is done by filling into "deployment tracking sheet" (_TODO: provide Google-sheet link_). Deployment request has to include:

  - Version name (number)
  - Release note
  - Any additional action to do, script to execute, before or after deployment, compare to a normal deployment process

- Maintainer sends an email to notify devops, PM and QA about new application version availability. Devops replies the email to update delopment statuses.
