"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[1]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[2]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[9].use[3]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[9].use[4]!../../packages/ui-kits/src/styles/globals.scss":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[1]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[2]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[9].use[3]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[9].use[4]!../../packages/ui-kits/src/styles/globals.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_pnpm_next_12_3_4_babel_core_7_27_4_babel_plugin_macros_3_1_0_react_dom_18_2_0_react_18_2_0_react_18_2_0_sass_1_89_2_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_pnpm_next_12_3_4_babel_core_7_27_4_babel_plugin_macros_3_1_0_react_dom_18_2_0_react_18_2_0_react_18_2_0_sass_1_89_2_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_pnpm_next_12_3_4_babel_core_7_27_4_babel_plugin_macros_3_1_0_react_dom_18_2_0_react_18_2_0_react_18_2_0_sass_1_89_2_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_pnpm_next_12_3_4_babel_core_7_27_4_babel_plugin_macros_3_1_0_react_dom_18_2_0_react_18_2_0_react_18_2_0_sass_1_89_2_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@import url(\\\"https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap\\\");\\n*, ::before, ::after{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n::backdrop{\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*/\\n/*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\\n  tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n/*\\nRemove the default font size and weight for headings.\\n*/\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n/*\\nAdd the correct font size in all browsers.\\n*/\\nsmall {\\n  font-size: 80%;\\n}\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\nsub {\\n  bottom: -0.25em;\\n}\\nsup {\\n  top: -0.5em;\\n}\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n:-moz-focusring {\\n  outline: auto;\\n}\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\nprogress {\\n  vertical-align: baseline;\\n}\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\nsummary {\\n  display: list-item;\\n}\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\nlegend {\\n  padding: 0;\\n}\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\ntextarea {\\n  resize: vertical;\\n}\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n/*\\nSet the default cursor for buttons.\\n*/\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n*{\\n  scrollbar-color: initial;\\n  scrollbar-width: initial;\\n}\\nimg {\\n    display: inline-block;\\n  }\\n.\\\\!py-4{\\n  padding-top: 1rem !important;\\n  padding-bottom: 1rem !important;\\n}\\n/* Hide scrollbar for Chrome, Safari and Opera */\\n/* Hide scrollbar for IE, Edge and Firefox */\\n/* Import Roboto from Google Fonts */\\n/* roboto-100 - latin_vietnamese */\\n/* Using Google Fonts instead of local files */\\n/* All Roboto fonts are now loaded from Google Fonts above */\\n/* Password font - using fallback for now */\\n@font-face {\\n  font-family: \\\"password\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  src: url(\\\"/assets/fonts/password.ttf\\\"), url(\\\"data:font/truetype;charset=utf-8;base64,\\\") format(\\\"truetype\\\");\\n}\\n@font-face {\\n  font-family: \\\"vie\\\";\\n  src: url(\\\"/fonts/vie-font/vie.ttf?viefv617\\\") format(\\\"truetype\\\"), url(\\\"/fonts/vie-font/vie.woff?viefv617\\\") format(\\\"woff\\\"), url(\\\"/fonts/vie-font/vie.svg?viefv617#vie\\\") format(\\\"svg\\\");\\n  font-weight: normal;\\n  font-style: normal;\\n}\\nbody{\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 17 17 / var(--tw-bg-opacity, 1));\\n  font-family: Roboto;\\n}\\n\\ntextarea{\\n  margin-bottom: 1rem;\\n  padding: 0.5rem;\\n}\\n\\n/* Update CSS React-DatePicker */\\n.react-datepicker__triangle {\\n  left: 21% !important;\\n  fill: #595959 !important;\\n  color: #595959 !important;\\n  stroke: unset !important;\\n}\\n\\n.react-datepicker__input-container {\\n  border-bottom: 1px solid #646464;\\n}\\n\\n.react-datepicker__month-container {\\n  position: relative;\\n  z-index: 10;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header {\\n  background-color: #595959;\\n  border: none;\\n  position: unset;\\n  padding-left: 8px;\\n  padding-right: 8px;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header .react-datepicker__current-month {\\n  padding-bottom: 4px;\\n  font-weight: 500;\\n}\\n\\n.react-datepicker__header .react-datepicker__day-name,\\n.react-datepicker__week .react-datepicker__day {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 14px;\\n  line-height: 34px;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__month {\\n  background-color: #222;\\n  margin: 0;\\n}\\n\\n.react-datepicker__week .react-datepicker__day:hover {\\n  background-color: #383838;\\n  transition: background-color 0.2s ease-in-out;\\n}\\n\\n.react-datepicker-popper .react-datepicker {\\n  border: none;\\n  font-family: Roboto;\\n  border-radius: 8px;\\n  display: block;\\n}\\n\\n.react-datepicker .react-datepicker__navigation-icon::before {\\n  width: 10px;\\n  height: 10px;\\n  border-color: #ffffff;\\n}\\n\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--selected {\\n  background-color: transparent;\\n  border: 1px solid #3ac882;\\n  border-radius: 4px;\\n  color: #3ac882;\\n}\\n\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--keyboard-selected,\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--today {\\n  background-color: #3ac882;\\n  border: 1px solid #3ac882;\\n  border-radius: 4px;\\n  color: #ffffff !important;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header div,\\n.react-datepicker__month-container .react-datepicker__month div {\\n  color: #ffffff;\\n}\\n.react-datepicker__month-container .react-datepicker__header div [aria-disabled=true],\\n.react-datepicker__month-container .react-datepicker__month div [aria-disabled=true] {\\n  color: #404040;\\n}\\n\\n@media screen and (max-width: 1440px) {\\n  .react-datepicker__week .react-datepicker__day,\\n  .react-datepicker__header .react-datepicker__day-name {\\n    width: 32px;\\n    height: 32px;\\n    line-height: 26px;\\n  }\\n  .react-datepicker__month-container .react-datepicker__header {\\n    padding-bottom: 0;\\n    padding-top: 2px;\\n  }\\n  .react-datepicker__month-container .react-datepicker__day-names {\\n    height: 28px;\\n    display: flex;\\n    align-items: center;\\n    margin-bottom: 0;\\n    position: relative;\\n    z-index: 0;\\n  }\\n}\\n@media screen and (max-width: 1280px) {\\n  .react-datepicker__week .react-datepicker__day,\\n  .react-datepicker__header .react-datepicker__day-name {\\n    width: 28px;\\n    height: 28px;\\n    line-height: 28px;\\n  }\\n}\\n/* End Update CSS React-DatePicker */\\n#pause-player-ads {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n/* Custom scrollbar styles */\\n.custom-scrollbar {\\n  scrollbar-width: thin; /* For Firefox */\\n  scrollbar-color: #888 #f1f1f1; /* For Firefox */\\n  padding-right: 5px;\\n  box-sizing: content-box;\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar {\\n  width: 2px; /* Width of the scrollbar */\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-track {\\n  background: #f1f1f1; /* Background of the scrollbar track */\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-thumb {\\n  background-color: #888; /* Color of the scrollbar thumb */\\n  border-radius: 10px; /* Roundness of the scrollbar thumb */\\n  border: 3px;\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\\n  background: #555; /* Color of the scrollbar thumb on hover */\\n}\\n\\n.modal-limit-region > :not([hidden]) ~ :not([hidden]){\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(20px * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(20px * var(--tw-space-y-reverse));\\n}\\n\\n.grecaptcha-badge{\\n  display: none;\\n}\\n\\n@media (min-width: 768px){\\n  .md\\\\:\\\\!py-6{\\n    padding-top: 1.5rem !important;\\n    padding-bottom: 1.5rem !important;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://../../packages/ui-kits/src/styles/_font-roboto.scss\",\"webpack://../../packages/ui-kits/src/styles/globals.scss\"],\"names\":[],\"mappings\":\"AACQ,6JAAA;AAAA;EAAA,wBAAA;EAAA,wBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,cAAA;EAAA,cAAA;EAAA,cAAA;EAAA,eAAA;EAAA,eAAA;EAAA,aAAA;EAAA,aAAA;EAAA,kBAAA;EAAA,sCAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,eAAA;EAAA,oBAAA;EAAA,sBAAA;EAAA,uBAAA;EAAA,wBAAA;EAAA,kBAAA;EAAA,2BAAA;EAAA,4BAAA;EAAA,sCAAA;EAAA,kCAAA;EAAA,2BAAA;EAAA,sBAAA;EAAA,8BAAA;EAAA,YAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,iBAAA;EAAA,kBAAA;EAAA,cAAA;EAAA,gBAAA;EAAA,aAAA;EAAA,mBAAA;EAAA,qBAAA;EAAA,2BAAA;EAAA,yBAAA;EAAA,0BAAA;EAAA,2BAAA;EAAA,uBAAA;EAAA,wBAAA;EAAA,yBAAA;EAAA,sBAAA;EAAA,oBAAA;EAAA,sBAAA;EAAA,qBAAA;EAAA;AAAA;AAAA;EAAA,wBAAA;EAAA,wBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,cAAA;EAAA,cAAA;EAAA,cAAA;EAAA,eAAA;EAAA,eAAA;EAAA,aAAA;EAAA,aAAA;EAAA,kBAAA;EAAA,sCAAA;EAAA,8BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,eAAA;EAAA,oBAAA;EAAA,sBAAA;EAAA,uBAAA;EAAA,wBAAA;EAAA,kBAAA;EAAA,2BAAA;EAAA,4BAAA;EAAA,sCAAA;EAAA,kCAAA;EAAA,2BAAA;EAAA,sBAAA;EAAA,8BAAA;EAAA,YAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,iBAAA;EAAA,kBAAA;EAAA,cAAA;EAAA,gBAAA;EAAA,aAAA;EAAA,mBAAA;EAAA,qBAAA;EAAA,2BAAA;EAAA,yBAAA;EAAA,0BAAA;EAAA,2BAAA;EAAA,uBAAA;EAAA,wBAAA;EAAA,yBAAA;EAAA,sBAAA;EAAA,oBAAA;EAAA,sBAAA;EAAA,qBAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;;CAAA;AAAA;;;EAAA,sBAAA,EAAA,MAAA;EAAA,eAAA,EAAA,MAAA;EAAA,mBAAA,EAAA,MAAA;EAAA,qBAAA,EAAA,MAAA;AAAA;AAAA;;EAAA,gBAAA;AAAA;AAAA;;;;;;;;CAAA;AAAA;;EAAA,gBAAA,EAAA,MAAA;EAAA,8BAAA,EAAA,MAAA,EAAA,MAAA;EAAA,WAAA,EAAA,MAAA;EAAA,+HAAA,EAAA,MAAA;EAAA,6BAAA,EAAA,MAAA;EAAA,+BAAA,EAAA,MAAA;EAAA,wCAAA,EAAA,MAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA,SAAA,EAAA,MAAA;EAAA,oBAAA,EAAA,MAAA;AAAA;AAAA;;;;CAAA;AAAA;EAAA,SAAA,EAAA,MAAA;EAAA,cAAA,EAAA,MAAA;EAAA,qBAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,yCAAA;UAAA,iCAAA;AAAA;AAAA;;CAAA;AAAA;;;;;;EAAA,kBAAA;EAAA,oBAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,cAAA;EAAA,wBAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,mBAAA;AAAA;AAAA;;;;;CAAA;AAAA;;;;EAAA,+GAAA,EAAA,MAAA;EAAA,6BAAA,EAAA,MAAA;EAAA,+BAAA,EAAA,MAAA;EAAA,cAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,cAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,cAAA;EAAA,cAAA;EAAA,kBAAA;EAAA,wBAAA;AAAA;AAAA;EAAA,eAAA;AAAA;AAAA;EAAA,WAAA;AAAA;AAAA;;;;CAAA;AAAA;EAAA,cAAA,EAAA,MAAA;EAAA,qBAAA,EAAA,MAAA;EAAA,yBAAA,EAAA,MAAA;AAAA;AAAA;;;;CAAA;AAAA;;;;;EAAA,oBAAA,EAAA,MAAA;EAAA,8BAAA,EAAA,MAAA;EAAA,gCAAA,EAAA,MAAA;EAAA,eAAA,EAAA,MAAA;EAAA,oBAAA,EAAA,MAAA;EAAA,oBAAA,EAAA,MAAA;EAAA,uBAAA,EAAA,MAAA;EAAA,cAAA,EAAA,MAAA;EAAA,SAAA,EAAA,MAAA;EAAA,UAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,oBAAA;AAAA;AAAA;;;CAAA;AAAA;;;;EAAA,0BAAA,EAAA,MAAA;EAAA,6BAAA,EAAA,MAAA;EAAA,sBAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,aAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,gBAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,wBAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,YAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA,6BAAA,EAAA,MAAA;EAAA,oBAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,wBAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA,0BAAA,EAAA,MAAA;EAAA,aAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,kBAAA;AAAA;AAAA;;CAAA;AAAA;;;;;;;;;;;;;EAAA,SAAA;AAAA;AAAA;EAAA,SAAA;EAAA,UAAA;AAAA;AAAA;EAAA,UAAA;AAAA;AAAA;;;EAAA,gBAAA;EAAA,SAAA;EAAA,UAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,UAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,gBAAA;AAAA;AAAA;;;CAAA;AAAA;;EAAA,UAAA,EAAA,MAAA;EAAA,cAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,eAAA;AAAA;AAAA;;CAAA;AAAA;EAAA,eAAA;AAAA;AAAA;;;;CAAA;AAAA;;;;;;;;EAAA,cAAA,EAAA,MAAA;EAAA,sBAAA,EAAA,MAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA,eAAA;EAAA,YAAA;AAAA;AAAA,wEAAA;AAAA;EAAA,aAAA;AAAA;AAAA;EAAA,wBAAA;EAAA;AAAA;AAAA;IAAA,qBAAA;EAAA;ACAR;EAAA,4BAAA;EAAA;AAAA;AAaA,gDAAA;AAIE,4CAAA;ADlBF,oCAAA;AAGA,kCAAA;AACA,8CAAA;AACA,4DAAA;AACA,2CAAA;AACA;EACE,uBAAA;EACA,kBAAA;EACA,gBAAA;EACA,0GAAA;ACGF;ADXA;EACA,kBAAA;EACA,mLAAA;EAGE,mBAAA;EACA,kBAAA;ACWF;AAME;EAAA,kBAAA;EAAA,yDAAA;EAAA;AAAA;;AAWF;EAAA,mBAAA;EAAA;AAAA;;AAGA,gCAAA;AACA;EALA,oBAAA;EACE,wBAAA;EAOF,yBAAA;EACE,wBAAA;AAOF;;AAJA;EAKE,gCAAA;AAGF;;AAAA;EACE,kBAAA;EALF,WAAA;AAaA;;AAJA;EALA,yBAAA;EACE,YAAA;EACA,eAAA;EAOF,iBAAA;EACE,kBAAA;AAOF;;AAJA;EALE,mBAAA;EACA,gBAAA;AAaF;;AAJA;;EAJE,WAAA;EACA,YAAA;EAOF,eAAA;EACE,iBAAA;AAOF;;AAJA;EALE,sBAAA;EACA,SAAA;AAaF;;AAJA;EALA,yBAAA;EACE,6CAAA;AAaF;;AAJA;EALA,YAAA;EACE,mBAAA;EACA,kBAAA;EAOF,cAAA;AAOA;;AAJA;EALE,WAAA;EACA,YAAA;EACA,qBAAA;AAaF;;AAVA;EACE,6BAAA;EACA,yBAAA;EACA,kBAAA;EAOF,cAAA;AAOA;;AAJA;;EAJE,yBAAA;EACA,yBAAA;EAOF,kBAAA;EACE,yBAAA;AAOF;;AAJA;;EAJE,cAAA;AAaF;AALA;;EACI,cAAA;AAQJ;;AAJA;EALE;;IACE,WAAA;IAQJ,YAAA;IACI,iBAAA;EAOF;EAXA;IAOE,iBAAA;IALA,gBAAA;EAaF;EALA;IACA,YAAA;IALA,aAAA;IACE,mBAAA;IACA,gBAAA;IAOF,kBAAA;IALA,UAAA;EAaA;AACF;AAJA;EACE;;IAEA,WAAA;IACF,YAAA;IAJA,iBAAA;EAWE;AACF;AAHA,oCAAA;AAQA;EACA,aAAA;EADA,uBAAA;EACE,mBAAA;AAKF;;AADA,4BAAA;AACA;EACE,qBAAA,EAAA,gBAAA;EAFF,6BAAA,EAAA,gBAAA;EACA,kBAAA;EACE,uBAAA;AAOF;;AADA;EACA,UAAA,EAAA,2BAAA;AAIA;;AADA;EACA,mBAAA,EAAA,sCAAA;AAIA;;AADA;EACA,sBAAA,EAAA,iCAAA;EACE,mBAAA,EAAA,qCAAA;EAFF,WAAA;AAOA;;AADA;EACA,gBAAA,EAAA,0CAAA;AAIA;;AAAA;EAAA,uBAAA;EAAA,4DAAA;EAAA;AAAA;;AAIA;EAAA;AAAA;;AD1MQ;EAAA;IAAA,8BAAA;IAAA;EAAA;AAAA\",\"sourcesContent\":[\"/* Import Roboto from Google Fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');\\n\\n/* roboto-100 - latin_vietnamese */\\n/* Using Google Fonts instead of local files */\\n/* All Roboto fonts are now loaded from Google Fonts above */\\n/* Password font - using fallback for now */\\n@font-face {\\n  font-family: 'password';\\n  font-style: normal;\\n  font-weight: 400;\\n  src: url('/assets/fonts/password.ttf'), url('data:font/truetype;charset=utf-8;base64,') format('truetype');\\n}\\n\",\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n@import './_font-roboto';\\n\\n@font-face {\\n  font-family: 'vie';\\n  src: url('/fonts/vie-font/vie.ttf?viefv617') format('truetype'),\\n    url('/fonts/vie-font/vie.woff?viefv617') format('woff'),\\n    url('/fonts/vie-font/vie.svg?viefv617#vie') format('svg');\\n  font-weight: normal;\\n  font-style: normal;\\n}\\n\\n@layer utilities {\\n  .custom-gradient {\\n    @apply bg-custom-radial;\\n  }\\n  .custom-gradient-1 {\\n    @apply bg-custom-radial-1;\\n  }\\n  /* Hide scrollbar for Chrome, Safari and Opera */\\n  .no-scrollbar::-webkit-scrollbar {\\n    display: none;\\n  }\\n  /* Hide scrollbar for IE, Edge and Firefox */\\n  .no-scrollbar {\\n    -ms-overflow-style: none; /* IE and Edge */\\n    scrollbar-width: none; /* Firefox */\\n  }\\n}\\n\\nbody {\\n  @apply bg-[#111] font-Roboto;\\n}\\n\\ntextarea {\\n  @apply mb-4 p-2;\\n}\\n\\n/* Update CSS React-DatePicker */\\n.react-datepicker__triangle {\\n  left: 21% !important;\\n  fill: #595959 !important;\\n  color: #595959 !important;\\n  stroke: unset !important;\\n}\\n\\n.react-datepicker__input-container {\\n  border-bottom: 1px solid #646464;\\n}\\n\\n.react-datepicker__input-container {\\n  border-bottom: 1px solid #646464;\\n}\\n\\n.react-datepicker__month-container {\\n  position: relative;\\n  z-index: 10;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header {\\n  background-color: #595959;\\n  border: none;\\n  position: unset;\\n  padding-left: 8px;\\n  padding-right: 8px;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header .react-datepicker__current-month {\\n  padding-bottom: 4px;\\n  font-weight: 500;\\n}\\n\\n.react-datepicker__header .react-datepicker__day-name,\\n.react-datepicker__week .react-datepicker__day {\\n  width: 36px;\\n  height: 36px;\\n  font-size: 14px;\\n  line-height: 34px;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__month {\\n  background-color: #222;\\n  margin: 0;\\n}\\n\\n.react-datepicker__week .react-datepicker__day:hover {\\n  background-color: #383838;\\n  transition: background-color 0.2s ease-in-out;\\n}\\n\\n.react-datepicker-popper .react-datepicker {\\n  border: none;\\n  font-family: Roboto;\\n  border-radius: 8px;\\n  display: block;\\n}\\n\\n.react-datepicker .react-datepicker__navigation-icon::before {\\n  width: 10px;\\n  height: 10px;\\n  border-color: #ffffff;\\n}\\n\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--selected {\\n  background-color: transparent;\\n  border: 1px solid #3ac882;\\n  border-radius: 4px;\\n  color: #3ac882;\\n}\\n\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--keyboard-selected,\\n.react-datepicker__week .react-datepicker__day.react-datepicker__day--today {\\n  background-color: #3ac882;\\n  border: 1px solid #3ac882;\\n  border-radius: 4px;\\n  color: #ffffff !important;\\n}\\n\\n.react-datepicker__month-container .react-datepicker__header div,\\n.react-datepicker__month-container .react-datepicker__month div {\\n  color: #ffffff;\\n\\n  [aria-disabled='true'] {\\n    color: #404040;\\n  }\\n}\\n\\n@media screen and (max-width: 1440px) {\\n  .react-datepicker__week .react-datepicker__day,\\n  .react-datepicker__header .react-datepicker__day-name {\\n    width: 32px;\\n    height: 32px;\\n    line-height: 26px;\\n  }\\n  .react-datepicker__month-container .react-datepicker__header {\\n    padding-bottom: 0;\\n    padding-top: 2px;\\n  }\\n  .react-datepicker__month-container .react-datepicker__day-names {\\n    height: 28px;\\n    display: flex;\\n    align-items: center;\\n    margin-bottom: 0;\\n    position: relative;\\n    z-index: 0;\\n  }\\n}\\n\\n@media screen and (max-width: 1280px) {\\n  .react-datepicker__week .react-datepicker__day,\\n  .react-datepicker__header .react-datepicker__day-name {\\n    width: 28px;\\n    height: 28px;\\n    line-height: 28px;\\n  }\\n}\\n\\n/* End Update CSS React-DatePicker */\\n\\n@layer base {\\n  img {\\n    display: inline-block;\\n  }\\n}\\n\\n#pause-player-ads {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n/* Custom scrollbar styles */\\n.custom-scrollbar {\\n  scrollbar-width: thin; /* For Firefox */\\n  scrollbar-color: #888 #f1f1f1; /* For Firefox */\\n  padding-right: 5px;\\n  box-sizing: content-box;\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar {\\n  width: 2px; /* Width of the scrollbar */\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-track {\\n  background: #f1f1f1; /* Background of the scrollbar track */\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-thumb {\\n  background-color: #888; /* Color of the scrollbar thumb */\\n  border-radius: 10px; /* Roundness of the scrollbar thumb */\\n  border: 3px;\\n}\\n\\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\\n  background: #555; /* Color of the scrollbar thumb on hover */\\n}\\n\\n.modal-limit-region {\\n  @apply space-y-[20px];\\n}\\n\\n.grecaptcha-badge {\\n  @apply hidden;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[1]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[2].oneOf[9].use[2]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[2].oneOf[9].use[3]!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[2].oneOf[9].use[4]!../../packages/ui-kits/src/styles/globals.scss\n"));

/***/ })

});