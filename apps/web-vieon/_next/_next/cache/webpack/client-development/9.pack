wpc   �� �webpack/lib/cache/PackFileCacheStrategy�PackContentItems�]  ResolverCachePlugin|normal|default|fallback=[|assert=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/assert/assert.js|buffer=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/buffer/index.js|constants=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/constants-browserify/constants.json|crypto=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/crypto-browserify/index.js|domain=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/domain-browser/index.js|http=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/stream-http/index.js|https=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/https-browserify/index.js|os=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/os-browserify/browser.js|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/path-browserify/index.js|punycode=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/punycode/punycode.js|process=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/polyfills/process.js|querystring=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/querystring-es3/index.js|stream=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/stream-browserify/index.js|string_decoder=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/string_decoder/string_decoder.js|sys=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/util/util.js|timers=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/timers-browserify/main.js|tty=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/tty-browserify/index.js|util=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/util/util.js|vm=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/vm-browserify/index.js|zlib=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/browserify-zlib/index.js|events=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/events/events.js|setImmediate=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/setimmediate/setImmediate.js|]|dependencyType=|esm|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/core/utils/script/AAdsNetwork�  Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/@next/react-refresh-utils/dist/loader.js!/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use[1]!/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx|main�   Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx|main�webpack/lib/cache/ResolverCachePlugin��`�_ResolverCachePluginCacheMiss�context�path�request�query�fragment�module�directory�file�internal�fullySpecified�descriptionFilePath�descriptionFileData�descriptionFileRoot�relativePath�__innerRequest_request�__innerRequest_relativePath�__innerRequest�issuer�issuerLayer�compiler�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�client�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx�� �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/package.json
�name�version�description�main�types�scripts�dependencies�devDependencies�peerDependencies�files�@vieon/core�1.0.0�VieON core utilities and shared code�dist/index.js�dist/index.d.ts�build�dev�clean�type-check�lint�lint:fix�tsc�tsc --watch�rimraf dist�tsc --noEmit�eslint src --ext .ts,.tsx�eslint src --ext .ts,.tsx --fix�@vieon/models�axios�redux�redux-thunk�immer�lodash�moment�crypto-js�workspace:*�1.7.8�4.2.1�2.4.2�10.0.3�4.17.21�2.29.4�4.2.0�@types/lodash�@types/crypto-js�typescript�rimraf�^4.17.17�^4.2.2�^5.8.3�^3.0.2�react�18.2.0�dist�src�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core�./src/utils/script/AAdsNetwork.tsx���webpack/lib/FileSystemInfo�Snapshot@�     �#�zyB`�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json���/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages�/Users/<USER>/Desktop/Project/VieON/web-mono-repo�/Users/<USER>/Desktop/Project/VieON�/Users/<USER>/Desktop/Project�/Users/<USER>/Desktop�/Users/<USER>/Users�/�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils�safeTime�timestamp! �zyB  �zyB�! �?szyB �?szyB�!  ҫzyB �ѫzyB� � � � � � � � � � � � `�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.mjs�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.ts�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.jsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.wasm�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.mjs�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.js�    �sources�runtimeRequirements�data�hash�javascript�webpack/lib/util/registerExternalSerializer�webpack-sources/CachedSource   n�  �webpack/lib/util/registerExternalSerializer�webpack-sources/ConcatSource��webpack/lib/util/registerExternalSerializer�webpack-sources/RawSource�  __webpack_require__.r(__webpack_exports__);
/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_async_to_generator.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread_props.mjs");
/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ "../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ "../../packages/provider/src/providerRecaptcha.tsx");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ "../../packages/tracking/src/services/functions/TrackingApp.ts");
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ "../../packages/core/src/config/ConfigEnv.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ "../../packages/core/src/constants/constants.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx");
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ "../../packages/core/src/utils/script/AAdsNetwork.tsx");
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ "../../packages/core/src/store/createStore.ts");
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ "../../packages/ui-kits/src/styles/globals.scss");
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ "../../packages/ui-kits/src/styles/methodItem.css");
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ "../../packages/ui-kits/src/styles/rotate.css");
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ "../../packages/ui-kits/src/styles/style.css");
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ "../../node_modules/.pnpm/next-redux-wrapper@4.0.1_next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react_16ad1338e2a4d36018f83f499cb8265f/node_modules/next-redux-wrapper/es6/index.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/router.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css");
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css");
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ "../../node_modules/.pnpm/react-redux@7.2.9_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-redux/es/index.js");
/* provided dependency */ var process = __webpack_require__(/*! process */ "../../node_modules/.pnpm/process@0.11.10/node_modules/process/browser.js");
�webpack/lib/util/registerExternalSerializer�webpack-sources/ReplaceSource�webpack/lib/util/registerExternalSerializer�webpack-sources/SourceMapSource�5  import _async_to_generator from "@swc/helpers/src/_async_to_generator.mjs";
import _object_spread from "@swc/helpers/src/_object_spread.mjs";
import _object_spread_props from "@swc/helpers/src/_object_spread_props.mjs";
import _ts_generator from "@swc/helpers/src/_ts_generator.mjs";
import { jsxDEV as _jsxDEV, Fragment as _Fragment } from "react/jsx-dev-runtime";
var _s = $RefreshSig$();
import ReCaptchaProvider from "@vieon/provider/providerRecaptcha";
import TrackingApp from "@vieon/tracking/services/functions/TrackingApp";
import { ENABLE_SDK_AIACTIV } from "@vieon/core/config/ConfigEnv";
import { PAGE } from "@vieon/core/constants/constants";
import LayoutContainer from "@vieon/ui-kits/components/containers/LayoutContainer";
import AAdsNetwork from "@vieon/core/utils/script/AAdsNetwork";
import createStore from "@vieon/core/store/createStore";
import "@vieon/ui-kits/styles/globals.scss";
import "@vieon/ui-kits/styles/methodItem.css";
import "@vieon/ui-kits/styles/rotate.css";
import "@vieon/ui-kits/styles/style.css";
import withRedux from "next-redux-wrapper";
import { withRouter } from "next/router";
import React, { useEffect } from "react";
import "react-datepicker/dist/react-datepicker.css";
import "react-popper-tooltip/dist/styles.css";
import { Provider } from "react-redux";
var EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (!process.browser) React.useLayoutEffect = React.useEffect;
function MyApp(props) {
    _s();
    var ref = props || {}, Component = ref.Component, store = ref.store, pageProps = ref.pageProps, layoutProps = ref.layoutProps, router = ref.router;
    var componentProps = {
        router: router,
        layoutProps: layoutProps,
        pageProps: pageProps
    };
    var pathname = router === null || router === void 0 ? void 0 : router.pathname;
    var isTPBank = (pathname || "").includes(PAGE.PAYMENT_TPBANK);
    var isListWinners = (pathname || "").includes(PAGE.LIST_WINNERS);
    useEffect(function() {
        var checkAndProcessCache = function() {
            var _ref = _async_to_generator(function() {
                var cache, response, clonedResponse, trackingData;
                return _ts_generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            return [
                                4,
                                caches.open("offline-cache-v1")
                            ];
                        case 1:
                            cache = _state.sent();
                            return [
                                4,
                                cache.match("/tracking-data")
                            ];
                        case 2:
                            response = _state.sent();
                            if (!(response && response.ok)) return [
                                3,
                                5
                            ];
                            clonedResponse = response.clone();
                            return [
                                4,
                                clonedResponse.json()
                            ];
                        case 3:
                            trackingData = _state.sent();
                            TrackingApp.offlineDetect(trackingData);
                            return [
                                4,
                                cache.delete("/tracking-data")
                            ];
                        case 4:
                            _state.sent();
                            _state.label = 5;
                        case 5:
                            return [
                                2
                            ];
                    }
                });
            });
            return function checkAndProcessCache() {
                return _ref.apply(this, arguments);
            };
        }();
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return function() {
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ _jsxDEV(Provider, {
            store: store,
            children: /*#__PURE__*/ _jsxDEV(Component, _object_spread({}, componentProps), void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ _jsxDEV(_Fragment, {
        children: [
            ENABLE_SDK_AIACTIV && /*#__PURE__*/ _jsxDEV(AAdsNetwork, {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ _jsxDEV(ReCaptchaProvider, {
                children: /*#__PURE__*/ _jsxDEV(Provider, {
                    store: store,
                    children: /*#__PURE__*/ _jsxDEV(LayoutContainer, _object_spread_props(_object_spread({}, layoutProps), {
                        children: /*#__PURE__*/ _jsxDEV(Component, _object_spread({}, componentProps), void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }), void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MyApp, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = MyApp;
MyApp.getInitialProps = function() {
    var _ref = _async_to_generator(function(param) {
        var Component, ctx, usingSSR, layoutProps, _tmp, pageProps, _tmp1, error, req;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    Component = param.Component, ctx = param.ctx;
                    usingSSR = EXPORT_SSR === "true";
                    if (!usingSSR) return [
                        3,
                        9
                    ];
                    if (((ctx === null || ctx === void 0 ? void 0 : ctx.asPath) || "").includes(PAGE.PAYMENT_TPBANK)) return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
                    _state.label = 1;
                case 1:
                    _state.trys.push([
                        1,
                        8,
                        ,
                        9
                    ]);
                    if (!LayoutContainer.getInitialProps) return [
                        3,
                        3
                    ];
                    return [
                        4,
                        LayoutContainer.getInitialProps(ctx)
                    ];
                case 2:
                    _tmp = _state.sent();
                    return [
                        3,
                        4
                    ];
                case 3:
                    _tmp = {};
                    _state.label = 4;
                case 4:
                    layoutProps = _tmp;
                    if (!Component.getInitialProps) return [
                        3,
                        6
                    ];
                    return [
                        4,
                        Component.getInitialProps(ctx)
                    ];
                case 5:
                    _tmp1 = _state.sent();
                    return [
                        3,
                        7
                    ];
                case 6:
                    _tmp1 = {};
                    _state.label = 7;
                case 7:
                    pageProps = _tmp1;
                    if (pageProps && Object.keys(pageProps).length > 0) {
                        return [
                            2,
                            {
                                layoutProps: layoutProps,
                                pageProps: pageProps
                            }
                        ];
                    }
                    return [
                        2,
                        {
                            layoutProps: layoutProps
                        }
                    ];
                case 8:
                    error = _state.sent();
                    req = ctx.req;
                    console.log("Request ".concat(req === null || req === void 0 ? void 0 : req.url, " has and err IN SSR: msg =>"), error === null || error === void 0 ? void 0 : error.message);
                    return [
                        3,
                        9
                    ];
                case 9:
                    return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
            }
        });
    });
    return function(_) {
        return _ref.apply(this, arguments);
    };
}();
var WrappedApp = withRedux(createStore)(MyApp);
export default _c1 = withRouter(WrappedApp);
var _c, _c1;
$RefreshReg$(_c, "MyApp");
$RefreshReg$(_c1, "%default%");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = __webpack_module__.exports;
            // @ts-ignore __webpack_module__ is global
            var prevExports = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports on update so we can compare the boundary
                // signatures.
                __webpack_module__.hot.dispose(function (data) {
                    data.prevExports = currentExports;
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                import.meta.webpackHot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevExports !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                        __webpack_module__.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevExports !== null;
                if (isNoLongerABoundary) {
                    __webpack_module__.hot.invalidate();
                }
            }
        }
    })();
�  webpack://../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/compiled/@next/react-refresh-utils/dist/loader.js!../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use[1]!./pages/_app.tsxn!  {"version":3,"sources":["webpack://./pages/_app.tsx"],"sourcesContent":["import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';\nimport TrackingApp from '@vieon/tracking/services/functions/TrackingApp';\nimport { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';\nimport { PAGE } from '@vieon/core/constants/constants';\nimport LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';\nimport AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';\nimport createStore from '@vieon/core/store/createStore';\nimport '@vieon/ui-kits/styles/globals.scss';\nimport '@vieon/ui-kits/styles/methodItem.css';\nimport '@vieon/ui-kits/styles/rotate.css';\nimport '@vieon/ui-kits/styles/style.css';\nimport withRedux from 'next-redux-wrapper';\nimport { AppProps } from 'next/app';\nimport { withRouter } from 'next/router';\nimport React, { useEffect } from 'react';\nimport 'react-datepicker/dist/react-datepicker.css';\nimport 'react-popper-tooltip/dist/styles.css';\nimport { Provider } from 'react-redux';\n\nconst EXPORT_SSR =\n  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'\n    ? process.env.NEXT_PUBLIC_EXPORT_SSR\n    : 'true';\nif (!process.browser) React.useLayoutEffect = React.useEffect;\n\ninterface CustomAppProps extends AppProps {\n  store: any;\n  layoutProps?: any;\n}\n\nfunction MyApp(props: any): any {\n  const { Component, store, pageProps, layoutProps, router } = props || {};\n\n  const componentProps = { router, layoutProps, pageProps };\n  const pathname = router?.pathname;\n  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);\n  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);\n\n  useEffect(() => {\n    const checkAndProcessCache = async () => {\n      const cache = await caches.open('offline-cache-v1');\n      const response = await cache.match('/tracking-data');\n\n      if (response && response.ok) {\n        const clonedResponse = response.clone();\n        const trackingData = await clonedResponse.json();\n        TrackingApp.offlineDetect(trackingData);\n        await cache.delete('/tracking-data');\n      }\n    };\n\n    checkAndProcessCache();\n\n    window.addEventListener('online', checkAndProcessCache);\n\n    return () => {\n      window.removeEventListener('online', checkAndProcessCache);\n    };\n  }, []);\n\n  if (isTPBank || isListWinners) {\n    return (\n      <Provider store={store}>\n        <Component {...componentProps} />\n      </Provider>\n    );\n  }\n\n  return (\n    <>\n      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}\n      <ReCaptchaProvider>\n        <Provider store={store}>\n          <LayoutContainer {...layoutProps}>\n            <Component {...componentProps} />\n          </LayoutContainer>\n        </Provider>\n      </ReCaptchaProvider>\n    </>\n  );\n}\n\nMyApp.getInitialProps = async ({ Component, ctx }: any) => {\n  const usingSSR = EXPORT_SSR === 'true';\n  if (usingSSR) {\n    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };\n    try {\n      const layoutProps = LayoutContainer.getInitialProps\n        ? await LayoutContainer.getInitialProps(ctx)\n        : {};\n      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};\n      if (pageProps && Object.keys(pageProps).length > 0) {\n        return { layoutProps, pageProps };\n      }\n      return { layoutProps };\n    } catch (error: any) {\n      const { req } = ctx;\n      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);\n    }\n  }\n  return { layoutProps: {} };\n};\nconst WrappedApp = withRedux(createStore)(MyApp);\nexport default withRouter(WrappedApp as any);\n"],"names":["ReCaptchaProvider","TrackingApp","ENABLE_SDK_AIACTIV","PAGE","LayoutContainer","AAdsNetwork","createStore","withRedux","withRouter","React","useEffect","Provider","EXPORT_SSR","process","env","NEXT_PUBLIC_EXPORT_SSR","browser","useLayoutEffect","MyApp","props","Component","store","pageProps","layoutProps","router","componentProps","pathname","isTPBank","includes","PAYMENT_TPBANK","isListWinners","LIST_WINNERS","checkAndProcessCache","cache","response","clonedResponse","trackingData","caches","open","match","ok","clone","json","offlineDetect","delete","window","addEventListener","removeEventListener","getInitialProps","ctx","usingSSR","error","req","asPath","Object","keys","length","console","log","url","message","WrappedApp"],"mappings":"AAAA;;;;;;AAAA,OAAOA,iBAAiB,MAAM,mCAAmC,CAAC;AAClE,OAAOC,WAAW,MAAM,gDAAgD,CAAC;AACzE,SAASC,kBAAkB,QAAQ,8BAA8B,CAAC;AAClE,SAASC,IAAI,QAAQ,iCAAiC,CAAC;AACvD,OAAOC,eAAe,MAAM,sDAAsD,CAAC;AACnF,OAAOC,WAAW,MAAM,sCAAsC,CAAC;AAC/D,OAAOC,WAAW,MAAM,+BAA+B,CAAC;AACxD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,OAAOC,SAAS,MAAM,oBAAoB,CAAC;AAE3C,SAASC,UAAU,QAAQ,aAAa,CAAC;AACzC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO,CAAC;AACzC,OAAO,4CAA4C,CAAC;AACpD,OAAO,sCAAsC,CAAC;AAC9C,SAASC,QAAQ,QAAQ,aAAa,CAAC;AAEvC,IAAMC,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM,AAAC;AACb,IAAI,CAACF,OAAO,CAACG,OAAO,EAAEP,KAAK,CAACQ,eAAe,GAAGR,KAAK,CAACC,SAAS,CAAC;AAO9D,SAASQ,KAAK,CAACC,KAAU,EAAO;;IAC9B,IAA6DA,GAAW,GAAXA,KAAK,IAAI,EAAE,EAAhEC,SAAS,GAA4CD,GAAW,CAAhEC,SAAS,EAAEC,KAAK,GAAqCF,GAAW,CAArDE,KAAK,EAAEC,SAAS,GAA0BH,GAAW,CAA9CG,SAAS,EAAEC,WAAW,GAAaJ,GAAW,CAAnCI,WAAW,EAAEC,MAAM,GAAKL,GAAW,CAAtBK,MAAM,AAAiB;IAEzE,IAAMC,cAAc,GAAG;QAAED,MAAM,EAANA,MAAM;QAAED,WAAW,EAAXA,WAAW;QAAED,SAAS,EAATA,SAAS;KAAE,AAAC;IAC1D,IAAMI,QAAQ,GAAGF,MAAM,aAANA,MAAM,WAAU,GAAhBA,KAAAA,CAAgB,GAAhBA,MAAM,CAAEE,QAAQ,AAAC;IAClC,IAAMC,QAAQ,GAAG,AAACD,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,AAAC;IAChE,IAAMC,aAAa,GAAG,AAACJ,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC4B,YAAY,CAAC,AAAC;IAEnErB,SAAS,CAAC,WAAM;QACd,IAAMsB,oBAAoB;uBAAG,oBAAA,WAAY;oBACjCC,KAAK,EACLC,QAAQ,EAGNC,cAAc,EACdC,YAAY;;;;4BALN;;gCAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;8BAAA;;4BAA7CL,KAAK,GAAG,aAAqC,CAAA;4BAClC;;gCAAMA,KAAK,CAACM,KAAK,CAAC,gBAAgB,CAAC;8BAAA;;4BAA9CL,QAAQ,GAAG,aAAmC,CAAA;iCAEhDA,CAAAA,QAAQ,IAAIA,QAAQ,CAACM,EAAE,CAAA,EAAvBN;;;8BAAuB;4BACnBC,cAAc,GAAGD,QAAQ,CAACO,KAAK,EAAE,CAAC;4BACnB;;gCAAMN,cAAc,CAACO,IAAI,EAAE;8BAAA;;4BAA1CN,YAAY,GAAG,aAA2B,CAAA;4BAChDnC,WAAW,CAAC0C,aAAa,CAACP,YAAY,CAAC,CAAC;4BACxC;;gCAAMH,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC;8BAAA;;4BAApC,aAAoC,CAAC;;;;;;;;YAEzC,CAAC,CAAA;4BAVKZ,oBAAoB;;;WAUzB,AAAC;QAEFA,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,WAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,QAACnB,QAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,cAAA,QAACD,SAAS,qBAAKK,cAAc;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,kBAAkB,kBAAI,QAACG,WAAW;;;;oBAAG;0BACtC,QAACL,iBAAiB;0BAChB,cAAA,QAACW,QAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,cAAA,QAACjB,eAAe,0CAAKmB,WAAW;kCAC9B,cAAA,QAACH,SAAS,qBAAKK,cAAc;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;GAlDQP,KAAK;AAALA,KAAAA,KAAK,CAAA;AAoDdA,KAAK,CAAC8B,eAAe;eAAG,oBAAA,gBAAmC;YAA1B5B,SAAS,EAAE6B,GAAG,EACvCC,QAAQ,EAIJ3B,WAAW,QAGXD,SAAS,SAKR6B,KAAK,EACJC,GAAG;;;;oBAdgBhC,SAAS,SAATA,SAAS,EAAE6B,GAAG,SAAHA,GAAG;oBACvCC,QAAQ,GAAGtC,UAAU,KAAK,MAAM,CAAC;yBACnCsC,QAAQ,EAARA;;;sBAAQ;oBACV,IAAI,AAACD,CAAAA,CAAAA,GAAG,aAAHA,GAAG,WAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEI,MAAM,CAAA,IAAI,EAAE,CAAA,CAAEzB,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,EAAE;;wBAAO;4BAAEN,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;;;;;;;yBAE5DnB,eAAe,CAAC4C,eAAe,EAA/B5C;;;sBAA+B;oBAC/C;;wBAAMA,eAAe,CAAC4C,eAAe,CAACC,GAAG,CAAC;sBAAA;;2BAA1C,aAA0C,CAAA;;;;;;2BAC1C,EAAE;;;oBAFA1B,WAAW,OAEX,CAAA;yBACYH,SAAS,CAAC4B,eAAe,EAAzB5B;;;sBAAyB;oBAAG;;wBAAMA,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC;sBAAA;;4BAApC,aAAoC,CAAA;;;;;;4BAAG,EAAE;;;oBAAjF3B,SAAS,QAAwE,CAAA;oBACvF,IAAIA,SAAS,IAAIgC,MAAM,CAACC,IAAI,CAACjC,SAAS,CAAC,CAACkC,MAAM,GAAG,CAAC,EAAE;wBAClD;;4BAAO;gCAAEjC,WAAW,EAAXA,WAAW;gCAAED,SAAS,EAATA,SAAS;6BAAE;0BAAC;oBACpC,CAAC;oBACD;;wBAAO;4BAAEC,WAAW,EAAXA,WAAW;yBAAE;sBAAC;;oBAChB4B,KAAK;oBACN,AAAEC,GAAG,GAAKH,GAAG,CAAXG,GAAG,AAAQ,CAAC;oBACpBK,OAAO,CAACC,GAAG,CAAC,AAAC,UAAQ,CAAW,MAA2B,CAApCN,GAAG,aAAHA,GAAG,WAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEO,GAAG,EAAC,6BAA2B,CAAC,EAAER,KAAK,aAALA,KAAK,WAAS,GAAdA,KAAAA,CAAc,GAAdA,KAAK,CAAES,OAAO,CAAC,CAAC;;;;;;oBAGlF;;wBAAO;4BAAErC,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;IAC7B,CAAC,CAAA;;;;GAAA,CAAC;AACF,IAAMsC,UAAU,GAAGtD,SAAS,CAACD,WAAW,CAAC,CAACY,KAAK,CAAC,AAAC;AACjD,eAAeV,MAAAA,UAAU,CAACqD,UAAU,CAAQ,CAAC","file":"x"}cE JL_�   �   �   �       l  �  �  �      U  W  �  �  �  �  !  #  Z  \  �  �  �  �  �  �    
  7  9  _a  c  �  �  �  �  �  �    1  7  f  l  �  �  �  �  �  �  �  �  �  �  D  V  �  �   
  8
  �  �  �  _�    #  0  =  !  '  )  1  V  g  z  �  �  �  �  �  �  �  �  �  �  �  /  5  7  E  H  [  ]  j  �  W�  �  �  t  �       �  �  b  �  (  F  �'  (  	(  (  (  ,(  (  ,(  3(  <(  I(       �$�@M�*  �*  A+  R+  �,  �,  �-  .  Q/  m/  �2  �2  �5  �5  �����������������������process��false�{}�_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK�_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERS�(0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)�(0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__["default"])�(0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__.__generator)�_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetect�(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)�react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider��(0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])��react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment�_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIV��(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default())��(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default())����(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default())�(0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__["default"])�������(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps)�_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps�(0,next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__["default"])�(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default())�/* harmony default export */ __webpack_exports__["default"] = (��(0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)�);�module��module.id��module.hot.accept���buffer�source�size�maps�
X  __webpack_require__.r(__webpack_exports__);
/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_async_to_generator.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread_props.mjs");
/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ "../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ "../../packages/provider/src/providerRecaptcha.tsx");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ "../../packages/tracking/src/services/functions/TrackingApp.ts");
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ "../../packages/core/src/config/ConfigEnv.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ "../../packages/core/src/constants/constants.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx");
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ "../../packages/core/src/utils/script/AAdsNetwork.tsx");
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ "../../packages/core/src/store/createStore.ts");
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ "../../packages/ui-kits/src/styles/globals.scss");
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ "../../packages/ui-kits/src/styles/methodItem.css");
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ "../../packages/ui-kits/src/styles/rotate.css");
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ "../../packages/ui-kits/src/styles/style.css");
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ "../../node_modules/.pnpm/next-redux-wrapper@4.0.1_next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react_16ad1338e2a4d36018f83f499cb8265f/node_modules/next-redux-wrapper/es6/index.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/router.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css");
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css");
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ "../../node_modules/.pnpm/react-redux@7.2.9_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-redux/es/index.js");
/* provided dependency */ var process = __webpack_require__(/*! process */ "../../node_modules/.pnpm/process@0.11.10/node_modules/process/browser.js");





var _s = $RefreshSig$();

















var EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (false) {}
function MyApp(props) {
    _s();
    var ref = props || {}, Component = ref.Component, store = ref.store, pageProps = ref.pageProps, layoutProps = ref.layoutProps, router = ref.router;
    var componentProps = {
        router: router,
        layoutProps: layoutProps,
        pageProps: pageProps
    };
    var pathname = router === null || router === void 0 ? void 0 : router.pathname;
    var isTPBank = (pathname || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK);
    var isListWinners = (pathname || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERS);
    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(function() {
        var checkAndProcessCache = function() {
            var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__["default"])(function() {
                var cache, response, clonedResponse, trackingData;
                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__.__generator)(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            return [
                                4,
                                caches.open("offline-cache-v1")
                            ];
                        case 1:
                            cache = _state.sent();
                            return [
                                4,
                                cache.match("/tracking-data")
                            ];
                        case 2:
                            response = _state.sent();
                            if (!(response && response.ok)) return [
                                3,
                                5
                            ];
                            clonedResponse = response.clone();
                            return [
                                4,
                                clonedResponse.json()
                            ];
                        case 3:
                            trackingData = _state.sent();
                            _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetect(trackingData);
                            return [
                                4,
                                cache.delete("/tracking-data")
                            ];
                        case 4:
                            _state.sent();
                            _state.label = 5;
                        case 5:
                            return [
                                2
                            ];
                    }
                });
            });
            return function checkAndProcessCache() {
                return _ref.apply(this, arguments);
            };
        }();
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return function() {
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {
            store: store,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])({}, componentProps), void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIV && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default()), {
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {
                    store: store,
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default()), (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__["default"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])({}, layoutProps), {
                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])({}, componentProps), void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }), void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MyApp, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = MyApp;
MyApp.getInitialProps = function() {
    var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__["default"])(function(param) {
        var Component, ctx, usingSSR, layoutProps, _tmp, pageProps, _tmp1, error, req;
        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__.__generator)(this, function(_state) {
            switch(_state.label){
                case 0:
                    Component = param.Component, ctx = param.ctx;
                    usingSSR = EXPORT_SSR === "true";
                    if (!usingSSR) return [
                        3,
                        9
                    ];
                    if (((ctx === null || ctx === void 0 ? void 0 : ctx.asPath) || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK)) return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
                    _state.label = 1;
                case 1:
                    _state.trys.push([
                        1,
                        8,
                        ,
                        9
                    ]);
                    if (!(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps)) return [
                        3,
                        3
                    ];
                    return [
                        4,
                        _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(ctx)
                    ];
                case 2:
                    _tmp = _state.sent();
                    return [
                        3,
                        4
                    ];
                case 3:
                    _tmp = {};
                    _state.label = 4;
                case 4:
                    layoutProps = _tmp;
                    if (!Component.getInitialProps) return [
                        3,
                        6
                    ];
                    return [
                        4,
                        Component.getInitialProps(ctx)
                    ];
                case 5:
                    _tmp1 = _state.sent();
                    return [
                        3,
                        7
                    ];
                case 6:
                    _tmp1 = {};
                    _state.label = 7;
                case 7:
                    pageProps = _tmp1;
                    if (pageProps && Object.keys(pageProps).length > 0) {
                        return [
                            2,
                            {
                                layoutProps: layoutProps,
                                pageProps: pageProps
                            }
                        ];
                    }
                    return [
                        2,
                        {
                            layoutProps: layoutProps
                        }
                    ];
                case 8:
                    error = _state.sent();
                    req = ctx.req;
                    console.log("Request ".concat(req === null || req === void 0 ? void 0 : req.url, " has and err IN SSR: msg =>"), error === null || error === void 0 ? void 0 : error.message);
                    return [
                        3,
                        9
                    ];
                case 9:
                    return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
            }
        });
    });
    return function(_) {
        return _ref.apply(this, arguments);
    };
}();
var WrappedApp = (0,next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__["default"])((_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default()))(MyApp);
/* harmony default export */ __webpack_exports__["default"] = (_c1 = (0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)(WrappedApp));
var _c, _c1;
$RefreshReg$(_c, "MyApp");
$RefreshReg$(_c1, "%default%");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports on update so we can compare the boundary
                // signatures.
                module.hot.dispose(function (data) {
                    data.prevExports = currentExports;
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevExports !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevExports !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();
�{"filename":"[file].map[query]","module":true,"columns":true,"noSources":false,"namespace":"_N_E"}�map�bufferedMapr���]����mappings�sourcesContent�names�xP
  ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;AAAkE;AACO;AACP;AACX;AAC4B;AACpB;AACP;AACZ;AACE;AACJ;AACD;AACE;AAEF;AACA;AACW;AACN;AACP;AAEvC,IAAMY,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM;AACZ,IAAI,KAAgB,EAAEN,EAAwC;AAO9D,SAASS,KAAK,CAACC,KAAU,EAAO;;IAC9B,IAA6DA,GAAW,GAAXA,KAAK,IAAI,EAAE,EAAhEC,SAAS,GAA4CD,GAAW,CAAhEC,SAAS,EAAEC,KAAK,GAAqCF,GAAW,CAArDE,KAAK,EAAEC,SAAS,GAA0BH,GAAW,CAA9CG,SAAS,EAAEC,WAAW,GAAaJ,GAAW,CAAnCI,WAAW,EAAEC,MAAM,GAAKL,GAAW,CAAtBK,MAAM;IAExD,IAAMC,cAAc,GAAG;QAAED,MAAM,EAANA,MAAM;QAAED,WAAW,EAAXA,WAAW;QAAED,SAAS,EAATA,SAAS;KAAE;IACzD,IAAMI,QAAQ,GAAGF,MAAM,aAANA,MAAM,WAAU,GAAhBA,KAAAA,CAAgB,GAAhBA,MAAM,CAAEE,QAAQ;IACjC,IAAMC,QAAQ,GAAG,CAACD,QAAQ,IAAI,EAAE,EAAEE,QAAQ,CAACzB,gFAAmB,CAAC;IAC/D,IAAM2B,aAAa,GAAG,CAACJ,QAAQ,IAAI,EAAE,EAAEE,QAAQ,CAACzB,8EAAiB,CAAC;IAElEO,iDAAS,CAAC,WAAM;QACd,IAAMsB,oBAAoB;uBAAG,gGAAY;oBACjCC,KAAK,EACLC,QAAQ,EAGNC,cAAc,EACdC,YAAY;;;;4BALN;;gCAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;8BAAA;;4BAA7CL,KAAK,GAAG,aAAqC;4BAClC;;gCAAMA,KAAK,CAACM,KAAK,CAAC,gBAAgB,CAAC;8BAAA;;4BAA9CL,QAAQ,GAAG,aAAmC;iCAEhDA,CAAAA,QAAQ,IAAIA,QAAQ,CAACM,EAAE,GAAvBN;;;8BAAuB;4BACnBC,cAAc,GAAGD,QAAQ,CAACO,KAAK,EAAE,CAAC;4BACnB;;gCAAMN,cAAc,CAACO,IAAI,EAAE;8BAAA;;4BAA1CN,YAAY,GAAG,aAA2B;4BAChDnC,mGAAyB,CAACmC,YAAY,CAAC,CAAC;4BACxC;;gCAAMH,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC;8BAAA;;4BAApC,aAAoC,CAAC;;;;;;;;YAEzC,CAAC;4BAVKZ,oBAAoB;;;WAUzB;QAEDA,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,WAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,8DAACnB,kDAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,4EAACD,SAAS,sFAAKK,cAAc;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,4EAAkB,kBAAI,8DAACG,6EAAW;;;;oBAAG;0BACtC,8DAACL,0EAAiB;0BAChB,4EAACW,kDAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,4EAACjB,6FAAe,4KAAKmB,WAAW;kCAC9B,4EAACH,SAAS,sFAAKK,cAAc;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;GAlDQP,KAAK;AAALA,KAAAA,KAAK;AAoDdA,KAAK,CAAC8B,eAAe;eAAG,qGAAmC;YAA1B5B,SAAS,EAAE6B,GAAG,EACvCC,QAAQ,EAIJ3B,WAAW,QAGXD,SAAS,SAKR6B,KAAK,EACJC,GAAG;;;;oBAdgBhC,SAAS,SAATA,SAAS,EAAE6B,GAAG,SAAHA,GAAG;oBACvCC,QAAQ,GAAGtC,UAAU,KAAK,MAAM,CAAC;yBACnCsC,QAAQ,EAARA;;;sBAAQ;oBACV,IAAI,CAACD,CAAAA,GAAG,aAAHA,GAAG,WAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEI,MAAM,KAAI,EAAE,EAAEzB,QAAQ,CAACzB,gFAAmB,CAAC,EAAE;;wBAAO;4BAAEoB,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;;;;;;;yBAE5DnB,6GAA+B,EAA/BA;;;sBAA+B;oBAC/C;;wBAAMA,2GAA+B,CAAC6C,GAAG,CAAC;sBAAA;;2BAA1C,aAA0C;;;;;;2BAC1C,EAAE;;;oBAFA1B,WAAW,OAEX;yBACYH,SAAS,CAAC4B,eAAe,EAAzB5B;;;sBAAyB;oBAAG;;wBAAMA,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC;sBAAA;;4BAApC,aAAoC;;;;;;4BAAG,EAAE;;;oBAAjF3B,SAAS,QAAwE;oBACvF,IAAIA,SAAS,IAAIgC,MAAM,CAACC,IAAI,CAACjC,SAAS,CAAC,CAACkC,MAAM,GAAG,CAAC,EAAE;wBAClD;;4BAAO;gCAAEjC,WAAW,EAAXA,WAAW;gCAAED,SAAS,EAATA,SAAS;6BAAE;0BAAC;oBACpC,CAAC;oBACD;;wBAAO;4BAAEC,WAAW,EAAXA,WAAW;yBAAE;sBAAC;;oBAChB4B,KAAK;oBACN,GAAK,GAAKF,GAAG,CAAXG,GAAG,CAAS;oBACpBK,OAAO,CAACC,GAAG,CAAC,UAAS,CAAW,MAA2B,CAApCN,GAAG,aAAHA,GAAG,WAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEO,GAAG,EAAC,6BAA2B,CAAC,EAAER,KAAK,aAALA,KAAK,WAAS,GAAdA,KAAAA,CAAc,GAAdA,KAAK,CAAES,OAAO,CAAC,CAAC;;;;;;oBAGlF;;wBAAO;4BAAErC,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;IAC7B,CAAC;;;;GAAA,CAAC;AACF,IAAMsC,UAAU,GAAGtD,+DAAS,CAACD,sEAAW,CAAC,CAACY,KAAK,CAAC;AAChD,+DAAeV,MAAAA,wDAAU,CAACqD,UAAU,CAAQ,EAAC�webpack://./pages/_app.tsx�
  import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';
import TrackingApp from '@vieon/tracking/services/functions/TrackingApp';
import { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';
import { PAGE } from '@vieon/core/constants/constants';
import LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';
import AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';
import createStore from '@vieon/core/store/createStore';
import '@vieon/ui-kits/styles/globals.scss';
import '@vieon/ui-kits/styles/methodItem.css';
import '@vieon/ui-kits/styles/rotate.css';
import '@vieon/ui-kits/styles/style.css';
import withRedux from 'next-redux-wrapper';
import { AppProps } from 'next/app';
import { withRouter } from 'next/router';
import React, { useEffect } from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-popper-tooltip/dist/styles.css';
import { Provider } from 'react-redux';

const EXPORT_SSR =
  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'
    ? process.env.NEXT_PUBLIC_EXPORT_SSR
    : 'true';
if (!process.browser) React.useLayoutEffect = React.useEffect;

interface CustomAppProps extends AppProps {
  store: any;
  layoutProps?: any;
}

function MyApp(props: any): any {
  const { Component, store, pageProps, layoutProps, router } = props || {};

  const componentProps = { router, layoutProps, pageProps };
  const pathname = router?.pathname;
  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);
  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);

  useEffect(() => {
    const checkAndProcessCache = async () => {
      const cache = await caches.open('offline-cache-v1');
      const response = await cache.match('/tracking-data');

      if (response && response.ok) {
        const clonedResponse = response.clone();
        const trackingData = await clonedResponse.json();
        TrackingApp.offlineDetect(trackingData);
        await cache.delete('/tracking-data');
      }
    };

    checkAndProcessCache();

    window.addEventListener('online', checkAndProcessCache);

    return () => {
      window.removeEventListener('online', checkAndProcessCache);
    };
  }, []);

  if (isTPBank || isListWinners) {
    return (
      <Provider store={store}>
        <Component {...componentProps} />
      </Provider>
    );
  }

  return (
    <>
      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}
      <ReCaptchaProvider>
        <Provider store={store}>
          <LayoutContainer {...layoutProps}>
            <Component {...componentProps} />
          </LayoutContainer>
        </Provider>
      </ReCaptchaProvider>
    </>
  );
}

MyApp.getInitialProps = async ({ Component, ctx }: any) => {
  const usingSSR = EXPORT_SSR === 'true';
  if (usingSSR) {
    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };
    try {
      const layoutProps = LayoutContainer.getInitialProps
        ? await LayoutContainer.getInitialProps(ctx)
        : {};
      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
      if (pageProps && Object.keys(pageProps).length > 0) {
        return { layoutProps, pageProps };
      }
      return { layoutProps };
    } catch (error: any) {
      const { req } = ctx;
      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
    }
  }
  return { layoutProps: {} };
};
const WrappedApp = withRedux(createStore)(MyApp);
export default withRouter(WrappedApp as any);
`>�ReCaptchaProvider�TrackingApp�ENABLE_SDK_AIACTIV�PAGE�LayoutContainer�AAdsNetwork�createStore�withRedux�withRouter�React�useEffect�Provider�EXPORT_SSR�process�env�NEXT_PUBLIC_EXPORT_SSR�browser�useLayoutEffect�MyApp�props�Component�store�pageProps�layoutProps�router�componentProps�pathname�isTPBank�includes�PAYMENT_TPBANK�isListWinners�LIST_WINNERS�checkAndProcessCache�cache�response�clonedResponse�trackingData�caches�open�match�ok�clone�json�offlineDetect�delete�window�addEventListener�removeEventListener�getInitialProps�ctx�usingSSR�error�req�asPath�Object�keys�length�console�log�url�message�WrappedApp   ConcatSourceRawSource�  __webpack_require__.r(__webpack_exports__);
/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_async_to_generator.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread.mjs");
/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ "../../node_modules/.pnpm/@swc+helpers@0.4.11/node_modules/@swc/helpers/src/_object_spread_props.mjs");
/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ "../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/jsx-dev-runtime.js");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ "../../packages/provider/src/providerRecaptcha.tsx");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ "../../packages/tracking/src/services/functions/TrackingApp.ts");
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ "../../packages/core/src/config/ConfigEnv.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ "../../packages/core/src/constants/constants.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx");
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ "../../packages/core/src/utils/script/AAdsNetwork.tsx");
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ "../../packages/core/src/store/createStore.ts");
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ "../../packages/ui-kits/src/styles/globals.scss");
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ "../../packages/ui-kits/src/styles/methodItem.css");
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ "../../packages/ui-kits/src/styles/rotate.css");
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ "../../packages/ui-kits/src/styles/style.css");
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ "../../node_modules/.pnpm/next-redux-wrapper@4.0.1_next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react_16ad1338e2a4d36018f83f499cb8265f/node_modules/next-redux-wrapper/es6/index.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/router.js");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ "../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css");
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css");
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ "../../node_modules/.pnpm/react-redux@7.2.9_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-redux/es/index.js");
/* provided dependency */ var process = __webpack_require__(/*! process */ "../../node_modules/.pnpm/process@0.11.10/node_modules/process/browser.js");
   ReplaceSourceSourceMapSource�5  import _async_to_generator from "@swc/helpers/src/_async_to_generator.mjs";
import _object_spread from "@swc/helpers/src/_object_spread.mjs";
import _object_spread_props from "@swc/helpers/src/_object_spread_props.mjs";
import _ts_generator from "@swc/helpers/src/_ts_generator.mjs";
import { jsxDEV as _jsxDEV, Fragment as _Fragment } from "react/jsx-dev-runtime";
var _s = $RefreshSig$();
import ReCaptchaProvider from "@vieon/provider/providerRecaptcha";
import TrackingApp from "@vieon/tracking/services/functions/TrackingApp";
import { ENABLE_SDK_AIACTIV } from "@vieon/core/config/ConfigEnv";
import { PAGE } from "@vieon/core/constants/constants";
import LayoutContainer from "@vieon/ui-kits/components/containers/LayoutContainer";
import AAdsNetwork from "@vieon/core/utils/script/AAdsNetwork";
import createStore from "@vieon/core/store/createStore";
import "@vieon/ui-kits/styles/globals.scss";
import "@vieon/ui-kits/styles/methodItem.css";
import "@vieon/ui-kits/styles/rotate.css";
import "@vieon/ui-kits/styles/style.css";
import withRedux from "next-redux-wrapper";
import { withRouter } from "next/router";
import React, { useEffect } from "react";
import "react-datepicker/dist/react-datepicker.css";
import "react-popper-tooltip/dist/styles.css";
import { Provider } from "react-redux";
var EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (!process.browser) React.useLayoutEffect = React.useEffect;
function MyApp(props) {
    _s();
    var ref = props || {}, Component = ref.Component, store = ref.store, pageProps = ref.pageProps, layoutProps = ref.layoutProps, router = ref.router;
    var componentProps = {
        router: router,
        layoutProps: layoutProps,
        pageProps: pageProps
    };
    var pathname = router === null || router === void 0 ? void 0 : router.pathname;
    var isTPBank = (pathname || "").includes(PAGE.PAYMENT_TPBANK);
    var isListWinners = (pathname || "").includes(PAGE.LIST_WINNERS);
    useEffect(function() {
        var checkAndProcessCache = function() {
            var _ref = _async_to_generator(function() {
                var cache, response, clonedResponse, trackingData;
                return _ts_generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            return [
                                4,
                                caches.open("offline-cache-v1")
                            ];
                        case 1:
                            cache = _state.sent();
                            return [
                                4,
                                cache.match("/tracking-data")
                            ];
                        case 2:
                            response = _state.sent();
                            if (!(response && response.ok)) return [
                                3,
                                5
                            ];
                            clonedResponse = response.clone();
                            return [
                                4,
                                clonedResponse.json()
                            ];
                        case 3:
                            trackingData = _state.sent();
                            TrackingApp.offlineDetect(trackingData);
                            return [
                                4,
                                cache.delete("/tracking-data")
                            ];
                        case 4:
                            _state.sent();
                            _state.label = 5;
                        case 5:
                            return [
                                2
                            ];
                    }
                });
            });
            return function checkAndProcessCache() {
                return _ref.apply(this, arguments);
            };
        }();
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return function() {
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ _jsxDEV(Provider, {
            store: store,
            children: /*#__PURE__*/ _jsxDEV(Component, _object_spread({}, componentProps), void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ _jsxDEV(_Fragment, {
        children: [
            ENABLE_SDK_AIACTIV && /*#__PURE__*/ _jsxDEV(AAdsNetwork, {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ _jsxDEV(ReCaptchaProvider, {
                children: /*#__PURE__*/ _jsxDEV(Provider, {
                    store: store,
                    children: /*#__PURE__*/ _jsxDEV(LayoutContainer, _object_spread_props(_object_spread({}, layoutProps), {
                        children: /*#__PURE__*/ _jsxDEV(Component, _object_spread({}, componentProps), void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }), void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MyApp, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = MyApp;
MyApp.getInitialProps = function() {
    var _ref = _async_to_generator(function(param) {
        var Component, ctx, usingSSR, layoutProps, _tmp, pageProps, _tmp1, error, req;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    Component = param.Component, ctx = param.ctx;
                    usingSSR = EXPORT_SSR === "true";
                    if (!usingSSR) return [
                        3,
                        9
                    ];
                    if (((ctx === null || ctx === void 0 ? void 0 : ctx.asPath) || "").includes(PAGE.PAYMENT_TPBANK)) return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
                    _state.label = 1;
                case 1:
                    _state.trys.push([
                        1,
                        8,
                        ,
                        9
                    ]);
                    if (!LayoutContainer.getInitialProps) return [
                        3,
                        3
                    ];
                    return [
                        4,
                        LayoutContainer.getInitialProps(ctx)
                    ];
                case 2:
                    _tmp = _state.sent();
                    return [
                        3,
                        4
                    ];
                case 3:
                    _tmp = {};
                    _state.label = 4;
                case 4:
                    layoutProps = _tmp;
                    if (!Component.getInitialProps) return [
                        3,
                        6
                    ];
                    return [
                        4,
                        Component.getInitialProps(ctx)
                    ];
                case 5:
                    _tmp1 = _state.sent();
                    return [
                        3,
                        7
                    ];
                case 6:
                    _tmp1 = {};
                    _state.label = 7;
                case 7:
                    pageProps = _tmp1;
                    if (pageProps && Object.keys(pageProps).length > 0) {
                        return [
                            2,
                            {
                                layoutProps: layoutProps,
                                pageProps: pageProps
                            }
                        ];
                    }
                    return [
                        2,
                        {
                            layoutProps: layoutProps
                        }
                    ];
                case 8:
                    error = _state.sent();
                    req = ctx.req;
                    console.log("Request ".concat(req === null || req === void 0 ? void 0 : req.url, " has and err IN SSR: msg =>"), error === null || error === void 0 ? void 0 : error.message);
                    return [
                        3,
                        9
                    ];
                case 9:
                    return [
                        2,
                        {
                            layoutProps: {}
                        }
                    ];
            }
        });
    });
    return function(_) {
        return _ref.apply(this, arguments);
    };
}();
var WrappedApp = withRedux(createStore)(MyApp);
export default _c1 = withRouter(WrappedApp);
var _c, _c1;
$RefreshReg$(_c, "MyApp");
$RefreshReg$(_c1, "%default%");


;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = __webpack_module__.exports;
            // @ts-ignore __webpack_module__ is global
            var prevExports = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports on update so we can compare the boundary
                // signatures.
                __webpack_module__.hot.dispose(function (data) {
                    data.prevExports = currentExports;
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                import.meta.webpackHot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevExports !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {
                        __webpack_module__.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevExports !== null;
                if (isNoLongerABoundary) {
                    __webpack_module__.hot.invalidate();
                }
            }
        }
    })();
n!  {"version":3,"sources":["webpack://./pages/_app.tsx"],"sourcesContent":["import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';\nimport TrackingApp from '@vieon/tracking/services/functions/TrackingApp';\nimport { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';\nimport { PAGE } from '@vieon/core/constants/constants';\nimport LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';\nimport AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';\nimport createStore from '@vieon/core/store/createStore';\nimport '@vieon/ui-kits/styles/globals.scss';\nimport '@vieon/ui-kits/styles/methodItem.css';\nimport '@vieon/ui-kits/styles/rotate.css';\nimport '@vieon/ui-kits/styles/style.css';\nimport withRedux from 'next-redux-wrapper';\nimport { AppProps } from 'next/app';\nimport { withRouter } from 'next/router';\nimport React, { useEffect } from 'react';\nimport 'react-datepicker/dist/react-datepicker.css';\nimport 'react-popper-tooltip/dist/styles.css';\nimport { Provider } from 'react-redux';\n\nconst EXPORT_SSR =\n  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'\n    ? process.env.NEXT_PUBLIC_EXPORT_SSR\n    : 'true';\nif (!process.browser) React.useLayoutEffect = React.useEffect;\n\ninterface CustomAppProps extends AppProps {\n  store: any;\n  layoutProps?: any;\n}\n\nfunction MyApp(props: any): any {\n  const { Component, store, pageProps, layoutProps, router } = props || {};\n\n  const componentProps = { router, layoutProps, pageProps };\n  const pathname = router?.pathname;\n  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);\n  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);\n\n  useEffect(() => {\n    const checkAndProcessCache = async () => {\n      const cache = await caches.open('offline-cache-v1');\n      const response = await cache.match('/tracking-data');\n\n      if (response && response.ok) {\n        const clonedResponse = response.clone();\n        const trackingData = await clonedResponse.json();\n        TrackingApp.offlineDetect(trackingData);\n        await cache.delete('/tracking-data');\n      }\n    };\n\n    checkAndProcessCache();\n\n    window.addEventListener('online', checkAndProcessCache);\n\n    return () => {\n      window.removeEventListener('online', checkAndProcessCache);\n    };\n  }, []);\n\n  if (isTPBank || isListWinners) {\n    return (\n      <Provider store={store}>\n        <Component {...componentProps} />\n      </Provider>\n    );\n  }\n\n  return (\n    <>\n      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}\n      <ReCaptchaProvider>\n        <Provider store={store}>\n          <LayoutContainer {...layoutProps}>\n            <Component {...componentProps} />\n          </LayoutContainer>\n        </Provider>\n      </ReCaptchaProvider>\n    </>\n  );\n}\n\nMyApp.getInitialProps = async ({ Component, ctx }: any) => {\n  const usingSSR = EXPORT_SSR === 'true';\n  if (usingSSR) {\n    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };\n    try {\n      const layoutProps = LayoutContainer.getInitialProps\n        ? await LayoutContainer.getInitialProps(ctx)\n        : {};\n      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};\n      if (pageProps && Object.keys(pageProps).length > 0) {\n        return { layoutProps, pageProps };\n      }\n      return { layoutProps };\n    } catch (error: any) {\n      const { req } = ctx;\n      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);\n    }\n  }\n  return { layoutProps: {} };\n};\nconst WrappedApp = withRedux(createStore)(MyApp);\nexport default withRouter(WrappedApp as any);\n"],"names":["ReCaptchaProvider","TrackingApp","ENABLE_SDK_AIACTIV","PAGE","LayoutContainer","AAdsNetwork","createStore","withRedux","withRouter","React","useEffect","Provider","EXPORT_SSR","process","env","NEXT_PUBLIC_EXPORT_SSR","browser","useLayoutEffect","MyApp","props","Component","store","pageProps","layoutProps","router","componentProps","pathname","isTPBank","includes","PAYMENT_TPBANK","isListWinners","LIST_WINNERS","checkAndProcessCache","cache","response","clonedResponse","trackingData","caches","open","match","ok","clone","json","offlineDetect","delete","window","addEventListener","removeEventListener","getInitialProps","ctx","usingSSR","error","req","asPath","Object","keys","length","console","log","url","message","WrappedApp"],"mappings":"AAAA;;;;;;AAAA,OAAOA,iBAAiB,MAAM,mCAAmC,CAAC;AAClE,OAAOC,WAAW,MAAM,gDAAgD,CAAC;AACzE,SAASC,kBAAkB,QAAQ,8BAA8B,CAAC;AAClE,SAASC,IAAI,QAAQ,iCAAiC,CAAC;AACvD,OAAOC,eAAe,MAAM,sDAAsD,CAAC;AACnF,OAAOC,WAAW,MAAM,sCAAsC,CAAC;AAC/D,OAAOC,WAAW,MAAM,+BAA+B,CAAC;AACxD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,OAAOC,SAAS,MAAM,oBAAoB,CAAC;AAE3C,SAASC,UAAU,QAAQ,aAAa,CAAC;AACzC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO,CAAC;AACzC,OAAO,4CAA4C,CAAC;AACpD,OAAO,sCAAsC,CAAC;AAC9C,SAASC,QAAQ,QAAQ,aAAa,CAAC;AAEvC,IAAMC,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM,AAAC;AACb,IAAI,CAACF,OAAO,CAACG,OAAO,EAAEP,KAAK,CAACQ,eAAe,GAAGR,KAAK,CAACC,SAAS,CAAC;AAO9D,SAASQ,KAAK,CAACC,KAAU,EAAO;;IAC9B,IAA6DA,GAAW,GAAXA,KAAK,IAAI,EAAE,EAAhEC,SAAS,GAA4CD,GAAW,CAAhEC,SAAS,EAAEC,KAAK,GAAqCF,GAAW,CAArDE,KAAK,EAAEC,SAAS,GAA0BH,GAAW,CAA9CG,SAAS,EAAEC,WAAW,GAAaJ,GAAW,CAAnCI,WAAW,EAAEC,MAAM,GAAKL,GAAW,CAAtBK,MAAM,AAAiB;IAEzE,IAAMC,cAAc,GAAG;QAAED,MAAM,EAANA,MAAM;QAAED,WAAW,EAAXA,WAAW;QAAED,SAAS,EAATA,SAAS;KAAE,AAAC;IAC1D,IAAMI,QAAQ,GAAGF,MAAM,aAANA,MAAM,WAAU,GAAhBA,KAAAA,CAAgB,GAAhBA,MAAM,CAAEE,QAAQ,AAAC;IAClC,IAAMC,QAAQ,GAAG,AAACD,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,AAAC;IAChE,IAAMC,aAAa,GAAG,AAACJ,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC4B,YAAY,CAAC,AAAC;IAEnErB,SAAS,CAAC,WAAM;QACd,IAAMsB,oBAAoB;uBAAG,oBAAA,WAAY;oBACjCC,KAAK,EACLC,QAAQ,EAGNC,cAAc,EACdC,YAAY;;;;4BALN;;gCAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;8BAAA;;4BAA7CL,KAAK,GAAG,aAAqC,CAAA;4BAClC;;gCAAMA,KAAK,CAACM,KAAK,CAAC,gBAAgB,CAAC;8BAAA;;4BAA9CL,QAAQ,GAAG,aAAmC,CAAA;iCAEhDA,CAAAA,QAAQ,IAAIA,QAAQ,CAACM,EAAE,CAAA,EAAvBN;;;8BAAuB;4BACnBC,cAAc,GAAGD,QAAQ,CAACO,KAAK,EAAE,CAAC;4BACnB;;gCAAMN,cAAc,CAACO,IAAI,EAAE;8BAAA;;4BAA1CN,YAAY,GAAG,aAA2B,CAAA;4BAChDnC,WAAW,CAAC0C,aAAa,CAACP,YAAY,CAAC,CAAC;4BACxC;;gCAAMH,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC;8BAAA;;4BAApC,aAAoC,CAAC;;;;;;;;YAEzC,CAAC,CAAA;4BAVKZ,oBAAoB;;;WAUzB,AAAC;QAEFA,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,WAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,QAACnB,QAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,cAAA,QAACD,SAAS,qBAAKK,cAAc;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,kBAAkB,kBAAI,QAACG,WAAW;;;;oBAAG;0BACtC,QAACL,iBAAiB;0BAChB,cAAA,QAACW,QAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,cAAA,QAACjB,eAAe,0CAAKmB,WAAW;kCAC9B,cAAA,QAACH,SAAS,qBAAKK,cAAc;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;GAlDQP,KAAK;AAALA,KAAAA,KAAK,CAAA;AAoDdA,KAAK,CAAC8B,eAAe;eAAG,oBAAA,gBAAmC;YAA1B5B,SAAS,EAAE6B,GAAG,EACvCC,QAAQ,EAIJ3B,WAAW,QAGXD,SAAS,SAKR6B,KAAK,EACJC,GAAG;;;;oBAdgBhC,SAAS,SAATA,SAAS,EAAE6B,GAAG,SAAHA,GAAG;oBACvCC,QAAQ,GAAGtC,UAAU,KAAK,MAAM,CAAC;yBACnCsC,QAAQ,EAARA;;;sBAAQ;oBACV,IAAI,AAACD,CAAAA,CAAAA,GAAG,aAAHA,GAAG,WAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEI,MAAM,CAAA,IAAI,EAAE,CAAA,CAAEzB,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,EAAE;;wBAAO;4BAAEN,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;;;;;;;yBAE5DnB,eAAe,CAAC4C,eAAe,EAA/B5C;;;sBAA+B;oBAC/C;;wBAAMA,eAAe,CAAC4C,eAAe,CAACC,GAAG,CAAC;sBAAA;;2BAA1C,aAA0C,CAAA;;;;;;2BAC1C,EAAE;;;oBAFA1B,WAAW,OAEX,CAAA;yBACYH,SAAS,CAAC4B,eAAe,EAAzB5B;;;sBAAyB;oBAAG;;wBAAMA,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC;sBAAA;;4BAApC,aAAoC,CAAA;;;;;;4BAAG,EAAE;;;oBAAjF3B,SAAS,QAAwE,CAAA;oBACvF,IAAIA,SAAS,IAAIgC,MAAM,CAACC,IAAI,CAACjC,SAAS,CAAC,CAACkC,MAAM,GAAG,CAAC,EAAE;wBAClD;;4BAAO;gCAAEjC,WAAW,EAAXA,WAAW;gCAAED,SAAS,EAATA,SAAS;6BAAE;0BAAC;oBACpC,CAAC;oBACD;;wBAAO;4BAAEC,WAAW,EAAXA,WAAW;yBAAE;sBAAC;;oBAChB4B,KAAK;oBACN,AAAEC,GAAG,GAAKH,GAAG,CAAXG,GAAG,AAAQ,CAAC;oBACpBK,OAAO,CAACC,GAAG,CAAC,AAAC,UAAQ,CAAW,MAA2B,CAApCN,GAAG,aAAHA,GAAG,WAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEO,GAAG,EAAC,6BAA2B,CAAC,EAAER,KAAK,aAALA,KAAK,WAAS,GAAdA,KAAAA,CAAc,GAAdA,KAAK,CAAES,OAAO,CAAC,CAAC;;;;;;oBAGlF;;wBAAO;4BAAErC,WAAW,EAAE,EAAE;yBAAE;sBAAC;;;IAC7B,CAAC,CAAA;;;;GAAA,CAAC;AACF,IAAMsC,UAAU,GAAGtD,SAAS,CAACD,WAAW,CAAC,CAACY,KAAK,CAAC,AAAC;AACjD,eAAeV,MAAAA,UAAU,CAACqD,UAAU,CAAQ,CAAC","file":"x"}�  false074undefined76140undefined142218undefined220282undefined284364undefined391456undefined458530undefined532597undefined599653undefined655737undefined739801undefined803858undefined860903undefined905950undefined952993undefined9951035undefined10371079undefined10811121undefined11231163undefined11651216undefined12181263undefined12651303undefined13291335processundefined13821388processundefined14311446falseundefined14491488{}undefined19261944_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANKundefined19982014_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERSundefined20222030(0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)undefined21162134(0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__["default"])undefined22392251(0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__.__generator)undefined33603384_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetectundefined42994305(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined43074314react_redux__WEBPACK_IMPORTED_MODULE_17__.Providerundefined43814387(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined44004413(0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])undefined48974903(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined49054913react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragmentundefined49504967_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIVundefined49864992(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined49945004(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default())undefined52565262(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined52645280(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default())undefined53255331(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined53335340react_redux__WEBPACK_IMPORTED_MODULE_17__.Providerundefined54235429(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined54315445(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default())undefined54485467(0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_21__["default"])undefined54695482(0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])undefined55525558(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)undefined55715584(0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_20__["default"])undefined67726790(0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_18__["default"])undefined69126924(0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_19__.__generator)undefined73457363_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANKundefined77787808(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps)undefined79768006_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialPropsundefined1023910247(0,next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__["default"])undefined1024910259(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default())undefined1027010284/* harmony default export */ __webpack_exports__["default"] = (undefined1027010284undefined1029110300(0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)undefined1031310313.5);undefined1094810965moduleundefined1107311090moduleundefined1140311423module.idundefined1176711784moduleundefined1211312141module.hot.acceptundefined1302513042moduleundefined1370713724moduleundefined��__webpack_require__�__webpack_require__.n�__webpack_exports__�__webpack_require__.r����module.id�a04449b474a716b0��   �  �webpack/lib/util/registerExternalSerializer�webpack-sources/RawSource�  throw new Error("Module parse failed: Unexpected token (3:17)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> const AAdsNetwork: React.FC = () => {\n|   // Placeholder for AAdsNetwork component\n|   // You can add your actual ads network logic here");��  throw new Error("Module parse failed: Unexpected token (3:17)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> const AAdsNetwork: React.FC = () => {\n|   // Placeholder for AAdsNetwork component\n|   // You can add your actual ads network logic here");��	   RawSource� �7dc0755cd1321c29