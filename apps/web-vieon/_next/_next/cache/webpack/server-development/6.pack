wpc   �  �webpack/lib/cache/PackFileCacheStrategy�PackContentItems��   ResolverCachePlugin|normal|default|dependencyType=|esm|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/ui-kits/components/containers/LayoutContainer�   Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.tsx|webpack-runtime�webpack/lib/cache/ResolverCachePlugin��`�_ResolverCachePluginCacheMiss�context�path�request�query�fragment�module�directory�file�internal�fullySpecified�descriptionFilePath�descriptionFileData�descriptionFileRoot�relativePath�issuer�issuerLayer�compiler�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�server�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.tsx�� �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/package.json
�name�version�description�main�types�scripts�dependencies�devDependencies�peerDependencies�files�@vieon/ui-kits�1.0.0�VieON shared UI components and utilities�dist/index.js�dist/index.d.ts�build�dev�clean�type-check�lint�lint:fix�tsc�tsc --watch�rimraf dist�tsc --noEmit�eslint src --ext .ts,.tsx�eslint src --ext .ts,.tsx --fix
�@vieon/models�@vieon/core�classnames�framer-motion�react-intersection-observer�react-lottie�react-popper-tooltip�react-responsive�react-select�swiper�workspace:*��2.3.2�^12.4.1�9.5.2�1.2.10�2.11.1�^10.0.0�^5.10.0�^6.8.4�@types/react�@types/react-dom�@types/react-lottie�typescript�rimraf�^19.1.6�^19.1.5�^1.2.10�^5.8.3�^3.0.2�react�react-dom�18.2.0��dist�src�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits�./src/components/containers/LayoutContainer.tsx�webpack/lib/FileSystemInfo�Snapshot@�     0p�zyB`�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json���/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages�/Users/<USER>/Desktop/Project/VieON/web-mono-repo�/Users/<USER>/Desktop/Project/VieON�/Users/<USER>/Desktop/Project�/Users/<USER>/Desktop�/Users/<USER>/Users�/�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers�safeTime�timestamp! �zyB  �zyB�! πzyB  πzyB�! ��zyB ��zyB� � � � � � � � � � � � `�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.mjs�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.ts�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.jsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.wasm�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.js�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/ui-kits/src/components/containers/LayoutContainer.mjs�    �sources�runtimeRequirements�data�javascript�webpack/lib/util/registerExternalSerializer�webpack-sources/CachedSource   �  �webpack/lib/util/registerExternalSerializer�webpack-sources/RawSourceu  throw new Error("Module parse failed: The keyword 'interface' is reserved (3:0)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> interface LayoutContainerProps {\n|   children: React.ReactNode;\n|   [key: string]: any;");�buffer�source�size�maps�hashu  throw new Error("Module parse failed: The keyword 'interface' is reserved (3:0)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> interface LayoutContainerProps {\n|   children: React.ReactNode;\n|   [key: string]: any;");�{"filename":"[file].map[query]","module":true,"columns":true,"noSources":false,"namespace":"@vieon/web-vieon"}�map�bufferedMap� 