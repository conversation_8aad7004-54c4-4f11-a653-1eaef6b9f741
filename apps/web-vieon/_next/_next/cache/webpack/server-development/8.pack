wpc   2�  �webpack/lib/cache/PackFileCacheStrategy�PackContentItems��   ResolverCachePlugin|normal|default|dependencyType=|esm|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/core/utils/script/AAdsNetwork�  Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use!/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx|webpack-runtime�   Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx|webpack-runtime�webpack/lib/cache/ResolverCachePlugin��`�_ResolverCachePluginCacheMiss�context�path�request�query�fragment�module�directory�file�internal�fullySpecified�descriptionFilePath�descriptionFileData�descriptionFileRoot�relativePath�issuer�issuerLayer�compiler�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�server�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx�� �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/package.json
�name�version�description�main�types�scripts�dependencies�devDependencies�peerDependencies�files�@vieon/core�1.0.0�VieON core utilities and shared code�dist/index.js�dist/index.d.ts�build�dev�clean�type-check�lint�lint:fix�tsc�tsc --watch�rimraf dist�tsc --noEmit�eslint src --ext .ts,.tsx�eslint src --ext .ts,.tsx --fix�@vieon/models�axios�redux�redux-thunk�immer�lodash�moment�crypto-js�workspace:*�1.7.8�4.2.1�2.4.2�10.0.3�4.17.21�2.29.4�4.2.0�@types/lodash�@types/crypto-js�typescript�rimraf�^4.17.17�^4.2.2�^5.8.3�^3.0.2�react�18.2.0�dist�src�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core�./src/utils/script/AAdsNetwork.tsx�webpack/lib/FileSystemInfo�Snapshot@�     Pe�zyB`�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json���/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages�/Users/<USER>/Desktop/Project/VieON/web-mono-repo�/Users/<USER>/Desktop/Project/VieON�/Users/<USER>/Desktop/Project�/Users/<USER>/Desktop�/Users/<USER>/Users�/�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script�safeTime�timestamp! �zyB  �zyB�! �?szyB �?szyB�!  ҫzyB �ѫzyB� � � � � � � � � � � � `�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.mjs�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.ts�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.jsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.wasm�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.js�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/core/src/utils/script/AAdsNetwork.mjs�    �sources�runtimeRequirements�data�javascript�webpack/lib/util/registerExternalSerializer�webpack-sources/CachedSource   4Z  �webpack/lib/util/registerExternalSerializer�webpack-sources/ConcatSource��webpack/lib/util/registerExternalSerializer�webpack-sources/RawSource�  __webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ "../../packages/provider/src/providerRecaptcha.tsx");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ "../../packages/tracking/src/services/functions/TrackingApp.ts");
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ "../../packages/core/src/config/ConfigEnv.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ "../../packages/core/src/constants/constants.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx");
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ "../../packages/core/src/utils/script/AAdsNetwork.tsx");
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ "../../packages/core/src/store/createStore.ts");
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ "../../packages/ui-kits/src/styles/globals.scss");
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ "../../packages/ui-kits/src/styles/methodItem.css");
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ "../../packages/ui-kits/src/styles/rotate.css");
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ "../../packages/ui-kits/src/styles/style.css");
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ "next-redux-wrapper");
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ "next/router");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css");
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css");
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ "react-redux");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_17__);
�webpack/lib/util/registerExternalSerializer�webpack-sources/ReplaceSource�webpack/lib/util/registerExternalSerializer�webpack-sources/SourceMapSource�  import { jsxDEV as _jsxDEV, Fragment as _Fragment } from "react/jsx-dev-runtime";
import ReCaptchaProvider from "@vieon/provider/providerRecaptcha";
import TrackingApp from "@vieon/tracking/services/functions/TrackingApp";
import { ENABLE_SDK_AIACTIV } from "@vieon/core/config/ConfigEnv";
import { PAGE } from "@vieon/core/constants/constants";
import LayoutContainer from "@vieon/ui-kits/components/containers/LayoutContainer";
import AAdsNetwork from "@vieon/core/utils/script/AAdsNetwork";
import createStore from "@vieon/core/store/createStore";
import "@vieon/ui-kits/styles/globals.scss";
import "@vieon/ui-kits/styles/methodItem.css";
import "@vieon/ui-kits/styles/rotate.css";
import "@vieon/ui-kits/styles/style.css";
import withRedux from "next-redux-wrapper";
import { withRouter } from "next/router";
import React, { useEffect } from "react";
import "react-datepicker/dist/react-datepicker.css";
import "react-popper-tooltip/dist/styles.css";
import { Provider } from "react-redux";
const EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (!process.browser) React.useLayoutEffect = React.useEffect;
function MyApp(props) {
    const { Component , store , pageProps , layoutProps , router  } = props || {};
    const componentProps = {
        router,
        layoutProps,
        pageProps
    };
    const pathname = router?.pathname;
    const isTPBank = (pathname || "").includes(PAGE.PAYMENT_TPBANK);
    const isListWinners = (pathname || "").includes(PAGE.LIST_WINNERS);
    useEffect(()=>{
        const checkAndProcessCache = async ()=>{
            const cache = await caches.open("offline-cache-v1");
            const response = await cache.match("/tracking-data");
            if (response && response.ok) {
                const clonedResponse = response.clone();
                const trackingData = await clonedResponse.json();
                TrackingApp.offlineDetect(trackingData);
                await cache.delete("/tracking-data");
            }
        };
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return ()=>{
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ _jsxDEV(Provider, {
            store: store,
            children: /*#__PURE__*/ _jsxDEV(Component, {
                ...componentProps
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ _jsxDEV(_Fragment, {
        children: [
            ENABLE_SDK_AIACTIV && /*#__PURE__*/ _jsxDEV(AAdsNetwork, {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ _jsxDEV(ReCaptchaProvider, {
                children: /*#__PURE__*/ _jsxDEV(Provider, {
                    store: store,
                    children: /*#__PURE__*/ _jsxDEV(LayoutContainer, {
                        ...layoutProps,
                        children: /*#__PURE__*/ _jsxDEV(Component, {
                            ...componentProps
                        }, void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
MyApp.getInitialProps = async ({ Component , ctx  })=>{
    const usingSSR = EXPORT_SSR === "true";
    if (usingSSR) {
        if ((ctx?.asPath || "").includes(PAGE.PAYMENT_TPBANK)) return {
            layoutProps: {}
        };
        try {
            const layoutProps = LayoutContainer.getInitialProps ? await LayoutContainer.getInitialProps(ctx) : {};
            const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
            if (pageProps && Object.keys(pageProps).length > 0) {
                return {
                    layoutProps,
                    pageProps
                };
            }
            return {
                layoutProps
            };
        } catch (error) {
            const { req  } = ctx;
            console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
        }
    }
    return {
        layoutProps: {}
    };
};
const WrappedApp = withRedux(createStore)(MyApp);
export default withRouter(WrappedApp);
  webpack://../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use!./pages/_app.tsxb  {"version":3,"sources":["webpack://./pages/_app.tsx"],"sourcesContent":["import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';\nimport TrackingApp from '@vieon/tracking/services/functions/TrackingApp';\nimport { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';\nimport { PAGE } from '@vieon/core/constants/constants';\nimport LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';\nimport AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';\nimport createStore from '@vieon/core/store/createStore';\nimport '@vieon/ui-kits/styles/globals.scss';\nimport '@vieon/ui-kits/styles/methodItem.css';\nimport '@vieon/ui-kits/styles/rotate.css';\nimport '@vieon/ui-kits/styles/style.css';\nimport withRedux from 'next-redux-wrapper';\nimport { AppProps } from 'next/app';\nimport { withRouter } from 'next/router';\nimport React, { useEffect } from 'react';\nimport 'react-datepicker/dist/react-datepicker.css';\nimport 'react-popper-tooltip/dist/styles.css';\nimport { Provider } from 'react-redux';\n\nconst EXPORT_SSR =\n  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'\n    ? process.env.NEXT_PUBLIC_EXPORT_SSR\n    : 'true';\nif (!process.browser) React.useLayoutEffect = React.useEffect;\n\ninterface CustomAppProps extends AppProps {\n  store: any;\n  layoutProps?: any;\n}\n\nfunction MyApp(props: any): any {\n  const { Component, store, pageProps, layoutProps, router } = props || {};\n\n  const componentProps = { router, layoutProps, pageProps };\n  const pathname = router?.pathname;\n  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);\n  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);\n\n  useEffect(() => {\n    const checkAndProcessCache = async () => {\n      const cache = await caches.open('offline-cache-v1');\n      const response = await cache.match('/tracking-data');\n\n      if (response && response.ok) {\n        const clonedResponse = response.clone();\n        const trackingData = await clonedResponse.json();\n        TrackingApp.offlineDetect(trackingData);\n        await cache.delete('/tracking-data');\n      }\n    };\n\n    checkAndProcessCache();\n\n    window.addEventListener('online', checkAndProcessCache);\n\n    return () => {\n      window.removeEventListener('online', checkAndProcessCache);\n    };\n  }, []);\n\n  if (isTPBank || isListWinners) {\n    return (\n      <Provider store={store}>\n        <Component {...componentProps} />\n      </Provider>\n    );\n  }\n\n  return (\n    <>\n      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}\n      <ReCaptchaProvider>\n        <Provider store={store}>\n          <LayoutContainer {...layoutProps}>\n            <Component {...componentProps} />\n          </LayoutContainer>\n        </Provider>\n      </ReCaptchaProvider>\n    </>\n  );\n}\n\nMyApp.getInitialProps = async ({ Component, ctx }: any) => {\n  const usingSSR = EXPORT_SSR === 'true';\n  if (usingSSR) {\n    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };\n    try {\n      const layoutProps = LayoutContainer.getInitialProps\n        ? await LayoutContainer.getInitialProps(ctx)\n        : {};\n      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};\n      if (pageProps && Object.keys(pageProps).length > 0) {\n        return { layoutProps, pageProps };\n      }\n      return { layoutProps };\n    } catch (error: any) {\n      const { req } = ctx;\n      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);\n    }\n  }\n  return { layoutProps: {} };\n};\nconst WrappedApp = withRedux(createStore)(MyApp);\nexport default withRouter(WrappedApp as any);\n"],"names":["ReCaptchaProvider","TrackingApp","ENABLE_SDK_AIACTIV","PAGE","LayoutContainer","AAdsNetwork","createStore","withRedux","withRouter","React","useEffect","Provider","EXPORT_SSR","process","env","NEXT_PUBLIC_EXPORT_SSR","browser","useLayoutEffect","MyApp","props","Component","store","pageProps","layoutProps","router","componentProps","pathname","isTPBank","includes","PAYMENT_TPBANK","isListWinners","LIST_WINNERS","checkAndProcessCache","cache","caches","open","response","match","ok","clonedResponse","clone","trackingData","json","offlineDetect","delete","window","addEventListener","removeEventListener","getInitialProps","ctx","usingSSR","asPath","Object","keys","length","error","req","console","log","url","message","WrappedApp"],"mappings":"AAAA;AAAA,OAAOA,iBAAiB,MAAM,mCAAmC,CAAC;AAClE,OAAOC,WAAW,MAAM,gDAAgD,CAAC;AACzE,SAASC,kBAAkB,QAAQ,8BAA8B,CAAC;AAClE,SAASC,IAAI,QAAQ,iCAAiC,CAAC;AACvD,OAAOC,eAAe,MAAM,sDAAsD,CAAC;AACnF,OAAOC,WAAW,MAAM,sCAAsC,CAAC;AAC/D,OAAOC,WAAW,MAAM,+BAA+B,CAAC;AACxD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,OAAOC,SAAS,MAAM,oBAAoB,CAAC;AAE3C,SAASC,UAAU,QAAQ,aAAa,CAAC;AACzC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO,CAAC;AACzC,OAAO,4CAA4C,CAAC;AACpD,OAAO,sCAAsC,CAAC;AAC9C,SAASC,QAAQ,QAAQ,aAAa,CAAC;AAEvC,MAAMC,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM,AAAC;AACb,IAAI,CAACF,OAAO,CAACG,OAAO,EAAEP,KAAK,CAACQ,eAAe,GAAGR,KAAK,CAACC,SAAS,CAAC;AAO9D,SAASQ,KAAK,CAACC,KAAU,EAAO;IAC9B,MAAM,EAAEC,SAAS,CAAA,EAAEC,KAAK,CAAA,EAAEC,SAAS,CAAA,EAAEC,WAAW,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGL,KAAK,IAAI,EAAE,AAAC;IAEzE,MAAMM,cAAc,GAAG;QAAED,MAAM;QAAED,WAAW;QAAED,SAAS;KAAE,AAAC;IAC1D,MAAMI,QAAQ,GAAGF,MAAM,EAAEE,QAAQ,AAAC;IAClC,MAAMC,QAAQ,GAAG,AAACD,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,AAAC;IAChE,MAAMC,aAAa,GAAG,AAACJ,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC4B,YAAY,CAAC,AAAC;IAEnErB,SAAS,CAAC,IAAM;QACd,MAAMsB,oBAAoB,GAAG,UAAY;YACvC,MAAMC,KAAK,GAAG,MAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC,AAAC;YACpD,MAAMC,QAAQ,GAAG,MAAMH,KAAK,CAACI,KAAK,CAAC,gBAAgB,CAAC,AAAC;YAErD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,EAAE,EAAE;gBAC3B,MAAMC,cAAc,GAAGH,QAAQ,CAACI,KAAK,EAAE,AAAC;gBACxC,MAAMC,YAAY,GAAG,MAAMF,cAAc,CAACG,IAAI,EAAE,AAAC;gBACjDzC,WAAW,CAAC0C,aAAa,CAACF,YAAY,CAAC,CAAC;gBACxC,MAAMR,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,AAAC;QAEFZ,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,IAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,QAACnB,QAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,cAAA,QAACD,SAAS;gBAAE,GAAGK,cAAc;;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,kBAAkB,kBAAI,QAACG,WAAW;;;;oBAAG;0BACtC,QAACL,iBAAiB;0BAChB,cAAA,QAACW,QAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,cAAA,QAACjB,eAAe;wBAAE,GAAGmB,WAAW;kCAC9B,cAAA,QAACH,SAAS;4BAAE,GAAGK,cAAc;;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;AAEDP,KAAK,CAAC8B,eAAe,GAAG,OAAO,EAAE5B,SAAS,CAAA,EAAE6B,GAAG,CAAA,EAAO,GAAK;IACzD,MAAMC,QAAQ,GAAGtC,UAAU,KAAK,MAAM,AAAC;IACvC,IAAIsC,QAAQ,EAAE;QACZ,IAAI,AAACD,CAAAA,GAAG,EAAEE,MAAM,IAAI,EAAE,CAAA,CAAEvB,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,EAAE,OAAO;YAAEN,WAAW,EAAE,EAAE;SAAE,CAAC;QAClF,IAAI;YACF,MAAMA,WAAW,GAAGnB,eAAe,CAAC4C,eAAe,GAC/C,MAAM5C,eAAe,CAAC4C,eAAe,CAACC,GAAG,CAAC,GAC1C,EAAE,AAAC;YACP,MAAM3B,SAAS,GAAGF,SAAS,CAAC4B,eAAe,GAAG,MAAM5B,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC,GAAG,EAAE,AAAC;YACxF,IAAI3B,SAAS,IAAI8B,MAAM,CAACC,IAAI,CAAC/B,SAAS,CAAC,CAACgC,MAAM,GAAG,CAAC,EAAE;gBAClD,OAAO;oBAAE/B,WAAW;oBAAED,SAAS;iBAAE,CAAC;YACpC,CAAC;YACD,OAAO;gBAAEC,WAAW;aAAE,CAAC;QACzB,EAAE,OAAOgC,KAAK,EAAO;YACnB,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGP,GAAG,AAAC;YACpBQ,OAAO,CAACC,GAAG,CAAC,CAAC,QAAQ,EAAEF,GAAG,EAAEG,GAAG,CAAC,2BAA2B,CAAC,EAAEJ,KAAK,EAAEK,OAAO,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IACD,OAAO;QAAErC,WAAW,EAAE,EAAE;KAAE,CAAC;AAC7B,CAAC,CAAC;AACF,MAAMsC,UAAU,GAAGtD,SAAS,CAACD,WAAW,CAAC,CAACY,KAAK,CAAC,AAAC;AACjD,eAAeV,UAAU,CAACqD,UAAU,CAAQ,CAAC","file":"x"}c1 PR_�   �   �   �      "  X  Z  �  �  �  �  %  '  R  T  �  �  �  �  �  �      ,  .  V  X  �  �  �  �  _�  d  s  v  �  �  �  �  �        %  �  �  &	  ,	  .	  5	  x	  ~	  �  �  �  �  �  �  �  �  �  �  �  ]�  �  

  7
  =
  ?
  F
  �
  �
  �
  �
      ,  >  �  �  �  �  <  D  F  P  [  i  [  i  j  s  �       ���@�������������������true�(react__WEBPACK_IMPORTED_MODULE_14___default().useLayoutEffect)�(react__WEBPACK_IMPORTED_MODULE_14___default().useEffect)�_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK�_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERS�(0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)�_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetect�(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)�react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider���react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment�_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIV��(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default())��(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default())����(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default())���(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps)�_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps�next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default()�(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default())�/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (��(0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)�);�buffer�source�size�maps�hash\4  __webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ "react/jsx-dev-runtime");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ "../../packages/provider/src/providerRecaptcha.tsx");
/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ "../../packages/tracking/src/services/functions/TrackingApp.ts");
/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ "../../packages/core/src/config/ConfigEnv.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ "../../packages/core/src/constants/constants.ts");
/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx");
/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ "../../packages/core/src/utils/script/AAdsNetwork.tsx");
/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ "../../packages/core/src/store/createStore.ts");
/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ "../../packages/ui-kits/src/styles/globals.scss");
/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ "../../packages/ui-kits/src/styles/methodItem.css");
/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ "../../packages/ui-kits/src/styles/rotate.css");
/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ "../../packages/ui-kits/src/styles/style.css");
/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ "next-redux-wrapper");
/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ "next/router");
/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css");
/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css");
/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ "react-redux");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_17__);


















const EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (true) (react__WEBPACK_IMPORTED_MODULE_14___default().useLayoutEffect) = (react__WEBPACK_IMPORTED_MODULE_14___default().useEffect);
function MyApp(props) {
    const { Component , store , pageProps , layoutProps , router  } = props || {};
    const componentProps = {
        router,
        layoutProps,
        pageProps
    };
    const pathname = router?.pathname;
    const isTPBank = (pathname || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK);
    const isListWinners = (pathname || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERS);
    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{
        const checkAndProcessCache = async ()=>{
            const cache = await caches.open("offline-cache-v1");
            const response = await cache.match("/tracking-data");
            if (response && response.ok) {
                const clonedResponse = response.clone();
                const trackingData = await clonedResponse.json();
                _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetect(trackingData);
                await cache.delete("/tracking-data");
            }
        };
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return ()=>{
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {
            store: store,
            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {
                ...componentProps
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {
        children: [
            _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIV && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default()), {
                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {
                    store: store,
                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default()), {
                        ...layoutProps,
                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {
                            ...componentProps
                        }, void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
MyApp.getInitialProps = async ({ Component , ctx  })=>{
    const usingSSR = EXPORT_SSR === "true";
    if (usingSSR) {
        if ((ctx?.asPath || "").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK)) return {
            layoutProps: {}
        };
        try {
            const layoutProps = (_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps) ? await _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(ctx) : {};
            const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
            if (pageProps && Object.keys(pageProps).length > 0) {
                return {
                    layoutProps,
                    pageProps
                };
            }
            return {
                layoutProps
            };
        } catch (error) {
            const { req  } = ctx;
            console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
        }
    }
    return {
        layoutProps: {}
    };
};
const WrappedApp = next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default()((_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default()))(MyApp);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)(WrappedApp));
�{"filename":"[file].map[query]","module":true,"columns":true,"noSources":false,"namespace":"@vieon/web-vieon"}�map�bufferedMapr���`����mappings�sourcesContent�names�x_
  ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAkE;AACO;AACP;AACX;AAC4B;AACpB;AACP;AACZ;AACE;AACJ;AACD;AACE;AAEF;AACA;AACW;AACN;AACP;AAEvC,MAAMY,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM;AACZ,IAAI,IAAgB,EAAEN,+DAAqB,GAAGA,yDAAe,CAAC;AAO9D,SAASS,KAAK,CAACC,KAAU,EAAO;IAC9B,MAAM,EAAEC,SAAS,GAAEC,KAAK,GAAEC,SAAS,GAAEC,WAAW,GAAEC,MAAM,GAAE,GAAGL,KAAK,IAAI,EAAE;IAExE,MAAMM,cAAc,GAAG;QAAED,MAAM;QAAED,WAAW;QAAED,SAAS;KAAE;IACzD,MAAMI,QAAQ,GAAGF,MAAM,EAAEE,QAAQ;IACjC,MAAMC,QAAQ,GAAG,CAACD,QAAQ,IAAI,EAAE,EAAEE,QAAQ,CAACzB,gFAAmB,CAAC;IAC/D,MAAM2B,aAAa,GAAG,CAACJ,QAAQ,IAAI,EAAE,EAAEE,QAAQ,CAACzB,8EAAiB,CAAC;IAElEO,iDAAS,CAAC,IAAM;QACd,MAAMsB,oBAAoB,GAAG,UAAY;YACvC,MAAMC,KAAK,GAAG,MAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC;YACnD,MAAMC,QAAQ,GAAG,MAAMH,KAAK,CAACI,KAAK,CAAC,gBAAgB,CAAC;YAEpD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,EAAE,EAAE;gBAC3B,MAAMC,cAAc,GAAGH,QAAQ,CAACI,KAAK,EAAE;gBACvC,MAAMC,YAAY,GAAG,MAAMF,cAAc,CAACG,IAAI,EAAE;gBAChDzC,mGAAyB,CAACwC,YAAY,CAAC,CAAC;gBACxC,MAAMR,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAEDZ,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,IAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,8DAACnB,kDAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,4EAACD,SAAS;gBAAE,GAAGK,cAAc;;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,4EAAkB,kBAAI,8DAACG,6EAAW;;;;oBAAG;0BACtC,8DAACL,0EAAiB;0BAChB,4EAACW,kDAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,4EAACjB,6FAAe;wBAAE,GAAGmB,WAAW;kCAC9B,4EAACH,SAAS;4BAAE,GAAGK,cAAc;;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;AAEDP,KAAK,CAAC8B,eAAe,GAAG,OAAO,EAAE5B,SAAS,GAAE6B,GAAG,GAAO,GAAK;IACzD,MAAMC,QAAQ,GAAGtC,UAAU,KAAK,MAAM;IACtC,IAAIsC,QAAQ,EAAE;QACZ,IAAI,CAACD,GAAG,EAAEE,MAAM,IAAI,EAAE,EAAEvB,QAAQ,CAACzB,gFAAmB,CAAC,EAAE,OAAO;YAAEoB,WAAW,EAAE,EAAE;SAAE,CAAC;QAClF,IAAI;YACF,MAAMA,WAAW,GAAGnB,6GAA+B,GAC/C,MAAMA,2GAA+B,CAAC6C,GAAG,CAAC,GAC1C,EAAE;YACN,MAAM3B,SAAS,GAAGF,SAAS,CAAC4B,eAAe,GAAG,MAAM5B,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC,GAAG,EAAE;YACvF,IAAI3B,SAAS,IAAI8B,MAAM,CAACC,IAAI,CAAC/B,SAAS,CAAC,CAACgC,MAAM,GAAG,CAAC,EAAE;gBAClD,OAAO;oBAAE/B,WAAW;oBAAED,SAAS;iBAAE,CAAC;YACpC,CAAC;YACD,OAAO;gBAAEC,WAAW;aAAE,CAAC;QACzB,EAAE,OAAOgC,KAAK,EAAO;YACnB,MAAM,EAAEC,GAAG,GAAE,GAAGP,GAAG;YACnBQ,OAAO,CAACC,GAAG,CAAC,CAAC,QAAQ,EAAEF,GAAG,EAAEG,GAAG,CAAC,2BAA2B,CAAC,EAAEJ,KAAK,EAAEK,OAAO,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IACD,OAAO;QAAErC,WAAW,EAAE,EAAE;KAAE,CAAC;AAC7B,CAAC,CAAC;AACF,MAAMsC,UAAU,GAAGtD,0DAAS,CAACD,sEAAW,CAAC,CAACY,KAAK,CAAC;AAChD,iEAAeV,wDAAU,CAACqD,UAAU,CAAQ,EAAC�webpack://./pages/_app.tsx�
  import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';
import TrackingApp from '@vieon/tracking/services/functions/TrackingApp';
import { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';
import { PAGE } from '@vieon/core/constants/constants';
import LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';
import AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';
import createStore from '@vieon/core/store/createStore';
import '@vieon/ui-kits/styles/globals.scss';
import '@vieon/ui-kits/styles/methodItem.css';
import '@vieon/ui-kits/styles/rotate.css';
import '@vieon/ui-kits/styles/style.css';
import withRedux from 'next-redux-wrapper';
import { AppProps } from 'next/app';
import { withRouter } from 'next/router';
import React, { useEffect } from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-popper-tooltip/dist/styles.css';
import { Provider } from 'react-redux';

const EXPORT_SSR =
  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'
    ? process.env.NEXT_PUBLIC_EXPORT_SSR
    : 'true';
if (!process.browser) React.useLayoutEffect = React.useEffect;

interface CustomAppProps extends AppProps {
  store: any;
  layoutProps?: any;
}

function MyApp(props: any): any {
  const { Component, store, pageProps, layoutProps, router } = props || {};

  const componentProps = { router, layoutProps, pageProps };
  const pathname = router?.pathname;
  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);
  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);

  useEffect(() => {
    const checkAndProcessCache = async () => {
      const cache = await caches.open('offline-cache-v1');
      const response = await cache.match('/tracking-data');

      if (response && response.ok) {
        const clonedResponse = response.clone();
        const trackingData = await clonedResponse.json();
        TrackingApp.offlineDetect(trackingData);
        await cache.delete('/tracking-data');
      }
    };

    checkAndProcessCache();

    window.addEventListener('online', checkAndProcessCache);

    return () => {
      window.removeEventListener('online', checkAndProcessCache);
    };
  }, []);

  if (isTPBank || isListWinners) {
    return (
      <Provider store={store}>
        <Component {...componentProps} />
      </Provider>
    );
  }

  return (
    <>
      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}
      <ReCaptchaProvider>
        <Provider store={store}>
          <LayoutContainer {...layoutProps}>
            <Component {...componentProps} />
          </LayoutContainer>
        </Provider>
      </ReCaptchaProvider>
    </>
  );
}

MyApp.getInitialProps = async ({ Component, ctx }: any) => {
  const usingSSR = EXPORT_SSR === 'true';
  if (usingSSR) {
    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };
    try {
      const layoutProps = LayoutContainer.getInitialProps
        ? await LayoutContainer.getInitialProps(ctx)
        : {};
      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
      if (pageProps && Object.keys(pageProps).length > 0) {
        return { layoutProps, pageProps };
      }
      return { layoutProps };
    } catch (error: any) {
      const { req } = ctx;
      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
    }
  }
  return { layoutProps: {} };
};
const WrappedApp = withRedux(createStore)(MyApp);
export default withRouter(WrappedApp as any);
`>�ReCaptchaProvider�TrackingApp�ENABLE_SDK_AIACTIV�PAGE�LayoutContainer�AAdsNetwork�createStore�withRedux�withRouter�React�useEffect�Provider�EXPORT_SSR�process�env�NEXT_PUBLIC_EXPORT_SSR�browser�useLayoutEffect�MyApp�props�Component�store�pageProps�layoutProps�router�componentProps�pathname�isTPBank�includes�PAYMENT_TPBANK�isListWinners�LIST_WINNERS�checkAndProcessCache�cache�caches�open�response�match�ok�clonedResponse�clone�trackingData�json�offlineDetect�delete�window�addEventListener�removeEventListener�getInitialProps�ctx�usingSSR�asPath�Object�keys�length�error�req�console�log�url�message�WrappedApp��__webpack_require__�__webpack_require__.n�__webpack_exports__�__webpack_require__.r�__webpack_require__.d��   �  �webpack/lib/util/registerExternalSerializer�webpack-sources/RawSource�  throw new Error("Module parse failed: Unexpected token (3:17)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> const AAdsNetwork: React.FC = () => {\n|   // Placeholder for AAdsNetwork component\n|   // You can add your actual ads network logic here");��  throw new Error("Module parse failed: Unexpected token (3:17)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> const AAdsNetwork: React.FC = () => {\n|   // Placeholder for AAdsNetwork component\n|   // You can add your actual ads network logic here");�� 