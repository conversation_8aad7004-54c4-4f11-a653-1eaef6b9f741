wpc   l�  �webpack/lib/cache/PackFileCacheStrategy�PackContentItems�	�   ResolverCachePlugin|normal|default|dependencyType=|esm|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon|request=|private-next-pages/_app�  Compilation/modules|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use!/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsxX  ResolverCachePlugin|normal|default|dependencyType=|esm|modules=[|0=|node_modules|]|fallback=|false|exportsFields=[|0=|exports|]|importsFields=[|0=|imports|]|conditionNames=[|0=|node|1=|import|]|descriptionFiles=[|0=|package.json|]|extensions=[|0=|.js|1=|.json|2=|.node|]|enforceExtensions=|false|symlinks=|true|mainFields=[|0=|main|]|mainFiles=[|0=|index|]|roots=[|]|fullySpecified=|true|preferRelative=|false|preferAbsolute=|false|restrictions=[|]|alias=|false|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/tracking/services/functions/TrackingAppq  ResolverCachePlugin|normal|default|dependencyType=|esm|modules=[|0=|node_modules|]|fallback=|false|exportsFields=[|0=|exports|]|importsFields=[|0=|imports|]|conditionNames=[|0=|node|1=|import|]|descriptionFiles=[|0=|package.json|]|extensions=[|0=|.js|1=|.json|2=|.node|]|enforceExtensions=|false|symlinks=|true|mainFields=[|0=|main|]|mainFiles=[|0=|index|]|roots=[|]|fullySpecified=|true|preferRelative=|false|preferAbsolute=|false|restrictions=[|]|alias=|false|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp|query=||fragment=||module=|true|directory=|false|file=|false|internal=|false|fullySpecified=|true|descriptionFilePath=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json|descriptionFileData=[|name=|@vieon/web-vieon|version=|1.0.0|main=|server/index.js|author=|https://vieon.vn|license=|VieON|description=|VieON Web Application - Main Next.js application|scripts=[|start=|nodemon -w server server/index.ts|analyze=|NEXT_PUBLIC_ANALYZE=true next build|prepare=|pnpm run clean|build:server=|tsc --project tsconfig.server.json|build=|pnpm run clean && cross-env NEXT_PUBLIC_NODE_ENV=production next build && pnpm run build:server|serve=|cross-env NEXT_PUBLIC_NODE_ENV=production node build/index.js|serve-static=|npm run export && cross-env NEXT_PUBLIC_NODE_ENV=production node server-static/index.js|export=|NEXT_PUBLIC_EXPORT_SSR=false next export -o _next/static|export-ssr=|NEXT_PUBLIC_NODE_ENV=production NEXT_PUBLIC_EXPORT_SSR=true next export -o _next/static|export-server=|NEXT_PUBLIC_NODE_ENV=production node server-static/index.js|test:ci=|jest --maxWorkers=8 --ci --coverage|test=|jest|test-watch=|jest --watchAll|clean=|rimraf node_modules/.cache _next|lint:fix=|eslint --fix .|format:fix=|prettier --write .|check-format=|prettier --check .|]|dependencies=[|@floating-ui/react-dom=|2.0.2|@floating-ui/react-dom-interactions=|0.10.3|@next/env=|^12.3.4|@sentry/nextjs=|7.77.0|@vieon/analytics-node=|^1.0.0|ajv=|^8.17.1|bowser=|2.11.0|cookie-parser=|1.4.6|cross-env=|^7.0.3|date-fns=|^4.1.0|express=|4.18.2|fingerprintjs2=|^2.1.4|firebase=|10.5.2|ip=|1.1.8|lru-cache=|10.0.1|next=|12.3.4|next-redux-wrapper=|4.0.1|next-seo=|4.29.0|nodemon=|2.0.22|pnpm=|^10.5.2|postcss=|^8.4.31|prop-types=|15.8.1|react=|18.2.0|react-datepicker=|^7.6.0|react-device-detect=|2.2.3|react-dom=|18.2.0|react-google-recaptcha-v3=|^1.11.0|react-popper-tooltip=|2.11.1|react-redux=|7.2.9|react-responsive=|^10.0.0|react-select=|^5.10.0|swiper=|^6.8.4|tailwind-scrollbar=|^4.0.1|tailwindcss=|^3.4.17|terser-webpack-plugin=|5.3.9|ts-migrate=|^0.1.35|ts-node=|^10.9.2|webpack=|5.94.0|webpack-bundle-analyzer=|^4.9.1|xml-js=|^1.6.11|@vieon/core=|workspace:*|@vieon/ui-kits=|workspace:*|@vieon/auth=|workspace:*|@vieon/tracking=|workspace:*|@vieon/models=|workspace:*|@vieon/player=|workspace:*|@vieon/payment=|workspace:*|@vieon/ads=|workspace:*|]|devDependencies=[|@testing-library/jest-dom=|^5.17.0|@types/compression=|^1.8.0|@types/cookie-parser=|^1.4.8|@types/crypto-js=|^4.2.2|@types/ejs=|^3.1.5|@types/eslint=|^9.6.1|@types/express=|^5.0.2|@types/jest=|^29.5.14|@types/lodash=|^4.17.17|@types/node=|^22.15.24|@types/prettier=|^2.7.3|@types/qrcode=|^1.5.5|@types/react=|^19.1.6|@types/react-cookies=|^0.1.4|@types/react-datepicker=|^7.0.0|@types/react-dom=|^19.1.5|@types/react-lottie=|^1.2.10|@types/react-redux=|^7.1.34|@types/webpack-bundle-analyzer=|^4.7.0|@typescript-eslint/eslint-plugin=|^8.33.0|@typescript-eslint/parser=|^8.33.0|autoprefixer=|^10.4.20|compression=|^1.7.4|copy-webpack-plugin=|^11.0.0|ejs=|^3.1.9|eslint=|^8.55.0|eslint-config-prettier=|^9.0.0|eslint-plugin-prettier=|^3.4.1|eslint-plugin-react=|^7.33.2|husky=|^8.0.3|jest=|^29.7.0|jest-cli=|^29.7.0|lint-staged=|^12.5.0|mini-css-extract-plugin=|^2.7.6|prettier=|^2.8.8|redux-devtools-extension=|^2.13.9|rimraf=|^3.0.2|sass=|^1.84.0|typescript=|^5.8.3|webpack=|5.94.0|webpack-bundle-analyzer=|^4.10.2|]|resolutions=[|react=|18.2.0|]|engines=[|node=|>=18.16.0|]|browserslist=[|0=|>0.3%|1=|not ie 11|2=|not dead|3=|not op_mini all|]|packageManager=|pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977|]|descriptionFileRoot=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon|relativePath=|./pagesR  ResolverCachePlugin|normal|default|dependencyType=|commonjs|modules=[|0=|node_modules|]|fallback=|false|exportsFields=[|0=|exports|]|importsFields=[|0=|imports|]|conditionNames=[|0=|node|1=|require|]|descriptionFiles=[|0=|package.json|]|extensions=[|0=|.js|1=|.json|2=|.node|]|enforceExtensions=|false|symlinks=|true|mainFields=[|0=|main|]|mainFiles=[|0=|index|]|roots=[|]|fullySpecified=|false|preferRelative=|false|preferAbsolute=|false|restrictions=[|]|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/tracking/services/functions/TrackingAppl  ResolverCachePlugin|normal|default|dependencyType=|commonjs|modules=[|0=|node_modules|]|fallback=|false|exportsFields=[|0=|exports|]|importsFields=[|0=|imports|]|conditionNames=[|0=|node|1=|require|]|descriptionFiles=[|0=|package.json|]|extensions=[|0=|.js|1=|.json|2=|.node|]|enforceExtensions=|false|symlinks=|true|mainFields=[|0=|main|]|mainFiles=[|0=|index|]|roots=[|]|fullySpecified=|false|preferRelative=|false|preferAbsolute=|false|restrictions=[|]|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp|query=||fragment=||module=|true|directory=|false|file=|false|internal=|false|fullySpecified=|false|descriptionFilePath=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json|descriptionFileData=[|name=|@vieon/web-vieon|version=|1.0.0|main=|server/index.js|author=|https://vieon.vn|license=|VieON|description=|VieON Web Application - Main Next.js application|scripts=[|start=|nodemon -w server server/index.ts|analyze=|NEXT_PUBLIC_ANALYZE=true next build|prepare=|pnpm run clean|build:server=|tsc --project tsconfig.server.json|build=|pnpm run clean && cross-env NEXT_PUBLIC_NODE_ENV=production next build && pnpm run build:server|serve=|cross-env NEXT_PUBLIC_NODE_ENV=production node build/index.js|serve-static=|npm run export && cross-env NEXT_PUBLIC_NODE_ENV=production node server-static/index.js|export=|NEXT_PUBLIC_EXPORT_SSR=false next export -o _next/static|export-ssr=|NEXT_PUBLIC_NODE_ENV=production NEXT_PUBLIC_EXPORT_SSR=true next export -o _next/static|export-server=|NEXT_PUBLIC_NODE_ENV=production node server-static/index.js|test:ci=|jest --maxWorkers=8 --ci --coverage|test=|jest|test-watch=|jest --watchAll|clean=|rimraf node_modules/.cache _next|lint:fix=|eslint --fix .|format:fix=|prettier --write .|check-format=|prettier --check .|]|dependencies=[|@floating-ui/react-dom=|2.0.2|@floating-ui/react-dom-interactions=|0.10.3|@next/env=|^12.3.4|@sentry/nextjs=|7.77.0|@vieon/analytics-node=|^1.0.0|ajv=|^8.17.1|bowser=|2.11.0|cookie-parser=|1.4.6|cross-env=|^7.0.3|date-fns=|^4.1.0|express=|4.18.2|fingerprintjs2=|^2.1.4|firebase=|10.5.2|ip=|1.1.8|lru-cache=|10.0.1|next=|12.3.4|next-redux-wrapper=|4.0.1|next-seo=|4.29.0|nodemon=|2.0.22|pnpm=|^10.5.2|postcss=|^8.4.31|prop-types=|15.8.1|react=|18.2.0|react-datepicker=|^7.6.0|react-device-detect=|2.2.3|react-dom=|18.2.0|react-google-recaptcha-v3=|^1.11.0|react-popper-tooltip=|2.11.1|react-redux=|7.2.9|react-responsive=|^10.0.0|react-select=|^5.10.0|swiper=|^6.8.4|tailwind-scrollbar=|^4.0.1|tailwindcss=|^3.4.17|terser-webpack-plugin=|5.3.9|ts-migrate=|^0.1.35|ts-node=|^10.9.2|webpack=|5.94.0|webpack-bundle-analyzer=|^4.9.1|xml-js=|^1.6.11|@vieon/core=|workspace:*|@vieon/ui-kits=|workspace:*|@vieon/auth=|workspace:*|@vieon/tracking=|workspace:*|@vieon/models=|workspace:*|@vieon/player=|workspace:*|@vieon/payment=|workspace:*|@vieon/ads=|workspace:*|]|devDependencies=[|@testing-library/jest-dom=|^5.17.0|@types/compression=|^1.8.0|@types/cookie-parser=|^1.4.8|@types/crypto-js=|^4.2.2|@types/ejs=|^3.1.5|@types/eslint=|^9.6.1|@types/express=|^5.0.2|@types/jest=|^29.5.14|@types/lodash=|^4.17.17|@types/node=|^22.15.24|@types/prettier=|^2.7.3|@types/qrcode=|^1.5.5|@types/react=|^19.1.6|@types/react-cookies=|^0.1.4|@types/react-datepicker=|^7.0.0|@types/react-dom=|^19.1.5|@types/react-lottie=|^1.2.10|@types/react-redux=|^7.1.34|@types/webpack-bundle-analyzer=|^4.7.0|@typescript-eslint/eslint-plugin=|^8.33.0|@typescript-eslint/parser=|^8.33.0|autoprefixer=|^10.4.20|compression=|^1.7.4|copy-webpack-plugin=|^11.0.0|ejs=|^3.1.9|eslint=|^8.55.0|eslint-config-prettier=|^9.0.0|eslint-plugin-prettier=|^3.4.1|eslint-plugin-react=|^7.33.2|husky=|^8.0.3|jest=|^29.7.0|jest-cli=|^29.7.0|lint-staged=|^12.5.0|mini-css-extract-plugin=|^2.7.6|prettier=|^2.8.8|redux-devtools-extension=|^2.13.9|rimraf=|^3.0.2|sass=|^1.84.0|typescript=|^5.8.3|webpack=|5.94.0|webpack-bundle-analyzer=|^4.10.2|]|resolutions=[|react=|18.2.0|]|engines=[|node=|>=18.16.0|]|browserslist=[|0=|>0.3%|1=|not ie 11|2=|not dead|3=|not op_mini all|]|packageManager=|pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977|]|descriptionFileRoot=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon|relativePath=|./pages�   ResolverCachePlugin|normal|default|dependencyType=|esm|path=|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages|request=|@vieon/tracking/services/functions/TrackingApp�  FlagDependencyExportsPlugin|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use!/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�   Compilation/codeGeneration|/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.ts|webpack-runtime�webpack/lib/cache/ResolverCachePlugin��`�_ResolverCachePluginCacheMiss�context�path�request�query�fragment�module�directory�file�internal�fullySpecified�descriptionFilePath�descriptionFileData�descriptionFileRoot�relativePath�issuer�issuerLayer�compiler��server�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�� �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json`
�name�version�main�author�license�description�scripts�dependencies�devDependencies�resolutions�engines�browserslist�packageManager�@vieon/web-vieon�1.0.0�server/index.js�https://vieon.vn�VieON�VieON Web Application - Main Next.js application`�start�analyze�prepare�build:server�build�serve�serve-static�export�export-ssr�export-server�test:ci�test�test-watch�clean�lint:fix�format:fix�check-format�nodemon -w server server/index.ts�NEXT_PUBLIC_ANALYZE=true next build�pnpm run clean�tsc --project tsconfig.server.json�pnpm run clean && cross-env NEXT_PUBLIC_NODE_ENV=production next build && pnpm run build:server�cross-env NEXT_PUBLIC_NODE_ENV=production node build/index.js�npm run export && cross-env NEXT_PUBLIC_NODE_ENV=production node server-static/index.js�NEXT_PUBLIC_EXPORT_SSR=false next export -o _next/static�NEXT_PUBLIC_NODE_ENV=production NEXT_PUBLIC_EXPORT_SSR=true next export -o _next/static�NEXT_PUBLIC_NODE_ENV=production node server-static/index.js�jest --maxWorkers=8 --ci --coverage�jest�jest --watchAll�rimraf node_modules/.cache _next�eslint --fix .�prettier --write .�prettier --check .`0�@floating-ui/react-dom�@floating-ui/react-dom-interactions�@next/env�@sentry/nextjs�@vieon/analytics-node�ajv�bowser�cookie-parser�cross-env�date-fns�express�fingerprintjs2�firebase�ip�lru-cache�next�next-redux-wrapper�next-seo�nodemon�pnpm�postcss�prop-types�react�react-datepicker�react-device-detect�react-dom�react-google-recaptcha-v3�react-popper-tooltip�react-redux�react-responsive�react-select�swiper�tailwind-scrollbar�tailwindcss�terser-webpack-plugin�ts-migrate�ts-node�webpack�webpack-bundle-analyzer�xml-js�@vieon/core�@vieon/ui-kits�@vieon/auth�@vieon/tracking�@vieon/models�@vieon/player�@vieon/payment�@vieon/ads�2.0.2�0.10.3�^12.3.4�7.77.0�^1.0.0�^8.17.1�2.11.0�1.4.6�^7.0.3�^4.1.0�4.18.2�^2.1.4�10.5.2�1.1.8�10.0.1�12.3.4�4.0.1�4.29.0�2.0.22�^10.5.2�^8.4.31�15.8.1�18.2.0�^7.6.0�2.2.3��^1.11.0�2.11.1�7.2.9�^10.0.0�^5.10.0�^6.8.4�^4.0.1�^3.4.17�5.3.9�^0.1.35�^10.9.2�5.94.0�^4.9.1�^1.6.11�workspace:*�������`)�@testing-library/jest-dom�@types/compression�@types/cookie-parser�@types/crypto-js�@types/ejs�@types/eslint�@types/express�@types/jest�@types/lodash�@types/node�@types/prettier�@types/qrcode�@types/react�@types/react-cookies�@types/react-datepicker�@types/react-dom�@types/react-lottie�@types/react-redux�@types/webpack-bundle-analyzer�@typescript-eslint/eslint-plugin�@typescript-eslint/parser�autoprefixer�compression�copy-webpack-plugin�ejs�eslint�eslint-config-prettier�eslint-plugin-prettier�eslint-plugin-react�husky��jest-cli�lint-staged�mini-css-extract-plugin�prettier�redux-devtools-extension�rimraf�sass�typescript���^5.17.0�^1.8.0�^1.4.8�^4.2.2�^3.1.5�^9.6.1�^5.0.2�^29.5.14�^4.17.17�^22.15.24�^2.7.3�^1.5.5�^19.1.6�^0.1.4�^7.0.0�^19.1.5�^1.2.10�^7.1.34�^4.7.0�^8.33.0��^10.4.20�^1.7.4�^11.0.0�^3.1.9�^8.55.0�^9.0.0�^3.4.1�^7.33.2�^8.0.3�^29.7.0��^12.5.0�^2.7.6�^2.8.8�^2.13.9�^3.0.2�^1.84.0�^5.8.3��^4.10.2n�����node�>=18.16.0�>0.3%�not ie 11�not dead�not op_mini all�   pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon�./pages/_app.tsx�webpack/lib/FileSystemInfo�Snapshot@�    �l�zyB����/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps�safeTime�timestamp! �R�zyB �R�zyB� � � `�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.mjs�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.ts�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.jsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx.wasm�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/package.json�  �  �l�zyB�����/Users/<USER>/Desktop/Project/VieON/web-mono-repo�/Users/<USER>/Desktop/Project/VieON�/Users/<USER>/Desktop/Project�/Users/<USER>/Desktop�/Users/<USER>/Users�/�! �zyB  �zyB� � � � � � � @�   �   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/lib/NormalModule�webpack/lib/util/registerExternalSerializer�webpack-sources/SourceMapSource�  import { jsxDEV as _jsxDEV, Fragment as _Fragment } from "react/jsx-dev-runtime";
import ReCaptchaProvider from "@vieon/provider/providerRecaptcha";
import TrackingApp from "@vieon/tracking/services/functions/TrackingApp";
import { ENABLE_SDK_AIACTIV } from "@vieon/core/config/ConfigEnv";
import { PAGE } from "@vieon/core/constants/constants";
import LayoutContainer from "@vieon/ui-kits/components/containers/LayoutContainer";
import AAdsNetwork from "@vieon/core/utils/script/AAdsNetwork";
import createStore from "@vieon/core/store/createStore";
import "@vieon/ui-kits/styles/globals.scss";
import "@vieon/ui-kits/styles/methodItem.css";
import "@vieon/ui-kits/styles/rotate.css";
import "@vieon/ui-kits/styles/style.css";
import withRedux from "next-redux-wrapper";
import { withRouter } from "next/router";
import React, { useEffect } from "react";
import "react-datepicker/dist/react-datepicker.css";
import "react-popper-tooltip/dist/styles.css";
import { Provider } from "react-redux";
const EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== "undefined" ? process.env.NEXT_PUBLIC_EXPORT_SSR : "true";
if (!process.browser) React.useLayoutEffect = React.useEffect;
function MyApp(props) {
    const { Component , store , pageProps , layoutProps , router  } = props || {};
    const componentProps = {
        router,
        layoutProps,
        pageProps
    };
    const pathname = router?.pathname;
    const isTPBank = (pathname || "").includes(PAGE.PAYMENT_TPBANK);
    const isListWinners = (pathname || "").includes(PAGE.LIST_WINNERS);
    useEffect(()=>{
        const checkAndProcessCache = async ()=>{
            const cache = await caches.open("offline-cache-v1");
            const response = await cache.match("/tracking-data");
            if (response && response.ok) {
                const clonedResponse = response.clone();
                const trackingData = await clonedResponse.json();
                TrackingApp.offlineDetect(trackingData);
                await cache.delete("/tracking-data");
            }
        };
        checkAndProcessCache();
        window.addEventListener("online", checkAndProcessCache);
        return ()=>{
            window.removeEventListener("online", checkAndProcessCache);
        };
    }, []);
    if (isTPBank || isListWinners) {
        return /*#__PURE__*/ _jsxDEV(Provider, {
            store: store,
            children: /*#__PURE__*/ _jsxDEV(Component, {
                ...componentProps
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
            lineNumber: 63,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ _jsxDEV(_Fragment, {
        children: [
            ENABLE_SDK_AIACTIV && /*#__PURE__*/ _jsxDEV(AAdsNetwork, {}, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 71,
                columnNumber: 30
            }, this),
            /*#__PURE__*/ _jsxDEV(ReCaptchaProvider, {
                children: /*#__PURE__*/ _jsxDEV(Provider, {
                    store: store,
                    children: /*#__PURE__*/ _jsxDEV(LayoutContainer, {
                        ...layoutProps,
                        children: /*#__PURE__*/ _jsxDEV(Component, {
                            ...componentProps
                        }, void 0, false, {
                            fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                            lineNumber: 75,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                        lineNumber: 74,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
MyApp.getInitialProps = async ({ Component , ctx  })=>{
    const usingSSR = EXPORT_SSR === "true";
    if (usingSSR) {
        if ((ctx?.asPath || "").includes(PAGE.PAYMENT_TPBANK)) return {
            layoutProps: {}
        };
        try {
            const layoutProps = LayoutContainer.getInitialProps ? await LayoutContainer.getInitialProps(ctx) : {};
            const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};
            if (pageProps && Object.keys(pageProps).length > 0) {
                return {
                    layoutProps,
                    pageProps
                };
            }
            return {
                layoutProps
            };
        } catch (error) {
            const { req  } = ctx;
            console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);
        }
    }
    return {
        layoutProps: {}
    };
};
const WrappedApp = withRedux(createStore)(MyApp);
export default withRouter(WrappedApp);
  webpack://../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js??ruleSet[1].rules[2].oneOf[2].use!./pages/_app.tsxb  {"version":3,"sources":["webpack://./pages/_app.tsx"],"sourcesContent":["import ReCaptchaProvider from '@vieon/provider/providerRecaptcha';\nimport TrackingApp from '@vieon/tracking/services/functions/TrackingApp';\nimport { ENABLE_SDK_AIACTIV } from '@vieon/core/config/ConfigEnv';\nimport { PAGE } from '@vieon/core/constants/constants';\nimport LayoutContainer from '@vieon/ui-kits/components/containers/LayoutContainer';\nimport AAdsNetwork from '@vieon/core/utils/script/AAdsNetwork';\nimport createStore from '@vieon/core/store/createStore';\nimport '@vieon/ui-kits/styles/globals.scss';\nimport '@vieon/ui-kits/styles/methodItem.css';\nimport '@vieon/ui-kits/styles/rotate.css';\nimport '@vieon/ui-kits/styles/style.css';\nimport withRedux from 'next-redux-wrapper';\nimport { AppProps } from 'next/app';\nimport { withRouter } from 'next/router';\nimport React, { useEffect } from 'react';\nimport 'react-datepicker/dist/react-datepicker.css';\nimport 'react-popper-tooltip/dist/styles.css';\nimport { Provider } from 'react-redux';\n\nconst EXPORT_SSR =\n  typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== 'undefined'\n    ? process.env.NEXT_PUBLIC_EXPORT_SSR\n    : 'true';\nif (!process.browser) React.useLayoutEffect = React.useEffect;\n\ninterface CustomAppProps extends AppProps {\n  store: any;\n  layoutProps?: any;\n}\n\nfunction MyApp(props: any): any {\n  const { Component, store, pageProps, layoutProps, router } = props || {};\n\n  const componentProps = { router, layoutProps, pageProps };\n  const pathname = router?.pathname;\n  const isTPBank = (pathname || '').includes(PAGE.PAYMENT_TPBANK);\n  const isListWinners = (pathname || '').includes(PAGE.LIST_WINNERS);\n\n  useEffect(() => {\n    const checkAndProcessCache = async () => {\n      const cache = await caches.open('offline-cache-v1');\n      const response = await cache.match('/tracking-data');\n\n      if (response && response.ok) {\n        const clonedResponse = response.clone();\n        const trackingData = await clonedResponse.json();\n        TrackingApp.offlineDetect(trackingData);\n        await cache.delete('/tracking-data');\n      }\n    };\n\n    checkAndProcessCache();\n\n    window.addEventListener('online', checkAndProcessCache);\n\n    return () => {\n      window.removeEventListener('online', checkAndProcessCache);\n    };\n  }, []);\n\n  if (isTPBank || isListWinners) {\n    return (\n      <Provider store={store}>\n        <Component {...componentProps} />\n      </Provider>\n    );\n  }\n\n  return (\n    <>\n      {ENABLE_SDK_AIACTIV && <AAdsNetwork />}\n      <ReCaptchaProvider>\n        <Provider store={store}>\n          <LayoutContainer {...layoutProps}>\n            <Component {...componentProps} />\n          </LayoutContainer>\n        </Provider>\n      </ReCaptchaProvider>\n    </>\n  );\n}\n\nMyApp.getInitialProps = async ({ Component, ctx }: any) => {\n  const usingSSR = EXPORT_SSR === 'true';\n  if (usingSSR) {\n    if ((ctx?.asPath || '').includes(PAGE.PAYMENT_TPBANK)) return { layoutProps: {} };\n    try {\n      const layoutProps = LayoutContainer.getInitialProps\n        ? await LayoutContainer.getInitialProps(ctx)\n        : {};\n      const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};\n      if (pageProps && Object.keys(pageProps).length > 0) {\n        return { layoutProps, pageProps };\n      }\n      return { layoutProps };\n    } catch (error: any) {\n      const { req } = ctx;\n      console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);\n    }\n  }\n  return { layoutProps: {} };\n};\nconst WrappedApp = withRedux(createStore)(MyApp);\nexport default withRouter(WrappedApp as any);\n"],"names":["ReCaptchaProvider","TrackingApp","ENABLE_SDK_AIACTIV","PAGE","LayoutContainer","AAdsNetwork","createStore","withRedux","withRouter","React","useEffect","Provider","EXPORT_SSR","process","env","NEXT_PUBLIC_EXPORT_SSR","browser","useLayoutEffect","MyApp","props","Component","store","pageProps","layoutProps","router","componentProps","pathname","isTPBank","includes","PAYMENT_TPBANK","isListWinners","LIST_WINNERS","checkAndProcessCache","cache","caches","open","response","match","ok","clonedResponse","clone","trackingData","json","offlineDetect","delete","window","addEventListener","removeEventListener","getInitialProps","ctx","usingSSR","asPath","Object","keys","length","error","req","console","log","url","message","WrappedApp"],"mappings":"AAAA;AAAA,OAAOA,iBAAiB,MAAM,mCAAmC,CAAC;AAClE,OAAOC,WAAW,MAAM,gDAAgD,CAAC;AACzE,SAASC,kBAAkB,QAAQ,8BAA8B,CAAC;AAClE,SAASC,IAAI,QAAQ,iCAAiC,CAAC;AACvD,OAAOC,eAAe,MAAM,sDAAsD,CAAC;AACnF,OAAOC,WAAW,MAAM,sCAAsC,CAAC;AAC/D,OAAOC,WAAW,MAAM,+BAA+B,CAAC;AACxD,OAAO,oCAAoC,CAAC;AAC5C,OAAO,sCAAsC,CAAC;AAC9C,OAAO,kCAAkC,CAAC;AAC1C,OAAO,iCAAiC,CAAC;AACzC,OAAOC,SAAS,MAAM,oBAAoB,CAAC;AAE3C,SAASC,UAAU,QAAQ,aAAa,CAAC;AACzC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO,CAAC;AACzC,OAAO,4CAA4C,CAAC;AACpD,OAAO,sCAAsC,CAAC;AAC9C,SAASC,QAAQ,QAAQ,aAAa,CAAC;AAEvC,MAAMC,UAAU,GACd,OAAOC,OAAO,CAACC,GAAG,CAACC,sBAAsB,KAAK,WAAW,GACrDF,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClC,MAAM,AAAC;AACb,IAAI,CAACF,OAAO,CAACG,OAAO,EAAEP,KAAK,CAACQ,eAAe,GAAGR,KAAK,CAACC,SAAS,CAAC;AAO9D,SAASQ,KAAK,CAACC,KAAU,EAAO;IAC9B,MAAM,EAAEC,SAAS,CAAA,EAAEC,KAAK,CAAA,EAAEC,SAAS,CAAA,EAAEC,WAAW,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGL,KAAK,IAAI,EAAE,AAAC;IAEzE,MAAMM,cAAc,GAAG;QAAED,MAAM;QAAED,WAAW;QAAED,SAAS;KAAE,AAAC;IAC1D,MAAMI,QAAQ,GAAGF,MAAM,EAAEE,QAAQ,AAAC;IAClC,MAAMC,QAAQ,GAAG,AAACD,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,AAAC;IAChE,MAAMC,aAAa,GAAG,AAACJ,CAAAA,QAAQ,IAAI,EAAE,CAAA,CAAEE,QAAQ,CAACzB,IAAI,CAAC4B,YAAY,CAAC,AAAC;IAEnErB,SAAS,CAAC,IAAM;QACd,MAAMsB,oBAAoB,GAAG,UAAY;YACvC,MAAMC,KAAK,GAAG,MAAMC,MAAM,CAACC,IAAI,CAAC,kBAAkB,CAAC,AAAC;YACpD,MAAMC,QAAQ,GAAG,MAAMH,KAAK,CAACI,KAAK,CAAC,gBAAgB,CAAC,AAAC;YAErD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,EAAE,EAAE;gBAC3B,MAAMC,cAAc,GAAGH,QAAQ,CAACI,KAAK,EAAE,AAAC;gBACxC,MAAMC,YAAY,GAAG,MAAMF,cAAc,CAACG,IAAI,EAAE,AAAC;gBACjDzC,WAAW,CAAC0C,aAAa,CAACF,YAAY,CAAC,CAAC;gBACxC,MAAMR,KAAK,CAACW,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,AAAC;QAEFZ,oBAAoB,EAAE,CAAC;QAEvBa,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,oBAAoB,CAAC,CAAC;QAExD,OAAO,IAAM;YACXa,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEf,oBAAoB,CAAC,CAAC;QAC7D,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAIL,QAAQ,IAAIG,aAAa,EAAE;QAC7B,qBACE,QAACnB,QAAQ;YAACU,KAAK,EAAEA,KAAK;sBACpB,cAAA,QAACD,SAAS;gBAAE,GAAGK,cAAc;;;;;oBAAI;;;;;gBACxB,CACX;IACJ,CAAC;IAED,qBACE;;YACGvB,kBAAkB,kBAAI,QAACG,WAAW;;;;oBAAG;0BACtC,QAACL,iBAAiB;0BAChB,cAAA,QAACW,QAAQ;oBAACU,KAAK,EAAEA,KAAK;8BACpB,cAAA,QAACjB,eAAe;wBAAE,GAAGmB,WAAW;kCAC9B,cAAA,QAACH,SAAS;4BAAE,GAAGK,cAAc;;;;;gCAAI;;;;;4BACjB;;;;;wBACT;;;;;oBACO;;oBACnB,CACH;AACJ,CAAC;AAEDP,KAAK,CAAC8B,eAAe,GAAG,OAAO,EAAE5B,SAAS,CAAA,EAAE6B,GAAG,CAAA,EAAO,GAAK;IACzD,MAAMC,QAAQ,GAAGtC,UAAU,KAAK,MAAM,AAAC;IACvC,IAAIsC,QAAQ,EAAE;QACZ,IAAI,AAACD,CAAAA,GAAG,EAAEE,MAAM,IAAI,EAAE,CAAA,CAAEvB,QAAQ,CAACzB,IAAI,CAAC0B,cAAc,CAAC,EAAE,OAAO;YAAEN,WAAW,EAAE,EAAE;SAAE,CAAC;QAClF,IAAI;YACF,MAAMA,WAAW,GAAGnB,eAAe,CAAC4C,eAAe,GAC/C,MAAM5C,eAAe,CAAC4C,eAAe,CAACC,GAAG,CAAC,GAC1C,EAAE,AAAC;YACP,MAAM3B,SAAS,GAAGF,SAAS,CAAC4B,eAAe,GAAG,MAAM5B,SAAS,CAAC4B,eAAe,CAACC,GAAG,CAAC,GAAG,EAAE,AAAC;YACxF,IAAI3B,SAAS,IAAI8B,MAAM,CAACC,IAAI,CAAC/B,SAAS,CAAC,CAACgC,MAAM,GAAG,CAAC,EAAE;gBAClD,OAAO;oBAAE/B,WAAW;oBAAED,SAAS;iBAAE,CAAC;YACpC,CAAC;YACD,OAAO;gBAAEC,WAAW;aAAE,CAAC;QACzB,EAAE,OAAOgC,KAAK,EAAO;YACnB,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGP,GAAG,AAAC;YACpBQ,OAAO,CAACC,GAAG,CAAC,CAAC,QAAQ,EAAEF,GAAG,EAAEG,GAAG,CAAC,2BAA2B,CAAC,EAAEJ,KAAK,EAAEK,OAAO,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IACD,OAAO;QAAErC,WAAW,EAAE,EAAE;KAAE,CAAC;AAC7B,CAAC,CAAC;AACF,MAAMsC,UAAU,GAAGtD,SAAS,CAACD,WAAW,CAAC,CAACY,KAAK,CAAC,AAAC;AACjD,eAAeV,UAAU,CAACqD,UAAU,CAAQ,CAAC","file":"x"}�exportsType�namespace
�javascript/auto`�
�`�cacheable�parsed�fileDependencies�contextDependencies�missingDependencies�buildDependencies�valueDependencies�hash�assets�assetsInfo�strict�exportsArgument�topLevelDeclarations�snapshot�webpack/lib/util/LazySet�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js	�webpack/DefinePlugin_hash�webpack/DefinePlugin process.browser�0a54965e�false�e25f19491c2f2991�__webpack_exports__�MyApp�EXPORT_SSR�WrappedApp  Pd�zyB	�����`�webpack/lib/dependencies/HarmonyCompatibilityDependency
d� � ��webpack/lib/dependencies/ConstDependency� `Q

 `Q�`R@�   

 `B�A�   �   

 `I�A�   !  

 `B�A"  Y  

 `7�AZ  �  

 `S�A�  �  

 `?�A�  &  

 `8�A'  S  

	 	`,�AT  �  

 
`.�A�  �  

c *�A�  �  

c )�A�    

c
 
+�A  -  

c )�A.  W  

c )�AX  �  

c 4�A�  �  

c .�A�  �  

c '�trueAd  t  

c >�webpack/lib/dependencies/HarmonyExportHeaderDependency	Aj  �  	A[  �  

@�    @�   `&	`.�webpack/lib/dependencies/HarmonyImportSideEffectDependency�react/jsx-dev-runtime�

 `Q�@vieon/provider/providerRecaptcha�

 `B�@vieon/tracking/services/functions/TrackingApp�

 `I�@vieon/core/config/ConfigEnv�

 `B�@vieon/core/constants/constants�

 `7�@vieon/ui-kits/components/containers/LayoutContainer�

 `S�@vieon/core/utils/script/AAdsNetwork�

 `?�@vieon/core/store/createStore�

 `8	�@vieon/ui-kits/styles/globals.scss�

	 	`,
�@vieon/ui-kits/styles/methodItem.css�

 
`.`�@vieon/ui-kits/styles/rotate.css�

c *`�@vieon/ui-kits/styles/style.css�

c )`
��������

c
 
+`�next/router�

c )`��������

c )`�react-datepicker/dist/react-datepicker.css�

c 4`�react-popper-tooltip/dist/styles.css�

c .`��������

c '�webpack/lib/dependencies/HarmonyImportSpecifierDependencya�default�useLayoutEffect�ReactAv  �  
`���������

c+��useEffect�A�  �  
`���������

c.=�PAGE�PAYMENT_TPBANK�A�  �  
���

c/B��LIST_WINNERS�A    
���

c4E��A  &  `���������

c
�offlineDetect�TrackingAppA�  �  
���

c&&)�jsxDEV�_jsxDEVA&	  -	  ���

c11$�Provider�A.	  6	  

`���������

c1%1-��Ax	  	  ���

c3$3+��A�  �  ���

c@@ �Fragment�_FragmentA�  �  

���

c@!@*�ENABLE_SDK_AIACTIV�A�  �  

���

cBB��A�  �  ���

cB0B7��AAdsNetworkA�  �  

���

cB8BC��A�  �  ���

cGG!��ReCaptchaProviderA�  
  

���

cG"G3��A7
  >
  ���

cH(H/��A?
  G
  

`y���y����

cH0H8��A�
  �
  ���

cJ,J3��LayoutContainerA�
  �
  

���

cJ4JC��A    ���

cL0L7���A,  ?  
���

ch)h<��getInitialProps�A�  �  
���

cl l?���A�  �  
���

clHlg��withReduxA<  E  `
U���U����

@�   `@�   `��createStoreAF  Q  

���

@�   `@�   `(�webpack/lib/dependencies/HarmonyExportExpressionDependency@y���z����

@�    @�   `&�withRouter�Aj  t  `���

@�   `@�   ` @�    `n�zyB�����/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/package.json
���
����! З�zyB ���zyB �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/node_modules/@vieon/tracking�@vieon/tracking@1.0.0�*missing
��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/package.json
 
@�   �����/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp @�   �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/node_modules�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/node_modules�/Users/<USER>/Desktop/Project/VieON/node_modules�/Users/<USER>/Desktop/Project/node_modules�/Users/<USER>/Desktop/node_modules�/Users/<USER>/node_modules�/Users/<USER>/node_modules� @   
�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/functions/package.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/functions/TrackingApp�@    �n�zyB��
�@�    �n�zyB���������� ����
��
�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/functions/TrackingApp.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/functions/TrackingApp.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/node_modules/@vieon/tracking/services/functions/TrackingApp.node
���@�   �/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.js�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.json�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.node@    `o�zyB��
��
����
��������@�����/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.ts�� �

�����������������types�������������peerDependencies�files!��������VieON analytics and tracking�dist/index.js�dist/index.d.ts
�����dev�����type-check�lint�����tsc�tsc --watch�rimraf dist�tsc --noEmit�eslint src --ext .ts,.tsx�eslint src --ext .ts,.tsx --fix
����������?���?������
e���c���L���O�����������r���u���
����+����dist�src�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking�./src/services/functions/TrackingApp.ts@�    `o�zyB��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages��/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions�
���� 
���� 
���� 
���� 
����! �y}EtyB �y}EtyB
���� `�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/package.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/package.json��   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.js�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.mjs�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.ts�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.jsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.json�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.wasm�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.mjs�/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/TrackingApp.tsx�   /Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/Users/<USER>/Desktop/Project/VieON/web-mono-repo/packages/tracking/src/services/functions/package.json�  
����������webpack/lib/ModuleGraph�RestoreProvidedData

�����provided�canMangleProvide�terminalBinding�exportsInfo���

�sources�runtimeRequirements�data�javascript�webpack/lib/util/registerExternalSerializer�webpack-sources/CachedSource   �  �webpack/lib/util/registerExternalSerializer�webpack-sources/RawSource�  throw new Error("Module parse failed: Unexpected token (10:32)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import { parseVODType, segmentEvent, segmentPageView } from '../TrackingSegment';\n| \n> export const pageView = (seoData: any) => {\n|   segmentPageView(seoData);\n| };");�buffer�source�size�maps�����  throw new Error("Module parse failed: Unexpected token (10:32)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import { parseVODType, segmentEvent, segmentPageView } from '../TrackingSegment';\n| \n> export const pageView = (seoData: any) => {\n|   segmentPageView(seoData);\n| };");�{"filename":"[file].map[query]","module":true,"columns":true,"noSources":false,"namespace":"@vieon/web-vieon"}�map�bufferedMap 