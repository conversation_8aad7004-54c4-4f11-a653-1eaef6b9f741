/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./pages/_error.ts":
/*!*************************!*\
  !*** ./pages/_error.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _vieon_ui_kits_components_notfound_ContentNotFound__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @vieon/ui-kits/components/notfound/ContentNotFound */ \"../../packages/ui-kits/src/components/notfound/ContentNotFound.tsx\");\n/* harmony import */ var _vieon_ui_kits_components_notfound_ContentNotFound__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_notfound_ContentNotFound__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((_vieon_ui_kits_components_notfound_ContentNotFound__WEBPACK_IMPORTED_MODULE_0___default()));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZXJyb3IudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlGO0FBRWpGLGlFQUFlQSwyRkFBZSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHZpZW9uL3dlYi12aWVvbi8uL3BhZ2VzL19lcnJvci50cz85YjNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb250ZW50Tm90Rm91bmQgZnJvbSAnQHZpZW9uL3VpLWtpdHMvY29tcG9uZW50cy9ub3Rmb3VuZC9Db250ZW50Tm90Rm91bmQnO1xuXG5leHBvcnQgZGVmYXVsdCBDb250ZW50Tm90Rm91bmQ7XG4iXSwibmFtZXMiOlsiQ29udGVudE5vdEZvdW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_error.ts\n");

/***/ }),

/***/ "../../packages/ui-kits/src/components/notfound/ContentNotFound.tsx":
/*!**************************************************************************!*\
  !*** ../../packages/ui-kits/src/components/notfound/ContentNotFound.tsx ***!
  \**************************************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (11:78)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import ContentSupportApp from '../components/notfound/ContentSupportApp';\n| \n> const ContentNotFound = ({ dataRibbon, pageProps, isLiveTv, isVerticalPlayer }: any) => {\n|   const dispatch = useDispatch();\n|   const [loadedData, setLoadedData] = useState(false);");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_error.ts"));
module.exports = __webpack_exports__;

})();