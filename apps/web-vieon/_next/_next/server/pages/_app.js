/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vieon/provider/providerRecaptcha */ \"../../packages/provider/src/providerRecaptcha.tsx\");\n/* harmony import */ var _vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @vieon/tracking/services/functions/TrackingApp */ \"../../packages/tracking/src/services/functions/TrackingApp.ts\");\n/* harmony import */ var _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ \"../../packages/core/src/config/ConfigEnv.ts\");\n/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/constants/constants */ \"../../packages/core/src/constants/constants.ts\");\n/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/ui-kits/components/containers/LayoutContainer */ \"../../packages/ui-kits/src/components/containers/LayoutContainer.tsx\");\n/* harmony import */ var _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @vieon/core/utils/script/AAdsNetwork */ \"../../packages/core/src/utils/script/AAdsNetwork.tsx\");\n/* harmony import */ var _vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @vieon/core/store/createStore */ \"../../packages/core/src/store/createStore.ts\");\n/* harmony import */ var _vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @vieon/ui-kits/styles/globals.scss */ \"../../packages/ui-kits/src/styles/globals.scss\");\n/* harmony import */ var _vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_globals_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @vieon/ui-kits/styles/methodItem.css */ \"../../packages/ui-kits/src/styles/methodItem.css\");\n/* harmony import */ var _vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_methodItem_css__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @vieon/ui-kits/styles/rotate.css */ \"../../packages/ui-kits/src/styles/rotate.css\");\n/* harmony import */ var _vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_rotate_css__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @vieon/ui-kits/styles/style.css */ \"../../packages/ui-kits/src/styles/style.css\");\n/* harmony import */ var _vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_vieon_ui_kits_styles_style_css__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-redux-wrapper */ \"next-redux-wrapper\");\n/* harmony import */ var next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-popper-tooltip/dist/styles.css */ \"../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css\");\n/* harmony import */ var react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(react_popper_tooltip_dist_styles_css__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-redux */ \"react-redux\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(react_redux__WEBPACK_IMPORTED_MODULE_17__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EXPORT_SSR = typeof process.env.NEXT_PUBLIC_EXPORT_SSR !== \"undefined\" ? process.env.NEXT_PUBLIC_EXPORT_SSR : \"true\";\nif (true) (react__WEBPACK_IMPORTED_MODULE_14___default().useLayoutEffect) = (react__WEBPACK_IMPORTED_MODULE_14___default().useEffect);\nfunction MyApp(props) {\n    const { Component , store , pageProps , layoutProps , router  } = props || {};\n    const componentProps = {\n        router,\n        layoutProps,\n        pageProps\n    };\n    const pathname = router?.pathname;\n    const isTPBank = (pathname || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK);\n    const isListWinners = (pathname || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.LIST_WINNERS);\n    (0,react__WEBPACK_IMPORTED_MODULE_14__.useEffect)(()=>{\n        const checkAndProcessCache = async ()=>{\n            const cache = await caches.open(\"offline-cache-v1\");\n            const response = await cache.match(\"/tracking-data\");\n            if (response && response.ok) {\n                const clonedResponse = response.clone();\n                const trackingData = await clonedResponse.json();\n                _vieon_tracking_services_functions_TrackingApp__WEBPACK_IMPORTED_MODULE_2___default().offlineDetect(trackingData);\n                await cache.delete(\"/tracking-data\");\n            }\n        };\n        checkAndProcessCache();\n        window.addEventListener(\"online\", checkAndProcessCache);\n        return ()=>{\n            window.removeEventListener(\"online\", checkAndProcessCache);\n        };\n    }, []);\n    if (isTPBank || isListWinners) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {\n            store: store,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...componentProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_3__.ENABLE_SDK_AIACTIV && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_core_utils_script_AAdsNetwork__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                lineNumber: 71,\n                columnNumber: 30\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_provider_providerRecaptcha__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_17__.Provider, {\n                    store: store,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        ...layoutProps,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...componentProps\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_app.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nMyApp.getInitialProps = async ({ Component , ctx  })=>{\n    const usingSSR = EXPORT_SSR === \"true\";\n    if (usingSSR) {\n        if ((ctx?.asPath || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_4__.PAGE.PAYMENT_TPBANK)) return {\n            layoutProps: {}\n        };\n        try {\n            const layoutProps = (_vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps) ? await _vieon_ui_kits_components_containers_LayoutContainer__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(ctx) : {};\n            const pageProps = Component.getInitialProps ? await Component.getInitialProps(ctx) : {};\n            if (pageProps && Object.keys(pageProps).length > 0) {\n                return {\n                    layoutProps,\n                    pageProps\n                };\n            }\n            return {\n                layoutProps\n            };\n        } catch (error) {\n            const { req  } = ctx;\n            console.log(`Request ${req?.url} has and err IN SSR: msg =>`, error?.message);\n        }\n    }\n    return {\n        layoutProps: {}\n    };\n};\nconst WrappedApp = next_redux_wrapper__WEBPACK_IMPORTED_MODULE_12___default()((_vieon_core_store_createStore__WEBPACK_IMPORTED_MODULE_7___default()))(MyApp);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_router__WEBPACK_IMPORTED_MODULE_13__.withRouter)(WrappedApp));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-datepicker@7.6.0_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-datepicker/dist/react-datepicker.css ***!
  \***********************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-popper-tooltip@2.11.1_react-dom@18.2.0_react@18.2.0__react@18.2.0/node_modules/react-popper-tooltip/dist/styles.css ***!
  \**********************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/ui-kits/src/styles/globals.scss":
/*!******************************************************!*\
  !*** ../../packages/ui-kits/src/styles/globals.scss ***!
  \******************************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/ui-kits/src/styles/methodItem.css":
/*!********************************************************!*\
  !*** ../../packages/ui-kits/src/styles/methodItem.css ***!
  \********************************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/ui-kits/src/styles/rotate.css":
/*!****************************************************!*\
  !*** ../../packages/ui-kits/src/styles/rotate.css ***!
  \****************************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/ui-kits/src/styles/style.css":
/*!***************************************************!*\
  !*** ../../packages/ui-kits/src/styles/style.css ***!
  \***************************************************/
/***/ (() => {



/***/ }),

/***/ "../../packages/core/src/config/ConfigEnv.ts":
/*!***************************************************!*\
  !*** ../../packages/core/src/config/ConfigEnv.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"AADNETWORK_ID\": () => (/* binding */ AADNETWORK_ID),\n/* harmony export */   \"AADNETWORK_SDK\": () => (/* binding */ AADNETWORK_SDK),\n/* harmony export */   \"AI_DOMAIN_CONTENT_SERVICE_API\": () => (/* binding */ AI_DOMAIN_CONTENT_SERVICE_API),\n/* harmony export */   \"APPLE_STORE_ID\": () => (/* binding */ APPLE_STORE_ID),\n/* harmony export */   \"BUILD_ID\": () => (/* binding */ BUILD_ID),\n/* harmony export */   \"DISABLE_3RD\": () => (/* binding */ DISABLE_3RD),\n/* harmony export */   \"DMP_ID\": () => (/* binding */ DMP_ID),\n/* harmony export */   \"DOMAIN_API\": () => (/* binding */ DOMAIN_API),\n/* harmony export */   \"DOMAIN_API_CM\": () => (/* binding */ DOMAIN_API_CM),\n/* harmony export */   \"DOMAIN_API_CM_V5\": () => (/* binding */ DOMAIN_API_CM_V5),\n/* harmony export */   \"DOMAIN_API_GAME\": () => (/* binding */ DOMAIN_API_GAME),\n/* harmony export */   \"DOMAIN_API_GAME_QUIZ\": () => (/* binding */ DOMAIN_API_GAME_QUIZ),\n/* harmony export */   \"DOMAIN_API_GAME_VOTING\": () => (/* binding */ DOMAIN_API_GAME_VOTING),\n/* harmony export */   \"DOMAIN_CAKE_API\": () => (/* binding */ DOMAIN_CAKE_API),\n/* harmony export */   \"DOMAIN_KIDS_ACTIVITY_API\": () => (/* binding */ DOMAIN_KIDS_ACTIVITY_API),\n/* harmony export */   \"DOMAIN_LIVE_API\": () => (/* binding */ DOMAIN_LIVE_API),\n/* harmony export */   \"DOMAIN_LOCAL_BACKEND_USER\": () => (/* binding */ DOMAIN_LOCAL_BACKEND_USER),\n/* harmony export */   \"DOMAIN_LOCAL_BILLING\": () => (/* binding */ DOMAIN_LOCAL_BILLING),\n/* harmony export */   \"DOMAIN_LOCAL_SERVICE_USER_REPORT\": () => (/* binding */ DOMAIN_LOCAL_SERVICE_USER_REPORT),\n/* harmony export */   \"DOMAIN_LOCAL_VIEON_CM_ACTIVITY\": () => (/* binding */ DOMAIN_LOCAL_VIEON_CM_ACTIVITY),\n/* harmony export */   \"DOMAIN_LOCAL_VIEON_CM_V5\": () => (/* binding */ DOMAIN_LOCAL_VIEON_CM_V5),\n/* harmony export */   \"DOMAIN_LOYALTY_API\": () => (/* binding */ DOMAIN_LOYALTY_API),\n/* harmony export */   \"DOMAIN_NOTIFICATION_API\": () => (/* binding */ DOMAIN_NOTIFICATION_API),\n/* harmony export */   \"DOMAIN_POLICY_SERVICE_API\": () => (/* binding */ DOMAIN_POLICY_SERVICE_API),\n/* harmony export */   \"DOMAIN_REFERRAL_API\": () => (/* binding */ DOMAIN_REFERRAL_API),\n/* harmony export */   \"DOMAIN_WEB\": () => (/* binding */ DOMAIN_WEB),\n/* harmony export */   \"ENABLE_SDK_AIACTIV\": () => (/* binding */ ENABLE_SDK_AIACTIV),\n/* harmony export */   \"ENABLE_SDK_DMCA\": () => (/* binding */ ENABLE_SDK_DMCA),\n/* harmony export */   \"ENABLE_SDK_FB\": () => (/* binding */ ENABLE_SDK_FB),\n/* harmony export */   \"ENABLE_SDK_FRESHCHAT\": () => (/* binding */ ENABLE_SDK_FRESHCHAT),\n/* harmony export */   \"ENABLE_SDK_GG\": () => (/* binding */ ENABLE_SDK_GG),\n/* harmony export */   \"ENABLE_SDK_GG_ADSENSE\": () => (/* binding */ ENABLE_SDK_GG_ADSENSE),\n/* harmony export */   \"ENABLE_SDK_GG_IMA\": () => (/* binding */ ENABLE_SDK_GG_IMA),\n/* harmony export */   \"ENABLE_SDK_GPT\": () => (/* binding */ ENABLE_SDK_GPT),\n/* harmony export */   \"ENABLE_SDK_GTM\": () => (/* binding */ ENABLE_SDK_GTM),\n/* harmony export */   \"ENABLE_SDK_MIDESK_CHAT\": () => (/* binding */ ENABLE_SDK_MIDESK_CHAT),\n/* harmony export */   \"ENABLE_SDK_MOENGAGE\": () => (/* binding */ ENABLE_SDK_MOENGAGE),\n/* harmony export */   \"ENABLE_SDK_NAPAS\": () => (/* binding */ ENABLE_SDK_NAPAS),\n/* harmony export */   \"ENV\": () => (/* binding */ ENV),\n/* harmony export */   \"FB_APP_ID\": () => (/* binding */ FB_APP_ID),\n/* harmony export */   \"FCM_APP_ID\": () => (/* binding */ FCM_APP_ID),\n/* harmony export */   \"FRESH_CHAT_TK\": () => (/* binding */ FRESH_CHAT_TK),\n/* harmony export */   \"GG_CLIENT_ID\": () => (/* binding */ GG_CLIENT_ID),\n/* harmony export */   \"GOOGLE_STORE_ID\": () => (/* binding */ GOOGLE_STORE_ID),\n/* harmony export */   \"GTM_ID\": () => (/* binding */ GTM_ID),\n/* harmony export */   \"IS_SERVER\": () => (/* binding */ IS_SERVER),\n/* harmony export */   \"LINK_DOWNLOAD_APP_URL\": () => (/* binding */ LINK_DOWNLOAD_APP_URL),\n/* harmony export */   \"LINK_QRCODE_DOWNLOAD_APP\": () => (/* binding */ LINK_QRCODE_DOWNLOAD_APP),\n/* harmony export */   \"LINK_QRCODE_DOWNLOAD_APP_TV\": () => (/* binding */ LINK_QRCODE_DOWNLOAD_APP_TV),\n/* harmony export */   \"LINK_TUTORIAL_DOWNLOAD_APP_MOBILE\": () => (/* binding */ LINK_TUTORIAL_DOWNLOAD_APP_MOBILE),\n/* harmony export */   \"LINK_TUTORIAL_DOWNLOAD_APP_TV\": () => (/* binding */ LINK_TUTORIAL_DOWNLOAD_APP_TV),\n/* harmony export */   \"LOCAL_DOMAIN_ENABLE\": () => (/* binding */ LOCAL_DOMAIN_ENABLE),\n/* harmony export */   \"MOENGAGE_APP_ID\": () => (/* binding */ MOENGAGE_APP_ID),\n/* harmony export */   \"NAPAS_CHANNEL\": () => (/* binding */ NAPAS_CHANNEL),\n/* harmony export */   \"NAPAS_SOURCE_OF_FUNDS_TYPE\": () => (/* binding */ NAPAS_SOURCE_OF_FUNDS_TYPE),\n/* harmony export */   \"NODE_ENV\": () => (/* binding */ NODE_ENV),\n/* harmony export */   \"QNET_API\": () => (/* binding */ QNET_API),\n/* harmony export */   \"RECAPTCHA_SITE_KEY\": () => (/* binding */ RECAPTCHA_SITE_KEY),\n/* harmony export */   \"RECONNECT_SOCKET\": () => (/* binding */ RECONNECT_SOCKET),\n/* harmony export */   \"REPORT_LINK\": () => (/* binding */ REPORT_LINK),\n/* harmony export */   \"ROBOTS\": () => (/* binding */ ROBOTS),\n/* harmony export */   \"SDK_NAPAS\": () => (/* binding */ SDK_NAPAS),\n/* harmony export */   \"SECRET_KEY\": () => (/* binding */ SECRET_KEY),\n/* harmony export */   \"SEGMENT_ID\": () => (/* binding */ SEGMENT_ID),\n/* harmony export */   \"SENTRY_ENV\": () => (/* binding */ SENTRY_ENV),\n/* harmony export */   \"SENTRY_SERVER\": () => (/* binding */ SENTRY_SERVER),\n/* harmony export */   \"SIGMA_DRM_FAIR_PLAY\": () => (/* binding */ SIGMA_DRM_FAIR_PLAY),\n/* harmony export */   \"SIGMA_DRM_FAIR_PLAY_CERT\": () => (/* binding */ SIGMA_DRM_FAIR_PLAY_CERT),\n/* harmony export */   \"SIGMA_DRM_PLAY_READY\": () => (/* binding */ SIGMA_DRM_PLAY_READY),\n/* harmony export */   \"SIGMA_DRM_WIDE_VINE\": () => (/* binding */ SIGMA_DRM_WIDE_VINE),\n/* harmony export */   \"SIGMA_DRM_WINDOWS\": () => (/* binding */ SIGMA_DRM_WINDOWS),\n/* harmony export */   \"SOCKET_SERVER\": () => (/* binding */ SOCKET_SERVER),\n/* harmony export */   \"STATIC_DOMAIN\": () => (/* binding */ STATIC_DOMAIN),\n/* harmony export */   \"TIME_OUT_REQUEST_API\": () => (/* binding */ TIME_OUT_REQUEST_API),\n/* harmony export */   \"TODAY_DRM_SERVER_FAIR_PLAY\": () => (/* binding */ TODAY_DRM_SERVER_FAIR_PLAY),\n/* harmony export */   \"TODAY_DRM_SERVER_FAIR_PLAY_CERT\": () => (/* binding */ TODAY_DRM_SERVER_FAIR_PLAY_CERT),\n/* harmony export */   \"TODAY_DRM_SERVER_WIDE_VINE\": () => (/* binding */ TODAY_DRM_SERVER_WIDE_VINE),\n/* harmony export */   \"VERSION\": () => (/* binding */ VERSION),\n/* harmony export */   \"VIEON_URL\": () => (/* binding */ VIEON_URL),\n/* harmony export */   \"WEB_ENV\": () => (/* binding */ WEB_ENV)\n/* harmony export */ });\nconst DOMAIN_API = \"https://testing-api.vieon.vn/backend\";\n// const DOMAIN_API = 'https://api.vieon.vn/backend'\nconst DOMAIN_API_CM = \"https://testing-api.vieon.vn/backend/cm\";\n// const DOMAIN_API = 'https://api.vieon.vn/cm'\nconst DOMAIN_API_CM_V5 = `${DOMAIN_API}/cm/v5`;\n// const DOMAIN_API_CM_V5 = 'https://apis.vieon.vn/backend/cm/v5'\nconst DOMAIN_LIVE_API = \"https://testing-live-comment-api.vieon.vn/backend\";\n// const DOMAIN_LIVE_API = 'https://testing-live-comment-api.vieon.vn/backend'\nconst DOMAIN_REFERRAL_API = \"https://testing-referral-api.vieon.vn/v1\";\n// const DOMAIN_REFERRAL_API = 'https://dev-referral-api.vieon.vn/v1'\nconst DOMAIN_KIDS_ACTIVITY_API = \"https://testing-recommend-engine.vieon.vn/api/v1\";\n// const DOMAIN_KIDS_ACTIVITY_API = 'dev-recommend-engine.vieon.vn/api/v1'\n\nconst AI_DOMAIN_CONTENT_SERVICE_API = \"https://testing-api.vieon.vn/content-service\";\n// const DOMAIN_CONTENT_SERVICE_API = 'https://dev-api.vieon.vn/content-service'\n\nconst DOMAIN_CAKE_API = \"https://testing-api.vieon.vn/promotion-service/api/v1\";\n// const DOMAIN_CAKE_API = 'https://dev-api.vieon.vn/promotion-service/api/v1'\n\nconst DOMAIN_NOTIFICATION_API = \"https://testing-notificator.vieon.vn/v1\";\n// const DOMAIN_NOTIFICATION_API = https://testing-notificator.vieon.vn/v1\nconst DOMAIN_POLICY_SERVICE_API = \"https://testing-api.vieon.vn/policy-service/v1\";\n// const DOMAIN_POLICY_SERVICE_API = https://api.vieon.vn/policy-service/v1\n\nconst ROBOTS = process.env.NEXT_PUBLIC_ROBOTS;\n\nconst DOMAIN_LOYALTY_API = \"https://vieon.dgvdigital.net/api/v1\";\n// const DOMAIN_LOYALTY_API = 'https://vieon.dgvdigital.net/api/v1/'\n\nconst DOMAIN_API_GAME = \"https://testing-game-api.vieon.vn\";\nconst DOMAIN_API_GAME_VOTING = `${DOMAIN_API_GAME}/voting`;\nconst DOMAIN_API_GAME_QUIZ = `${DOMAIN_API_GAME}/quiz`;\nconst DISABLE_3RD = process.env.NEXT_PUBLIC_DISABLE_3RD;\nconst LINK_QRCODE_DOWNLOAD_APP =\n  \"https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_web.png\" || 0;\nconst LINK_TUTORIAL_DOWNLOAD_APP_MOBILE = \"https://vieon.vn/\";\nconst LINK_QRCODE_DOWNLOAD_APP_TV = \"/assets/img/rap-viet/qrcode-vieon-home.png\";\nconst LINK_DOWNLOAD_APP_URL = \"https://click.vieon.vn/tqd5/ddxed5kh\";\nconst LINK_TUTORIAL_DOWNLOAD_APP_TV = \"https://vieon.vn/\";\nconst QNET_API = \"https://sandbox.msky.vn\" || 0;\n// DRM\n\nconst SIGMA_DRM_WINDOWS = !!\"true\";\nconst SIGMA_DRM_PLAY_READY =\n  \"https://license-staging.sigmadrm.com/license/verify/playready\" ||\n  0;\nconst SIGMA_DRM_WIDE_VINE =\n  \"https://license-staging.sigmadrm.com/license/verify/widevine\" ||\n  0;\nconst SIGMA_DRM_FAIR_PLAY =\n  \"https://license-staging.sigmadrm.com/license/verify/fairplay\" ||\n  0;\nconst SIGMA_DRM_FAIR_PLAY_CERT =\n  \"https://cert-staging.sigmadrm.com/app/fairplay/\" || 0;\nconst TODAY_DRM_SERVER_WIDE_VINE =\n  \"https://lic.staging.drmtoday.com/license-proxy-widevine/cenc/\" ||\n  0;\nconst TODAY_DRM_SERVER_FAIR_PLAY =\n  \"https://lic.staging.drmtoday.com/license-server-fairplay/\" ||\n  0;\nconst TODAY_DRM_SERVER_FAIR_PLAY_CERT =\n  \"https://lic.staging.drmtoday.com/license-server-fairplay/cert/\" ||\n  0;\n// End DRM\nconst SECRET_KEY = process.env.SECRET || 'secret_key_vieon';\nconst TIME_OUT_REQUEST_API = (\"3000\" || 0);\nconst STATIC_DOMAIN = \"https://local.vieon.vn\" + '/';\nconst DOMAIN_WEB = \"https://local.vieon.vn\";\nconst VERSION = process.env.NEXT_PUBLIC_VERSION;\nconst REPORT_LINK = \"https://forms.office.com/Pages/ResponsePage.aspx?id=QKxWuRijg0ejR9L30kL-sGpwjEzV_UtCvjXjJLcMRI9UNDRCMUhNN0tVOVhXSzNSWVAyTElNWVROWiQlQCN0PWcu\" || 0;\nconst SEGMENT_ID = \"ii5tNZL7x01EjyKG0shmo2lYSoKyuaVl\" || 0;\nconst SENTRY_ENV = \"TESTING\";\nconst VIEON_URL = process.env.NEXT_PUBLIC_VIEON_URL || '';\nconst FCM_APP_ID = process.env.NEXT_PUBLIC_FCM_APP_ID || '';\nconst GTM_ID = \"GTM-KVF5G3L\";\nconst FRESH_CHAT_TK =\n  \"ee64e11f-55ea-40bc-a0f0-cbf40a5e8c67\" || 0;\nconst MOENGAGE_APP_ID = \"KT1E9EHT9A3KFBH9WI92QKAV\" || 0;\nconst SENTRY_SERVER =\n  \"https://<EMAIL>/6\" ||\n  0;\nconst SOCKET_SERVER = \"testing-socket.vieon.vn\" || 0;\nconst BUILD_ID =\n   true\n    ? 1750738938729\n    : 0;\nconst WEB_ENV = \"testing\" || 0;\nconst NODE_ENV = \"development\" || 0;\nconst LOCAL_DOMAIN_ENABLE = \"false\" === 'true';\nconst DOMAIN_LOCAL_BILLING = process.env.NEXT_PUBLIC_DOMAIN_LOCAL_BILLING || '';\nconst DOMAIN_LOCAL_VIEON_CM_ACTIVITY = \"http://cm-activity-api.activity.svc.cluster.local:1993/backend/cm/activity\" || 0;\nconst DOMAIN_LOCAL_BACKEND_USER = \"http://backend-user-api.user-service.svc.cluster.local:1993/backend/user\" || 0;\nconst DOMAIN_LOCAL_SERVICE_USER_REPORT =\n  \"http://services-user-report.user-service.svc.cluster.local:1993/backend/user-report\" || 0;\nconst DOMAIN_LOCAL_VIEON_CM_V5 = \"http://cm-v5-api.cm-v5-service.svc.cluster.local:1993/backend/cm/v5\" || 0;\nconst DMP_ID = \"b8a3ccf2-5d49-4912-b2cc-87dc46e10277@web\" || 0;\nconst AADNETWORK_ID = \"ca8ec140-d43a-4b99-81bb-adea3bfe80ca\" || 0;\nconst AADNETWORK_SDK =\n  \"https://sdk-cdn.aiactiv.io/aiactiv-sdk.test.min.js\" || 0;\nconst NAPAS_CHANNEL = \"6014\" || 0;\nconst NAPAS_SOURCE_OF_FUNDS_TYPE = \"CARD\" || 0;\nconst SDK_NAPAS =\n  \"https://dps-staging.napas.com.vn/api/restjs/resources/js/napas.paymentpage.min.js\" ||\n  0;\nconst RECONNECT_SOCKET = process.env.NEXT_PUBLIC_RECONNECT_SOCKET;\nconst GG_CLIENT_ID =\n  \"422317786610-d48sb63v1ckno5euvdehv7miuop1dvuf.apps.googleusercontent.com\" ||\n  0;\nconst FB_APP_ID = \"2155775314565146\" || 0;\n// const ENABLE_SDK = process.env.NEXT_PUBLIC_ENABLE_SDK || true\nconst ENABLE_SDK_NAPAS = \"true\" || 0;\nconst ENABLE_SDK_MOENGAGE = \"true\" || 0;\nconst ENABLE_SDK_GG = \"true\" || 0;\nconst ENABLE_SDK_FRESHCHAT = \"true\" || 0;\nconst ENABLE_SDK_MIDESK_CHAT = \"true\" || 0;\nconst ENABLE_SDK_FB = \"true\" || 0;\nconst ENABLE_SDK_AIACTIV = \"true\" || 0;\nconst ENABLE_SDK_GG_ADSENSE = \"true\" || 0;\nconst ENABLE_SDK_DMCA = \"true\" || 0;\nconst ENABLE_SDK_GTM = \"true\" || 0;\nconst ENABLE_SDK_GPT = \"true\" || 0;\nconst ENABLE_SDK_GG_IMA = \"true\" || 0;\nconst ENV = \"development\";\nconst APPLE_STORE_ID = \"id1357819721\";\nconst GOOGLE_STORE_ID = \"vieon.phim.tv\";\nconst IS_SERVER = !false;\nconst RECAPTCHA_SITE_KEY = \"6LfvjlcrAAAAAE9Sgwc3hMgOUGqPbiCf91WAg7Mq\" || 0;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/src/config/ConfigEnv.ts\n");

/***/ }),

/***/ "../../packages/core/src/constants/constants.ts":
/*!******************************************************!*\
  !*** ../../packages/core/src/constants/constants.ts ***!
  \******************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (360:19)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| };\n| \n> export const FOOTER: any = {\n|   NAV: {\n|     RULE: 'Quy định',");

/***/ }),

/***/ "../../packages/core/src/store/createStore.ts":
/*!****************************************************!*\
  !*** ../../packages/core/src/store/createStore.ts ***!
  \****************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (8:39)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import { composeWithDevTools } from 'redux-devtools-extension';\n| \n> function createMiddlewares({ isServer }: any) {\n|   const middlewares = [thunkMiddleware];\n|   return middlewares;");

/***/ }),

/***/ "../../packages/core/src/utils/script/AAdsNetwork.tsx":
/*!************************************************************!*\
  !*** ../../packages/core/src/utils/script/AAdsNetwork.tsx ***!
  \************************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (3:17)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> const AAdsNetwork: React.FC = () => {\n|   // Placeholder for AAdsNetwork component\n|   // You can add your actual ads network logic here");

/***/ }),

/***/ "../../packages/provider/src/providerRecaptcha.tsx":
/*!*********************************************************!*\
  !*** ../../packages/provider/src/providerRecaptcha.tsx ***!
  \*********************************************************/
/***/ (() => {

throw new Error("Module parse failed: The keyword 'interface' is reserved (4:0)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';\n| \n> interface ReCaptchaProviderProps {\n|   children: React.ReactNode;\n| }");

/***/ }),

/***/ "../../packages/tracking/src/services/functions/TrackingApp.ts":
/*!*********************************************************************!*\
  !*** ../../packages/tracking/src/services/functions/TrackingApp.ts ***!
  \*********************************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (10:32)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import { parseVODType, segmentEvent, segmentPageView } from '../TrackingSegment';\n| \n> export const pageView = (seoData: any) => {\n|   segmentPageView(seoData);\n| };");

/***/ }),

/***/ "../../packages/ui-kits/src/components/containers/LayoutContainer.tsx":
/*!****************************************************************************!*\
  !*** ../../packages/ui-kits/src/components/containers/LayoutContainer.tsx ***!
  \****************************************************************************/
/***/ (() => {

throw new Error("Module parse failed: The keyword 'interface' is reserved (3:0)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import React from 'react';\n| \n> interface LayoutContainerProps {\n|   children: React.ReactNode;\n|   [key: string]: any;");

/***/ }),

/***/ "next-redux-wrapper":
/*!*************************************!*\
  !*** external "next-redux-wrapper" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-redux-wrapper");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-redux":
/*!******************************!*\
  !*** external "react-redux" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-redux");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();