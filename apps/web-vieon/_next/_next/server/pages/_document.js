/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/document.js\");\n/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vieon/core/constants/constants */ \"../../packages/core/src/constants/constants.ts\");\n/* harmony import */ var _vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vieon/core/config/ConfigEnv */ \"../../packages/core/src/config/ConfigEnv.ts\");\n/* harmony import */ var _vieon_tracking_services_TrackingGTM__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @vieon/tracking/services/TrackingGTM */ \"../../packages/tracking/src/services/TrackingGTM.tsx\");\n/* harmony import */ var _vieon_tracking_services_TrackingGTM__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_vieon_tracking_services_TrackingGTM__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nclass CustomDocument extends next_document__WEBPACK_IMPORTED_MODULE_2__[\"default\"] {\n    static async getInitialProps(ctx) {\n        const originalRenderPage = ctx.renderPage;\n        ctx.renderPage = ()=>originalRenderPage({\n                // useful for wrapping the whole react tree\n                enhanceApp: (App)=>App,\n                // useful for wrapping in a per-page basis\n                enhanceComponent: (Component)=>Component\n            });\n        const documentProps = await next_document__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getInitialProps(ctx);\n        return {\n            ...documentProps,\n            url: ctx.req.url\n        };\n    }\n    loadHead = ({ url  })=>{\n        const isTPBank = (url || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__.PAGE.PAYMENT_TPBANK);\n        if (isTPBank) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"stylesheet\",\n                href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/vieon-in-app-tpbank.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/typography.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/app.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    \"data-type\": \"vieon\",\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/components.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    \"data-type\": \"vieon\",\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/modules.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/splash.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/css/player.css?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"manifest\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.DOMAIN_WEB}/manifest.json?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/img/VieON_16x16.png?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`,\n                    rel: \"icon\",\n                    type: \"image/png\",\n                    sizes: \"16x16\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/img/VieON_32x32.png?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`,\n                    rel: \"icon\",\n                    type: \"image/png\",\n                    sizes: \"32x32\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"apple-touch-icon\",\n                    href: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/img/VieON_32x32.png?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.ENABLE_SDK_GPT === true || _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.ENABLE_SDK_GPT === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"preload\",\n                    href: \"https://securepubads.g.doubleclick.net/tag/js/gpt.js\",\n                    as: \"script\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true);\n    };\n    loadScript = ({ url , isViewApp  })=>{\n        const isTPBank = (url || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__.PAGE.PAYMENT_TPBANK);\n        const isSupportSmartTv = (url || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__.PAGE.PAGE_SUPPORT_SMART_TV);\n        const isInAppZalopay = (url || \"\").includes(_vieon_core_constants_constants__WEBPACK_IMPORTED_MODULE_3__.PAGE.ZALOPAY) ?? false;\n        if (isSupportSmartTv || isViewApp) return null;\n        if (isTPBank) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/js/tpbank/tpbankhandler.js?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`,\n                        type: \"text/javascript\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/js/tpbank/tpbanksdk-1.1.min.js?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`,\n                        type: \"text/javascript\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                isInAppZalopay && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: `${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.STATIC_DOMAIN}assets/js/zalopay.js?v=${_vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.BUILD_ID}`,\n                    type: \"text/javascript\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this),\n                _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.ENABLE_SDK_GPT === true || _vieon_core_config_ConfigEnv__WEBPACK_IMPORTED_MODULE_4__.ENABLE_SDK_GPT === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    async: true,\n                    src: \"https://securepubads.g.doubleclick.net/tag/js/gpt.js\",\n                    type: \"text/javascript\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 13\n                }, this),\n                (0,_vieon_tracking_services_TrackingGTM__WEBPACK_IMPORTED_MODULE_5__.GTMVieONScript)()\n            ]\n        }, void 0, true);\n    };\n    render() {\n        const url = this?.props?.url;\n        const isViewApp = this?.props?.__NEXT_DATA__?.query?.isViewApp;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            lang: \"vi\",\n            className: \"user vieon-layout theme-dark overflow-x\",\n            id: \"html-head\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: this.loadHead({\n                        url\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: \"App\",\n                    id: \"app\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        this.loadScript({\n                            url,\n                            isViewApp\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Project/VieON/web-mono-repo/apps/web-vieon/pages/_document.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/pages/_document.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/pages/_document.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        var _defer;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: (_defer = scriptProps.defer) != null ? _defer : !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })), \n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        var _nonce, _nonce1;\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }),  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce = this.props.nonce) != null ? _nonce : \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce1 = this.props.nonce) != null ? _nonce1 : \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ], \n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }, \n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles, \n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__ ,  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/is-error.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/is-error.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/pretty-bytes.js":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/pretty-bytes.js ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/document.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/document.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTIuMy40X0BiYWJlbCtjb3JlQDcuMjcuNF9iYWJlbC1wbHVnaW4tbWFjcm9zQDMuMS4wX3JlYWN0LWRvbUAxOC4yLjBfcmVhY3RAMTguMi4wX19yZWFjdEAxOC4yLjBfc2Fzc0AxLjg5LjIvbm9kZV9tb2R1bGVzL25leHQvZG9jdW1lbnQuanMuanMiLCJtYXBwaW5ncyI6IkFBQUEseVBBQWtEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHZpZW9uL3dlYi12aWVvbi8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxMi4zLjRfQGJhYmVsK2NvcmVANy4yNy40X2JhYmVsLXBsdWdpbi1tYWNyb3NAMy4xLjBfcmVhY3QtZG9tQDE4LjIuMF9yZWFjdEAxOC4yLjBfX3JlYWN0QDE4LjIuMF9zYXNzQDEuODkuMi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcz83MWY1Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L3BhZ2VzL19kb2N1bWVudCcpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/.pnpm/next@12.3.4_@babel+core@7.27.4_babel-plugin-macros@3.1.0_react-dom@18.2.0_react@18.2.0__react@18.2.0_sass@1.89.2/node_modules/next/document.js\n");

/***/ }),

/***/ "../../packages/core/src/config/ConfigEnv.ts":
/*!***************************************************!*\
  !*** ../../packages/core/src/config/ConfigEnv.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"AADNETWORK_ID\": () => (/* binding */ AADNETWORK_ID),\n/* harmony export */   \"AADNETWORK_SDK\": () => (/* binding */ AADNETWORK_SDK),\n/* harmony export */   \"AI_DOMAIN_CONTENT_SERVICE_API\": () => (/* binding */ AI_DOMAIN_CONTENT_SERVICE_API),\n/* harmony export */   \"APPLE_STORE_ID\": () => (/* binding */ APPLE_STORE_ID),\n/* harmony export */   \"BUILD_ID\": () => (/* binding */ BUILD_ID),\n/* harmony export */   \"DISABLE_3RD\": () => (/* binding */ DISABLE_3RD),\n/* harmony export */   \"DMP_ID\": () => (/* binding */ DMP_ID),\n/* harmony export */   \"DOMAIN_API\": () => (/* binding */ DOMAIN_API),\n/* harmony export */   \"DOMAIN_API_CM\": () => (/* binding */ DOMAIN_API_CM),\n/* harmony export */   \"DOMAIN_API_CM_V5\": () => (/* binding */ DOMAIN_API_CM_V5),\n/* harmony export */   \"DOMAIN_API_GAME\": () => (/* binding */ DOMAIN_API_GAME),\n/* harmony export */   \"DOMAIN_API_GAME_QUIZ\": () => (/* binding */ DOMAIN_API_GAME_QUIZ),\n/* harmony export */   \"DOMAIN_API_GAME_VOTING\": () => (/* binding */ DOMAIN_API_GAME_VOTING),\n/* harmony export */   \"DOMAIN_CAKE_API\": () => (/* binding */ DOMAIN_CAKE_API),\n/* harmony export */   \"DOMAIN_KIDS_ACTIVITY_API\": () => (/* binding */ DOMAIN_KIDS_ACTIVITY_API),\n/* harmony export */   \"DOMAIN_LIVE_API\": () => (/* binding */ DOMAIN_LIVE_API),\n/* harmony export */   \"DOMAIN_LOCAL_BACKEND_USER\": () => (/* binding */ DOMAIN_LOCAL_BACKEND_USER),\n/* harmony export */   \"DOMAIN_LOCAL_BILLING\": () => (/* binding */ DOMAIN_LOCAL_BILLING),\n/* harmony export */   \"DOMAIN_LOCAL_SERVICE_USER_REPORT\": () => (/* binding */ DOMAIN_LOCAL_SERVICE_USER_REPORT),\n/* harmony export */   \"DOMAIN_LOCAL_VIEON_CM_ACTIVITY\": () => (/* binding */ DOMAIN_LOCAL_VIEON_CM_ACTIVITY),\n/* harmony export */   \"DOMAIN_LOCAL_VIEON_CM_V5\": () => (/* binding */ DOMAIN_LOCAL_VIEON_CM_V5),\n/* harmony export */   \"DOMAIN_LOYALTY_API\": () => (/* binding */ DOMAIN_LOYALTY_API),\n/* harmony export */   \"DOMAIN_NOTIFICATION_API\": () => (/* binding */ DOMAIN_NOTIFICATION_API),\n/* harmony export */   \"DOMAIN_POLICY_SERVICE_API\": () => (/* binding */ DOMAIN_POLICY_SERVICE_API),\n/* harmony export */   \"DOMAIN_REFERRAL_API\": () => (/* binding */ DOMAIN_REFERRAL_API),\n/* harmony export */   \"DOMAIN_WEB\": () => (/* binding */ DOMAIN_WEB),\n/* harmony export */   \"ENABLE_SDK_AIACTIV\": () => (/* binding */ ENABLE_SDK_AIACTIV),\n/* harmony export */   \"ENABLE_SDK_DMCA\": () => (/* binding */ ENABLE_SDK_DMCA),\n/* harmony export */   \"ENABLE_SDK_FB\": () => (/* binding */ ENABLE_SDK_FB),\n/* harmony export */   \"ENABLE_SDK_FRESHCHAT\": () => (/* binding */ ENABLE_SDK_FRESHCHAT),\n/* harmony export */   \"ENABLE_SDK_GG\": () => (/* binding */ ENABLE_SDK_GG),\n/* harmony export */   \"ENABLE_SDK_GG_ADSENSE\": () => (/* binding */ ENABLE_SDK_GG_ADSENSE),\n/* harmony export */   \"ENABLE_SDK_GG_IMA\": () => (/* binding */ ENABLE_SDK_GG_IMA),\n/* harmony export */   \"ENABLE_SDK_GPT\": () => (/* binding */ ENABLE_SDK_GPT),\n/* harmony export */   \"ENABLE_SDK_GTM\": () => (/* binding */ ENABLE_SDK_GTM),\n/* harmony export */   \"ENABLE_SDK_MIDESK_CHAT\": () => (/* binding */ ENABLE_SDK_MIDESK_CHAT),\n/* harmony export */   \"ENABLE_SDK_MOENGAGE\": () => (/* binding */ ENABLE_SDK_MOENGAGE),\n/* harmony export */   \"ENABLE_SDK_NAPAS\": () => (/* binding */ ENABLE_SDK_NAPAS),\n/* harmony export */   \"ENV\": () => (/* binding */ ENV),\n/* harmony export */   \"FB_APP_ID\": () => (/* binding */ FB_APP_ID),\n/* harmony export */   \"FCM_APP_ID\": () => (/* binding */ FCM_APP_ID),\n/* harmony export */   \"FRESH_CHAT_TK\": () => (/* binding */ FRESH_CHAT_TK),\n/* harmony export */   \"GG_CLIENT_ID\": () => (/* binding */ GG_CLIENT_ID),\n/* harmony export */   \"GOOGLE_STORE_ID\": () => (/* binding */ GOOGLE_STORE_ID),\n/* harmony export */   \"GTM_ID\": () => (/* binding */ GTM_ID),\n/* harmony export */   \"IS_SERVER\": () => (/* binding */ IS_SERVER),\n/* harmony export */   \"LINK_DOWNLOAD_APP_URL\": () => (/* binding */ LINK_DOWNLOAD_APP_URL),\n/* harmony export */   \"LINK_QRCODE_DOWNLOAD_APP\": () => (/* binding */ LINK_QRCODE_DOWNLOAD_APP),\n/* harmony export */   \"LINK_QRCODE_DOWNLOAD_APP_TV\": () => (/* binding */ LINK_QRCODE_DOWNLOAD_APP_TV),\n/* harmony export */   \"LINK_TUTORIAL_DOWNLOAD_APP_MOBILE\": () => (/* binding */ LINK_TUTORIAL_DOWNLOAD_APP_MOBILE),\n/* harmony export */   \"LINK_TUTORIAL_DOWNLOAD_APP_TV\": () => (/* binding */ LINK_TUTORIAL_DOWNLOAD_APP_TV),\n/* harmony export */   \"LOCAL_DOMAIN_ENABLE\": () => (/* binding */ LOCAL_DOMAIN_ENABLE),\n/* harmony export */   \"MOENGAGE_APP_ID\": () => (/* binding */ MOENGAGE_APP_ID),\n/* harmony export */   \"NAPAS_CHANNEL\": () => (/* binding */ NAPAS_CHANNEL),\n/* harmony export */   \"NAPAS_SOURCE_OF_FUNDS_TYPE\": () => (/* binding */ NAPAS_SOURCE_OF_FUNDS_TYPE),\n/* harmony export */   \"NODE_ENV\": () => (/* binding */ NODE_ENV),\n/* harmony export */   \"QNET_API\": () => (/* binding */ QNET_API),\n/* harmony export */   \"RECAPTCHA_SITE_KEY\": () => (/* binding */ RECAPTCHA_SITE_KEY),\n/* harmony export */   \"RECONNECT_SOCKET\": () => (/* binding */ RECONNECT_SOCKET),\n/* harmony export */   \"REPORT_LINK\": () => (/* binding */ REPORT_LINK),\n/* harmony export */   \"ROBOTS\": () => (/* binding */ ROBOTS),\n/* harmony export */   \"SDK_NAPAS\": () => (/* binding */ SDK_NAPAS),\n/* harmony export */   \"SECRET_KEY\": () => (/* binding */ SECRET_KEY),\n/* harmony export */   \"SEGMENT_ID\": () => (/* binding */ SEGMENT_ID),\n/* harmony export */   \"SENTRY_ENV\": () => (/* binding */ SENTRY_ENV),\n/* harmony export */   \"SENTRY_SERVER\": () => (/* binding */ SENTRY_SERVER),\n/* harmony export */   \"SIGMA_DRM_FAIR_PLAY\": () => (/* binding */ SIGMA_DRM_FAIR_PLAY),\n/* harmony export */   \"SIGMA_DRM_FAIR_PLAY_CERT\": () => (/* binding */ SIGMA_DRM_FAIR_PLAY_CERT),\n/* harmony export */   \"SIGMA_DRM_PLAY_READY\": () => (/* binding */ SIGMA_DRM_PLAY_READY),\n/* harmony export */   \"SIGMA_DRM_WIDE_VINE\": () => (/* binding */ SIGMA_DRM_WIDE_VINE),\n/* harmony export */   \"SIGMA_DRM_WINDOWS\": () => (/* binding */ SIGMA_DRM_WINDOWS),\n/* harmony export */   \"SOCKET_SERVER\": () => (/* binding */ SOCKET_SERVER),\n/* harmony export */   \"STATIC_DOMAIN\": () => (/* binding */ STATIC_DOMAIN),\n/* harmony export */   \"TIME_OUT_REQUEST_API\": () => (/* binding */ TIME_OUT_REQUEST_API),\n/* harmony export */   \"TODAY_DRM_SERVER_FAIR_PLAY\": () => (/* binding */ TODAY_DRM_SERVER_FAIR_PLAY),\n/* harmony export */   \"TODAY_DRM_SERVER_FAIR_PLAY_CERT\": () => (/* binding */ TODAY_DRM_SERVER_FAIR_PLAY_CERT),\n/* harmony export */   \"TODAY_DRM_SERVER_WIDE_VINE\": () => (/* binding */ TODAY_DRM_SERVER_WIDE_VINE),\n/* harmony export */   \"VERSION\": () => (/* binding */ VERSION),\n/* harmony export */   \"VIEON_URL\": () => (/* binding */ VIEON_URL),\n/* harmony export */   \"WEB_ENV\": () => (/* binding */ WEB_ENV)\n/* harmony export */ });\nconst DOMAIN_API = \"https://testing-api.vieon.vn/backend\";\n// const DOMAIN_API = 'https://api.vieon.vn/backend'\nconst DOMAIN_API_CM = \"https://testing-api.vieon.vn/backend/cm\";\n// const DOMAIN_API = 'https://api.vieon.vn/cm'\nconst DOMAIN_API_CM_V5 = `${DOMAIN_API}/cm/v5`;\n// const DOMAIN_API_CM_V5 = 'https://apis.vieon.vn/backend/cm/v5'\nconst DOMAIN_LIVE_API = \"https://testing-live-comment-api.vieon.vn/backend\";\n// const DOMAIN_LIVE_API = 'https://testing-live-comment-api.vieon.vn/backend'\nconst DOMAIN_REFERRAL_API = \"https://testing-referral-api.vieon.vn/v1\";\n// const DOMAIN_REFERRAL_API = 'https://dev-referral-api.vieon.vn/v1'\nconst DOMAIN_KIDS_ACTIVITY_API = \"https://testing-recommend-engine.vieon.vn/api/v1\";\n// const DOMAIN_KIDS_ACTIVITY_API = 'dev-recommend-engine.vieon.vn/api/v1'\n\nconst AI_DOMAIN_CONTENT_SERVICE_API = \"https://testing-api.vieon.vn/content-service\";\n// const DOMAIN_CONTENT_SERVICE_API = 'https://dev-api.vieon.vn/content-service'\n\nconst DOMAIN_CAKE_API = \"https://testing-api.vieon.vn/promotion-service/api/v1\";\n// const DOMAIN_CAKE_API = 'https://dev-api.vieon.vn/promotion-service/api/v1'\n\nconst DOMAIN_NOTIFICATION_API = \"https://testing-notificator.vieon.vn/v1\";\n// const DOMAIN_NOTIFICATION_API = https://testing-notificator.vieon.vn/v1\nconst DOMAIN_POLICY_SERVICE_API = \"https://testing-api.vieon.vn/policy-service/v1\";\n// const DOMAIN_POLICY_SERVICE_API = https://api.vieon.vn/policy-service/v1\n\nconst ROBOTS = process.env.NEXT_PUBLIC_ROBOTS;\n\nconst DOMAIN_LOYALTY_API = \"https://vieon.dgvdigital.net/api/v1\";\n// const DOMAIN_LOYALTY_API = 'https://vieon.dgvdigital.net/api/v1/'\n\nconst DOMAIN_API_GAME = \"https://testing-game-api.vieon.vn\";\nconst DOMAIN_API_GAME_VOTING = `${DOMAIN_API_GAME}/voting`;\nconst DOMAIN_API_GAME_QUIZ = `${DOMAIN_API_GAME}/quiz`;\nconst DISABLE_3RD = process.env.NEXT_PUBLIC_DISABLE_3RD;\nconst LINK_QRCODE_DOWNLOAD_APP =\n  \"https://static.vieon.vn/vieon-images/rapviet/qrcode_download-app_web.png\" || 0;\nconst LINK_TUTORIAL_DOWNLOAD_APP_MOBILE = \"https://vieon.vn/\";\nconst LINK_QRCODE_DOWNLOAD_APP_TV = \"/assets/img/rap-viet/qrcode-vieon-home.png\";\nconst LINK_DOWNLOAD_APP_URL = \"https://click.vieon.vn/tqd5/ddxed5kh\";\nconst LINK_TUTORIAL_DOWNLOAD_APP_TV = \"https://vieon.vn/\";\nconst QNET_API = \"https://sandbox.msky.vn\" || 0;\n// DRM\n\nconst SIGMA_DRM_WINDOWS = !!\"true\";\nconst SIGMA_DRM_PLAY_READY =\n  \"https://license-staging.sigmadrm.com/license/verify/playready\" ||\n  0;\nconst SIGMA_DRM_WIDE_VINE =\n  \"https://license-staging.sigmadrm.com/license/verify/widevine\" ||\n  0;\nconst SIGMA_DRM_FAIR_PLAY =\n  \"https://license-staging.sigmadrm.com/license/verify/fairplay\" ||\n  0;\nconst SIGMA_DRM_FAIR_PLAY_CERT =\n  \"https://cert-staging.sigmadrm.com/app/fairplay/\" || 0;\nconst TODAY_DRM_SERVER_WIDE_VINE =\n  \"https://lic.staging.drmtoday.com/license-proxy-widevine/cenc/\" ||\n  0;\nconst TODAY_DRM_SERVER_FAIR_PLAY =\n  \"https://lic.staging.drmtoday.com/license-server-fairplay/\" ||\n  0;\nconst TODAY_DRM_SERVER_FAIR_PLAY_CERT =\n  \"https://lic.staging.drmtoday.com/license-server-fairplay/cert/\" ||\n  0;\n// End DRM\nconst SECRET_KEY = process.env.SECRET || 'secret_key_vieon';\nconst TIME_OUT_REQUEST_API = (\"3000\" || 0);\nconst STATIC_DOMAIN = \"https://local.vieon.vn\" + '/';\nconst DOMAIN_WEB = \"https://local.vieon.vn\";\nconst VERSION = process.env.NEXT_PUBLIC_VERSION;\nconst REPORT_LINK = \"https://forms.office.com/Pages/ResponsePage.aspx?id=QKxWuRijg0ejR9L30kL-sGpwjEzV_UtCvjXjJLcMRI9UNDRCMUhNN0tVOVhXSzNSWVAyTElNWVROWiQlQCN0PWcu\" || 0;\nconst SEGMENT_ID = \"ii5tNZL7x01EjyKG0shmo2lYSoKyuaVl\" || 0;\nconst SENTRY_ENV = \"TESTING\";\nconst VIEON_URL = process.env.NEXT_PUBLIC_VIEON_URL || '';\nconst FCM_APP_ID = process.env.NEXT_PUBLIC_FCM_APP_ID || '';\nconst GTM_ID = \"GTM-KVF5G3L\";\nconst FRESH_CHAT_TK =\n  \"ee64e11f-55ea-40bc-a0f0-cbf40a5e8c67\" || 0;\nconst MOENGAGE_APP_ID = \"KT1E9EHT9A3KFBH9WI92QKAV\" || 0;\nconst SENTRY_SERVER =\n  \"https://<EMAIL>/6\" ||\n  0;\nconst SOCKET_SERVER = \"testing-socket.vieon.vn\" || 0;\nconst BUILD_ID =\n   true\n    ? 1750738938729\n    : 0;\nconst WEB_ENV = \"testing\" || 0;\nconst NODE_ENV = \"development\" || 0;\nconst LOCAL_DOMAIN_ENABLE = \"false\" === 'true';\nconst DOMAIN_LOCAL_BILLING = process.env.NEXT_PUBLIC_DOMAIN_LOCAL_BILLING || '';\nconst DOMAIN_LOCAL_VIEON_CM_ACTIVITY = \"http://cm-activity-api.activity.svc.cluster.local:1993/backend/cm/activity\" || 0;\nconst DOMAIN_LOCAL_BACKEND_USER = \"http://backend-user-api.user-service.svc.cluster.local:1993/backend/user\" || 0;\nconst DOMAIN_LOCAL_SERVICE_USER_REPORT =\n  \"http://services-user-report.user-service.svc.cluster.local:1993/backend/user-report\" || 0;\nconst DOMAIN_LOCAL_VIEON_CM_V5 = \"http://cm-v5-api.cm-v5-service.svc.cluster.local:1993/backend/cm/v5\" || 0;\nconst DMP_ID = \"b8a3ccf2-5d49-4912-b2cc-87dc46e10277@web\" || 0;\nconst AADNETWORK_ID = \"ca8ec140-d43a-4b99-81bb-adea3bfe80ca\" || 0;\nconst AADNETWORK_SDK =\n  \"https://sdk-cdn.aiactiv.io/aiactiv-sdk.test.min.js\" || 0;\nconst NAPAS_CHANNEL = \"6014\" || 0;\nconst NAPAS_SOURCE_OF_FUNDS_TYPE = \"CARD\" || 0;\nconst SDK_NAPAS =\n  \"https://dps-staging.napas.com.vn/api/restjs/resources/js/napas.paymentpage.min.js\" ||\n  0;\nconst RECONNECT_SOCKET = process.env.NEXT_PUBLIC_RECONNECT_SOCKET;\nconst GG_CLIENT_ID =\n  \"422317786610-d48sb63v1ckno5euvdehv7miuop1dvuf.apps.googleusercontent.com\" ||\n  0;\nconst FB_APP_ID = \"2155775314565146\" || 0;\n// const ENABLE_SDK = process.env.NEXT_PUBLIC_ENABLE_SDK || true\nconst ENABLE_SDK_NAPAS = \"true\" || 0;\nconst ENABLE_SDK_MOENGAGE = \"true\" || 0;\nconst ENABLE_SDK_GG = \"true\" || 0;\nconst ENABLE_SDK_FRESHCHAT = \"true\" || 0;\nconst ENABLE_SDK_MIDESK_CHAT = \"true\" || 0;\nconst ENABLE_SDK_FB = \"true\" || 0;\nconst ENABLE_SDK_AIACTIV = \"true\" || 0;\nconst ENABLE_SDK_GG_ADSENSE = \"true\" || 0;\nconst ENABLE_SDK_DMCA = \"true\" || 0;\nconst ENABLE_SDK_GTM = \"true\" || 0;\nconst ENABLE_SDK_GPT = \"true\" || 0;\nconst ENABLE_SDK_GG_IMA = \"true\" || 0;\nconst ENV = \"development\";\nconst APPLE_STORE_ID = \"id1357819721\";\nconst GOOGLE_STORE_ID = \"vieon.phim.tv\";\nconst IS_SERVER = !false;\nconst RECAPTCHA_SITE_KEY = \"6LfvjlcrAAAAAE9Sgwc3hMgOUGqPbiCf91WAg7Mq\" || 0;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/core/src/config/ConfigEnv.ts\n");

/***/ }),

/***/ "../../packages/core/src/constants/constants.ts":
/*!******************************************************!*\
  !*** ../../packages/core/src/constants/constants.ts ***!
  \******************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (360:19)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| };\n| \n> export const FOOTER: any = {\n|   NAV: {\n|     RULE: 'Quy định',");

/***/ }),

/***/ "../../packages/tracking/src/services/TrackingGTM.tsx":
/*!************************************************************!*\
  !*** ../../packages/tracking/src/services/TrackingGTM.tsx ***!
  \************************************************************/
/***/ (() => {

throw new Error("Module parse failed: Unexpected token (7:8)\nYou may need an appropriate loader to handle this file type, currently no loaders are configured to process this file. See https://webpack.js.org/concepts#loaders\n| import TrackingApp from './functions/TrackingApp';\n| \n> declare const window: any;\n| \n| export const GTMVieONScript = () => {");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.tsx"));
module.exports = __webpack_exports__;

})();