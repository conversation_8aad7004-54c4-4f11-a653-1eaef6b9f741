<svg width="1920" height="700" viewBox="0 0 1920 700" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_7_59)">
<g filter="url(#filter0_f_7_59)">
<path d="M1554.71 588.991C1568.42 588.991 1579.48 600.042 1579.48 613.732C1579.48 627.421 1568.42 638.472 1554.71 638.472C1541.01 638.472 1529.94 627.421 1529.94 613.732C1529.94 600.042 1541.01 588.991 1554.71 588.991ZM1637.27 704.446C1650.98 704.446 1662.04 715.496 1662.04 729.186C1662.04 742.876 1650.98 753.926 1637.27 753.926C1623.57 753.926 1612.5 742.876 1612.5 729.186C1612.5 715.496 1623.57 704.446 1637.27 704.446ZM1536.71 753.926L1513.43 730.67L1655.27 588.991L1678.55 612.247L1536.71 753.926ZM1705.8 520.543L1715.87 584.208L1773.83 613.732L1744.6 671.459L1773.99 729.186L1715.54 758.709L1705.47 822.374L1641.24 812.313L1595.5 858L1549.59 811.653L1485.86 822.209L1475.62 758.05L1417.99 728.691L1447.38 670.964L1418.16 613.732L1476.11 583.878L1486.19 520.873L1550.09 531.264L1595.99 485L1641.57 530.604L1705.8 520.543ZM1729.74 628.081L1686.81 605.485L1678.55 557.819L1630.67 564.746L1595.99 531.264L1561.32 564.746L1513.43 557.819L1505.18 605.485L1462.24 628.081L1483.71 671.459L1462.24 714.837L1505.18 737.433L1513.43 785.099L1561.32 778.172L1595.99 811.653L1630.67 778.172L1678.55 785.099L1686.81 737.433L1729.74 714.837L1708.27 671.459L1729.74 628.081V628.081Z" fill="url(#paint0_linear_7_59)"/>
</g>
<g opacity="0.3" filter="url(#filter1_f_7_59)">
<ellipse cx="477.858" cy="401.912" rx="477.858" ry="401.912" transform="matrix(-0.56165 0.827375 0.827375 0.56165 1760.02 339.182)" fill="url(#paint1_linear_7_59)"/>
</g>
<rect width="4.0628" height="4.0628" transform="matrix(-0.707105 0.707109 0.707105 0.707109 1793.8 616.021)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="2.83948" height="2.83948" transform="matrix(-0.707113 0.707101 0.707113 0.707101 1731.74 695.859)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="5.29067" height="5.29067" transform="matrix(-0.707121 0.707092 0.707121 0.707092 1766.33 681.66)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.839644 -0.543137 -0.543143 0.83964 1285.29 602.105)" fill="#006933"/>
<rect width="7.02292" height="7.02292" transform="matrix(-0.839641 -0.543142 -0.543139 0.839643 1209.83 580.51)" fill="#006933"/>
<rect width="3.26791" height="3.26791" transform="matrix(0.839639 0.543145 -0.543136 0.839645 1178.02 455.904)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.839667 -0.543102 -0.543179 0.839617 1128.83 521.6)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.83964 -0.543143 -0.543138 0.839644 1309.4 675.242)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.83964 -0.543143 -0.543138 0.839644 1419.95 632.594)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="2.42714" height="2.42714" transform="matrix(-0.839623 -0.54317 -0.54311 0.839661 1342 594.979)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="4.52826" height="4.52826" transform="matrix(-0.839649 -0.543129 -0.543152 0.839634 1360.07 621.365)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="7.02292" height="7.02292" transform="matrix(-0.985733 0.168318 0.16832 0.985732 1477.57 492.715)" fill="#006933"/>
<rect width="3.26791" height="3.26791" transform="matrix(0.985733 -0.168315 0.168323 0.985732 1369.81 422.486)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.985732 0.16832 0.168318 0.985733 1378.08 504.154)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.985732 0.168322 0.168316 0.985733 1614.97 495.076)" fill="#006933"/>
<rect width="3.47734" height="3.47734" transform="matrix(-0.985732 0.168323 0.168315 0.985733 1667.48 388.85)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="2.42714" height="2.42714" transform="matrix(-0.985735 0.168303 0.168335 0.98573 1584.65 413.908)" fill="#00FF85" fill-opacity="0.5"/>
<rect width="4.52826" height="4.52826" transform="matrix(-0.985733 0.168317 0.168321 0.985732 1615.83 421.105)" fill="#00FF85" fill-opacity="0.5"/>
</g>
<defs>
<filter id="filter0_f_7_59" x="1405.99" y="473" width="380" height="397" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6" result="effect1_foregroundBlur_7_59"/>
</filter>
<filter id="filter1_f_7_59" x="896.823" y="4.93449" width="1854.69" height="1910.7" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_7_59"/>
</filter>
<linearGradient id="paint0_linear_7_59" x1="1773.99" y1="671" x2="1371.99" y2="671" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D2328"/>
<stop offset="1" stop-color="#1D2328" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_7_59" x1="0" y1="401.914" x2="955.716" y2="401.914" gradientUnits="userSpaceOnUse">
<stop stop-color="#3AC882"/>
<stop offset="1" stop-color="#97E98A"/>
</linearGradient>
<clipPath id="clip0_7_59">
<rect width="1920" height="700" fill="white"/>
</clipPath>
</defs>
</svg>
