var partnerHandler = new function () {
    var RequestTPB = {
        version_platform: "1.0",
        event: "",
        checksum: "",
        vendorID: "",
        deviceCode: "",
        userToken: "",
        transactionID: "",
        providerCode: "",
        serviceCode: "",
        invoice: "",
        amount: "",
        transactionDesc: "",
        payload: "",
        currency: "VNĐ",
        transactionDate: "",
        viewInfo: []
    }
    var tpbankDataUI = {}

    // subscriber data from web
    window.addEventListener('message', function(event) {
        if(!tpbanksdk) return;
        tpbanksdk.updateFromNative(event.data)
    });

    this.updateFromDataNative = function(message) {
            // TODO process data received from native and web tpbank
    }

    this.setPlatform = function(message) {
        // TODO process data received from native and web tpbank
        if(message){
            const dataMsg = JSON.parse(message);
            if(typeof localStorage !== 'undefined'){
                localStorage.setItem("tpbankData<PERSON>", message)
            }
            tpbankDataUI = {
                ...tpbankDataUI,
                ... dataMsg
            }
            // Update data
            Object.keys(RequestTPB).forEach(key=> {
                if(dataMsg?.[key]){
                    RequestTPB[key] = dataMsg[key]
                }
            })
            if(dataMsg?.uiMode){
                const eleHead = document.getElementById("html-head");
                if(eleHead){
                    eleHead.classList.remove("theme-dark");
                    eleHead.classList.add("theme-light-tpbank");
                }
            }
        }
    }

    this.getDataUI = function(){
        return tpbankDataUI;
    }

    this.getRequestTPB = function(){
        return RequestTPB;
    }

    this.updateDataForNative = function(data = {}){
        RequestTPB = {...RequestTPB, ...data}
    }

    this.sendDataForNative = function(window,event) {
        if(!tpbanksdk) return;
        RequestTPB.event = event
        tpbanksdk.sendMessage(window,RequestTPB)
    }
}


