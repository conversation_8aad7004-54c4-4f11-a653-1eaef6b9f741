const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');

const assetPrefix = process.env.NEXT_PUBLIC_STATIC_DOMAIN || '';
const STATUS_EXPORT_SSR = process.env.NEXT_PUBLIC_EXPORT_SSR === 'true';
const buildID = `${new Date().getTime()}`;
const isProduction = process.env.NEXT_PUBLIC_NODE_ENV === 'production';

const nextConfig = {
  eslint: {
    dirs: ['.']
  },
  trailingSlash: true,
  compress: true,
  distDir: '_next/_next', // folder build
  assetPrefix, // CDN support
  // target, //static file required server
  // async rewrites() {
  // return [
  // {
  //   source: '/nghe-si/:slug.html',
  //   destination: '/nghe-si/:slug'
  // },
  // ];
  // },
  webpack: (config) => {
    if (process.env.NEXT_PUBLIC_ANALYZE === 'true' && !isProduction) {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'server',
          analyzerPort: 8888,
          openAnalyzer: true
        })
      );
    }
    if (!STATUS_EXPORT_SSR) {
      config.plugins.push(
        new CopyWebpackPlugin({
          patterns: [
            {
              from: path.join(__dirname, 'public'),
              to: path.join(__dirname, '_next')
            },
            // copy certificate for build local
            {
              from: path.join(__dirname, 'certificate'),
              to: path.join(__dirname, '_next/certificate')
            }
          ]
        })
      );
    }
    config.plugins.push(
      new webpack.DefinePlugin({
        'process.env.NEXT_PUBLIC_BUILD_ID': buildID
      }),
      new MiniCssExtractPlugin()
    );

    config.optimization = isProduction
      ? {
          mangleWasmImports: true,
          mergeDuplicateChunks: true,
          minimize: true,
          minimizer: [new TerserPlugin({ parallel: true })],
          runtimeChunk: 'single'
        }
      : {};
    return config;
  }
};
// check export home page static
if (STATUS_EXPORT_SSR) {
  nextConfig.exportPathMap = () => ({
    '/': { page: '/' }
  });
}

module.exports = nextConfig;
