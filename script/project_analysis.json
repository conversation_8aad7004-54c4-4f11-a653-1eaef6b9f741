{"total_files": 1050, "structure": {"apis": {"path": "apps/web/src/apis", "file_count": 25, "files": ["Payment.ts", "aiactiv-third-tracking.ts", "liveTVApi.ts", "LiveStream.ts", "ResultVoting.ts", "PaymentV2.ts", "userApi.ts", "AccessTrade.ts", "tvodApi.ts", "detailApi.ts", "sportApi.ts", "cmApi.ts", "axiosClient.ts", "tpbank/tpbankApi.ts", "MultiProfile/index.ts", "cm/TriggerApi.ts", "cm/ArtistApi.ts", "cm/ContentApi.ts", "cm/NotificationApi.ts", "cm/TagApi.ts", "cm/SearchApi.ts", "cm/PageApi.ts", "billing/BillingApi.ts", "billing/BillingInfo.ts", "ccu/qnetApi.ts"]}, "config": {"path": "apps/web/src/config", "file_count": 19, "files": ["ConfigGTM.ts", "ConfigUser.ts", "ConfigErrorPlayer.ts", "ConfigDMP.ts", "LocalStorage.ts", "ConfigSocket.ts", "ConfigGAPayment.ts", "ConfigSessionStorage.ts", "ConfigPayment.ts", "ConfigCookie.ts", "ConfigImage.ts", "ConfigSegment.ts", "ConfigApi.ts", "ConfigMoEnage.ts", "ConfigGA.ts", "ConfigLocalStorage.ts", "ConfigSeo.ts", "ConfigEnv.ts", "__mocks__/ConfigEnv.ts"]}, "constants": {"path": "apps/web/src/constants", "file_count": 4, "files": ["text.ts", "player.ts", "types.ts", "constants.ts"]}, "provider": {"path": "apps/web/src/provider", "file_count": 1, "files": ["providerRecaptcha.tsx"]}, "models": {"path": "apps/web/src/models", "file_count": 32, "files": ["transactionItem.ts", "payment.ts", "contentDetail.ts", "ribbonItem.ts", "login.ts", "CardItemSport.ts", "giftcode.ts", "tpbank.ts", "MessageItem.ts", "forgetPassword.ts", "ComingSoonItem.ts", "pageBanner.ts", "TipItem.ts", "trialApp.ts", "MenuItem.ts", "channelItem.ts", "LoginResponse.ts", "PopupItem.ts", "CardItem.ts", "subModels.ts", "voucherItem.ts", "ribbon.ts", "kidActivity.ts", "Profile.ts", "episodeItem.ts", "vipInfo.ts", "register.ts", "userType.ts", "packageItem.ts", "StreamItem.ts", "epgItem.ts", "UserPackageInfo.ts"]}, "script": {"path": "apps/web/src/script", "file_count": 11, "files": ["DMCA.ts", "FBSdk.ts", "ADMP.tsx", "AAdsNetwork.ts", "firebase.ts", "FreshChat.tsx", "SigmaDRM.tsx", "ScriptAfterInteractive.tsx", "MoEngage.ts", "GGAdsense.ts", "MideskChat.ts"]}, "styles": {"path": "apps/web/src/styles", "file_count": 20, "files": ["rotate.css", "methodItem.css", "Group.module.scss", "_font-roboto.scss", "style.css", "Page.module.scss", "globals.scss", "fonts/roboto-v30-latin_vietnamese-italic.woff2", "fonts/roboto-v30-latin_vietnamese-500.woff2", "fonts/roboto-v30-latin_vietnamese-regular.woff2", "fonts/roboto-v30-latin_vietnamese-700.woff2", "fonts/password.ttf", "fonts/roboto-v30-latin_vietnamese-100italic.woff2", "fonts/roboto-v30-latin_vietnamese-500italic.woff2", "fonts/roboto-v30-latin_vietnamese-700italic.woff2", "fonts/roboto-v30-latin_vietnamese-300italic.woff2", "fonts/roboto-v30-latin_vietnamese-300.woff2", "fonts/roboto-v30-latin_vietnamese-900.woff2", "fonts/roboto-v30-latin_vietnamese-100.woff2", "fonts/roboto-v30-latin_vietnamese-900italic.woff2"]}, "components": {"path": "apps/web/src/components", "file_count": 702, "files": []}, "profile": {"path": "apps/web/src/profile", "file_count": 63, "files": []}, "hooks": {"path": "apps/web/src/hooks", "file_count": 3, "files": ["useRibbonBoardData.ts", "useClickOutside.ts", "useRecaptcha.ts"]}, "actions": {"path": "apps/web/src/actions", "file_count": 32, "files": ["trigger.ts", "payment.ts", "globalAuth.ts", "livestream.ts", "actionType.ts", "search.ts", "moca.ts", "tpbank.ts", "app.ts", "viettelPay.ts", "detail.ts", "sport.ts", "player.ts", "menu.ts", "artist.ts", "popup.ts", "shopeepay.ts", "page.ts", "appConfig.ts", "multiProfile.ts", "profile.ts", "notification.ts", "episode.ts", "napas.ts", "vod.ts", "register.ts", "billing.ts", "user.ts", "liveTV.ts", "tags.ts", "momo.ts", "result-voting.ts"]}, "functions": {"path": "apps/web/src/functions", "file_count": 1, "files": ["functions.ts"]}, "tracking": {"path": "apps/web/src/tracking", "file_count": 39, "files": ["TrackingMoEngage.ts", "video.ts", "TrackingDMP.tsx", "GGAdsense.tsx", "TrackingGTM.tsx", "TrackingSegment.ts", "functions/TrackingMWebToApp.ts", "functions/TrackingTriggerPoint.ts", "functions/TrackingApp.ts", "functions/payment.ts", "functions/TrackingTVodDialog.ts", "functions/TrackingTVodDialogTimeOut.ts", "functions/TrackingVieIndexing.ts", "functions/TrackingTVodToastReminder.ts", "functions/TrackingMultiProfileChoose.ts", "functions/TrackingAddMultiProfile.ts", "functions/TrackingCardDetail.ts", "functions/TrackingContentForceLoginPopupAuth.ts", "functions/TrackingPaymentConversion.ts", "functions/TrackingPVod.ts", "functions/TrackingPlayer.ts", "functions/TrackingEndScreen.ts", "functions/TrackingBindAccount.ts", "functions/TrackingMultiProfileLobby.ts", "functions/TrackingEngagementTrigger.ts", "functions/TrackingSegmentedUser.ts", "functions/TrackingRegistrationTrigger.ts", "functions/TrackingFirstPay.ts", "functions/TrackingTVodOnboarding.ts", "functions/TrackingTVodReminder.ts", "functions/TrackingOEM.ts", "functions/TrackingCommingSoon.ts", "functions/TrackingAccountDeletion.ts", "functions/TrackingAuthentication.ts", "functions/TrackingRevisePayment.ts", "functions/TrackingEditMultiProfile.ts", "functions/TrackingLoyalty.ts", "functions/TrackingAds.ts", "sentry/index.ts"]}, "containers": {"path": "apps/web/src/containers", "file_count": 47, "files": ["LayoutContainer.tsx", "LayoutContainer.module.scss", "Home/RibbonCustomBoard.tsx", "Home/HomeContainer.module.scss", "Home/HomeContainer.tsx", "tpbank/TpbankContainer.tsx", "Collection/CollectionContainer.tsx", "AboutUs/index.tsx", "AboutUs/AnnouncementContainer.tsx", "AboutUs/Privacy120424Container.tsx", "AboutUs/UsageV2Container.tsx", "AboutUs/UsageContainer.tsx", "AboutUs/FaqsContainer.tsx", "AboutUs/UsageV3Container.tsx", "AboutUs/UsageV4Container.tsx", "AboutUs/Agreement120424Container.tsx", "AboutUs/UsageV1Container.tsx", "AboutUs/PrivacyContainer.tsx", "AboutUs/AgreementContainer.tsx", "AboutUs/RegulationContainer.tsx", "AboutUs/LicenseContainer.tsx", "AboutUs/PolicyCancellationContainer.tsx", "SmartTv/TransactionInformation.tsx", "ActiveLink/ActiveLink.tsx", "MultiProfile/index.tsx", "Payment/PaymentContainer.tsx", "Payment/PaymentResultContainer.tsx", "Sport/Sport.module.scss", "Sport/SportContainer.tsx", "Tags/TagsContainer.tsx", "Search/PageSearchContainer.tsx", "LiveStream/Stream.module.scss", "LiveStream/StreamContainer.tsx", "LiveTV/LiveTVContainer.tsx", "Detail/DetailContainer.tsx", "Profile/PopupUpdateProfileContainer.tsx", "Profile/ProfileContainer.tsx", "Profile/PopupConfirmUpdatePhoneContainer.tsx", "Voucher/VoucherContainer.tsx", "Maintenance/Maintenance.tsx", "ComingSoon/ComingSoonContainer.tsx", "Artist/ArtistContainer.tsx", "Header/SearchInputContainer.tsx", "Header/HeaderContainer.tsx", "Header/NotificationContainer.tsx", "SeoTemplate/SeoTemplateContainer.tsx", "ListWinners/index.tsx"]}, "helpers": {"path": "apps/web/src/helpers", "file_count": 3, "files": ["settings.ts", "common.ts", "utils.ts"]}, "reducers": {"path": "apps/web/src/reducers", "file_count": 28, "files": ["trigger.ts", "payment.ts", "globalAuth.ts", "livestream.ts", "search.ts", "tpbank.ts", "app.ts", "initialState.ts", "detail.ts", "sport.ts", "player.ts", "menu.ts", "artist.ts", "popup.ts", "page.ts", "appConfig.ts", "multiProfile.ts", "profile.ts", "notification.ts", "episode.ts", "index.ts", "vod.ts", "register.ts", "billing.ts", "user.ts", "liveTV.ts", "tags.ts", "result-voting.ts"]}, "services": {"path": "apps/web/src/services", "file_count": 19, "files": ["contentService.ts", "liveTVServices.ts", "datetimeServices.ts", "menuServices.ts", "tvodService.ts", "ribbonServices.ts", "trackingServices.ts", "detailServices.ts", "playerServices.ts", "videoIndexingService.ts", "userServices.ts", "trackingLog.ts", "tVod.ts", "pageServices.ts", "multiProfileServices.ts", "paymentServices.ts", "adsServices.ts", "handleOffMasterPlayerService.ts", "popupServices.ts"]}, "store": {"path": "apps/web/src/store", "file_count": 1, "files": ["createStore.ts"]}}, "migration_map": {"packages/core/api/": ["apis"], "packages/core/config/": ["config"], "packages/core/constants/": ["constants"], "packages/core/utils/": ["helpers"], "packages/core/services/": ["services"], "packages/core/store/": ["reducers", "actions", "store"], "packages/ui-kits/components/": ["components"], "packages/ui-kits/styles/": ["styles"], "packages/ui-kits/hooks/": ["hooks"], "packages/auth/": ["profile"], "packages/tracking/": ["tracking"], "packages/models/types/": ["models"], "apps/web-vieon/pages/": ["pages (from apps/web/pages)"], "apps/web-vieon/public/": ["public (from apps/web/public)"]}}