#!/usr/bin/env python3
"""
Script to analyze the current project structure and prepare for migration
to Modular Monorepo Architecture
"""

import os
import json
from pathlib import Path
from collections import defaultdict

def count_files_in_directory(directory_path):
    """Count files in a directory recursively"""
    if not os.path.exists(directory_path):
        return 0
    
    count = 0
    for root, dirs, files in os.walk(directory_path):
        # Skip node_modules and other build directories
        dirs[:] = [d for d in dirs if d not in ['node_modules', '.git', 'dist', 'build', '.next']]
        count += len([f for f in files if not f.startswith('.')])
    return count

def analyze_directory_structure(base_path):
    """Analyze the directory structure and file counts"""
    src_path = os.path.join(base_path, 'src')
    
    if not os.path.exists(src_path):
        print(f"Source directory not found: {src_path}")
        return {}
    
    structure = {}
    
    # Analyze each subdirectory in src
    for item in os.listdir(src_path):
        item_path = os.path.join(src_path, item)
        if os.path.isdir(item_path):
            file_count = count_files_in_directory(item_path)
            structure[item] = {
                'path': item_path,
                'file_count': file_count,
                'files': []
            }
            
            # Get list of files for smaller directories
            if file_count <= 50:
                for root, dirs, files in os.walk(item_path):
                    dirs[:] = [d for d in dirs if d not in ['node_modules', '.git', 'dist', 'build', '.next']]
                    for file in files:
                        if not file.startswith('.'):
                            rel_path = os.path.relpath(os.path.join(root, file), item_path)
                            structure[item]['files'].append(rel_path)
    
    return structure

def main():
    """Main analysis function"""
    base_path = 'apps/web'
    
    print("=== VieON Web Project Structure Analysis ===\n")
    
    # Check if the project exists
    if not os.path.exists(base_path):
        print(f"Project directory not found: {base_path}")
        return
    
    # Analyze current structure
    structure = analyze_directory_structure(base_path)
    
    print("Current Source Code Structure:")
    print("-" * 50)
    
    total_files = 0
    for directory, info in sorted(structure.items()):
        print(f"{directory:20} | {info['file_count']:4} files")
        total_files += info['file_count']
    
    print("-" * 50)
    print(f"{'TOTAL':20} | {total_files:4} files")
    print()
    
    # Detailed breakdown for key directories
    print("Detailed Breakdown:")
    print("-" * 50)
    
    key_directories = ['components', 'apis', 'services', 'reducers', 'actions', 'models', 'profile', 'tracking']
    
    for directory in key_directories:
        if directory in structure:
            info = structure[directory]
            print(f"\n{directory.upper()} ({info['file_count']} files):")
            if info['file_count'] <= 50:
                for file in sorted(info['files'])[:20]:  # Show first 20 files
                    print(f"  - {file}")
                if len(info['files']) > 20:
                    print(f"  ... and {len(info['files']) - 20} more files")
    
    # Migration mapping
    print("\n" + "=" * 60)
    print("MIGRATION MAPPING TO MODULAR MONOREPO")
    print("=" * 60)
    
    migration_map = {
        'packages/core/api/': ['apis'],
        'packages/core/config/': ['config'],
        'packages/core/constants/': ['constants'],
        'packages/core/utils/': ['helpers'],
        'packages/core/services/': ['services'],
        'packages/core/store/': ['reducers', 'actions', 'store'],
        'packages/ui-kits/components/': ['components'],
        'packages/ui-kits/styles/': ['styles'],
        'packages/ui-kits/hooks/': ['hooks'],
        'packages/auth/': ['profile'],
        'packages/tracking/': ['tracking'],
        'packages/models/types/': ['models'],
        'apps/web-vieon/pages/': ['pages (from apps/web/pages)'],
        'apps/web-vieon/public/': ['public (from apps/web/public)']
    }
    
    for target, sources in migration_map.items():
        print(f"\n{target}")
        for source in sources:
            if source.startswith('pages') or source.startswith('public'):
                print(f"  ← {source}")
            elif source in structure:
                print(f"  ← src/{source} ({structure[source]['file_count']} files)")
            else:
                print(f"  ← src/{source} (not found)")
    
    # Save analysis to JSON for further processing
    analysis_data = {
        'total_files': total_files,
        'structure': structure,
        'migration_map': migration_map
    }
    
    with open('script/project_analysis.json', 'w') as f:
        json.dump(analysis_data, f, indent=2)
    
    print(f"\n\nAnalysis saved to: script/project_analysis.json")
    print("Ready for migration planning!")

if __name__ == "__main__":
    main()
