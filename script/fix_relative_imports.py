#!/usr/bin/env python3
"""
Fix relative import paths within packages
"""

import os
import re
from pathlib import Path

def get_relative_path(from_file, to_dir):
    """Calculate relative path from file to directory"""
    from_dir = os.path.dirname(from_file)
    rel_path = os.path.relpath(to_dir, from_dir)
    if rel_path == '.':
        return './'
    elif not rel_path.startswith('.'):
        return './' + rel_path
    return rel_path

def fix_core_package_imports(file_path):
    """Fix imports within core package"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Get the directory of the current file relative to packages/core/src
        rel_file_path = os.path.relpath(file_path, 'packages/core/src')
        file_dir = os.path.dirname(rel_file_path)
        
        # Calculate relative paths to each directory
        config_path = get_relative_path(rel_file_path, 'packages/core/src/config')
        constants_path = get_relative_path(rel_file_path, 'packages/core/src/constants')
        utils_path = get_relative_path(rel_file_path, 'packages/core/src/utils')
        services_path = get_relative_path(rel_file_path, 'packages/core/src/services')
        api_path = get_relative_path(rel_file_path, 'packages/core/src/api')
        store_path = get_relative_path(rel_file_path, 'packages/core/src/store')
        
        # Fix relative imports
        content = re.sub(r"from\s+['\"]../config/", f"from '{config_path}/", content)
        content = re.sub(r"from\s+['\"]../constants/", f"from '{constants_path}/", content)
        content = re.sub(r"from\s+['\"]../utils/", f"from '{utils_path}/", content)
        content = re.sub(r"from\s+['\"]../services/", f"from '{services_path}/", content)
        content = re.sub(r"from\s+['\"]../api/", f"from '{api_path}/", content)
        content = re.sub(r"from\s+['\"]../store/", f"from '{store_path}/", content)
        
        # Fix specific cases for subdirectories
        if file_dir.startswith('api/'):
            content = re.sub(r"from\s+['\"]../config/", "from '../../config/", content)
            content = re.sub(r"from\s+['\"]../constants/", "from '../../constants/", content)
            content = re.sub(r"from\s+['\"]../utils/", "from '../../utils/", content)
            content = re.sub(r"from\s+['\"]../services/", "from '../../services/", content)
            content = re.sub(r"from\s+['\"]../api/", "from '../", content)
            content = re.sub(r"from\s+['\"]../store/", "from '../../store/", content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def fix_package_relative_imports(package_dir):
    """Fix relative imports in a package"""
    
    updated_files = []
    
    for root, dirs, files in os.walk(package_dir):
        for file in files:
            if file.endswith(('.ts', '.tsx')):
                file_path = os.path.join(root, file)
                
                if 'packages/core' in file_path:
                    if fix_core_package_imports(file_path):
                        updated_files.append(file_path)
    
    return updated_files

def main():
    """Main function to fix relative imports"""
    
    print("🔧 FIXING RELATIVE IMPORT PATHS")
    print("=" * 60)
    
    # Fix core package imports
    core_updated = fix_package_relative_imports('packages/core/src')
    
    print(f"📦 Core package: {len(core_updated)} files updated")
    
    # Now test compilation
    print("\n🧪 TESTING TYPESCRIPT COMPILATION")
    print("-" * 40)
    
    import subprocess
    try:
        result = subprocess.run(
            ["npx", "tsc", "--noEmit", "--skipLibCheck"],
            cwd="packages/core",
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Core package compiles successfully!")
        else:
            print("❌ Core package still has errors:")
            print(result.stderr[:500])
            
    except Exception as e:
        print(f"Error testing compilation: {e}")
    
    print("\n" + "=" * 60)
    print("RELATIVE IMPORT FIXES COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    main()
