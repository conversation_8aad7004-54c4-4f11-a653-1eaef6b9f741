#!/usr/bin/env python3
"""
Update import paths in pages to use new workspace packages
"""

import os
import re
from pathlib import Path

def get_import_mapping():
    """Define the mapping from old import paths to new workspace packages"""
    
    return {
        # Core package mappings
        r'@apis/': '@vieon/core/api/',
        r'@config/': '@vieon/core/config/',
        r'@constants/': '@vieon/core/constants/',
        r'@helpers/': '@vieon/core/utils/',
        r'@services/': '@vieon/core/services/',
        r'@actions/': '@vieon/core/store/actions/',
        r'@reducers/': '@vieon/core/store/reducers/',
        r'@store/': '@vieon/core/store/',
        
        # UI Kits package mappings
        r'@components/': '@vieon/ui-kits/components/',
        r'@styles/': '@vieon/ui-kits/styles/',
        r'@hooks/': '@vieon/ui-kits/hooks/',
        
        # Models package mappings
        r'@models/': '@vieon/models/',
        
        # Auth package mappings
        r'@profile/': '@vieon/auth/services/',
        
        # Tracking package mappings
        r'@tracking/': '@vieon/tracking/services/',
        
        # Special cases
        r'@customHook': '@vieon/ui-kits/hooks/customHook',
        r'@/': '@vieon/',  # Generic @ prefix
        
        # Container mappings (these might be in ui-kits/components)
        r'@containers/': '@vieon/ui-kits/components/containers/',
        
        # Script mappings (these might be in core/utils)
        r'@script/': '@vieon/core/utils/script/',
        
        # Functions mappings (these might be in core/utils)
        r'@functions/': '@vieon/core/utils/functions/',
    }

def update_file_imports(file_path):
    """Update import statements in a single file"""
    
    if not file_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        import_mapping = get_import_mapping()
        
        # Update import statements
        for old_pattern, new_path in import_mapping.items():
            # Handle both single and double quotes
            # Pattern for: import ... from '@old/path'
            pattern1 = rf"(import\s+[^'\"]*from\s+['\"]){old_pattern}([^'\"]*['\"])"
            replacement1 = rf"\1{new_path}\2"
            content = re.sub(pattern1, replacement1, content)
            
            # Pattern for: import('@old/path')
            pattern2 = rf"(import\s*\(\s*['\"]){old_pattern}([^'\"]*['\"])"
            replacement2 = rf"\1{new_path}\2"
            content = re.sub(pattern2, replacement2, content)
        
        # Special handling for some common patterns
        # Fix double slashes that might have been created
        content = re.sub(r'@vieon/([^/]+)//+', r'@vieon/\1/', content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def update_directory_imports(directory_path):
    """Update import statements in all files in a directory"""
    
    updated_files = []
    total_files = 0
    
    print(f"📁 Updating imports in: {directory_path}")
    
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            if file.endswith(('.ts', '.tsx', '.js', '.jsx')):
                file_path = os.path.join(root, file)
                total_files += 1
                
                if update_file_imports(file_path):
                    rel_path = os.path.relpath(file_path, directory_path)
                    updated_files.append(rel_path)
                    print(f"  ✓ {rel_path}")
    
    print(f"  📊 Updated {len(updated_files)} out of {total_files} files")
    return updated_files

def main():
    """Main function to update all import paths"""
    
    print("🔄 UPDATING IMPORT PATHS TO WORKSPACE PACKAGES")
    print("=" * 60)
    
    # Check if pages directory exists
    pages_dir = "apps/web-vieon/pages"
    if not os.path.exists(pages_dir):
        print(f"❌ Pages directory not found: {pages_dir}")
        return False
    
    # Update imports in pages
    updated_files = update_directory_imports(pages_dir)
    
    # Also update server files if they exist
    server_dirs = [
        "apps/web-vieon/server",
        "apps/web-vieon/server-static"
    ]
    
    total_updated = len(updated_files)
    
    for server_dir in server_dirs:
        if os.path.exists(server_dir):
            server_updated = update_directory_imports(server_dir)
            total_updated += len(server_updated)
    
    print("\n" + "=" * 60)
    print("IMPORT PATH UPDATE COMPLETED!")
    print("=" * 60)
    print(f"✅ Total files updated: {total_updated}")
    print("📝 All import paths now use workspace packages")
    
    print("\n🔍 IMPORT MAPPING APPLIED:")
    print("-" * 40)
    mapping = get_import_mapping()
    for old, new in list(mapping.items())[:10]:  # Show first 10 mappings
        print(f"  {old:<20} → {new}")
    if len(mapping) > 10:
        print(f"  ... and {len(mapping) - 10} more mappings")
    
    print("\n🚀 NEXT STEPS:")
    print("-" * 40)
    print("1. Build packages: pnpm build")
    print("2. Start development: pnpm dev")
    print("3. Test application functionality")
    
    return True

if __name__ == "__main__":
    main()
