#!/usr/bin/env python3
"""
Fix all relative import paths correctly based on directory depth
"""

import os
import re
from pathlib import Path

def fix_relative_imports_in_file(file_path):
    """Fix relative imports based on file location"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Determine the depth of the file relative to the package src directory
        if 'packages/core/src' in file_path:
            # Get relative path from packages/core/src
            rel_path = os.path.relpath(file_path, 'packages/core/src')
            depth = len(Path(rel_path).parts) - 1  # -1 because we don't count the file itself
            
            # Create the correct relative path prefix
            if depth == 0:
                # File is directly in src/
                prefix = './'
            else:
                # File is in subdirectory, need to go up
                prefix = '../' * depth
            
            # Fix imports based on depth
            content = re.sub(r"from\s+['\"]../config/", f"from '{prefix}config/", content)
            content = re.sub(r"from\s+['\"]../constants/", f"from '{prefix}constants/", content)
            content = re.sub(r"from\s+['\"]../utils/", f"from '{prefix}utils/", content)
            content = re.sub(r"from\s+['\"]../services/", f"from '{prefix}services/", content)
            content = re.sub(r"from\s+['\"]../store/", f"from '{prefix}store/", content)
            
            # Special handling for api imports
            if 'api/' in rel_path:
                # For files in api subdirectories, api imports should go to parent api directory
                if depth > 1:  # In api subdirectory
                    content = re.sub(r"from\s+['\"]../api/", "from '../", content)
                else:  # In api root
                    content = re.sub(r"from\s+['\"]../api/", f"from '{prefix}api/", content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def fix_all_packages():
    """Fix relative imports in all packages"""
    
    print("🔧 FIXING ALL RELATIVE IMPORT PATHS")
    print("=" * 60)
    
    packages = ['packages/core/src']
    total_updated = 0
    
    for package_dir in packages:
        if not os.path.exists(package_dir):
            continue
            
        print(f"\n📦 Processing {package_dir}...")
        updated_files = []
        
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                if file.endswith(('.ts', '.tsx')):
                    file_path = os.path.join(root, file)
                    
                    if fix_relative_imports_in_file(file_path):
                        rel_file = os.path.relpath(file_path, package_dir)
                        updated_files.append(rel_file)
        
        print(f"  📊 Updated {len(updated_files)} files")
        total_updated += len(updated_files)
        
        if updated_files:
            print(f"  📝 Sample files:")
            for file in updated_files[:3]:
                print(f"    - {file}")
            if len(updated_files) > 3:
                print(f"    ... and {len(updated_files) - 3} more")
    
    print(f"\n✅ Total files updated: {total_updated}")
    return total_updated

def test_compilation():
    """Test TypeScript compilation"""
    
    print("\n🧪 TESTING TYPESCRIPT COMPILATION")
    print("-" * 40)
    
    import subprocess
    
    packages_to_test = [
        ('packages/core', 'Core package'),
        ('packages/ui-kits', 'UI Kits package'),
        ('packages/auth', 'Auth package'),
        ('packages/tracking', 'Tracking package')
    ]
    
    for package_dir, package_name in packages_to_test:
        if os.path.exists(f"{package_dir}/tsconfig.json"):
            try:
                result = subprocess.run(
                    ["npx", "tsc", "--noEmit", "--skipLibCheck"],
                    cwd=package_dir,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    print(f"  ✅ {package_name} compiles successfully!")
                else:
                    error_lines = result.stderr.split('\n')[:5]  # First 5 errors
                    print(f"  ❌ {package_name} has errors:")
                    for line in error_lines:
                        if line.strip():
                            print(f"     {line}")
                    
            except Exception as e:
                print(f"  ⚠️  {package_name} test failed: {e}")

def main():
    """Main function"""
    
    # Fix relative imports
    fix_all_packages()
    
    # Test compilation
    test_compilation()
    
    print("\n" + "=" * 60)
    print("RELATIVE PATH FIXES COMPLETED!")
    print("=" * 60)
    print("\n🚀 NEXT STEPS:")
    print("1. Test server: pnpm start")
    print("2. Check browser for runtime errors")
    print("3. Fix any remaining issues")

if __name__ == "__main__":
    main()
